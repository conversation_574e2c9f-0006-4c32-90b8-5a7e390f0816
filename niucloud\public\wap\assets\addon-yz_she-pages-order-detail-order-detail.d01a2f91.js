import{d as e,r as a,J as t,A as l,M as s,p as c,o as u,c as i,w as r,g as d,b as o,$ as n,t as _,x as f,n as v,y as p,z as y,F as m,e as k,i as h,j as x,k as b,S as g,Q as q,bG as w}from"./index-3caf046d.js";import{_ as j}from"./u-icon.ba193921.js";import{d as F,a as z,b as C,r as I}from"./recycle_order.a252d983.js";import{_ as $}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const A=$(e({__name:"order-detail",setup(e){const $=a(""),A=a(!1),G=a({id:0,order_no:"",status:1,product_name:"",product_code:"",product_image:"",quantity:1,expected_price:0,final_price:null,total_amount:null,voucher_amount:0,source_type:1,delivery_type:1,pickup_contact_name:"",pickup_contact_phone:"",pickup_address_detail:"",pickup_time:"",express_company:"",express_number:"",quality_score:null,quality_note:"",create_time:"",update_time:""}),J=a([{status:1,text:"待取件"},{status:2,text:"待收货"},{status:3,text:"待质检"},{status:4,text:"待确认"},{status:7,text:"已完成"}]);t((e=>{e.id&&($.value=e.id,M())}));const M=async()=>{if($.value){A.value=!0;try{const e=await F(parseInt($.value));1===e.code?G.value={...e.data,product_image:e.data.product_image?l(e.data.product_image):""}:s({title:e.msg||"获取订单详情失败",icon:"none"})}catch(e){console.error("加载订单详情失败:",e),s({title:"加载失败，请重试",icon:"none"})}finally{A.value=!1}}},Q=e=>({1:"待取件",2:"待收货",3:"待质检",4:"待确认",5:"待退回",6:"已退回",7:"已完成",8:"已取消"}[e]||"未知状态"),S=e=>({1:"等待快递员上门取件",2:"商品已寄出，等待平台收货",3:"平台已收货，正在进行质检",4:"质检完成，等待您确认价格",5:"商品正在退回中",6:"商品已退回",7:"订单已完成，感谢您的使用",8:"订单已取消"}[e]||""),B=e=>({1:"clock",2:"car",3:"search",4:"checkmark-circle",5:"return-left",6:"return-left",7:"checkmark-circle",8:"close-circle"}[e]||"clock"),D=c((()=>G.value.voucher_amount>0||null!==G.value.final_price||null!==G.value.total_amount)),E=c((()=>null!==G.value.quality_score||G.value.quality_note)),H=c((()=>K(G.value.status).length>0)),K=e=>{const a=[];switch(e){case 1:a.push({type:"cancel",text:"取消订单"});break;case 4:a.push({type:"reject",text:"申请退回"}),a.push({type:"confirm",text:"确认价格"});break;case 2:case 3:a.push({type:"contact",text:"联系客服"})}return a},L=async()=>{w({title:"确认取消",content:"确定要取消这个订单吗？",success:async e=>{if(e.confirm)try{const e=await z(G.value.id);1===e.code?(s({title:"订单已取消",icon:"success"}),M()):s({title:e.msg||"取消失败",icon:"none"})}catch(a){s({title:"取消失败，请重试",icon:"none"})}}})},N=async()=>{var e;w({title:"确认价格",content:`确认接受质检价格 ¥${null==(e=G.value.final_price)?void 0:e.toFixed(2)} 吗？`,success:async e=>{if(e.confirm)try{const e=await C(G.value.id);1===e.code?(s({title:"确认成功",icon:"success"}),M()):s({title:e.msg||"确认失败",icon:"none"})}catch(a){s({title:"确认失败，请重试",icon:"none"})}}})},O=async()=>{w({title:"申请退回",content:"不接受质检价格，申请退回商品？",success:async e=>{if(e.confirm)try{const e=await I(G.value.id,"不接受质检价格");1===e.code?(s({title:"申请成功",icon:"success"}),M()):s({title:e.msg||"申请失败",icon:"none"})}catch(a){s({title:"申请失败，请重试",icon:"none"})}}})},P=()=>{w({title:"联系客服",content:"如有疑问，请联系客服：************",showCancel:!1})};return(e,a)=>{const t=h(x("u-icon"),j),l=b,s=g,c=q;return u(),i(l,{class:"order-detail-page"},{default:r((()=>[d(" 订单状态卡片 "),o(l,{class:"status-card"},{default:r((()=>[o(l,{class:"status-header"},{default:r((()=>{return[o(l,{class:n(["status-icon",(e=G.value.status,8===e?"status-cancelled":7===e?"status-completed":e>=5?"status-warning":"status-processing")])},{default:r((()=>[o(t,{name:B(G.value.status),color:"#fff",size:"24"},null,8,["name"])])),_:1},8,["class"]),o(l,{class:"status-info"},{default:r((()=>[o(s,{class:"status-title"},{default:r((()=>[_(f(Q(G.value.status)),1)])),_:1}),o(s,{class:"status-desc"},{default:r((()=>[_(f(S(G.value.status)),1)])),_:1})])),_:1})];var e})),_:1}),d(" 进度条 "),o(l,{class:"progress-bar"},{default:r((()=>[o(l,{class:"progress-line"},{default:r((()=>{return[o(l,{class:"progress-fill",style:v({width:(e=G.value.status,{1:"20%",2:"40%",3:"60%",4:"80%",5:"60%",6:"60%",7:"100%",8:"0%"}[e]||"0%")})},null,8,["style"])];var e})),_:1}),o(l,{class:"progress-steps"},{default:r((()=>[(u(!0),p(m,null,y(J.value,(e=>(u(),i(l,{class:n(["step-item",{active:e.status<=G.value.status,current:e.status===G.value.status}]),key:e.status},{default:r((()=>[o(l,{class:"step-dot"}),o(s,{class:"step-text"},{default:r((()=>[_(f(e.text),1)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1})])),_:1})])),_:1}),d(" 商品信息卡片 "),o(l,{class:"product-card"},{default:r((()=>[o(l,{class:"card-header"},{default:r((()=>[o(s,{class:"card-title"},{default:r((()=>[_("商品信息")])),_:1}),o(s,{class:"order-no"},{default:r((()=>[_(f(G.value.order_no),1)])),_:1})])),_:1}),o(l,{class:"product-info"},{default:r((()=>[d(" 商品图片或图标 "),G.value.product_image?(u(),i(c,{key:0,src:G.value.product_image,class:"product-image",mode:"aspectFit"},null,8,["src"])):(u(),i(l,{key:1,class:"product-image no-image"},{default:r((()=>[o(t,{name:"shopping-cart-fill",color:"#16a085",size:"40"})])),_:1})),o(l,{class:"product-details"},{default:r((()=>[o(s,{class:"product-name"},{default:r((()=>[_(f(G.value.product_name||"回收商品"),1)])),_:1}),G.value.product_code?(u(),i(s,{key:0,class:"product-code"},{default:r((()=>[_(f(G.value.product_code),1)])),_:1})):d("v-if",!0),o(l,{class:"product-meta"},{default:r((()=>[o(s,{class:"meta-item"},{default:r((()=>[_("数量: "+f(G.value.quantity)+"件",1)])),_:1}),o(s,{class:"meta-item"},{default:r((()=>{return[_("来源: "+f((e=G.value.source_type,{1:"估价订单",2:"直接回收",3:"批量下单"}[e]||"未知来源")),1)];var e})),_:1})])),_:1})])),_:1}),o(l,{class:"price-info"},{default:r((()=>[o(s,{class:"price-label"},{default:r((()=>{return[_(f((e=G.value.status,e>=7?"最终价格":e>=4?"质检价格":"预期价格")),1)];var e})),_:1}),o(l,{class:"price-amount"},{default:r((()=>[o(s,{class:"currency"},{default:r((()=>[_("¥")])),_:1}),o(s,{class:"price-value"},{default:r((()=>{return[_(f((e=G.value,null!==e.total_amount?e.total_amount.toFixed(2):null!==e.final_price?e.final_price.toFixed(2):(null==(a=e.expected_price)?void 0:a.toFixed(2))||"0.00")),1)];var e,a})),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),d(" 配送信息卡片 "),o(l,{class:"delivery-card"},{default:r((()=>[o(l,{class:"card-header"},{default:r((()=>[o(s,{class:"card-title"},{default:r((()=>[_("配送信息")])),_:1})])),_:1}),o(l,{class:"delivery-info"},{default:r((()=>[o(l,{class:"delivery-item"},{default:r((()=>[o(s,{class:"delivery-label"},{default:r((()=>[_("配送方式")])),_:1}),o(s,{class:"delivery-value"},{default:r((()=>{return[_(f((e=G.value.delivery_type,1===e?"快递上门":"用户自寄")),1)];var e})),_:1})])),_:1}),d(" 快递上门信息 "),1===G.value.delivery_type?(u(),p(m,{key:0},[G.value.pickup_contact_name?(u(),i(l,{key:0,class:"delivery-item"},{default:r((()=>[o(s,{class:"delivery-label"},{default:r((()=>[_("联系人")])),_:1}),o(s,{class:"delivery-value"},{default:r((()=>[_(f(G.value.pickup_contact_name),1)])),_:1})])),_:1})):d("v-if",!0),G.value.pickup_contact_phone?(u(),i(l,{key:1,class:"delivery-item"},{default:r((()=>[o(s,{class:"delivery-label"},{default:r((()=>[_("联系电话")])),_:1}),o(s,{class:"delivery-value"},{default:r((()=>[_(f(G.value.pickup_contact_phone),1)])),_:1})])),_:1})):d("v-if",!0),G.value.pickup_address_detail?(u(),i(l,{key:2,class:"delivery-item"},{default:r((()=>[o(s,{class:"delivery-label"},{default:r((()=>[_("取件地址")])),_:1}),o(s,{class:"delivery-value address-detail"},{default:r((()=>[_(f(G.value.pickup_address_detail),1)])),_:1})])),_:1})):d("v-if",!0),G.value.pickup_time?(u(),i(l,{key:3,class:"delivery-item"},{default:r((()=>[o(s,{class:"delivery-label"},{default:r((()=>[_("预约时间")])),_:1}),o(s,{class:"delivery-value"},{default:r((()=>[_(f(G.value.pickup_time),1)])),_:1})])),_:1})):d("v-if",!0)],64)):d("v-if",!0),d(" 用户自寄信息 "),2===G.value.delivery_type?(u(),p(m,{key:1},[G.value.express_company?(u(),i(l,{key:0,class:"delivery-item"},{default:r((()=>[o(s,{class:"delivery-label"},{default:r((()=>[_("快递公司")])),_:1}),o(s,{class:"delivery-value"},{default:r((()=>[_(f(G.value.express_company),1)])),_:1})])),_:1})):d("v-if",!0),G.value.express_number?(u(),i(l,{key:1,class:"delivery-item"},{default:r((()=>[o(s,{class:"delivery-label"},{default:r((()=>[_("快递单号")])),_:1}),o(s,{class:"delivery-value"},{default:r((()=>[_(f(G.value.express_number),1)])),_:1})])),_:1})):d("v-if",!0)],64)):d("v-if",!0)])),_:1})])),_:1}),d(" 价格明细卡片 "),k(D)?(u(),i(l,{key:0,class:"price-card"},{default:r((()=>[o(l,{class:"card-header"},{default:r((()=>[o(s,{class:"card-title"},{default:r((()=>[_("价格明细")])),_:1})])),_:1}),o(l,{class:"price-detail"},{default:r((()=>[o(l,{class:"price-item"},{default:r((()=>[o(s,{class:"price-item-label"},{default:r((()=>[_("预期价格")])),_:1}),o(s,{class:"price-item-value"},{default:r((()=>[_("¥"+f(G.value.expected_price||"0.00"),1)])),_:1})])),_:1}),G.value.voucher_amount>0?(u(),i(l,{key:0,class:"price-item"},{default:r((()=>[o(s,{class:"price-item-label"},{default:r((()=>[_("加价券")])),_:1}),o(s,{class:"price-item-value voucher"},{default:r((()=>[_("+¥"+f(G.value.voucher_amount),1)])),_:1})])),_:1})):d("v-if",!0),null!==G.value.final_price?(u(),i(l,{key:1,class:"price-item"},{default:r((()=>[o(s,{class:"price-item-label"},{default:r((()=>[_("质检价格")])),_:1}),o(s,{class:"price-item-value"},{default:r((()=>[_("¥"+f(G.value.final_price),1)])),_:1})])),_:1})):d("v-if",!0),null!==G.value.total_amount?(u(),i(l,{key:2,class:"price-item total"},{default:r((()=>[o(s,{class:"price-item-label"},{default:r((()=>[_("最终金额")])),_:1}),o(s,{class:"price-item-value"},{default:r((()=>[_("¥"+f(G.value.total_amount),1)])),_:1})])),_:1})):d("v-if",!0)])),_:1})])),_:1})):d("v-if",!0),d(" 质检信息卡片 "),k(E)?(u(),i(l,{key:1,class:"quality-card"},{default:r((()=>[o(l,{class:"card-header"},{default:r((()=>[o(s,{class:"card-title"},{default:r((()=>[_("质检信息")])),_:1})])),_:1}),o(l,{class:"quality-info"},{default:r((()=>[null!==G.value.quality_score?(u(),i(l,{key:0,class:"quality-item"},{default:r((()=>[o(s,{class:"quality-label"},{default:r((()=>[_("质检评分")])),_:1}),o(l,{class:"quality-score"},{default:r((()=>[o(s,{class:"score-value"},{default:r((()=>[_(f(G.value.quality_score),1)])),_:1}),o(s,{class:"score-unit"},{default:r((()=>[_("分")])),_:1})])),_:1})])),_:1})):d("v-if",!0),G.value.quality_note?(u(),i(l,{key:1,class:"quality-item"},{default:r((()=>[o(s,{class:"quality-label"},{default:r((()=>[_("质检说明")])),_:1}),o(s,{class:"quality-note"},{default:r((()=>[_(f(G.value.quality_note),1)])),_:1})])),_:1})):d("v-if",!0)])),_:1})])),_:1})):d("v-if",!0),d(" 订单操作按钮 "),k(H)?(u(),i(l,{key:2,class:"action-buttons"},{default:r((()=>[(u(!0),p(m,null,y(K(G.value.status),(e=>(u(),i(l,{class:n(["action-btn",e.type]),key:e.type,onClick:a=>(async e=>{switch(e){case"cancel":await L();break;case"confirm":await N();break;case"reject":await O();break;case"contact":P()}})(e.type)},{default:r((()=>[o(s,{class:"action-text"},{default:r((()=>[_(f(e.text),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})):d("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-f5464f22"]]);export{A as default};
