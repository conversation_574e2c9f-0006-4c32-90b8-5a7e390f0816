import{d as e,r as a,p as l,_ as t,o as s,c as o,w as c,g as r,b as u,y as n,z as i,F as d,e as f,t as p,x as v,A as _,M as g,S as m,k as y,au as h,i as b,j as k,$ as x,O as w,bV as N,bG as C,be as j,bN as I,bO as $,Q as z}from"./index-3caf046d.js";import{_ as E}from"./u-icon.ba193921.js";import{g as T,a as R,r as S,b as F}from"./recycle_order.a252d983.js";import{_ as H}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const M=H(e({__name:"order-list",setup(e){const H=a([{label:"全部",value:"all"},{label:"待取件",value:1},{label:"待收货",value:2},{label:"待质检",value:3},{label:"待确认",value:4},{label:"待退回",value:5},{label:"已退回",value:6},{label:"已完成",value:7},{label:"已取消",value:8}]),M=a("all"),O=a(1),V=a(10),A=a(0),G=a(!1),Q=a(!0),q=a([]),B=l((()=>"all"===M.value?q.value:q.value.filter((e=>e.status===M.value)))),D=async(e=!1)=>{if(!G.value)try{G.value=!0;const a={page:O.value,limit:V.value};"all"!==M.value&&(a.status=M.value),console.log("加载订单列表参数:",a);const l=await T(a);if(1!==l.code)throw new Error(l.msg||"获取订单列表失败");{const a=(l.data.data||[]).map((e=>({...e,orderNo:e.order_no,productName:e.product_name,productCode:e.product_code,productImage:e.product_image?_(e.product_image):"",createTime:e.create_time_text,price:e.expected_price||0})));q.value=e?[...q.value,...a]:a,A.value=l.data.total||0,Q.value=q.value.length<A.value,console.log("订单列表加载成功:",{current:q.value.length,total:A.value,hasMore:Q.value})}}catch(a){console.error("加载订单列表失败:",a),g({title:a.message||"加载失败",icon:"none"})}finally{G.value=!1}},J=()=>{Q.value&&!G.value&&(O.value++,D(!0))},K=()=>{O.value=1,q.value=[],D()},L=e=>{if("all"===e)return"全部";return{1:"待取件",2:"待收货",3:"待质检",4:"待确认",5:"待退回",6:"已退回",7:"已完成",8:"已取消"}[e]||"未知状态"},P=e=>({1:[{type:"cancel",text:"取消订单"},{type:"contact",text:"联系客服"}],4:[{type:"reject",text:"拒绝价格"},{type:"accept",text:"接受价格"}],6:[{type:"logistics",text:"查看物流"}]}[e]||[]),U=async e=>{C({title:"确认取消",content:"确定要取消这个订单吗？",success:async a=>{if(a.confirm)try{const a=await R(e.id,"用户主动取消");if(1!==a.code)throw new Error(a.msg||"取消订单失败");{const a=q.value.findIndex((a=>a.id===e.id));-1!==a&&(q.value[a].status=8),g({title:"订单已取消",icon:"success"})}}catch(l){console.error("取消订单失败:",l),g({title:l.message||"取消失败",icon:"none"})}}})},W=e=>{g({title:"正在为您转接客服",icon:"none"})},X=async e=>{C({title:"拒绝价格",content:`确定要拒绝¥${e.price}的价格吗？商品将退回给您。`,success:async a=>{if(a.confirm)try{const a=await S(e.id,"用户拒绝质检价格");if(1!==a.code)throw new Error(a.msg||"申请退回失败");{const a=q.value.findIndex((a=>a.id===e.id));-1!==a&&(q.value[a].status=5),g({title:"已拒绝价格，商品将退回",icon:"success"})}}catch(l){console.error("拒绝价格失败:",l),g({title:l.message||"操作失败",icon:"none"})}}})},Y=async e=>{C({title:"接受价格",content:`确定接受¥${e.price}的价格吗？`,success:async a=>{if(a.confirm)try{const a=await F(e.id);if(1!==a.code)throw new Error(a.msg||"确认价格失败");{const a=q.value.findIndex((a=>a.id===e.id));-1!==a&&(q.value[a].status=7),g({title:"价格已确认，交易完成",icon:"success"})}}catch(l){console.error("确认价格失败:",l),g({title:l.message||"操作失败",icon:"none"})}}})},Z=e=>{j({url:`/addon/yz_she/pages/logistics/detail?orderNo=${e.orderNo}`})},ee=e=>{console.log("跳转到订单详情:",e);const a=e.id||e.orderNo;a?(I({title:"加载中...",mask:!0}),setTimeout((()=>{$(),j({url:`/addon/yz_she/pages/order/detail/order-detail?id=${a}`,success:()=>{console.log("跳转成功")},fail:e=>{console.error("跳转失败:",e),g({title:"页面跳转失败",icon:"none"})}})}),300)):g({title:"订单信息错误",icon:"none"})},ae=e=>{console.log("下拉刷新拉动中:",e.detail)},le=()=>{console.log("下拉刷新恢复")},te=e=>{const{scrollTop:a,scrollHeight:l,scrollViewHeight:t}=e.detail;a+t>=l-200&&Q.value&&!G.value&&J()};return t((()=>{console.log("订单列表页面加载完成"),D()})),(e,a)=>{const l=m,t=y,_=h,C=z,j=b(k("u-icon"),E);return s(),o(t,{class:"order-list-page"},{default:c((()=>[r(" 顶部状态筛选 "),u(_,{class:"status-tabs-container","scroll-x":"true","show-scrollbar":"false"},{default:c((()=>[u(t,{class:"status-tabs"},{default:c((()=>[(s(!0),n(d,null,i(H.value,(e=>(s(),o(t,{class:x(["tab-item",{active:M.value===e.value}]),key:e.value,onClick:a=>(e=>{if(console.log("切换状态:",e),N({type:"light"}),M.value===e)return;M.value=e;const a=L(e);g({title:`切换到${a}`,icon:"none",duration:1e3}),O.value=1,q.value=[],D()})(e.value)},{default:c((()=>[u(l,{class:"tab-text"},{default:c((()=>[p(v(e.label),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),r(" 订单列表 "),u(_,{class:"order-list","scroll-y":"true","refresher-enabled":"true","refresher-triggered":G.value,"refresher-threshold":"100","refresher-default-style":"black",onRefresherrefresh:K,onRefresherpulling:ae,onRefresherrestore:le,onScrolltolower:J,onScroll:te,"lower-threshold":"100"},{default:c((()=>[(s(!0),n(d,null,i(f(B),(e=>(s(),o(t,{class:"order-item",key:e.id||e.orderNo,onClick:a=>ee(e)},{default:c((()=>[u(t,{class:"order-content"},{default:c((()=>{return[u(t,{class:"order-header"},{default:c((()=>{return[u(t,{class:"order-header-left"},{default:c((()=>[u(l,{class:"order-no"},{default:c((()=>[p("订单号: "+v(e.orderNo),1)])),_:2},1024)])),_:2},1024),u(t,{class:x(["order-status",(a=e.status,`status-${a}`)])},{default:c((()=>[u(l,{class:"status-text"},{default:c((()=>[p(v(L(e.status)),1)])),_:2},1024)])),_:2},1032,["class"])];var a})),_:2},1024),u(t,{class:"order-time"},{default:c((()=>[u(l,{class:"time-text"},{default:c((()=>[p("创建时间: "+v(e.createTime),1)])),_:2},1024)])),_:2},1024),u(t,{class:"product-info"},{default:c((()=>[r(" 有商品图片时显示图片 "),e.productImage?(s(),o(C,{key:0,src:e.productImage,class:"product-image",mode:"aspectFit"},null,8,["src"])):(s(),n(d,{key:1},[r(" 批量回收订单显示图标 "),u(t,{class:"product-image batch-icon-container"},{default:c((()=>[u(j,{name:"shopping-cart-fill",color:"#16a085",size:"40"})])),_:1})],2112)),u(t,{class:"product-details"},{default:c((()=>[u(l,{class:"product-name"},{default:c((()=>[p(v(e.productName),1)])),_:2},1024),e.productCode?(s(),o(l,{key:0,class:"product-code"},{default:c((()=>[p(v(e.productCode),1)])),_:2},1024)):(s(),o(l,{key:1,class:"batch-label"},{default:c((()=>[p("批量回收商品")])),_:1}))])),_:2},1024),u(t,{class:"price-info"},{default:c((()=>[u(l,{class:"price-label"},{default:c((()=>{return[p(v((a=e.status,{1:"预估价格",2:"预估价格",3:"预估价格",4:"质检价格",5:"质检价格",6:"退回价格",7:"成交价格",8:"预估价格"}[a]||"预估价格")),1)];var a})),_:2},1024),u(t,{class:"price-amount"},{default:c((()=>[u(l,{class:"currency"},{default:c((()=>[p("¥")])),_:1}),u(l,{class:"price-value"},{default:c((()=>[p(v(e.price),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),r(" 操作按钮 "),(a=e.status,[1,4,6].includes(a)?(s(),o(t,{key:0,class:"order-actions"},{default:c((()=>[(s(!0),n(d,null,i(P(e.status),(a=>(s(),o(t,{class:x(["action-btn",a.type]),key:a.type,onClick:w((l=>((e,a)=>{switch(console.log("处理操作:",e.orderNo,a),N({type:"light"}),a){case"cancel":U(e);break;case"contact":W();break;case"reject":X(e);break;case"accept":Y(e);break;case"logistics":Z(e);break;case"detail":ee(e);break;default:console.log("未知操作类型:",a),g({title:"功能暂未开放",icon:"none"})}})(e,a.type)),["stop"])},{default:c((()=>[u(l,{class:"action-text"},{default:c((()=>[p(v(a.text),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)):r("v-if",!0))];var a})),_:2},1024)])),_:2},1032,["onClick"])))),128)),r(" 空状态 "),0!==f(B).length||G.value?r("v-if",!0):(s(),o(t,{key:0,class:"empty-state"},{default:c((()=>[u(t,{class:"empty-icon"},{default:c((()=>[u(j,{name:"shopping-cart",color:"#ccc",size:"80"})])),_:1}),u(l,{class:"empty-text"},{default:c((()=>[p("暂无"+v(L(M.value))+"订单",1)])),_:1})])),_:1})),r(" 加载更多提示 "),f(B).length>0?(s(),o(t,{key:1,class:"load-more"},{default:c((()=>[G.value?(s(),o(t,{key:0,class:"loading-text"},{default:c((()=>[u(l,null,{default:c((()=>[p("加载中...")])),_:1})])),_:1})):Q.value?(s(),o(t,{key:2,class:"pull-up-text"},{default:c((()=>[u(l,null,{default:c((()=>[p("上拉加载更多")])),_:1})])),_:1})):(s(),o(t,{key:1,class:"no-more-text"},{default:c((()=>[u(l,null,{default:c((()=>[p("没有更多数据了")])),_:1})])),_:1}))])),_:1})):r("v-if",!0)])),_:1},8,["refresher-triggered"]),r(" 底部加载更多 "),Q.value&&f(B).length>0?(s(),o(t,{key:0,class:"load-more"},{default:c((()=>[u(l,{class:"load-text"},{default:c((()=>[p("加载更多...")])),_:1})])),_:1})):r("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-72e78096"]]);export{M as default};
