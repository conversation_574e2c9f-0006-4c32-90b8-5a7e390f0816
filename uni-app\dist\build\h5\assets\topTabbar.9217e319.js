import{r as t,an as l,b1 as e}from"./index-3caf046d.js";function o(){const o=t({title:"",topStatusBar:{style:"style-1",bgColor:"transparent",rollBgColor:"#fff",textColor:"#fff",rollTextColor:"#333"}}),a=t(1),r=t(-1);l((t=>{t.scrollTop<=0?r.value=-1:t.scrollTop>a.value?r.value=1:r.value=2}));return{getScrollBool:()=>r.value,setTopTabbarParam:(t={})=>{if(t&&"object"!=typeof t)return o;for(let l in t)if("title"==l)o.value.title=t.title||"";else if("topStatusBar"==l&&t.topStatusBar)for(let e in t.topStatusBar)o.value.topStatusBar[e]=t.topStatusBar[e];else o.value[l]=t[l];return e(o.value)}}}export{o as t};
