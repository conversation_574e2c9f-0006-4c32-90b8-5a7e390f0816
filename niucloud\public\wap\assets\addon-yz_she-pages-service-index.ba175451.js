import{d as e,r as a,J as s,o as t,c as l,w as n,g as c,b as o,t as d,y as i,z as r,F as u,a$ as f,M as _,k as p,i as q,j as x,S as m,x as v,$ as b}from"./index-3caf046d.js";import{_ as h}from"./u-icon.ba193921.js";import{_ as w}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const g=w(e({__name:"index",setup(e){const w=a([{question:"哪些城市/地区支持回收服务？",answer:"您好，品牌鞋服回收暂时只开放了江浙沪地区，潮鞋潮服全国可回收。",expanded:!0},{question:"订单提交后能取消吗？",answer:"订单提交后，在商品未被取件前可以取消。如果商品已经被取件，则无法取消订单。",expanded:!1},{question:"不同意估价，衣服怎么退回？",answer:"如果您不同意我们的估价，可以选择退回商品。我们会免费为您寄回，您只需要承担退回的运费即可。",expanded:!1},{question:"我的衣服有轻微瑕疵，还能回收吗？",answer:"轻微瑕疵的衣服仍然可以回收，但会根据瑕疵程度调整回收价格。建议您在提交订单时详细描述商品状况。",expanded:!1}]),g=()=>{f({phoneNumber:"************",success:()=>{console.log("拨打电话成功")},fail:e=>{console.log("拨打电话失败",e),_({title:"拨打失败",icon:"none"})}})},k=()=>{_({title:"正在连接客服...",icon:"loading",duration:1500})};return s((()=>{})),(e,a)=>{const s=p,f=q(x("u-icon"),h),_=m;return t(),l(s,{class:"faq-page"},{default:n((()=>[c(" 顶部二维码组件 "),o(s,{class:"qr-section"},{default:n((()=>[o(s,{class:"qr-container"},{default:n((()=>[o(s,{class:"qr-code-wrapper"},{default:n((()=>[o(s,{class:"qr-code-placeholder"},{default:n((()=>[c(" 二维码占位符 "),o(s,{class:"qr-placeholder-bg"})])),_:1})])),_:1}),o(s,{class:"qr-info"},{default:n((()=>[o(s,{class:"qr-title"},{default:n((()=>[o(f,{name:"chat",color:"#fff",size:"18"}),o(_,{class:"title-text"},{default:n((()=>[d("1对1微信客服")])),_:1})])),_:1}),o(_,{class:"qr-desc"},{default:n((()=>[d("长按识别二维码添加")])),_:1})])),_:1})])),_:1})])),_:1}),c(" 主要内容区域 "),o(s,{class:"main-content"},{default:n((()=>[c(" 服务时间说明 "),o(s,{class:"service-time-section"},{default:n((()=>[o(_,{class:"service-time-text"},{default:n((()=>[d("人工客服工作时间为8:00-20:00，咨询高峰期会有延迟")])),_:1})])),_:1}),c(" 热门问题标题 "),o(s,{class:"faq-header"},{default:n((()=>[o(_,{class:"faq-header-title"},{default:n((()=>[d("热门问题")])),_:1}),o(s,{class:"faq-header-line"})])),_:1}),c(" FAQ列表 "),o(s,{class:"faq-container"},{default:n((()=>[(t(!0),i(u,null,r(w.value,((e,a)=>(t(),l(s,{class:"faq-item",key:a,onClick:e=>(e=>{w.value[e].expanded=!w.value[e].expanded})(a)},{default:n((()=>[o(s,{class:"faq-question"},{default:n((()=>[o(s,{class:"question-number"},{default:n((()=>[d(v(a+1)+".",1)])),_:2},1024),o(s,{class:"question-text"},{default:n((()=>[d(v(e.question),1)])),_:2},1024),o(s,{class:b(["arrow-icon",{expanded:e.expanded}])},{default:n((()=>[o(f,{name:"arrow-down",color:"#999",size:"14"})])),_:2},1032,["class"])])),_:2},1024),e.expanded?(t(),l(s,{key:0,class:"faq-answer"},{default:n((()=>[o(_,{class:"answer-text"},{default:n((()=>[d(v(e.answer),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1}),c(" 底部客服按钮 "),o(s,{class:"bottom-service"},{default:n((()=>[o(s,{class:"service-buttons"},{default:n((()=>[o(s,{class:"service-btn phone-btn",onClick:g},{default:n((()=>[o(f,{name:"phone",color:"#666",size:"20"}),o(_,{class:"btn-text"},{default:n((()=>[d("电话客服")])),_:1})])),_:1}),o(s,{class:"service-btn online-btn",onClick:k},{default:n((()=>[o(f,{name:"chat",color:"#666",size:"20"}),o(_,{class:"btn-text"},{default:n((()=>[d("在线客服")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})}}}),[["__scopeId","data-v-ab1addfe"]]);export{g as default};
