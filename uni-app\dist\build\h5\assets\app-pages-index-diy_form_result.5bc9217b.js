import{_ as e}from"./top-tabbar.f4fde406.js";import{d as a,r as t,J as r,o as s,c as l,w as o,b as i,e as f,t as p,x as n,g as d,n as c,a as x,i as u,j as m,S as _,k as b,R as v,P as g}from"./index-3caf046d.js";import{g as y}from"./diy_form.9eef685a.js";import{t as k}from"./topTabbar.9217e319.js";import{_ as h}from"./_plugin-vue_export-helper.1b428a4d.js";import"./manifest.ed582bbb.js";const C=h(a({__name:"diy_form_result",setup(a){const h=t(null),C=t(0),j=t(0);let w=k().setTopTabbarParam({title:"",isBack:!1});r((e=>{C.value=e.record_id||0,j.value=e.form_id||0,T()}));const T=()=>{y({record_id:C.value}).then((e=>{h.value=e.data}))},F=()=>{x({url:"/app/pages/index/index",mode:"reLaunch"})},B=()=>{x({url:"/app/pages/index/diy_form",param:{form_id:j.value},mode:"redirectTo"})};return(a,t)=>{const r=u(m("top-tabbar"),e),y=_,k=b,j=v;return s(),l(k,{style:c(a.themeColor())},{default:o((()=>[h.value?(s(),l(k,{key:0,class:"w-screen h-screen flex flex-col items-center"},{default:o((()=>[i(r,{ref:"topTabbarRef",data:f(w)},null,8,["data"]),i(k,{class:"flex-1 flex flex-col items-center w-full pt-[180rpx]"},{default:o((()=>[i(y,{class:"nc-iconfont nc-icon-duihaoV6mm text-[#06ae56] mb-[30rpx] !text-[65rpx]"}),i(k,{class:"px-[30rpx] text-center leading-[1.3] text-[42rpx] font-bold mb-[30rpx]"},{default:o((()=>[p(n("default"==h.value.submitConfig.tips_type?"填写成功":h.value.submitConfig.tips_text),1)])),_:1}),i(k,{class:"text-[32rpx] mt-[32rpx] text-[#576b95]",onClick:t[0]||(t[0]=e=>{x({url:"/app/pages/index/diy_form_detail",param:{record_id:C.value},mode:"redirectTo"})})},{default:o((()=>[p(n(f(g)("diyForm.viewFillingDetails")),1)])),_:1})])),_:1}),i(k,{class:"pb-[260rpx] action-wrap"},{default:o((()=>[h.value.submitConfig.success_after_action.finish?(s(),l(j,{key:0,class:"w-[380rpx] !border-0 h-[80rpx] text-[28rpx] !text-[#ffffff] !bg-[#20bf64] flex-center font-500 rounded-[6rpx]",plain:!0,onClick:F},{default:o((()=>[p(n(f(g)("complete")),1)])),_:1})):d("v-if",!0),h.value.submitConfig.success_after_action.goBack?(s(),l(j,{key:1,class:"w-[380rpx] !border-0 h-[80rpx] text-[28rpx] text-[#333] !bg-[#f2f2f2] flex-center font-500 rounded-[6rpx]",plain:!0,onClick:B},{default:o((()=>[p(n(f(g)("diyForm.back")),1)])),_:1})):d("v-if",!0)])),_:1})])),_:1})):d("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-5a838e69"]]);export{C as default};
