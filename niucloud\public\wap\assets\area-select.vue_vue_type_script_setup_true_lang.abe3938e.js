import{d as t,r as e,q as i,bk as a,s as c,bl as l,i as r,j as s,o,c as n,w as d,b as p,O as u,t as v,$ as f,x as y,g as m,f as x,y as k,F as _,z as h,v as g,k as C,au as b}from"./index-3caf046d.js";import{_ as j}from"./u-popup.1b30ffa7.js";const w=t({__name:"area-select",props:{areaId:{type:Number,default:0}},emits:["complete"],setup(t,{expose:w,emit:I}){const q=t,z=e(!1),F=i({province:[],city:[],district:[]}),N=e("province"),O=i({province:null,city:null,district:null});a(0).then((({data:t})=>{F.province=t})).catch(),c((()=>q.areaId),((t,e)=>{t&&!e&&l(t).then((({data:t})=>{t.province&&(O.province=t.province),t.city&&(O.city=t.city),t.district&&(O.district=t.district)}))}),{immediate:!0}),c((()=>O.province),(()=>{a(O.province.id).then((({data:t})=>{if(F.city=t,N.value="city",O.city){let e=!1;for(let i=0;i<t.length;i++)if(O.city.id==t[i].id){e=!0;break}e||(O.city=null)}})).catch()}),{deep:!0}),c((()=>O.city),(t=>{t?a(O.city.id).then((({data:t})=>{if(F.district=t,N.value="district",O.district){let e=!1;for(let i=0;i<t.length;i++)if(O.district.id==t[i].id){e=!0;break}e||(O.district=null)}t.length||(I("complete",O),z.value=!1)})).catch():(F.district=[],O.district=null)}),{deep:!0}),c((()=>O.district),(t=>{t&&(N.value="district",I("complete",O),z.value=!1)}),{deep:!0});return w({open:()=>{z.value=!0}}),(t,e)=>{const i=C,a=b,c=r(s("u-popup"),j);return o(),n(c,{show:z.value,onClose:e[4]||(e[4]=t=>z.value=!1),mode:"bottom",round:10},{default:d((()=>[p(i,{onTouchmove:e[3]||(e[3]=u((()=>{}),["prevent","stop"])),class:"popup-common"},{default:d((()=>[p(i,{class:"title"},{default:d((()=>[v("请选择地区")])),_:1}),p(i,{class:"flex p-[30rpx] pt-[0] text-sm font-500"},{default:d((()=>[F.province.length?(o(),n(i,{key:0,class:f(["flex-1 pr-[10rpx]",{"text-[var(--primary-color)]":"province"==N.value}]),onClick:e[0]||(e[0]=t=>N.value="province")},{default:d((()=>[O.province?(o(),n(i,{key:0},{default:d((()=>[v(y(O.province.name),1)])),_:1})):(o(),n(i,{key:1},{default:d((()=>[v("请选择")])),_:1}))])),_:1},8,["class"])):m("v-if",!0),F.city.length?(o(),n(i,{key:1,class:f(["flex-1 pr-[10rpx]",{"text-[var(--primary-color)]":"city"==N.value}]),onClick:e[1]||(e[1]=t=>N.value="city")},{default:d((()=>[O.city?(o(),n(i,{key:0},{default:d((()=>[v(y(O.city.name),1)])),_:1})):(o(),n(i,{key:1},{default:d((()=>[v("请选择")])),_:1}))])),_:1},8,["class"])):m("v-if",!0),F.district.length?(o(),n(i,{key:2,class:f(["flex-1 pr-[10rpx]",{"text-[var(--primary-color)]":"district"==N.value}]),onClick:e[2]||(e[2]=t=>N.value="district")},{default:d((()=>[O.district?(o(),n(i,{key:0},{default:d((()=>[v(y(O.district.name),1)])),_:1})):(o(),n(i,{key:1},{default:d((()=>[v("请选择")])),_:1}))])),_:1},8,["class"])):m("v-if",!0)])),_:1}),p(a,{"scroll-y":"true",class:"h-[50vh]"},{default:d((()=>[p(i,{class:"flex p-[30rpx] pt-0 text-sm"},{default:d((()=>[F.province.length?x((o(),n(i,{key:0},{default:d((()=>[(o(!0),k(_,null,h(F.province,(t=>(o(),n(i,{class:f(["h-[80rpx] flex items-center",{"text-[var(--primary-color)]":O.province&&O.province.id==t.id}]),onClick:e=>O.province=t},{default:d((()=>[v(y(t.name),1)])),_:2},1032,["class","onClick"])))),256))])),_:1},512)),[[g,"province"==N.value]]):m("v-if",!0),F.city.length?x((o(),n(i,{key:1},{default:d((()=>[(o(!0),k(_,null,h(F.city,(t=>(o(),n(i,{class:f(["h-[80rpx] flex items-center",{"text-[var(--primary-color)]":O.city&&O.city.id==t.id}]),onClick:e=>O.city=t},{default:d((()=>[v(y(t.name),1)])),_:2},1032,["class","onClick"])))),256))])),_:1},512)),[[g,"city"==N.value]]):m("v-if",!0),F.district.length?x((o(),n(i,{key:2},{default:d((()=>[(o(!0),k(_,null,h(F.district,(t=>(o(),n(i,{class:f(["h-[80rpx] flex items-center",{"text-[var(--primary-color)]":O.district&&O.district.id==t.id}]),onClick:e=>O.district=t},{default:d((()=>[v(y(t.name),1)])),_:2},1032,["class","onClick"])))),256))])),_:1},512)),[[g,"district"==N.value]]):m("v-if",!0)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])}}});export{w as _};
