import{r as e,aE as a,q as l,p as o,A as t,J as g,N as r,a2 as i,L as n,U as d,b1 as u,W as p,aq as s,ar as m,an as v}from"./index-3caf046d.js";import{b as c}from"./diy_form.9eef685a.js";function S(S={}){const b=e(!0),y=a(),f=e(S.form_id||0);e(S.name||""),e("");const h=e(""),B=l({}),x=e(S.needLogin||!1),k=l({pageMode:"diy",title:"",global:{},value:[],status:0}),_=o((()=>"decorate"==y.mode?y:k)),C=e(!1),T=(e=null)=>{let a=i();h.value=a[a.length-1]?a[a.length-1].route:"";let l=[];if(uni.getStorageSync("diyPageBlank")&&(l=uni.getStorageSync("diyPageBlank")),!l.length||l.length&&-1==l.indexOf(h.value)?y.topFixedStatus="home":l.length&&-1!=l.indexOf(h.value)&&(y.topFixedStatus="diy"),"decorate"==y.mode)y.init();else{if(!f.value)return;if(x.value&&!n())return void d().setLoginBack({url:"/app/pages/index/diy_form",param:{form_id:f.value}});c({form_id:f.value}).then((a=>{if(Object.assign(B,a.data),k.status=a.data.status,B.value){k.pageMode=B.mode,k.title=B.title,y.id=B.form_id;let e=B.value,a=uni.getStorageSync("diyFormStorage_"+y.id);if(a){var l=new Date;let o=parseInt(l.getTime()/1e3);a.validTime>o?a.components&&a.components.forEach((a=>{for(let l=0;l<e.value.length;l++)if("diy_form"==e.value[l].componentType&&a.id==e.value[l].id){let o=u(a),t=u(o.field);delete o.field,delete t.required,delete t.unique,delete t.autofill,delete t.privacyProtection,Object.assign(e.value[l],o),Object.assign(e.value[l].field,t);break}})):uni.removeStorageSync("diyFormStorage_"+y.id)}y.value=e.value,k.global=e.global,k.value=e.value,k.value.forEach(((e,a)=>{e.isHidden?k.value.splice(a,1):(e.pageStyle="",("FormSubmit"!=e.componentName||"FormSubmit"==e.componentName&&"hover_screen_bottom"!=e.btnPosition)&&(e.pageStartBgColor&&(e.pageStartBgColor&&e.pageEndBgColor?e.pageStyle+=`background:linear-gradient(${e.pageGradientAngle},${e.pageStartBgColor},${e.pageEndBgColor});`:e.pageStyle+="background-color:"+e.pageStartBgColor+";"),e.margin&&(e.margin.top>0&&(e.pageStyle+="padding-top:"+2*e.margin.top+"rpx;"),e.pageStyle+="padding-bottom:"+2*e.margin.bottom+"rpx;",e.pageStyle+="padding-right:"+2*e.margin.both+"rpx;",e.pageStyle+="padding-left:"+2*e.margin.both+"rpx;")))})),C.value=k.value.some((e=>e&&e.position&&"top_fixed"==e.position)),p({title:k.title})}b.value=!1,e&&e(B)}))}};return{getLoading:()=>b.value,requestData:B,data:_.value,isShowTopTabbar:C,pageStyle:()=>{var e="";return _.value.global.pageStartBgColor&&(_.value.global.pageStartBgColor&&_.value.global.pageEndBgColor?e+=`background:linear-gradient(${_.value.global.pageGradientAngle},${_.value.global.pageStartBgColor},${_.value.global.pageEndBgColor});`:e+="background-color:"+_.value.global.pageStartBgColor+";"),_.value.global.bottomTabBarSwitch?e+="min-height:calc(100vh - 50px);":e+="min-height:calc(100vh);",_.value.global.bgUrl&&(e+=`background-image:url('${t(_.value.global.bgUrl)}');`),_.value.global.bgHeightScale&&(e+=`background-size: 100% ${_.value.global.bgHeightScale}%;`),e},onLoad:(e=null)=>{g((a=>{y.mode=a.mode||"","decorate"==y.mode&&(B.status=1,B.error=[],b.value=!1),f.value=a.form_id||"",T(e)}))},onShow:(e=null)=>{r((()=>{e&&e(B)}))},onHide:(e=null)=>{s((()=>{let a=[];uni.getStorageSync("diyPageBlank")&&(a=uni.getStorageSync("diyPageBlank")),a.length&&(a=Array.from(new Set(a)),a.forEach(((e,a,l)=>{e==h.value&&l.splice(a,1)}))),"diy"==y.topFixedStatus&&a.push(h.value),uni.setStorageSync("diyPageBlank",a),e&&e()}))},onUnload:()=>{m((()=>{}))},onPageScroll:()=>{v((e=>{e.scrollTop>0&&(y.scrollTop=e.scrollTop)}))},getData:T}}export{S as u};
