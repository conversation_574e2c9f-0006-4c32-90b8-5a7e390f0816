import{d as e,r as a,J as t,_ as s,N as l,o as c,c as u,w as n,g as i,b as o,$ as r,t as d,e as _,x as f,y as v,z as m,F as E,M as p,k as U,i as D,j as h,S as y}from"./index-3caf046d.js";import{_ as S}from"./u-loading-icon.255170b9.js";import{V as k,a as x}from"./voucher.db23b7c0.js";import{_ as b}from"./_plugin-vue_export-helper.1b428a4d.js";const g=b(e({__name:"index",setup(e){const b=a(!1),g=a([]),N=a("all"),I=a(0),P=a(1),R=a(20),X=e=>{N.value!==e&&(N.value=e,P.value=1,g.value=[],w())},w=async()=>{try{b.value=!0;const e={page:P.value,limit:R.value};"all"!==N.value&&(e.status=N.value);const a=await x(e);if(1===a.code){const e=a.data||{};g.value=e.data||[],I.value=e.total||0}else p({title:a.msg||"加载失败",icon:"none"})}catch(e){console.error("加载加价券列表失败:",e),p({title:"网络错误，请重试",icon:"none"})}finally{b.value=!1}},C=()=>{switch(N.value){case k.UNUSED:return"暂无可用加价券";case k.USED:return"暂无已使用加价券";case k.EXPIRED:return"暂无过期加价券";default:return"暂无加价券"}},$=()=>{switch(N.value){case k.UNUSED:return"快去回收商品获取加价券吧";case k.USED:return"使用记录会在这里显示";case k.EXPIRED:return"过期的加价券会在这里显示";default:return"完成回收可获得加价券奖励"}},j=e=>e.min_condition_money>0?`满${e.min_condition_money}可用`:"无门槛",M=e=>{if(e.status===k.USED&&e.use_time_text)return`已于${e.use_time_text.split(" ")[0]}使用`;if(e.status===k.EXPIRED)return`已于${e.expire_time_text.split(" ")[0]}过期`;{const a=new Date(1e3*e.expire_time),t=new Date,s=Math.ceil((a.getTime()-t.getTime())/864e5);return s<=0?"已过期":s<=3?`${s}天后过期`:`有效期至${e.expire_time_text.split(" ")[0]}`}},T=e=>e.applicable_desc||"不限制商品使用";return t((()=>{console.log("VoucherStatus:",k)})),s((()=>{w()})),l((()=>{g.value.length>0&&w()})),(e,a)=>{const t=U,s=D(h("u-loading-icon"),S),l=y;return c(),u(t,{class:"voucher-page"},{default:n((()=>[i(" 状态筛选标签 "),o(t,{class:"status-tabs"},{default:n((()=>[o(t,{class:r(["tab-item",{active:"all"===N.value}]),onClick:a[0]||(a[0]=e=>X("all"))},{default:n((()=>[d(" 全部 ")])),_:1},8,["class"]),o(t,{class:r(["tab-item",{active:N.value===_(k).UNUSED}]),onClick:a[1]||(a[1]=e=>X(_(k).UNUSED))},{default:n((()=>[d(" 未使用 ")])),_:1},8,["class"]),o(t,{class:r(["tab-item",{active:N.value===_(k).USED}]),onClick:a[2]||(a[2]=e=>X(_(k).USED))},{default:n((()=>[d(" 已使用 ")])),_:1},8,["class"]),o(t,{class:r(["tab-item",{active:N.value===_(k).EXPIRED}]),onClick:a[3]||(a[3]=e=>X(_(k).EXPIRED))},{default:n((()=>[d(" 已过期 ")])),_:1},8,["class"])])),_:1}),i(" 加价券列表 "),o(t,{class:"voucher-list"},{default:n((()=>[b.value?(c(),u(t,{key:0,class:"loading-container"},{default:n((()=>[o(s,{mode:"spinner"}),o(l,{class:"loading-text"},{default:n((()=>[d("加载中...")])),_:1})])),_:1})):0===g.value.length?(c(),u(t,{key:1,class:"empty-container"},{default:n((()=>[o(t,{class:"empty-icon"},{default:n((()=>[d("🎫")])),_:1}),o(l,{class:"empty-text"},{default:n((()=>[d(f(C()),1)])),_:1}),o(l,{class:"empty-desc"},{default:n((()=>[d(f($()),1)])),_:1})])),_:1})):(c(),u(t,{key:2,class:"voucher-items"},{default:n((()=>[(c(!0),v(E,null,m(g.value,(e=>(c(),u(t,{key:e.id,class:r(["voucher-card",{used:e.status===_(k).USED,expired:e.status===_(k).EXPIRED,disabled:!e.can_use&&e.status===_(k).UNUSED}]),onClick:a=>(e=>{e.status===k.UNUSED&&e.can_use?p({title:"可在商品详情页使用",icon:"none"}):e.status!==k.UNUSED||e.can_use?p({title:e.status_name,icon:"none"}):p({title:e.disabled_reason||"暂不可用",icon:"none"})})(e)},{default:n((()=>[i(" 左侧金额区域 "),o(t,{class:"voucher-left"},{default:n((()=>[o(t,{class:"amount-container"},{default:n((()=>[o(l,{class:"currency"},{default:n((()=>[d("¥")])),_:1}),o(l,{class:"amount"},{default:n((()=>[d(f(e.price),1)])),_:2},1024)])),_:2},1024),o(l,{class:"condition"},{default:n((()=>[d(f(j(e)),1)])),_:2},1024)])),_:2},1024),i(" 右侧信息区域 "),o(t,{class:"voucher-right"},{default:n((()=>[o(t,{class:"voucher-info"},{default:n((()=>[o(l,{class:"voucher-title"},{default:n((()=>[d(f(e.title),1)])),_:2},1024),o(l,{class:"voucher-time"},{default:n((()=>[d(f(M(e)),1)])),_:2},1024),o(l,{class:"voucher-desc"},{default:n((()=>[d(f(T(e)),1)])),_:2},1024)])),_:2},1024),i(" 状态按钮 "),o(t,{class:"voucher-action"},{default:n((()=>[e.status===_(k).UNUSED&&e.can_use?(c(),u(t,{key:0,class:"action-btn available"},{default:n((()=>[d(" 可使用 ")])),_:1})):e.status!==_(k).UNUSED||e.can_use?e.status===_(k).USED?(c(),u(t,{key:2,class:"action-btn used"},{default:n((()=>[d(" 已使用 ")])),_:1})):e.status===_(k).EXPIRED?(c(),u(t,{key:3,class:"action-btn expired"},{default:n((()=>[d(" 已过期 ")])),_:1})):i("v-if",!0):(c(),u(t,{key:1,class:"action-btn disabled"},{default:n((()=>[d(" 不可用 ")])),_:1}))])),_:2},1024)])),_:2},1024),i(" 状态遮罩 "),e.status===_(k).UNUSED&&e.can_use?i("v-if",!0):(c(),u(t,{key:0,class:"voucher-mask"}))])),_:2},1032,["class","onClick"])))),128))])),_:1}))])),_:1}),i(" 底部提示 "),g.value.length>0?(c(),u(t,{key:0,class:"bottom-tip"},{default:n((()=>[o(l,{class:"tip-text"},{default:n((()=>[d("到底了～")])),_:1})])),_:1})):i("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-795657f9"]]);export{g as default};
