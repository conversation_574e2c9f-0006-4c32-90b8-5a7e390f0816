import{d as e,r as a,q as t,_ as l,o,c as r,w as i,f as d,v as s,b as c,n as u,k as m}from"./index-3caf046d.js";import{d as n}from"./index.9de114a1.js";import{c as p}from"./diy_form.9eef685a.js";const _=e({__name:"index",props:["record_id","completeLayout"],emits:["callback"],setup(e,{emit:_}){const f=e,y=a(!0),v=t({global:{},value:[]});return l((()=>{p({record_id:f.record_id}).then((e=>{v.global.completeLayout=f.completeLayout||"style-1",e.data.recordsFieldList&&e.data.recordsFieldList.forEach((e=>{let a={id:e.field_key,componentName:e.field_type,pageStyle:"",viewFormDetail:!0,field:{name:e.field_name,value:e.handle_field_value,required:e.field_required,unique:e.field_unique,privacyProtection:e.privacy_protection},margin:{top:0,bottom:0,both:0}};v.value.push(a)})),_("callback",e.data.recordsFieldList),y.value=!1})).catch((()=>{y.value=!1,_("callback",[])}))})),(e,a)=>{const t=m;return o(),r(t,{style:u(e.themeColor())},{default:i((()=>[d(c(t,{class:"diy-template-wrap"},{default:i((()=>[c(n,{ref:"diyGroupRef",data:v},null,8,["data"])])),_:1},512),[[s,!y.value]])])),_:1},8,["style"])}}});export{_};
