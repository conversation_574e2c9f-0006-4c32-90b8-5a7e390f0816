import{d as e,r as t,p as l,I as a,o as s,c as o,w as r,b as u,e as i,O as d,$ as n,t as p,x as c,g as x,y as m,z as _,F as f,L as v,M as g,U as y,bH as h,a as b,A as k,aB as w,Q as j,i as C,j as F,k as S,S as V,al as T,au as D,R as I,D as L,_ as O,u as z,J as E,N as M,X as N,b1 as B,bd as A,W as P,as as R,a9 as $,an as U,ar as H,aA as J,n as q,av as G,a2 as Q,a3 as K,aw as W}from"./index-3caf046d.js";import{_ as X}from"./u-swiper.1a811b26.js";import{_ as Y}from"./newcomer.6993dfef.js";import{_ as Z}from"./u-avatar.30e31e9c.js";import{_ as ee}from"./u-icon.ba193921.js";import{_ as te}from"./u--image.eb573bce.js";import{_ as le}from"./u-parse.406d0731.js";import{_ as ae,a as se}from"./u-popup.1b30ffa7.js";import{e as oe,f as re,h as ue,i as ie,j as de,k as ne}from"./goods.6a81cb49.js";import{g as pe,b as ce}from"./coupon.2f3f2d3d.js";import{_ as xe}from"./u-number-box.8a6aafb5.js";import{u as me}from"./cart.ef6e9a72.js";import{b as _e}from"./bind-mobile.25318c0e.js";import{d as fe}from"./index.23d98e09.js";import{_ as ve}from"./_plugin-vue_export-helper.1b428a4d.js";import{n as ge}from"./ns-goods-manjian.50c99c6a.js";import{s as ye}from"./share-poster.0fbc73fb.js";import{u as he}from"./useGoods.9c8f1c51.js";import{_ as be}from"./ns-goods-recommend.vue_vue_type_script_setup_true_lang.94c7a4d0.js";import"./u-loading-icon.255170b9.js";import"./u-text.f02e6497.js";/* empty css                                                               */import"./u-image.04cba9a2.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-safe-bottom.98e092c5.js";import"./u-form.49dbb57f.js";import"./u-line.69c0c00f.js";import"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import"./u-input.2d8dc7a4.js";import"./u-modal.8624728a.js";import"./u-checkbox-group.0328273c.js";import"./useDiyForm.26962b36.js";import"./diy_form.9eef685a.js";import"./index.9de114a1.js";import"./top-tabbar.f4fde406.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.abe3938e.js";import"./u-datetime-picker.a5259774.js";import"./u-upload.83871903.js";import"./u-radio-group.63482a1c.js";import"./tabbar.2c31519d.js";import"./u-tabbar-item.31141540.js";import"./u-tabbar.38f37e13.js";import"./index.32583a71.js";import"./point.0698952c.js";import"./rank.7a4c9318.js";import"./voucher.db23b7c0.js";import"./quote.9b84c391.js";import"./order.5c5c6bee.js";const ke=ve(e({__name:"ns-goods-sku",props:["goodsDetail"],emits:["change"],setup(e,{expose:O,emit:z}){const E=e,M=t(!1),N=t(null),B=t({skuId:"",name:[]}),A=t(""),P=t(1),R=t(0),$=t(0),U=t(0),H=t(0),J=l((()=>{let e="0.00";return e="newcomer_discount"==le.value.type&&v()&&le.value.newcomer_price?le.value.newcomer_price:le.value.show_price,e})),q=()=>{let e="";return e="newcomer_discount"==le.value.type&&v()&&le.value.newcomer_price?"newcomer_price":le.value.show_type,e},G=a(),Q=l((()=>G.info)),K=me();K.getList();const W=l((()=>le.value&&K.cartList["goods_"+le.value.goods_id]&&K.cartList["goods_"+le.value.goods_id]["sku_"+le.value.sku_id]?K.cartList["goods_"+le.value.goods_id]["sku_"+le.value.sku_id]:{})),X=l((()=>K.cartList)),Y=()=>{setTimeout((()=>{P.value=parseInt(P.value),(!P.value||P.value<=$.value)&&(P.value=$.value||1),P.value>=R.value&&(P.value=R.value),$.value>le.value.detail.stock&&(P.value=0)}),0)},Z=()=>{setTimeout((()=>{P.value=parseInt(P.value),(!P.value||P.value<=$.value)&&(P.value=$.value||1),P.value>=R.value&&(P.value=R.value),$.value>le.value.detail.stock&&(P.value=0,g({title:"商品库存小于起购数量",icon:"none"}))}),0)},ee=()=>{M.value=!1},le=l((()=>{let e=L(E.goodsDetail);if(Object.keys(e).length&&(Object.keys(B.value.name).length||(B.value.name=e.sku_spec_format.split(",")),e.goodsSpec.forEach(((e,t)=>{let l=e.spec_values.split(",");e.values=[],l.forEach(((l,a)=>{e.values[a]={},e.values[a].name=l,e.values[a].selected=!1,e.values[a].disabled=!1,B.value.name.forEach(((s,o)=>{o==t&&s==l&&(e.values[a].selected=!0)}))}))})),oe(),e.skuList&&Object.keys(e.skuList).length&&e.skuList.forEach(((t,l)=>{t.sku_id==B.value.skuId&&(e.detail=t)}))),e.goods.is_limit){if(e.goods.max_buy){let t=0;if(1==e.goods.limit_type)t=e.goods.max_buy;else{let l=e.goods.max_buy-(e.goods.has_buy||0);t=l>0?l:0}t>e.detail.stock?R.value=e.detail.stock:t<=e.detail.stock&&(R.value=t),0==R.value&&(P.value=0)}U.value=e.goods.max_buy}else R.value=e.detail.stock;return $.value=e.goods.min_buy>0?e.goods.min_buy:1,$.value>e.detail.stock?P.value=0:P.value=$.value,H.value=e.goods.min_buy,e})),oe=()=>{E.goodsDetail.skuList.forEach(((e,t)=>{e.sku_spec_format==B.value.name.toString()&&(B.value.skuId=e.sku_id,z("change",e.sku_id))}))},re=()=>{if($.value&&$.value>le.value.detail.stock)g({title:"商品库存小于起购数量",icon:"none"});else if(le.value.goods.is_limit){let e=`该商品单次限购${le.value.goods.max_buy}件`;1!=le.value.goods.limit_type&&(e=`该商品每人限购${le.value.goods.max_buy}件`,le.value.goods.max_buy-R.value&&(e+=`,已购${le.value.goods.max_buy-R.value}件`)),P.value>=R.value&&g({title:e,icon:"none"})}},ue=()=>{if($.value>1){let e=`该商品起购${$.value}件`;P.value<=$.value&&g({title:e,icon:"none"})}},ie=t(null);t(uni.getStorageSync("isBindMobile"));const de=t(null),ne=()=>{if(de.value.verify()&&!(P.value<1)){if(!Q.value&&uni.getStorageSync("isBindMobile"))return uni.setStorage({key:"loginBack",data:{url:"/addon/shop/pages/goods/detail",param:{sku_id:le.value.sku_id,type:le.value.type}}}),ie.value.open(),!1;if(!Q.value)return y().setLoginBack({url:"/addon/shop/pages/goods/detail",param:{sku_id:le.value.sku_id,type:le.value.type}}),!1;if("join_cart"==A.value){let e=0,t=0,l="";if(X.value["goods_"+le.value.goods_id]&&X.value["goods_"+le.value.goods_id]["sku_"+le.value.sku_id]&&(e=h(X.value["goods_"+le.value.goods_id]["sku_"+le.value.sku_id].num),l=h(X.value["goods_"+le.value.goods_id]["sku_"+le.value.sku_id].id)),X.value["goods_"+le.value.goods_id]&&X.value["goods_"+le.value.goods_id]&&(t=h(X.value["goods_"+le.value.goods_id].totalNum)),e+=Number(P.value),t+=Number(P.value),le.value.goods.is_limit){let e=`该商品单次限购${le.value.goods.max_buy}件`;if(1!=le.value.goods.limit_type&&(e=`该商品每人限购${le.value.goods.max_buy}件`,le.value.goods.max_buy-R.value&&(e+=`,已购${le.value.goods.max_buy-R.value}件`)),t>R.value)return g({title:e,icon:"none"}),!1}K.increase({id:l||"",goods_id:le.value.goods_id,sku_id:le.value.sku_id,stock:le.value.stock,sale_price:le.value.sale_price,num:e},0,(()=>{g({title:"加入购物车成功",icon:"none"})}))}else if("buy_now"==A.value){var e={sku_id:le.value.sku_id,num:P.value};uni.setStorage({key:"orderCreateData",data:{sku_data:[e],extend_data:{relate_id:"discount_price"==le.value.show_type?le.value.discount_info.discount_id:"",activity_type:"discount_price"==le.value.show_type?"discount":""}},success:()=>{b({url:"/addon/shop/pages/order/payment"})}})}ee()}};return O({open:(e="",t="")=>{A.value=e,M.value=!0,N.value=t}}),(e,t)=>{const l=j,a=C(F("u--image"),te),v=S,g=V,y=T,h=C(F("u-number-box"),xe),b=D,L=I,O=C(F("u-popup"),ae),z=C(F("u-overlay"),se);return s(),o(v,{onTouchmove:t[4]||(t[4]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(z,{show:M.value,onClick:ee,zIndex:"490"},{default:r((()=>[u(O,{class:"popup-type",show:M.value,onClose:ee,mode:"bottom",overlay:!1,zIndex:"500"},{default:r((()=>[i(le).detail?(s(),o(v,{key:0,class:"py-[32rpx] relative",onTouchmove:t[3]||(t[3]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(v,{class:n(["flex px-[32rpx]",{"mb-[58rpx]":!(i(le).is_newcomer&&i(le).newcomer_price!=i(le).price&&(Object.keys(i(W)).length?parseInt(i(W).num)+P.value:P.value)>1)}])},{default:r((()=>[u(v,{class:"rounded-[var(--goods-rounded-big)] overflow-hidden w-[180rpx] h-[180rpx]"},{default:r((()=>[u(a,{width:"180rpx",height:"180rpx",src:i(k)(i(le).detail.sku_image),onClick:t[0]||(t[0]=e=>(e=>{if(""===e)return!1;var t=[];t.push(k(e)),w({indicator:"number",loop:!0,urls:t})})(i(le).detail.sku_image)),model:"aspectFill"},{error:r((()=>[u(l,{class:"w-[180rpx] h-[180rpx]",src:i(k)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["src"])])),_:1}),u(v,{class:"flex flex-1 flex-col justify-between ml-[24rpx] py-[10rpx]"},{default:r((()=>[u(v,{class:"w-[100%]"},{default:r((()=>[u(v,{class:"text-[var(--price-text-color)] flex items-baseline"},{default:r((()=>[u(g,{class:"text-[32rpx] font-bold price-font"},{default:r((()=>[p("￥")])),_:1}),u(g,{class:"text-[48rpx] price-font"},{default:r((()=>[p(c(parseFloat(i(J)).toFixed(2).split(".")[0]),1)])),_:1}),u(g,{class:"text-[32rpx] mr-[6rpx] price-font"},{default:r((()=>[p("."+c(parseFloat(i(J)).toFixed(2).split(".")[1]),1)])),_:1}),"newcomer_price"==q()?(s(),o(l,{key:0,class:"h-[24rpx] ml-[6rpx] max-w-[60rpx]",src:i(k)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):x("v-if",!0),"member_price"==q()?(s(),o(l,{key:1,class:"h-[24rpx] ml-[6rpx] max-w-[44rpx]",src:i(k)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):x("v-if",!0),"discount_price"==q()?(s(),o(l,{key:2,class:"h-[24rpx] ml-[6rpx] max-w-[80rpx]",src:i(k)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):x("v-if",!0)])),_:1}),u(v,{class:"text-[26rpx] leading-[32rpx] text-[var(--text-color-light6)] mt-[12rpx]"},{default:r((()=>[p("库存"+c(i(le).detail.stock)+c(i(le).goods.unit),1)])),_:1})])),_:1}),i(le).goodsSpec&&i(le).goodsSpec.length?(s(),o(v,{key:0,class:"text-[26rpx] leading-[30rpx] text-[var(--text-color-light6)] w-[100%] max-h-[60rpx] multi-hidden"},{default:r((()=>[p("已选规格："+c(i(le).detail.sku_spec_format),1)])),_:1})):x("v-if",!0),x(' \t<view v-if="goodsDetail.goodsSpec && goodsDetail.goodsSpec.length">\n                                          <text>已选规格：{{goodsDetail.detail.sku_spec_format}}</text>\n                                        </view> ')])),_:1})])),_:1},8,["class"]),i(le).is_newcomer&&i(le).newcomer_price!=i(le).price&&(Object.keys(i(W)).length?parseInt(i(W).num)+P.value:P.value)>1?(s(),o(v,{key:0,class:"flex items-center px-[32rpx] pt-[8rpx] pb-[16rpx] h-[58rpx] box-border"},{default:r((()=>[u(l,{class:"h-[24rpx] w-[56rpx]",src:i(k)("addon/shop/newcomer.png"),mode:"aspectFit"},null,8,["src"]),u(v,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:r((()=>[p(" 第1"+c(i(le).goods.unit)+"，￥"+c(parseFloat(i(le).newcomer_price).toFixed(2))+"/"+c(i(le).goods.unit)+"；第"+c(parseInt(i(W).num||0)+P.value>2?"2~"+(parseInt(i(W).num||0)+P.value):"2")+c(i(le).goods.unit)+"，￥"+c(parseFloat(parseFloat(i(J))).toFixed(2))+"/"+c(i(le).goods.unit),1)])),_:1})])),_:1})):x("v-if",!0),u(b,{class:"h-[500rpx] px-[32rpx] box-border mb-[60rpx]","scroll-y":"true"},{default:r((()=>[(s(!0),m(f,null,_(i(le).goodsSpec,((e,t)=>(s(),o(v,{class:n({"mt-[20rpx]":0!=t}),key:t},{default:r((()=>[u(v,{class:"text-[28rpx] leading-[36rpx] mb-[24rpx]"},{default:r((()=>[p(c(e.spec_name),1)])),_:2},1024),u(v,{class:"flex flex-wrap"},{default:r((()=>[(s(!0),m(f,null,_(e.values,((e,l)=>(s(),o(v,{class:n(["box-border bg-[var(--temp-bg)] text-[24rpx] px-[44rpx] text-center h-[56rpx] flex-center mr-[20rpx] mb-[20rpx] border-1 border-solid rounded-[50rpx] border-[var(--temp-bg)]",{"!border-[var(--primary-color)] text-[var(--primary-color)] !bg-[var(--primary-color-light)]":e.selected}]),key:l,onClick:l=>((e,t)=>{B.value.name[t]=e.name,oe()})(e,t)},{default:r((()=>[p(c(e.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1032,["class"])))),128)),u(v,{class:"flex justify-between items-center my-[20rpx]"},{default:r((()=>[u(v,{class:"text-[28rpx]"},{default:r((()=>[p("购买数量")])),_:1}),U.value>0&&H.value>1?(s(),o(g,{key:0,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:r((()=>[p("("+c(H.value)+c(i(le).goods.unit)+"起售，限购"+c(U.value)+c(i(le).goods.unit)+")",1)])),_:1})):U.value>0?(s(),o(g,{key:1,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:r((()=>[p("(限购"+c(U.value)+c(i(le).goods.unit)+")",1)])),_:1})):H.value>1?(s(),o(g,{key:2,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:r((()=>[p("("+c(H.value)+c(i(le).goods.unit)+"起售)",1)])),_:1})):x("v-if",!0),u(h,{min:$.value,max:R.value,integer:"",step:1,"input-width":"68rpx",modelValue:P.value,"onUpdate:modelValue":t[2]||(t[2]=e=>P.value=e),"input-height":"52rpx"},{minus:r((()=>[u(v,{class:"relative w-[30rpx] h-[30rpx]",onClick:ue},{default:r((()=>[u(g,{class:n(["text-[30rpx] nc-iconfont nc-icon-jianV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":P.value<=$.value}])},null,8,["class"])])),_:1})])),input:r((()=>[u(y,{class:"text-[#303133] text-[28rpx] mx-[10rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:Y,onBlur:Z,modelValue:P.value,"onUpdate:modelValue":t[1]||(t[1]=e=>P.value=e)},null,8,["modelValue"])])),plus:r((()=>[u(v,{class:"relative w-[30rpx] h-[30rpx]",onClick:re},{default:r((()=>[u(g,{class:n(["text-[30rpx] nc-iconfont nc-icon-jiahaoV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":P.value>=R.value||0==P.value}])},null,8,["class"])])),_:1})])),_:1},8,["min","max","modelValue"])])),_:1}),u(v,{class:"mt-[40rpx]"},{default:r((()=>[u(fe,{ref_key:"diyFormRef",ref:de,form_id:i(le).goods.form_id,storage_name:"diyFormStorageByGoodsDetail_"+i(le).sku_id},null,8,["form_id","storage_name"])])),_:1})])),_:1}),u(v,{class:"px-[20rpx]"},{default:r((()=>[i(le).detail.stock>0?(s(),o(L,{key:0,"hover-class":"none",class:"!h-[80rpx] leading-[80rpx] text-[26rpx] font-500 rounded-[50rpx] primary-btn-bg",type:"primary",onClick:ne},{default:r((()=>[p("确定")])),_:1})):(s(),o(L,{key:1,"hover-class":"none",class:"!h-[80rpx] leading-[80rpx] text-[26rpx] font-500 text-[#fff] bg-[#ccc] rounded-[50rpx]"},{default:r((()=>[p("已售罄")])),_:1}))])),_:1})])),_:1})):x("v-if",!0)])),_:1},8,["show"])])),_:1},8,["show"]),x(" 强制绑定手机号 "),u(_e,{ref_key:"bindMobileRef",ref:ie},null,512)])),_:1})}}}),[["__scopeId","data-v-ab2f2d7d"]]),we=e({__name:"sow-show",props:{items:{type:Object,required:!0,default:()=>({})}},setup(e){const l=e,a=t(null);O((()=>{l.items&&l.items.url&&(a.value=function(e){const t=e.match(/treasure_id=(\d+)/);return t?t[1]:null}(l.items.url))}));const d=()=>{b({url:"/addon/sow_community/pages/sow_show",param:{treasure_id:a.value}})};return(t,l)=>{const a=V,n=S,v=j;return e.items.list&&e.items.list.length>0?(s(),o(n,{key:0,class:"card-template mt-[var(--top-m)]"},{default:r((()=>[u(n,{class:"flex justify-between items-center",onClick:d},{default:r((()=>[u(n,{class:"text-[30rpx]"},{default:r((()=>[u(a,null,{default:r((()=>[p("种草秀")])),_:1}),e.items.count?(s(),o(a,{key:0,class:"ml-[6rpx] text-[24rpx] text-[var(--text-color-light9)]"},{default:r((()=>[p(" ("+c(e.items.count)+") ",1)])),_:1})):x("v-if",!0)])),_:1}),u(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1}),u(n,{class:"grid grid-cols-3 gap-2 mt-[20rpx]"},{default:r((()=>[(s(!0),m(f,null,_(e.items.list,((e,t)=>(s(),o(n,{class:"w-[210rpx] h-[210rpx]",key:t,onClick:t=>(e=>{1==e.content_type?b({url:"/addon/sow_community/pages/image/detail",param:{content_id:e.content_id}}):b({url:"/addon/sow_community/pages/video/detail",param:{content_id:e.content_id}})})(e)},{default:r((()=>[u(v,{src:i(k)(e.content_cover),mode:"aspectFill",class:"w-[210rpx] h-[210rpx] rounded-[20rpx]"},null,8,["src"])])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):x("v-if",!0)}}}),je=ve(e({__name:"detail",setup(e){const h=he(),T=t({}),L=e=>{T.value=e},{setShare:O}=z(),se=a(),xe=l((()=>se.info)),_e=me();l((()=>_e.totalNum));const fe=t(null),ve=t({}),je=t("img"),Ce=t(null),Fe=t(!1),Se=t(!1),Ve=t(!1),Te=t(!1),De=t(!1),Ie=t(!1),Le=t(0),Oe=t(""),ze=t(""),Ee=t("");t(null);const Me=t(null);let Ne={};E((e=>{Ne=e})),M((()=>{setTimeout((()=>{uni.getStorageSync("sku_form_refresh")?uni.removeStorageSync("sku_form_refresh"):(Ve.value=!1,uni.removeStorageSync("distributionType"),_e.getList(),Re(),Be())}))}));const Be=()=>{oe({goods_id:Ne.goods_id||"",sku_id:Ne.sku_id||"",type:Ne.type||""}).then((e=>{if(!e.data.goods||"[]"===JSON.stringify(e.data)){return N({url:"/addon/shop/pages/index",title:"找不到该商品",mode:"reLaunch"}),!1}ve.value=B(e.data),Ke.value=ve.value.goods.is_collect,ve.value.delivery_type_list=ve.value.goods.delivery_type_list?Object.values(ve.value.goods.delivery_type_list):[],ve.value.goods.goods_image=ve.value.goods.goods_image.split(","),ve.value.goods.goods_image.forEach(((e,t)=>{ve.value.goods.goods_image[t]=k(e)})),Ve.value=!0;let t=B(e.data);if(ve.value.goods.attr_format=[],t.goods&&t.goods.attr_format){B(JSON.parse(t.goods.attr_format)).forEach(((e,t)=>{(e.attr_child_value_name&&!(e.attr_child_value_name instanceof Array)||e.attr_child_value_name instanceof Array&&e.attr_child_value_name.length)&&ve.value.goods.attr_format.push(e)}))}""!=ve.value.goods.goods_video&&(Me.value=A("goodsVideo")),Oe.value=ve.value.goods.goods_name,ze.value="/addon/shop/pages/goods/detail?sku_id="+ve.value.sku_id,ve.value.type&&(ze.value+="&type="+ve.value.type),Ee.value=k(ve.value.goods.goods_cover_thumb_mid);let l={title:ve.value.goods.goods_name,desc:ve.value.goods.sub_title,url:ve.value.goods.goods_cover_thumb_mid};if(P({title:ve.value.goods.goods_name}),O({wechat:{...l},weapp:{...l}}),Object.keys(ve.value.goods).length&&"discount"==ve.value.type&&ve.value.goods.is_discount&&Object.keys(ve.value.discount_info).length){let e=(new Date).getTime();Le.value=1e3*ve.value.discount_info.active.end_time-e.toFixed(0)}Ue(ve.value),Ye(),et(),v()&&Ae(ve.value.goods.goods_id),ft(),R((()=>{setTimeout((()=>{const e=G().in(it);e.select(".swiper-box").boundingClientRect((e=>{dt=e?e.height:0})).exec(),e.select(".detail-head").boundingClientRect((e=>{e&&(nt=e.height?e.height:0)})).exec(),xt.value&&(_t.sku_id=ve.value.sku_id,Ne.type&&(_t.active=Ne.type),xe.value&&xe.value.member_id&&(_t.member_id=xe.value.member_id),xt.value.loadPoster())}),400)}))}))},Ae=e=>{re({goods_id:e}).then((e=>{}))};let Pe=t({});const Re=()=>{ue({goods_id:Ne.goods_id||"",sku_id:Ne.sku_id||""}).then((e=>{Object.keys(e.data).length&&(Pe.value.condition_type=e.data.condition_type,Pe.value.rule_json=e.data.rule_json,Pe.value.name=e.data.manjian_name)}))},$e=t(-1),Ue=(e={})=>{if(e.goods.is_limit&&xe.value&&e.goods.stock>0&&e.goods.max_buy){let t=0;if(1==e.goods.limit_type)t=e.goods.max_buy;else{let l=e.goods.max_buy-(e.goods.has_buy||0);t=l>0?l:0}t>e.goods.stock?$e.value=e.goods.stock:t<=e.goods.stock&&($e.value=t)}},He=e=>{ve.value.skuList.forEach(((t,l)=>{t.sku_id==e&&Object.assign(ve.value,t)}))},Je=l((()=>{let e=!1;return ve.value.skuList.forEach(((t,l)=>{t.sku_spec_format&&(e=!0)})),!(!e&&ve.value.stock<=0)&&(!e&&ve.value.stock,!0)})),qe=l((()=>{let e=!1;return(ve.value.service&&ve.value.service.length||ve.value.goodsSpec&&ve.value.goodsSpec.length||"real"==ve.value.goods.goods_type&&ve.value.delivery_type_list&&ve.value.delivery_type_list.length||Xe.value.length)&&(e=!0),e})),Ge=()=>{Ce.value.open(Pe.value)},Qe=e=>{fe.value&&fe.value.open(e)},Ke=t(0),We=()=>{if(!xe.value)return g({title:"未登录，请先登录后再收藏商品",icon:"none"}),!1;(Ke.value?de({goods_ids:[ve.value.goods_id]}):ne(ve.value.goods_id)).then((e=>{Ke.value=!Ke.value,Ke.value?g({title:"收藏成功",icon:"none"}):g({title:"取消收藏",icon:"none"})}))},Xe=t([]),Ye=()=>{pe({category_id:ve.value.goods.goods_category||"",goods_id:ve.value.goods_id||""}).then((e=>{Xe.value=e.data.data.map((e=>(-1!=e.sum_count&&e.receive_count===e.sum_count&&(e.btnType="collected"),xe.value&&e.is_receive&&e.limit_count===e.member_receive_count?e.btnType="using":e.btnType="collecting",e)))}))},Ze=t({count:0}),et=()=>{ie(ve.value.goods_id).then((e=>{Ze.value=e.data}))},tt=(e,t)=>{if(Array.isArray(e)){if(!e.length)return!1;w({indicator:"number",current:t,loop:!0,urls:l=e})}else{if(""===e)return!1;var l;(l=[]).push(k(e)),w({indicator:"number",loop:!0,urls:l})}},lt=t(0),at=()=>{De.value=!0};let st=$().platform;const ot=l((()=>{let e="";return e+="height: 100rpx;",e+="padding-right: 30rpx;",e+="padding-left: 30rpx;",e+="font-size: 32rpx;","ios"===st?e+="font-weight: 500;":"android"===st&&(e+="font-size: 36rpx;"),e})),rt=l((()=>"")),ut=l((()=>{let e="";return e+="top: 100rpx;",e+="left: 30rpx;","top: 100rpx;left: 30rpx;"})),it=W();let dt=0,nt=0;const pt=t(!1);U((e=>{if(0==dt||0==nt)return;let t=dt-nt-20;pt.value=!1,e.scrollTop>=t&&(pt.value=!0)}));const ct=e=>{"number"==typeof e&&tt(ve.value.goods.goods_image,e)},xt=t(null),mt=t("");let _t={};const ft=()=>{mt.value="?sku_id="+ve.value.sku_id,ve.value.type&&(mt.value+="&type="+ve.value.type),xe.value&&xe.value.member_id&&(mt.value+="&mid="+xe.value.member_id)},vt=()=>{xt.value.openShare()},gt=l((()=>{let e="";return e="newcomer_discount"==ve.value.type&&v()&&ve.value.newcomer_price?"newcomer_price":ve.value.show_type,e})),yt=l((()=>{let e="0.00";return e="newcomer_discount"==ve.value.type&&v()&&ve.value.newcomer_price?ve.value.newcomer_price:ve.value.show_price,e}));return H((()=>{try{J()}catch(e){}})),(e,t)=>{const l=V,a=S,v=C(F("u-swiper"),X),g=j,w=C(F("up-count-down"),Y),O=C(F("u-avatar"),Z),z=C(F("u-icon"),ee),E=C(F("u--image"),te),M=C(F("u-parse"),le),N=I,B=D,A=C(F("u-popup"),ae);return s(),o(a,{style:q(e.themeColor())},{default:r((()=>[Object.keys(ve.value).length?(s(),o(a,{key:0,class:"bg-[var(--page-bg-color)] min-h-[100vh] relative"},{default:r((()=>[x(" 自定义头部 "),u(a,{class:n(["flex items-center fixed left-0 right-0 z-10 bg-transparent detail-head",{"!bg-[#fff]":pt.value}]),style:q(i(ot))},{default:r((()=>[u(a,{class:"flex-center h-[60rpx] rounded-[30rpx] box-border arrow-left px-[20rpx] leading-[1]",style:q(i(rt))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-zuoV6xx text-[18px]",onClick:t[0]||(t[0]=e=>{Q().length>1?K({delta:1}):b({url:"/addon/shop/pages/index",mode:"reLaunch"})})}),u(l,{class:"w-[2rpx] h-[26rpx] bg-[#999] mx-[14rpx]"}),u(l,{class:"nc-iconfont nc-icon-liebiao-xiV6xx1 text-[16px]",onClick:t[1]||(t[1]=e=>Se.value=!0)})])),_:1},8,["style"]),u(a,{class:n(["ml-auto !pt-[12rpx] !pb-[8rpx] p-[10rpx] bg-[rgba(255,255,255,.4)] rounded-full border-[2rpx] border-solid border-transparent box-border nc-iconfont nc-icon-fenxiangV6xx font-bold text-[#303133] text-[36rpx]",{"border-[#d8d8d8]":pt.value}]),onClick:vt},null,8,["class"])])),_:1},8,["class","style"]),Se.value?(s(),o(a,{key:0,class:"fixed top-0 left-0 right-0 bottom-0 z-100 bg-transparent",onClick:t[6]||(t[6]=e=>Se.value=!1)},{default:r((()=>[u(a,{class:"search-box w-[202rpx] bg-[#fff] rounded-[12rpx] relative",style:q(i(ut))},{default:r((()=>[u(a,{class:"px-[20rpx] flex-center",onClick:t[2]||(t[2]=e=>i(b)({url:"/addon/shop/pages/index",mode:"reLaunch"}))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-shouyeV6xx11 text-[30rpx] mr-[10rpx]"}),u(l,{class:"pl-[14rpx] py-[20rpx] flex-1 text-[24rpx] text-[#333] border-0 border-[#ddd] border-b-[1rpx] border-solid"},{default:r((()=>[p("首页")])),_:1})])),_:1}),u(a,{class:"px-[20rpx] flex-center",onClick:t[3]||(t[3]=e=>i(b)({url:"/addon/shop/pages/goods/search"}))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 text-[30rpx] mr-[10rpx]"}),u(l,{class:"pl-[14rpx] py-[20rpx] flex-1 text-[24rpx] text-[#333] border-0 border-[#ddd] border-b-[1rpx] border-solid"},{default:r((()=>[p("搜索")])),_:1})])),_:1}),u(a,{class:"px-[20rpx] flex-center",onClick:t[4]||(t[4]=e=>i(b)({url:"/addon/shop/pages/goods/cart"}))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-gouwucheV6xx1 text-[30rpx] mr-[10rpx]"}),u(l,{class:"pl-[14rpx] py-[20rpx] flex-1 text-[24rpx] text-[#333] border-0 border-[#ddd] border-b-[1rpx] border-solid"},{default:r((()=>[p("购物车")])),_:1})])),_:1}),u(a,{class:"px-[20rpx] flex-center",onClick:t[5]||(t[5]=e=>i(b)({url:"/addon/shop/pages/member/index"}))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-a-wodeV6xx-36 text-[30rpx] mr-[10rpx]"}),u(l,{class:"pl-[14rpx] py-[20rpx] flex-1 text-[24rpx] text-[#333]"},{default:r((()=>[p("个人中心")])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})):x("v-if",!0),u(a,{class:"w-full h-[100vw] relative overflow-hidden"},{default:r((()=>[u(a,{class:n(["absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-linear transform",{"translate-x-0":"img"===je.value,"translate-x-full":"img"!=je.value}])},{default:r((()=>[u(a,{class:"swiper-box"},{default:r((()=>[u(v,{list:ve.value.goods.goods_image,indicator:ve.value.goods.goods_image.length,indicatorStyle:{bottom:"70rpx"},autoplay:"img"===je.value,height:"100vw",radius:"0",onClick:ct},null,8,["list","indicator","autoplay"])])),_:1})])),_:1},8,["class"]),x(' <view class="media-mode absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-linear transform"\n                  :class="{\'translate-x-0\':switchMedia === \'video\',\'-translate-x-full\':switchMedia != \'video\'}">\n                  <video id="goodsVideo" class="w-full h-full" :src="img(goodsDetail.goods.goods_video)" :poster="img(goodsDetail.goods.goods_cover_thumb_mid)" objectFit="cover"></video>\n                </view> '),x(" 切换视频、图片 "),x(" <view class=\"media-mode absolute bottom-[74rpx] w-full text-center leading-[50rpx] \" v-if=\"goodsDetail.goods.goods_video != ''\">\n                    <text :class=\"{ '!bg-[var(--primary-color)]': switchMedia == 'video' }\" @click=\"switchMedia = 'video'\">视频</text>\n                    <text :class=\"{ '!bg-[var(--primary-color)]': switchMedia == 'img' }\" @click=\"(switchMedia = 'img'), videoContext.pause()\">图片</text>\n                </view> ")])),_:1}),"original_price"!=i(gt)?(s(),o(a,{key:1,class:"rounded-t-[40rpx] -mt-[44rpx] relative flex items-center justify-between !bg-cover box-border pb-[26rpx] h-[136rpx] px-[30rpx]",style:q({background:"url("+i(k)("addon/shop/detail/discount_price_bg.png")+") no-repeat"})},{default:r((()=>[u(a,{class:"text-[#fff]"},{default:r((()=>["newcomer_price"==i(gt)?(s(),o(l,{key:0,class:"text-[26rpx] mr-[10rpx] font-500 leading-[36rpx]"},{default:r((()=>[p("新人价")])),_:1})):"discount_price"==i(gt)?(s(),o(l,{key:1,class:"text-[26rpx] mr-[10rpx] font-500 leading-[36rpx]"},{default:r((()=>[p("折扣价")])),_:1})):"member_price"==i(gt)?(s(),o(l,{key:2,class:"text-[26rpx] mr-[10rpx] font-500 leading-[36rpx]"},{default:r((()=>[p("会员价")])),_:1})):x("v-if",!0),u(a,{class:"inline-block mr-[14rpx]"},{default:r((()=>[u(l,{class:"text-[32rpx] price-font mr-[4rpx]"},{default:r((()=>[p("￥")])),_:1}),u(l,{class:"text-[48rpx] -mb-[4rpx] price-font"},{default:r((()=>[p(c(parseFloat(i(yt)).toFixed(2).split(".")[0]),1)])),_:1}),u(l,{class:"text-[32rpx] price-font"},{default:r((()=>[p("."+c(parseFloat(i(yt)).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),u(a,{class:"inline-block"},{default:r((()=>[ve.value.price?(s(),o(l,{key:0,class:"text-[26rpx] mr-[6rpx]"},{default:r((()=>[p("售价:")])),_:1})):x("v-if",!0),u(l,{class:"text-[26rpx] price-font leading-[36rpx]"},{default:r((()=>[p("￥"+c(ve.value.price),1)])),_:1})])),_:1})])),_:1}),"discount_price"==i(gt)?(s(),o(a,{key:0,class:"flex flex-col text-[#fff] items-end h-[59rpx] justify-between"},{default:r((()=>[u(g,{class:"h-[28rpx] w-[auto] mr-[2rpx]",src:i(k)("addon/shop/detail/discount_price.png"),mode:"heightFix"},null,8,["src"]),u(a,{class:"flex items-center text-[24rpx] -mb-[10rpx] overflow-hidden h-[28rpx]"},{default:r((()=>[u(l,{class:"mr-[4rpx] whitespace-nowrap"},{default:r((()=>[p("距结束")])),_:1}),u(w,{class:"text-[#fff] text-[28rpx]",time:Le.value,format:"DD:HH:mm:ss",onChange:L},{default:r((()=>[u(a,{class:"flex"},{default:r((()=>[T.value.days>0?(s(),o(a,{key:0,class:"text-[24rpx] flex items-center"},{default:r((()=>[u(l,null,{default:r((()=>[p(c(T.value.days),1)])),_:1}),u(l,{class:"ml-[4rpx] text-[20rpx]"},{default:r((()=>[p("天")])),_:1})])),_:1})):x("v-if",!0),u(a,{class:"text-[24rpx] flex items-center"},{default:r((()=>[T.value.hours?(s(),o(l,{key:0,class:"min-w-[30rpx] text-center"},{default:r((()=>[p(c(T.value.hours>=10?T.value.hours:"0"+T.value.hours),1)])),_:1})):(s(),o(l,{key:1,class:"min-w-[30rpx] text-center"},{default:r((()=>[p("00")])),_:1})),u(l,{class:"text-[20rpx]"},{default:r((()=>[p("时")])),_:1})])),_:1}),u(a,{class:"text-[24rpx] flex items-center"},{default:r((()=>[u(l,{class:"min-w-[30rpx] text-center"},{default:r((()=>[p(c(T.value.minutes>=10?T.value.minutes:"0"+T.value.minutes),1)])),_:1}),u(l,{class:"text-[20rpx]"},{default:r((()=>[p("分")])),_:1})])),_:1}),u(a,{class:"text-[24rpx] flex items-center"},{default:r((()=>[u(l,{class:"min-w-[30rpx] text-center"},{default:r((()=>[p(c(T.value.seconds<10?"0"+T.value.seconds:T.value.seconds),1)])),_:1}),u(l,{class:"text-[20rpx]"},{default:r((()=>[p("秒")])),_:1})])),_:1})])),_:1})])),_:1},8,["time"])])),_:1})])),_:1})):x("v-if",!0)])),_:1},8,["style"])):x("v-if",!0),u(a,{class:"bg-[var(--page-bg-color)] rounded-[40rpx] overflow-hidden -mt-[34rpx] relative"},{default:r((()=>[u(a,{class:n(["detail-title relative px-[30rpx]",{"pt-[40rpx]":"original_price"!=i(gt),"pt-[30rpx]":"original_price"==i(gt)}])},{default:r((()=>["original_price"==i(gt)?(s(),o(a,{key:0,class:"text-[var(--price-text-color)] flex items-baseline mb-[12rpx]"},{default:r((()=>[u(a,{class:"inline-block goods-price-time"},{default:r((()=>[u(l,{class:"price-font text-[32rpx]"},{default:r((()=>[p("￥")])),_:1}),u(l,{class:"price-font text-[48rpx]"},{default:r((()=>[p(c(parseFloat(i(yt)).toFixed(2).split(".")[0]),1)])),_:1}),u(l,{class:"price-font text-[32rpx] mr-[10rpx]"},{default:r((()=>[p("."+c(parseFloat(i(yt)).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),x(' <text class="text-[26rpx] text-[var(--text-color-light9)] line-through price-font" v-if="goodsDetail.market_price && parseFloat(goodsDetail.market_price)">\n                          ￥{{ goodsDetail.market_price }}\n                        </text> ')])),_:1})):x("v-if",!0),u(a,{class:"text-[#333] font-medium text-[30rpx] multi-hidden leading-[40rpx]"},{default:r((()=>[ve.value.goods.goods_brand?(s(),o(a,{key:0,class:"brand-tag middle",style:q(i(h).baseTagStyle(ve.value.goods.goods_brand))},{default:r((()=>[p(c(ve.value.goods.goods_brand.brand_name),1)])),_:1},8,["style"])):x("v-if",!0),p(" "+c(ve.value.goods.goods_name),1)])),_:1}),u(a,{class:"flex justify-between items-start mt-[24rpx]"},{default:r((()=>[ve.value.market_price&&parseFloat(ve.value.market_price)?(s(),o(a,{key:0,class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:r((()=>[u(l,{class:"whitespace-nowrap mr-[4rpx]"},{default:r((()=>[p("划线价:")])),_:1}),u(l,{class:"line-through"},{default:r((()=>[p("￥"+c(ve.value.market_price),1)])),_:1})])),_:1})):x("v-if",!0),u(a,{class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:r((()=>[u(l,{class:"whitespace-nowrap mr-[4rpx]"},{default:r((()=>[p("库存:")])),_:1}),u(l,null,{default:r((()=>[p(c(ve.value.stock),1)])),_:1}),u(l,null,{default:r((()=>[p(c(ve.value.goods.unit),1)])),_:1})])),_:1}),u(a,{class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)] flex items-baseline"},{default:r((()=>[u(l,{class:"whitespace-nowrap mr-[4rpx]"},{default:r((()=>[p("销量:")])),_:1}),u(l,{class:"mx-[2rpx]"},{default:r((()=>[p(c(ve.value.goods.sale_num),1)])),_:1}),u(l,null,{default:r((()=>[p(c(ve.value.goods.unit),1)])),_:1})])),_:1})])),_:1}),ve.value.label_info&&ve.value.label_info.length?(s(),o(a,{key:1,class:"flex flex-wrap mt-[16rpx]"},{default:r((()=>[(s(!0),m(f,null,_(ve.value.label_info,(e=>(s(),m(f,{key:e.label_id},["icon"==e.style_type&&e.icon?(s(),o(g,{key:0,class:"img-tag middle",src:i(k)(e.icon),mode:"heightFix",onError:t=>i(h).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?x("v-if",!0):(s(),o(a,{key:1,class:"base-tag middle",style:q(i(h).baseTagStyle(e))},{default:r((()=>[p(c(e.label_name),1)])),_:2},1032,["style"]))],64)))),128))])),_:1})):x("v-if",!0)])),_:1},8,["class"]),i(qe)?(s(),o(a,{key:0,class:"mt-[24rpx] sidebar-margin card-template"},{default:r((()=>[ve.value.service&&ve.value.service.length?(s(),o(a,{key:0,onClick:t[7]||(t[7]=e=>Te.value=!Te.value),class:"card-template-item"},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0"},{default:r((()=>[p("服务")])),_:1}),u(a,{class:"text-[#343434] text-[26rpx] leading-[30rpx] font-400 truncate ml-auto"},{default:r((()=>[p(c(ve.value.service[0].service_name),1)])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0),ve.value.goodsSpec&&ve.value.goodsSpec.length?(s(),o(a,{key:1,onClick:Qe,class:"card-template-item"},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0 mr-[20rpx]"},{default:r((()=>[p("已选")])),_:1}),u(a,{class:"ml-auto text-right truncate flex-1 text-[#343434] text-[26rpx] leading-[30rpx] font-400"},{default:r((()=>[p(c(ve.value.sku_spec_format),1)])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0),"real"==ve.value.goods.goods_type&&ve.value.delivery_type_list&&ve.value.delivery_type_list.length?(s(),o(a,{key:2,class:"card-template-item",onClick:at},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0"},{default:r((()=>[p("配送")])),_:1}),u(a,{class:"ml-auto flex items-center text-[#343434] text-[26rpx] leading-[30rpx] font-400"},{default:r((()=>[p(c(ve.value.delivery_type_list[lt.value].name),1)])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0),Xe.value.length?(s(),o(a,{key:3,onClick:t[8]||(t[8]=e=>Ie.value=!0),class:"card-template-item"},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0 mr-[20rpx]"},{default:r((()=>[p("领券")])),_:1}),u(a,{class:"ml-auto flex-1 flex-nowrap flex items-center overflow-hidden h-[44rpx] content-between"},{default:r((()=>[(s(!0),m(f,null,_(Xe.value,((e,t)=>(s(),m(f,{key:t},[t<3?(s(),o(l,{key:0,class:n(["tag-item whitespace-nowrap border-[2rpx] px-[6rpx] h-[40rpx] border-solid border-[var(--primary-color)] text-[var(--primary-color)] mt-[4rpx]",{"mr-[12rpx]":Xe.value.length!=t+1&&t<2,"ml-auto":0==t}])},{default:r((()=>[p(c(e.title),1)])),_:2},1032,["class"])):x("v-if",!0)],64)))),128))])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0),Object.keys(i(Pe)).length?(s(),o(a,{key:4,class:"card-template-item",onClick:Ge},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0 mr-[20rpx]"},{default:r((()=>[p("优惠")])),_:1}),u(a,{class:"ml-auto flex-1 flex-nowrap flex items-center overflow-hidden h-[44rpx] justify-end"},{default:r((()=>[u(a,{class:"bg-[var(--primary-color-light)] text-[var(--primary-color)] rounded-[6rpx] text-[22rpx] flex items-center justify-center w-[86rpx] h-[34rpx] mr-[6rpx]"},{default:r((()=>[p("满减送")])),_:1}),u(a,{class:"truncate max-w-[430rpx] text-[26rpx]"},{default:r((()=>[p(c(i(Pe).name),1)])),_:1})])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0)])),_:1})):x("v-if",!0),ve.value.sow_show_list?(s(),o(a,{key:1,class:"sidebar-margin"},{default:r((()=>[u(we,{items:ve.value.sow_show_list},null,8,["items"])])),_:1})):x("v-if",!0),ve.value.evaluate_is_show?(s(),o(a,{key:2,class:"mt-[var(--top-m)] sidebar-margin card-template"},{default:r((()=>[u(a,{class:n(["flex items-center justify-between min-h-[40rpx]",{"mb-[30rpx]":Ze.value&&Ze.value.list&&Ze.value.list.length}])},{default:r((()=>[u(l,{class:"title !mb-[0]"},{default:r((()=>[p("宝贝评价("+c(Ze.value.count)+")",1)])),_:1}),Ze.value.count?(s(),o(a,{key:0,class:"h-[40rpx] flex items-center",onClick:t[9]||(t[9]=e=>(ve.value.goods_id,void b({url:"/addon/shop/pages/evaluate/list",param:{goods_id:ve.value.goods_id}})))},{default:r((()=>[u(l,{class:"text-[24rpx] text-[var(--text-color-light9)]"},{default:r((()=>[p("查看全部")])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):x("v-if",!0),Ze.value.count?x("v-if",!0):(s(),o(l,{key:1,class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:r((()=>[p("暂无评价 ")])),_:1}))])),_:1},8,["class"]),u(a,null,{default:r((()=>[(s(!0),m(f,null,_(Ze.value.list,((e,t)=>(s(),o(a,{class:n({"pb-[34rpx]":t!=Ze.value.list.length-1}),key:t},{default:r((()=>[u(a,{class:"flex items-center w-full"},{default:r((()=>[u(O,{"default-url":i(k)("static/resource/images/default_headimg.png"),src:i(k)(e.member_head),size:"50rpx",leftIcon:"none"},null,8,["default-url","src"]),u(l,{class:"ml-[10rpx] text-[28rpx] text-[#333]"},{default:r((()=>[p(c(e.member_name),1)])),_:2},1024)])),_:2},1024),u(a,{class:"flex justify-between w-full mt-[16rpx]"},{default:r((()=>[u(a,{class:"flex-1 w-[540rpx] text-[26rpx] text-[#333] max-h-[72rpx] leading-[36rpx] multi-hidden mr-[50rpx]"},{default:r((()=>[p(c(e.content),1)])),_:2},1024),u(a,{class:"w-[80rpx] shrink-0"},{default:r((()=>[e.image_mid&&e.image_mid.length?(s(),o(E,{key:0,width:"80rpx",height:"80rpx",radius:"var(--goods-rounded-mid)",src:i(k)(e.image_mid[0]),model:"aspectFill",onClick:t=>tt(e.images[0])},{error:r((()=>[u(z,{name:"photo",color:"#999",size:"50"})])),_:2},1032,["src","onClick"])):x("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1})])),_:1})):x("v-if",!0),ve.value.goods&&ve.value.goods.attr_format&&Object.keys(ve.value.goods.attr_format).length?(s(),o(a,{key:3,class:"my-[var(--top-m)] goods-sku sidebar-margin card-template"},{default:r((()=>[u(a,{class:"title mb-[30rpx]"},{default:r((()=>[p("商品属性")])),_:1}),u(a,null,{default:r((()=>[(s(!0),m(f,null,_(ve.value.goods.attr_format,((e,t)=>(s(),m(f,{key:t},[t<4||Fe.value?(s(),o(a,{key:0,class:"card-template-item"},{default:r((()=>[u(l,{class:"text-[26rpx] leading-[30rpx] w-[160rpx] font-400 shrink-0 text-[var(--text-color-light9)]"},{default:r((()=>[p(c(e.attr_value_name),1)])),_:2},1024),u(a,{class:"text-[#333] box-border value-wid text-[26rpx] leading-[30rpx] font-400 pl-[20rpx]"},{default:r((()=>[p(c(Array.isArray(e.attr_child_value_name)?e.attr_child_value_name.join(","):e.attr_child_value_name),1)])),_:2},1024),x(' <text class="nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"></text> ')])),_:2},1024)):x("v-if",!0)],64)))),128)),ve.value.goods.attr_format.length>4?(s(),o(a,{key:0,class:"flex-center",onClick:t[10]||(t[10]=e=>Fe.value=!Fe.value)},{default:r((()=>[u(l,{class:"text-[24rpx] mr-[10rpx]"},{default:r((()=>[p(c(Fe.value?"收起":"展开"),1)])),_:1}),u(l,{class:n(["nc-iconfont !text-[22rpx]",{"nc-icon-xiaV6xx":!Fe.value,"nc-icon-shangV6xx-1":Fe.value}])},null,8,["class"])])),_:1})):x("v-if",!0)])),_:1})])),_:1})):x("v-if",!0),u(a,{class:"my-[var(--top-m)] sidebar-margin card-template px-[var(--pad-sidebar-m)]"},{default:r((()=>[u(a,{class:"title"},{default:r((()=>[p("商品详情")])),_:1}),u(a,{class:"u-content"},{default:r((()=>[u(M,{content:ve.value.goods.goods_desc,tagStyle:{img:"vertical-align: top;",p:"overflow: hidden;word-break:break-word;"}},null,8,["content"])])),_:1})])),_:1}),u(be),u(a,{class:"tab-bar-placeholder"}),u(a,{class:"border-[0] border-t-[2rpx] border-solid border-[#f5f5f5] w-[100%] flex justify-between pl-[32rpx] pr-[4rpx] bg-[#fff] box-border fixed left-0 bottom-0 tab-bar z-1 items-center"},{default:r((()=>[u(a,{class:"flex items-center"},{default:r((()=>[u(a,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:t[11]||(t[11]=e=>i(b)({url:"/addon/shop/pages/index",mode:"reLaunch"}))},{default:r((()=>[u(a,{class:"nc-iconfont nc-icon-shouyeV6xx11 text-[36rpx]"}),u(l,{class:"text-[20rpx] mt-[10rpx]"},{default:r((()=>[p("首页")])),_:1})])),_:1}),u(a,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:t[12]||(t[12]=e=>i(b)({url:"/addon/shop/pages/goods/cart"}))},{default:r((()=>[u(a,{class:"iconfont icongouwuche2 text-[38rpx]"}),u(l,{class:"text-[20rpx] mt-[10rpx]"},{default:r((()=>[p("购物车")])),_:1})])),_:1}),u(a,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:We},{default:r((()=>[u(l,{class:n(["nc-iconfont text-[36rpx]",{"text-[#ff0000] nc-icon-xihuanV6mm":Ke.value,"text-[#303133] nc-icon-guanzhuV6xx":!Ke.value}])},null,8,["class"]),u(l,{class:"text-[20rpx] mt-[10rpx]"},{default:r((()=>[p("收藏")])),_:1})])),_:1})])),_:1}),1==ve.value.goods.status?(s(),o(a,{key:0,class:"flex flex-1"},{default:r((()=>[ve.value.goods.is_gift?(s(),o(N,{key:0,class:"!w-[420rpx] flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 leading-[70rpx] rounded-full remove-border"},{default:r((()=>[p("商品为赠品不可购买 ")])),_:1})):$e.value>0||-1==$e.value?(s(),m(f,{key:1},["newcomer_discount"!=i(Ne).type&&("real"==ve.value.goods.goods_type||"virtual"==ve.value.goods.goods_type&&"verify"!=ve.value.goods.virtual_receive_type)?(s(),o(N,{key:0,class:"cart-btn-bg flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border",onClick:t[13]||(t[13]=e=>Qe("join_cart"))},{default:r((()=>[p(" 加入购物车 ")])),_:1})):x("v-if",!0),i(Je)?(s(),o(N,{key:1,style:q({width:"real"==ve.value.goods.goods_type||"virtual"==ve.value.goods.goods_type&&"verify"!=ve.value.goods.virtual_receive_type?"200rpx":"400rpx!important"}),class:"flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] primary-btn-bg !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border",onClick:t[14]||(t[14]=e=>Qe("buy_now"))},{default:r((()=>[p("立即购买 ")])),_:1},8,["style"])):(s(),o(N,{key:2,style:q({width:"real"==ve.value.goods.goods_type||"virtual"==ve.value.goods.goods_type&&"verify"!=ve.value.goods.virtual_receive_type?"200rpx":"400rpx!important"}),class:"flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border"},{default:r((()=>[p("已售罄 ")])),_:1},8,["style"]))],64)):0==$e.value?(s(),o(N,{key:2,style:{width:"420rpx!important"},class:"flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 leading-[70rpx] rounded-full remove-border"},{default:r((()=>[p("已达限购数量 ")])),_:1})):x("v-if",!0)])),_:1})):(s(),o(a,{key:1,class:"flex flex-1"},{default:r((()=>[u(N,{class:"w-[100%] !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 leading-[70rpx] rounded-full remove-border"},{default:r((()=>[p("该商品已下架")])),_:1})])),_:1}))])),_:1})])),_:1}),x(" 服务 "),u(a,{onTouchmove:t[17]||(t[17]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(A,{class:"popup-type",show:Te.value,onClose:t[16]||(t[16]=e=>Te.value=!1)},{default:r((()=>[u(a,{class:"min-h-[480rpx] popup-common",onTouchmove:t[15]||(t[15]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(a,{class:"title"},{default:r((()=>[p("商品服务")])),_:1}),u(B,{class:"h-[520rpx]","scroll-y":"true"},{default:r((()=>[u(a,{class:"pl-[22rpx] pb-[28rpx] pr-[37rpx]"},{default:r((()=>[(s(!0),m(f,null,_(ve.value.service,((e,t)=>(s(),o(a,{class:"flex mb-[28rpx]"},{default:r((()=>[u(g,{class:"mt-[4rpx] w-[32rpx] h-[32rpx] mr-[14rpx]",src:i(k)(e.image||"addon/shop/icon_service.png"),mode:"aspectFit"},null,8,["src"]),u(a,{class:"flex-1"},{default:r((()=>[u(a,{class:"text-[30rpx] leading-[36rpx] text-[#333] mb-[8rpx]"},{default:r((()=>[p(c(e.service_name),1)])),_:2},1024),u(a,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)]"},{default:r((()=>[p(c(e.desc),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),x(" 配送 "),u(a,{onTouchmove:t[20]||(t[20]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(A,{class:"popup-type",show:De.value,onClose:t[19]||(t[19]=e=>De.value=!1)},{default:r((()=>[u(a,{class:"min-h-[360rpx] popup-common",onTouchmove:t[18]||(t[18]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(a,{class:"title"},{default:r((()=>[p("配送方式")])),_:1}),u(B,{class:"h-[520rpx]","scroll-y":"true"},{default:r((()=>[u(a,{class:"px-[var(--popup-sidebar-m)]"},{default:r((()=>[(s(!0),m(f,null,_(ve.value.delivery_type_list,((e,t)=>(s(),o(a,{class:"flex mb-[40rpx]",onClick:l=>((e,t)=>{lt.value=t,De.value=!1,uni.setStorageSync("distributionType",e.name)})(e,t)},{default:r((()=>[u(g,{class:"mt-[4rpx] w-[32rpx] h-[32rpx] mr-[14rpx]",src:i(k)("addon/shop/icon_service.png"),mode:"aspectFit"},null,8,["src"]),u(a,{class:"flex-1"},{default:r((()=>[u(a,{class:"text-[30rpx] leading-[36rpx] text-[#333] mb-[8rpx]"},{default:r((()=>[p(c(e.name),1)])),_:2},1024),u(a,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)]"},{default:r((()=>[p(c(e.desc),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),256))])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),x(" 优惠券 "),u(a,{onTouchmove:t[24]||(t[24]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(A,{class:"popup-type",show:Ie.value,onClose:t[23]||(t[23]=e=>Ie.value=!1)},{default:r((()=>[u(a,{class:"min-h-[480rpx] popup-common",onTouchmove:t[22]||(t[22]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(a,{class:"title"},{default:r((()=>[p("优惠券")])),_:1}),u(B,{class:"h-[520rpx]","scroll-y":"true"},{default:r((()=>[u(a,{class:"px-[32rpx]"},{default:r((()=>[(s(!0),m(f,null,_(Xe.value,((e,t)=>(s(),o(a,{class:"mb-[30rpx] flex items-center border-[2rpx] border-solid border-[rgba(0,0,0,.1)] rounded-[var(--rounded-small)]",key:t},{default:r((()=>[u(a,{class:"flex flex-col items-center my-[20rpx] w-[200rpx] border-0 border-r-[2rpx] border-dashed border-[rgba(0,0,0,.1)]"},{default:r((()=>[u(a,{class:"text-xs price-font"},{default:r((()=>[u(l,{class:"text-[28rpx]"},{default:r((()=>[p("￥")])),_:1}),u(l,{class:"text-[48rpx]"},{default:r((()=>[p(c(Number(e.price)),1)])),_:2},1024)])),_:2},1024),u(l,{class:"text-xs mt-[12rpx]"},{default:r((()=>[p(c(Number(e.min_condition_money)?"满"+Number(e.min_condition_money)+"元可用":"无门槛"),1)])),_:2},1024)])),_:2},1024),u(a,{class:"ml-[20rpx] flex-1 flex flex-col py-[20rpx]"},{default:r((()=>[u(l,{class:"text-xs font-500"},{default:r((()=>[p(c(e.title),1)])),_:2},1024),u(l,{class:"text-xs text-[var(--text-color-light6)] mt-[12rpx]"},{default:r((()=>[p(c(1==e.valid_type&&"领取之日起"+e.length+"天内有效"||2==e.valid_type&&"领取之日起至"+e.valid_end_time),1)])),_:2},1024)])),_:2},1024),"collecting"===e.btnType?(s(),o(l,{key:0,class:"bg-[var(--primary-color)] mr-[20rpx] w-[106rpx] box-border text-center text-[#fff] h-[50rpx] text-[22rpx] px-[20rpx] leading-[50rpx] rounded-[100rpx]",onClick:t=>((e,t)=>{if(!xe.value)return y().setLoginBack({url:"/addon/shop/pages/goods/detail",param:{sku_id:ve.value.sku_id,type:ve.value.type}}),!1;ce({coupon_id:e.id||"",number:1}).then((e=>{Ye()}))})(e)},{default:r((()=>[p("领取")])),_:2},1032,["onClick"])):(s(),o(l,{key:1,class:"!bg-[var(--primary-help-color4)] mr-[20rpx] text-[#fff] mr-[20rpx] h-[50rpx] text-[22rpx] px-[20rpx] leading-[50rpx] rounded-[100rpx]"},{default:r((()=>[p(c("collected"===e.btnType?"已领完":"已领取"),1)])),_:2},1024))])),_:2},1024)))),128))])),_:1})])),_:1}),u(a,{class:"btn-wrap"},{default:r((()=>[u(N,{class:"primary-btn-bg btn",onClick:t[21]||(t[21]=e=>Ie.value=!1)},{default:r((()=>[p("确定")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),Ve.value?(s(),o(ke,{key:2,ref_key:"goodsSkuRef",ref:fe,"goods-detail":ve.value,onChange:He},null,8,["goods-detail"])):x("v-if",!0),u(ge,{ref_key:"manjianShowRef",ref:Ce},null,512),u(ye,{ref_key:"sharePosterRef",ref:xt,posterType:"shop_goods",posterId:ve.value.goods.poster_id,posterParam:i(_t),copyUrlParam:mt.value},null,8,["posterId","posterParam","copyUrlParam"])])),_:1})):x("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-4a1b9147"]]);export{je as default};
