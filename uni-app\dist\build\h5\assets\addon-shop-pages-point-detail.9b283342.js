import{d as e,r as t,p as a,I as l,o as s,c as r,w as o,b as i,e as u,O as n,t as d,x as p,g as c,y as x,F as f,z as m,$ as _,U as v,a as g,A as h,aB as b,Q as y,i as k,j as w,k as j,S,al as C,au as F,R as V,P as T,L as E,D as I,u as L,J as O,X as z,b1 as A,W as B,as as D,N as P,a9 as R,an as U,ar as N,aA as J,n as M,av as G,a2 as K,a3 as Q,aw as W}from"./index-3caf046d.js";import{_ as X}from"./u-swiper.1a811b26.js";import{_ as $}from"./u-avatar.30e31e9c.js";import{_ as q}from"./u-icon.ba193921.js";import{_ as H}from"./u--image.eb573bce.js";import{_ as Y}from"./u-parse.406d0731.js";import{_ as Z}from"./u-popup.1b30ffa7.js";import{_ as ee}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{i as te}from"./goods.6a81cb49.js";import{c as ae}from"./point.0698952c.js";import{_ as le}from"./u-number-box.8a6aafb5.js";import{b as se}from"./bind-mobile.25318c0e.js";import{_ as re}from"./_plugin-vue_export-helper.1b428a4d.js";import{s as oe}from"./share-poster.0fbc73fb.js";import{u as ie}from"./useGoods.9c8f1c51.js";import"./u-loading-icon.255170b9.js";import"./u-text.f02e6497.js";/* empty css                                                               */import"./u-image.04cba9a2.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-safe-bottom.98e092c5.js";import"./u-form.49dbb57f.js";import"./u-line.69c0c00f.js";import"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import"./u-input.2d8dc7a4.js";import"./u-modal.8624728a.js";import"./u-checkbox-group.0328273c.js";const ue=re(e({__name:"goods-sku",props:["goodsDetail"],emits:["change"],setup(e,{expose:L,emit:O}){const z=e,A=t(!1),B=t(null),D=t({skuId:"",name:[]}),P=t(""),R=t(1),U=a((()=>{let e="0.00";return e=Object.keys(Q.value).length&&Object.keys(Q.value.goods).length&&Q.value.goods.member_discount&&E()&&Q.value.member_price!=Q.value.price&&Q.value.member_price?Q.value.member_price:Q.value.price,e})),N=()=>{setTimeout((()=>{(!R.value||R.value<=0)&&(R.value=1),R.value>=Number(Q.value.detail.limit_num)&&(R.value=Q.value.detail.limit_num)}),0)},J=()=>{setTimeout((()=>{(!R.value||R.value<=0)&&(R.value=1),R.value>=Number(Q.value.detail.limit_num)&&(R.value=Q.value.detail.limit_num)}),0)},M=l(),G=a((()=>M.info)),K=()=>{A.value=!1},Q=a((()=>{let e=I(z.goodsDetail);return Object.keys(e).length&&(Object.keys(D.value.name).length||(D.value.name=e.sku_spec_format.split(",")),e.goodsSpec.forEach(((e,t)=>{let a=e.spec_values.split(",");e.values=[],a.forEach(((a,l)=>{e.values[l]={},e.values[l].name=a,e.values[l].selected=!1,e.values[l].disabled=!1,D.value.name.forEach(((s,r)=>{r==t&&s==a&&(e.values[l].selected=!0)}))}))})),W(),e.skuList&&Object.keys(e.skuList).length&&e.skuList.forEach(((t,a)=>{t.sku_id==D.value.skuId&&(e.detail=t)}))),e})),W=()=>{z.goodsDetail.skuList.forEach(((e,t)=>{e.sku_spec_format==D.value.name.toString()&&(D.value.skuId=e.sku_id,O("change",e.sku_id))}))},X=t(null);t(uni.getStorageSync("isBindMobile"));const $=()=>{if(!G.value&&uni.getStorageSync("isBindMobile"))return uni.setStorage({key:"loginBack",data:{url:"/addon/shop/pages/point/detail",param:{id:Q.value.exchange_id}}}),X.value.open(),!1;if(!G.value)return v().setLoginBack({url:"/addon/shop/pages/point/detail",param:{id:Q.value.exchange_id}}),!1;var e={sku_id:Q.value.sku_id,num:R.value};uni.setStorage({key:"orderCreateData",data:{sku_data:[e]},success:()=>{g({url:"/addon/shop/pages/point/payment"})}}),K()};return L({open:(e="",t="")=>{P.value=e,A.value=!0,B.value=t}}),(e,t)=>{const a=y,l=k(w("u--image"),H),v=j,g=S,E=C,I=k(w("u-number-box"),le),L=F,O=V,z=k(w("u-popup"),Z);return s(),r(v,{onTouchmove:t[4]||(t[4]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[i(z,{class:"popup-type",show:A.value,onClose:K,mode:"bottom"},{default:o((()=>[u(Q).detail?(s(),r(v,{key:0,class:"py-[32rpx] relative",onTouchmove:t[3]||(t[3]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[i(v,{class:"flex px-[32rpx] mb-[40rpx]"},{default:o((()=>[i(v,{class:"w-[180rpx] h-[180rpx]"},{default:o((()=>[i(l,{width:"180rpx",height:"180rpx",radius:"var(--goods-rounded-big)",src:u(h)(u(Q).detail.sku_image),onClick:t[0]||(t[0]=e=>(e=>{if(""===e)return!1;var t=[];t.push(h(e)),b({indicator:"number",loop:!0,urls:t})})(u(Q).detail.sku_image)),model:"aspectFill"},{error:o((()=>[i(a,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:u(h)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["radius","src"])])),_:1}),i(v,{class:"flex flex-1 flex-col justify-between ml-[20rpx] py-[10rpx]"},{default:o((()=>[i(v,{class:"w-[100%]"},{default:o((()=>[i(v,{class:"text-[var(--price-text-color)] flex items-baseline"},{default:o((()=>[u(Q).point?(s(),r(g,{key:0,class:"price-font"},{default:o((()=>[i(g,{class:"text-[44rpx]"},{default:o((()=>[d(p(u(Q).point),1)])),_:1}),i(g,{class:"text-[38rpx]"},{default:o((()=>[d(p(u(T)("point")),1)])),_:1})])),_:1})):c("v-if",!0),u(Q).point&&parseFloat(u(Q).price)?(s(),r(g,{key:1,class:"text-[38rpx]"},{default:o((()=>[d("+ ")])),_:1})):c("v-if",!0),u(Q).point&&parseFloat(u(Q).price)?(s(),x(f,{key:2},[i(g,{class:"text-[44rpx] price-font"},{default:o((()=>[d(p(parseFloat(u(Q).price).toFixed(2)),1)])),_:1}),i(g,{class:"text-[38rpx] price-font"},{default:o((()=>[d("元")])),_:1})],64)):c("v-if",!0),u(Q).point||parseFloat(u(Q).price)?c("v-if",!0):(s(),x(f,{key:3},[i(g,{class:"text-[26rpx] price-font"},{default:o((()=>[d("￥")])),_:1}),i(g,{class:"text-[44rpx] price-font"},{default:o((()=>[d(p(parseFloat(u(Q).price).toFixed(2).split(".")[0]),1)])),_:1}),i(g,{class:"text-[26rpx] mr-[6rpx] price-font"},{default:o((()=>[d("."+p(parseFloat(u(U)).toFixed(2).split(".")[1]),1)])),_:1})],64))])),_:1}),i(v,{class:"text-[26rpx] leading-[32rpx] text-[var(--text-color-light6)] mt-[10rpx]"},{default:o((()=>[d("库存"+p(u(Q).detail.stock)+p(u(Q).goods.unit),1)])),_:1})])),_:1}),u(Q).goodsSpec&&u(Q).goodsSpec.length?(s(),r(v,{key:0,class:"text-[26rpx] leading-[30rpx] text-[var(--text-color-light6)] w-[100%] max-h-[60rpx] multi-hidden"},{default:o((()=>[d("已选规格："+p(u(Q).detail.sku_spec_format),1)])),_:1})):c("v-if",!0)])),_:1})])),_:1}),i(L,{class:"h-[500rpx] px-[32rpx] box-border mb-[60rpx]","scroll-y":"true"},{default:o((()=>[(s(!0),x(f,null,m(u(Q).goodsSpec,((e,t)=>(s(),r(v,{class:_({"mt-[20rpx]":0!=t}),key:t},{default:o((()=>[i(v,{class:"text-[28rpx] leading-[36rpx] mb-[24rpx]"},{default:o((()=>[d(p(e.spec_name),1)])),_:2},1024),i(v,{class:"flex flex-wrap"},{default:o((()=>[(s(!0),x(f,null,m(e.values,((e,a)=>(s(),r(v,{class:_(["box-border bg-[var(--temp-bg)] text-[24rpx] px-[44rpx] text-center h-[56rpx] flex-center mr-[20rpx] mb-[20rpx] border-1 border-solid rounded-[50rpx] border-[var(--temp-bg)]",{"!border-[var(--primary-color)] text-[var(--primary-color)] !bg-[var(--primary-color-light)]":e.selected}]),key:a,onClick:a=>((e,t)=>{D.value.name[t]=e.name,R.value=1,W()})(e,t)},{default:o((()=>[d(p(e.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1032,["class"])))),128)),i(v,{class:"flex justify-between items-center mt-[8rpx]"},{default:o((()=>[i(v,{class:"text-[28rpx]"},{default:o((()=>[d("购买数量")])),_:1}),u(Q).detail.limit_num>0?(s(),r(g,{key:0,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:o((()=>[d("(限购"+p(u(Q).detail.limit_num)+p(u(Q).goods.unit)+")",1)])),_:1})):c("v-if",!0),i(I,{min:1,max:parseInt(u(Q).detail.limit_num)<u(Q).stock?parseInt(u(Q).detail.limit_num):u(Q).stock,integer:"",step:1,"input-width":"68rpx",modelValue:R.value,"onUpdate:modelValue":t[2]||(t[2]=e=>R.value=e),"input-height":"52rpx"},{minus:o((()=>[i(g,{class:_(["text-[30rpx] nc-iconfont nc-icon-jianV6xx font-500",{"!text-[var(--text-color-light9)]":R.value<=1}])},null,8,["class"])])),input:o((()=>[i(E,{class:"text-[#303133] text-[28rpx] mx-[10rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:N,onBlur:J,modelValue:R.value,"onUpdate:modelValue":t[1]||(t[1]=e=>R.value=e)},null,8,["modelValue"])])),plus:o((()=>[i(g,{class:_(["text-[30rpx] nc-iconfont nc-icon-jiahaoV6xx font-500",{"!text-[var(--text-color-light9)]":R.value>=u(Q).stock||R.value==parseInt(u(Q).detail.limit_num)}])},null,8,["class"])])),_:1},8,["max","modelValue"])])),_:1})])),_:1}),i(v,{class:"px-[20rpx]"},{default:o((()=>[u(Q).detail.stock>0?(s(),r(O,{key:0,"hover-class":"none",class:"!h-[80rpx] primary-btn-bg leading-[80rpx] text-[26rpx] rounded-[50rpx] font-500",type:"primary",onClick:$},{default:o((()=>[d("确定")])),_:1})):(s(),r(O,{key:1,"hover-class":"none",class:"!h-[80rpx] leading-[80rpx] text-[26rpx] text-[#fff] bg-[#ccc] rounded-[50rpx] font-500"},{default:o((()=>[d("已售罄")])),_:1}))])),_:1})])),_:1})):c("v-if",!0)])),_:1},8,["show"]),c(" 强制绑定手机号 "),i(se,{ref_key:"bindMobileRef",ref:X},null,512)])),_:1})}}}),[["__scopeId","data-v-a5a6aacb"]]),ne=re(e({__name:"detail",setup(e){const v=ie(),{setShare:C}=L(),E=l(),I=a((()=>E.info)),le=t(""),se=t(""),re=t(""),ne=t(null),de=t({}),pe=t(!1),ce=t(!1),xe=t(!1),fe=t(!1);t(null),O((e=>{ae(e.id||"").then((e=>{if("[]"===JSON.stringify(e.data)){return z({url:"/addon/shop/pages/index",title:"找不到该商品",mode:"reLaunch"}),!1}de.value=A(e.data),de.value.delivery_type_list=de.value.goods.delivery_type_list?Object.values(de.value.goods.delivery_type_list):[],de.value.goods.goods_image=de.value.goods.goods_image_thumb_big,de.value.goods.goods_image.forEach(((e,t)=>{de.value.goods.goods_image[t]=h(e)}));let t=A(e.data);if(de.value.goods.attr_format=[],t.goods&&t.goods.attr_format){A(JSON.parse(t.goods.attr_format)).forEach(((e,t)=>{(e.attr_child_value_name&&!(e.attr_child_value_name instanceof Array)||e.attr_child_value_name instanceof Array&&e.attr_child_value_name.length)&&de.value.goods.attr_format.push(e)}))}le.value=de.value.goods.goods_name,se.value="/addon/shop/pages/point/detail?sku_id="+de.value.sku_id,re.value=h(de.value.goods.goods_cover_thumb_mid);let a={title:de.value.goods.goods_name,desc:de.value.goods.sub_title,url:de.value.goods.goods_cover_thumb_mid};B({title:de.value.goods.goods_name}),C({wechat:{...a},weapp:{...a}}),be(),Ae(),D((()=>{setTimeout((()=>{const e=G().in(Fe);e.select(".swiper-box").boundingClientRect((e=>{Ve=e?e.height:0})).exec(),e.select(".detail-head").boundingClientRect((e=>{e&&(Te=e.height?e.height:0)})).exec()}),400)}))}))})),P((()=>{uni.removeStorageSync("distributionType")}));const me=e=>{de.value.skuList.forEach(((t,a)=>{t.sku_id==e&&Object.assign(de.value,t)}))},_e=a((()=>{let e=!1;return de.value.skuList.forEach(((t,a)=>{t.sku_spec_format&&(e=!0)})),!(!e&&de.value.stock<=0)&&(!e&&de.value.stock,!0)})),ve=a((()=>{let e=!1;return(de.value.service&&de.value.service.length||de.value.goodsSpec&&de.value.goodsSpec.length||"real"==de.value.goods.goods_type&&de.value.delivery_type_list&&de.value.delivery_type_list.length)&&(e=!0),e})),ge=e=>{ne.value.open(e)},he=t({count:0}),be=()=>{te(de.value.goods_id).then((e=>{he.value=e.data}))},ye=(e,t)=>{if(Array.isArray(e)){var a=e;if(!e.length)return!1;b({indicator:"number",current:t,loop:!0,urls:a})}else{if(""===e)return!1;(a=[]).push(h(e)),b({indicator:"number",loop:!0,urls:a})}},ke=t(0),we=()=>{fe.value=!0};let je=R().platform;const Se=a((()=>{let e="";return e+="height: 100rpx;",e+="padding-right: 30rpx;",e+="padding-left: 30rpx;",e+="font-size: 32rpx;","ios"===je?e+="font-weight: 500;":"android"===je&&(e+="font-size: 36rpx;"),e})),Ce=a((()=>{let e="";return e+="font-size: 26px;","font-size: 26px;"})),Fe=W();let Ve=0,Te=0;const Ee=t(!1);U((e=>{let t=Ve-Te-20;Ee.value=!1,e.scrollTop>=t&&(Ee.value=!0)}));const Ie=e=>{"number"==typeof e&&ye(de.value.goods.goods_image,e)},Le=t(null),Oe=t("");let ze={};const Ae=()=>{Oe.value="?id="+de.value.exchange_id,I.value&&I.value.member_id&&(Oe.value+="&mid="+I.value.member_id)},Be=()=>{ze.id=de.value.exchange_id,I.value&&I.value.member_id&&(ze.member_id=I.value.member_id),Le.value.openShare()};return N((()=>{try{J()}catch(e){}})),(e,t)=>{const a=S,l=j,b=k(w("u-swiper"),X),C=y,E=k(w("u-avatar"),$),I=k(w("u-icon"),q),L=k(w("u--image"),H),O=k(w("u-parse"),Y),z=V,A=F,B=k(w("u-popup"),Z),D=k(w("loading-page"),ee);return s(),r(l,{style:M(e.themeColor())},{default:o((()=>[Object.keys(de.value).length?(s(),r(l,{key:0,class:"bg-[var(--page-bg-color)] min-h-[100vh] relative"},{default:o((()=>[c(" 自定义头部 "),i(l,{class:_(["flex items-center fixed left-0 right-0 z-10 bg-transparent detail-head",{"!bg-[#fff]":Ee.value}]),style:M(u(Se))},{default:o((()=>[i(a,{class:"nc-iconfont nc-icon-zuoV6xx",style:M(u(Ce)),onClick:t[0]||(t[0]=e=>{K().length>1?Q({delta:1}):g({url:"/addon/shop/pages/index",mode:"reLaunch"})})},null,8,["style"]),i(l,{class:_(["ml-auto !pt-[12rpx] !pb-[8rpx] p-[10rpx] bg-[rgba(255,255,255,.4)] rounded-full border-[2rpx] border-solid border-transparent box-border nc-iconfont nc-icon-fenxiangV6xx font-bold text-[#303133] text-[36rpx]",{"border-[#d8d8d8]":Ee.value}]),onClick:Be},null,8,["class"])])),_:1},8,["class","style"]),i(l,{class:"swiper-box"},{default:o((()=>[i(b,{list:de.value.goods.goods_image,indicator:de.value.goods.goods_image.length,indicatorStyle:{bottom:"68rpx"},autoplay:!0,height:"100vw",onClick:Ie},null,8,["list","indicator"])])),_:1}),i(l,{class:"rounded-t-[40rpx] -mt-[40rpx] relative flex items-center justify-between !bg-cover box-border pb-[26rpx] h-[136rpx] px-[30rpx]",style:M({background:"url("+u(h)("addon/shop/detail/discount_price_bg.png")+") no-repeat"})},{default:o((()=>[i(l,{class:"flex items-baseline text-[#fff]"},{default:o((()=>[i(l,{class:"flex items-center"},{default:o((()=>[de.value.point?(s(),r(l,{key:0,class:"inline-block price-font"},{default:o((()=>[i(a,{class:"text-[44rpx]"},{default:o((()=>[d(p(de.value.point),1)])),_:1}),i(a,{class:"text-[38rpx]"},{default:o((()=>[d(p(u(T)("point")),1)])),_:1})])),_:1})):c("v-if",!0),de.value.point&&parseFloat(de.value.price)?(s(),r(a,{key:1,class:"text-[38rpx]"},{default:o((()=>[d("+")])),_:1})):c("v-if",!0),parseFloat(de.value.price)?(s(),r(l,{key:2,class:"inline-block price-font"},{default:o((()=>[i(a,{class:"text-[44rpx]"},{default:o((()=>[d(p(parseFloat(de.value.price).toFixed(2)),1)])),_:1}),i(a,{class:"text-[38rpx]"},{default:o((()=>[d(p(u(T)("priceUnit")),1)])),_:1})])),_:1})):c("v-if",!0)])),_:1})])),_:1})])),_:1},8,["style"]),i(l,{class:"bg-[var(--page-bg-color)] overflow-hidden rounded-[40rpx] -mt-[28rpx] relative"},{default:o((()=>[i(l,{class:"detail-title relative px-[30rpx] pt-[40rpx]"},{default:o((()=>[i(l,{class:"text-[#333] font-medium text-[30rpx] multi-hidden leading-[40rpx]"},{default:o((()=>[de.value.goods.goods_brand?(s(),r(l,{key:0,class:"brand-tag middle",style:M(u(v).baseTagStyle(de.value.goods.goods_brand))},{default:o((()=>[d(p(de.value.goods.goods_brand.brand_name),1)])),_:1},8,["style"])):c("v-if",!0),d(" "+p(de.value.goods.goods_name),1)])),_:1}),i(l,{class:"flex justify-between items-start mt-[24rpx]"},{default:o((()=>[de.value.market_price&&parseFloat(de.value.market_price)?(s(),r(l,{key:0,class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:o((()=>[i(a,{class:"whitespace-nowrap mr-[4rpx]"},{default:o((()=>[d("划线价:")])),_:1}),i(a,{class:"line-through"},{default:o((()=>[d("￥"+p(de.value.market_price),1)])),_:1})])),_:1})):c("v-if",!0),de.value.stock&&parseFloat(de.value.stock)?(s(),r(l,{key:1,class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:o((()=>[i(a,{class:"whitespace-nowrap mr-[4rpx]"},{default:o((()=>[d("库存:")])),_:1}),i(a,null,{default:o((()=>[d(p(de.value.stock),1)])),_:1}),i(a,null,{default:o((()=>[d(p(de.value.goods.unit),1)])),_:1})])),_:1})):c("v-if",!0),i(l,{class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)] flex items-baseline"},{default:o((()=>[i(a,{class:"whitespace-nowrap mr-[4rpx]"},{default:o((()=>[d("销量:")])),_:1}),i(a,{class:"mx-[2rpx]"},{default:o((()=>[d(p(de.value.goods.sale_num),1)])),_:1}),i(a,null,{default:o((()=>[d(p(de.value.goods.unit),1)])),_:1})])),_:1})])),_:1}),de.value.label_info&&de.value.label_info.length?(s(),r(l,{key:0,class:"flex flex-wrap mt-[16rpx]"},{default:o((()=>[(s(!0),x(f,null,m(de.value.label_info,(e=>(s(),x(f,{key:e.label_id},["icon"==e.style_type&&e.icon?(s(),r(C,{key:0,class:"img-tag middle",src:u(h)(e.icon),mode:"heightFix",onError:t=>u(v).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?c("v-if",!0):(s(),r(l,{key:1,class:"base-tag middle",style:M(u(v).baseTagStyle(e))},{default:o((()=>[d(p(e.label_name),1)])),_:2},1032,["style"]))],64)))),128))])),_:1})):c("v-if",!0)])),_:1}),u(ve)?(s(),r(l,{key:0,class:"mt-[24rpx] sidebar-margin card-template"},{default:o((()=>[de.value.service&&de.value.service.length?(s(),r(l,{key:0,onClick:t[1]||(t[1]=e=>xe.value=!xe.value),class:"card-template-item"},{default:o((()=>[i(a,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0"},{default:o((()=>[d("服务")])),_:1}),i(l,{class:"text-[#343434] text-[26rpx] leading-[30rpx] font-400 truncate ml-auto"},{default:o((()=>[d(p(de.value.service[0].service_name),1)])),_:1}),i(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):c("v-if",!0),de.value.goodsSpec&&de.value.goodsSpec.length?(s(),r(l,{key:1,onClick:ge,class:"card-template-item"},{default:o((()=>[i(a,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0 mr-[20rpx]"},{default:o((()=>[d("已选")])),_:1}),i(l,{class:"ml-auto text-right truncate flex-1 text-[#343434] text-[26rpx] leading-[30rpx] font-400"},{default:o((()=>[d(p(de.value.sku_spec_format),1)])),_:1}),i(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):c("v-if",!0),"real"==de.value.goods.goods_type&&de.value.delivery_type_list&&de.value.delivery_type_list.length?(s(),r(l,{key:2,class:"card-template-item",onClick:we},{default:o((()=>[i(a,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0"},{default:o((()=>[d("配送")])),_:1}),i(l,{class:"ml-auto flex items-center text-[#343434] text-[26rpx] leading-[30rpx] font-400"},{default:o((()=>[d(p(de.value.delivery_type_list[ke.value].name),1)])),_:1}),i(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):c("v-if",!0)])),_:1})):c("v-if",!0),i(l,{class:"mt-[var(--top-m)] sidebar-margin card-template"},{default:o((()=>[i(l,{class:_(["flex items-center justify-between min-h-[40rpx]",{"mb-[30rpx]":he.value&&he.value.list&&he.value.list.length}])},{default:o((()=>[i(a,{class:"title !mb-[0]"},{default:o((()=>[d("宝贝评价("+p(he.value.count)+")",1)])),_:1}),he.value.count?(s(),r(l,{key:0,class:"h-[40rpx] flex items-center",onClick:t[2]||(t[2]=e=>(de.value.goods_id,void g({url:"/addon/shop/pages/evaluate/list",param:{goods_id:de.value.goods_id}})))},{default:o((()=>[i(a,{class:"text-[24rpx] text-[var(--text-color-light9)]"},{default:o((()=>[d("查看全部")])),_:1}),i(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):c("v-if",!0),he.value.count?c("v-if",!0):(s(),r(a,{key:1,class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:o((()=>[d("暂无评价 ")])),_:1}))])),_:1},8,["class"]),i(l,null,{default:o((()=>[(s(!0),x(f,null,m(he.value.list,((e,t)=>(s(),r(l,{class:_({"pb-[34rpx]":t!=he.value.list.length-1}),key:t},{default:o((()=>[i(l,{class:"flex items-center w-full"},{default:o((()=>[i(E,{"default-url":u(h)("static/resource/images/default_headimg.png"),src:u(h)(e.member_head),size:"50rpx",leftIcon:"none"},null,8,["default-url","src"]),i(a,{class:"ml-[10rpx] text-[28rpx] text-[#333]"},{default:o((()=>[d(p(e.member_name),1)])),_:2},1024)])),_:2},1024),i(l,{class:"flex justify-between w-full mt-[16rpx]"},{default:o((()=>[i(l,{class:"flex-1 w-[540rpx] text-[26rpx] text-[#333] max-h-[72rpx] leading-[36rpx] multi-hidden mr-[50rpx]"},{default:o((()=>[d(p(e.content),1)])),_:2},1024),i(l,{class:"w-[80rpx] shrink-0"},{default:o((()=>[e.image_mid&&e.image_mid.length?(s(),r(L,{key:0,width:"80rpx",height:"80rpx",radius:"16rpx",src:u(h)(e.image_mid[0]),model:"aspectFill",onClick:t=>ye(e.images[0])},{error:o((()=>[i(I,{name:"photo",color:"#999",size:"50"})])),_:2},1032,["src","onClick"])):c("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1})])),_:1}),de.value.goods&&de.value.goods.attr_format&&Object.keys(de.value.goods.attr_format).length?(s(),r(l,{key:1,class:"my-[var(--top-m)] goods-sku sidebar-margin card-template"},{default:o((()=>[i(l,{class:"title mb-[30rpx]"},{default:o((()=>[d("商品属性")])),_:1}),i(l,null,{default:o((()=>[(s(!0),x(f,null,m(de.value.goods.attr_format,((e,t)=>(s(),x(f,{key:t},[t<4||pe.value?(s(),r(l,{key:0,class:"card-template-item"},{default:o((()=>[i(l,{class:"text-[26rpx] leading-[30rpx] w-[160rpx] font-400 shrink-0 text-[var(--text-color-light9)]"},{default:o((()=>[d(p(e.attr_value_name),1)])),_:2},1024),i(l,{class:"text-[#333] box-border value-wid text-[26rpx] leading-[30rpx] font-400 pl-[20rpx]"},{default:o((()=>[d(p(Array.isArray(e.attr_child_value_name)?e.attr_child_value_name.join(","):e.attr_child_value_name),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0)],64)))),128)),de.value.goods.attr_format.length>4?(s(),r(l,{key:0,class:"flex-center",onClick:t[3]||(t[3]=e=>pe.value=!pe.value)},{default:o((()=>[i(a,{class:"text-[24rpx] mr-[10rpx]"},{default:o((()=>[d(p(pe.value?"展开":"收起"),1)])),_:1}),i(a,{class:_(["nc-iconfont !text-[22rpx]",{"nc-icon-xiaV6xx":pe.value,"nc-icon-shangV6xx-1":!pe.value}])},null,8,["class"])])),_:1})):c("v-if",!0)])),_:1})])),_:1})):c("v-if",!0),i(l,{class:"my-[var(--top-m)] sidebar-margin card-template px-[var(--pad-sidebar-m)]"},{default:o((()=>[i(l,{class:"title"},{default:o((()=>[d("商品详情")])),_:1}),i(l,{class:"u-content"},{default:o((()=>[i(O,{content:de.value.goods.goods_desc,tagStyle:{img:"vertical-align: top;",p:"overflow: hidden;word-break:break-word;"}},null,8,["content"])])),_:1})])),_:1}),i(l,{class:"tab-bar-placeholder"}),i(l,{class:"border-[0] border-t-[2rpx] border-solid border-[#f5f5f5] w-[100%] flex justify-between pl-[32rpx] pr-[4rpx] bg-[#fff] box-border fixed left-0 bottom-0 tab-bar z-1 items-center"},{default:o((()=>[i(l,{class:"flex items-center"},{default:o((()=>[i(l,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:t[4]||(t[4]=e=>u(g)({url:"/addon/shop/pages/index",mode:"reLaunch"}))},{default:o((()=>[i(l,{class:"nc-iconfont nc-icon-shouyeV6xx text-[36rpx]"}),i(a,{class:"text-[20rpx] mt-[10rpx]"},{default:o((()=>[d("首页")])),_:1})])),_:1}),i(l,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:Be},{default:o((()=>[i(l,{class:"nc-iconfont nc-icon-fenxiangV6xx text-[36rpx]"}),i(a,{class:"text-[20rpx] mt-[10rpx]"},{default:o((()=>[d("分享")])),_:1})])),_:1})])),_:1}),1==de.value.goods.status?(s(),r(l,{key:0,class:"flex flex-1"},{default:o((()=>[u(_e)?(s(),r(z,{key:0,class:"primary-btn-bg flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border",onClick:t[5]||(t[5]=e=>ge("buy_now"))},{default:o((()=>[d("立即兑换")])),_:1})):(s(),r(z,{key:1,class:"flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border"},{default:o((()=>[d("已售罄")])),_:1}))])),_:1})):(s(),r(l,{key:1,class:"flex flex-1"},{default:o((()=>[i(z,{class:"w-[100%] !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 leading-[70rpx] rounded-full remove-border"},{default:o((()=>[d("该商品已下架")])),_:1})])),_:1}))])),_:1})])),_:1}),c(" 服务 "),i(l,{onTouchmove:t[8]||(t[8]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[i(B,{class:"popup-type",show:xe.value,onClose:t[7]||(t[7]=e=>xe.value=!1)},{default:o((()=>[i(l,{class:"popup-common min-h-[480rpx]",onTouchmove:t[6]||(t[6]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[i(l,{class:"title"},{default:o((()=>[d("商品服务")])),_:1}),i(A,{class:"h-[520rpx]","scroll-y":"true"},{default:o((()=>[i(l,{class:"pl-[22rpx] pb-[28rpx] pr-[37rpx]"},{default:o((()=>[(s(!0),x(f,null,m(de.value.service,((e,t)=>(s(),r(l,{class:"flex mb-[28rpx]"},{default:o((()=>[i(C,{class:"mt-[4rpx] w-[32rpx] h-[32rpx] mr-[14rpx]",src:u(h)(e.image||"addon/shop/icon_service.png"),mode:"aspectFit"},null,8,["src"]),i(l,{class:"flex-1"},{default:o((()=>[i(l,{class:"text-[30rpx] leading-[36rpx] text-[#333] mb-[8rpx]"},{default:o((()=>[d(p(e.service_name),1)])),_:2},1024),i(l,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)]"},{default:o((()=>[d(p(e.desc),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),c(" 配送 "),i(l,{onTouchmove:t[11]||(t[11]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[i(B,{class:"popup-type",show:fe.value,onClose:t[10]||(t[10]=e=>fe.value=!1)},{default:o((()=>[i(l,{class:"min-h-[360rpx] popup-common",onTouchmove:t[9]||(t[9]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[i(l,{class:"title"},{default:o((()=>[d("配送方式")])),_:1}),i(A,{class:"h-[520rpx]","scroll-y":"true"},{default:o((()=>[i(l,{class:"px-[var(--popup-sidebar-m)]"},{default:o((()=>[(s(!0),x(f,null,m(de.value.delivery_type_list,((e,t)=>(s(),r(l,{class:"flex mb-[40rpx]",onClick:a=>((e,t)=>{ke.value=t,fe.value=!1,uni.setStorageSync("distributionType",e.name)})(e,t)},{default:o((()=>[i(C,{class:"mt-[4rpx] w-[32rpx] h-[32rpx] mr-[14rpx]",src:u(h)("addon/shop/icon_service.png"),mode:"aspectFit"},null,8,["src"]),i(l,{class:"flex-1"},{default:o((()=>[i(l,{class:"text-[30rpx] leading-[36rpx] text-[#333] mb-[8rpx]"},{default:o((()=>[d(p(e.name),1)])),_:2},1024),i(l,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)]"},{default:o((()=>[d(p(e.desc),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),256))])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),i(ue,{ref_key:"goodsSkuRef",ref:ne,"goods-detail":de.value,onChange:me},null,8,["goods-detail"]),i(oe,{ref_key:"sharePosterRef",ref:Le,posterType:"shop_point_goods",posterParam:u(ze),copyUrlParam:Oe.value},null,8,["posterParam","copyUrlParam"])])),_:1})):c("v-if",!0),i(D,{loading:ce.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-c98066dd"]]);export{ne as default};
