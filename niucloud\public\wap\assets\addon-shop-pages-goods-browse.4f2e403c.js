import{d as e,r as t,p as s,o as r,c as l,w as a,b as o,t as c,x as d,e as i,y as n,z as p,F as u,g as f,$ as x,n as m,am as h,an as _,M as v,S as g,k as b,i as k,j as y,R as w,O as j,A as F,a as C,Q as E}from"./index-3caf046d.js";import{_ as z}from"./u--image.eb573bce.js";import{m as I,n as M}from"./goods.6a81cb49.js";import{s as S}from"./select-date.b993e54c.js";import{M as U}from"./mescroll-body.36f14dc3.js";import{M as R}from"./mescroll-empty.d02c7bd6.js";import{u as A}from"./useMescroll.26ccf5de.js";import{_ as D}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-popup.1b30ffa7.js";import"./u-safe-bottom.98e092c5.js";import"./mescroll-i18n.e7c22011.js";const O=D(e({__name:"browse",setup(e){const{mescrollInit:D,downCallback:O,getMescroll:V}=A(_,h),$=t(!1);let B=t(!1);const N=t(!1),P=t(0);let Q=t([]);const Y=t([]),Z=e=>{B.value=!1;let t={page:e.num,limit:e.size,date:Y.value};I(t).then((t=>{P.value=t.data.total;let s=t.data.data;1===Number(e.num)&&(Q.value=[]);const r=s.reduce(((e,t)=>{const s=t.browse_time.split(" ")[0];return e[s]||(e[s]=[]),e[s].push(t),e}),{}),l=Object.keys(r).map((e=>({date:e,list:r[e]})));l.forEach((e=>{e.checked=!1,e.list.forEach((e=>{e.checked=!1}))})),l.forEach((e=>{const t=Q.value.findIndex((t=>t.date===e.date));-1!==t?Q.value[t].list=[...Q.value[t].list,...e.list]:Q.value.push(e)})),e.endSuccess(s.length),B.value=!0})).catch((()=>{B.value=!0,e.endErr()}))},q=s((()=>{let e=0;return Q.value.forEach((t=>{t.list.forEach((t=>{t.checked&&(e+=1)}))})),e})),G=t(!1),H=()=>{const e=Q.value.every((e=>e.checked));G.value=!!e},J=()=>{G.value=!G.value,Q.value.forEach((e=>{e.checked=G.value,e.list.forEach((e=>{e.checked=G.value}))}))},K=()=>{if(!q.value)return void v({title:"还没有选择商品",icon:"none"});if(N.value)return;N.value=!0;const e=[];Q.value.forEach((t=>{t.list.forEach((t=>{t.checked&&e.push(t.goods_id)}))})),M({goods_ids:e}).then((e=>{N.value=!1,V().resetUpScroll()}))},L=()=>{if(N.value)return;N.value=!0;const e=[];Q.value.forEach((t=>{t.list.forEach((t=>{e.push(t.goods_id)}))})),M({goods_ids:e}).then((e=>{V().resetUpScroll(),N.value=!1}))},T=t(null),W=()=>{T.value.show=!0},X=e=>{Y.value=e,Q.value=[],V().resetUpScroll()};return(e,t)=>{const s=g,h=b,_=E,v=k(y("u--image"),z),I=w;return r(),l(h,{class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden",style:m(e.themeColor())},{default:a((()=>[o(h,{class:"fixed top-0 left-0 right-0 z-200"},{default:a((()=>[o(h,{class:"tab-style-1 py-[20rpx] bg-[#fff] border-0 border-solid border-b-[1rpx] border-[#f6f6f6]"},{default:a((()=>[o(h,{class:"tab-left text-[28rpx]"},{default:a((()=>[o(s,null,{default:a((()=>[c("共")])),_:1}),o(s,{class:"text-primary"},{default:a((()=>[c(d(P.value),1)])),_:1}),o(s,null,{default:a((()=>[c("条")])),_:1})])),_:1}),o(h,{class:"tab-right !items-center"},{default:a((()=>[o(h,{class:"flex items-center",onClick:W},{default:a((()=>[o(h,{class:"tab-right-date"},{default:a((()=>[c("日期")])),_:1}),o(h,{class:"nc-iconfont nc-icon-a-riliV6xx-36 tab-right-icon"})])),_:1}),o(h,{class:"w-[2rpx] h-[28rpx] mx-[20rpx] bg-gradient-to-b from-[#333] to-[#fff]"}),o(h,{onClick:t[0]||(t[0]=e=>$.value=!$.value),class:"text-[#333] text-[28rpx]"},{default:a((()=>[c(d($.value?"完成":"管理"),1)])),_:1})])),_:1})])),_:1})])),_:1}),o(U,{ref:"mescrollRef",top:"76",bottom:"168",onInit:i(D),down:{use:!1},onUp:Z},{default:a((()=>[i(Q).length?(r(),l(h,{key:0},{default:a((()=>[(r(!0),n(u,null,p(i(Q),((e,t)=>(r(),l(h,{class:"bg-[#fff] mb-[20rpx] pt-[30rpx] px-[20rpx]",key:t},{default:a((()=>[o(h,{class:"flex items-center h-[34rpx] mb-[20rpx]"},{default:a((()=>[$.value?(r(),l(h,{key:0,class:"self-center w-[58rpx] flex items-center",onClick:j((t=>{return(s=e).checked=!s.checked,s.list.forEach((e=>{e.checked=s.checked})),void H();var s}),["stop"])},{default:a((()=>[o(h,{class:"bg-[#fff] w-[34rpx] h-[34rpx] rounded-[17rpx] flex items-center justify-center"},{default:a((()=>[o(s,{class:x(["iconfont text-primary text-[34rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden shrink-0",{iconxuanze1:e.checked,"bg-[#F5F5F5]":!e.checked}])},null,8,["class"])])),_:2},1024)])),_:2},1032,["onClick"])):f("v-if",!0),o(h,{class:"text-[28rpx] font-500 text-[#333]"},{default:a((()=>[c(d(e.date),1)])),_:2},1024)])),_:2},1024),o(h,{class:"flex flex-wrap"},{default:a((()=>[(r(!0),n(u,null,p(e.list,((t,n)=>(r(),l(h,{class:x(["w-[230rpx] mb-[20rpx]",{"mr-[10rpx]":(n+1)%3}]),key:n,onClick:e=>{C({url:"/addon/shop/pages/goods/detail",param:{goods_id:t.goods_id}})}},{default:a((()=>[o(h,{class:"relative w-[230rpx] h-[230rpx] rounded-[var(--goods-rounded-mid)] overflow-hidden mb-[10rpx]"},{default:a((()=>[o(v,{width:"230rpx",height:"230rpx",radius:"var(--goods-rounded-mid)",src:i(F)(t.goods_cover_thumb_mid?t.goods_cover_thumb_mid:""),mode:"aspectFill"},{error:a((()=>[o(_,{class:"w-[230rpx] h-[230rpx] rounded-[var(--goods-rounded-mid)] overflow-hidden",src:i(F)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["radius","src"]),0==t.status?(r(),l(h,{key:0,class:"absolute left-0 top-0 w-[230rpx] h-[230rpx] leading-[230rpx] text-center",style:{"background-color":"rgba(0,0,0,0.3)"}},{default:a((()=>[o(s,{class:"text-[#fff] text-[28rpx]"},{default:a((()=>[c("已失效")])),_:1})])),_:1})):f("v-if",!0),$.value?(r(),l(h,{key:1,class:"absolute top-0 left-0 right-0 bottom-0 p-[10rpx] flex justify-end items-start z-100",onClick:j((s=>((e,t)=>{t.checked=!t.checked;const s=e.list.every((e=>e.checked));e.checked=!!s,H()})(e,t)),["stop"])},{default:a((()=>[o(h,{class:"bg-[#fff] w-[34rpx] h-[34rpx] rounded-[17rpx] flex items-center justify-center"},{default:a((()=>[o(s,{class:x(["iconfont text-primary text-[34rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden shrink-0",{iconxuanze1:t.checked,"bg-[#F5F5F5]":!t.checked}])},null,8,["class"])])),_:2},1024)])),_:2},1032,["onClick"])):f("v-if",!0)])),_:2},1024),o(h,{class:"text-[var(--price-text-color)] price-font"},{default:a((()=>[o(s,{class:"text-[24rpx] font-500"},{default:a((()=>[c("￥")])),_:1}),o(s,{class:"text-[40rpx] font-500"},{default:a((()=>[c(d(parseFloat(t.show_price).toFixed(2).split(".")[0]),1)])),_:2},1024),o(s,{class:"text-[24rpx] font-500"},{default:a((()=>[c("."+d(parseFloat(t.show_price).toFixed(2).split(".")[1]),1)])),_:2},1024),"member_price"==t.show_type?(r(),l(_,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:i(F)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==t.show_type?(r(),l(_,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:i(F)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==t.show_type?(r(),l(_,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:i(F)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):f("v-if",!0)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1024)))),128))])),_:1})):f("v-if",!0),!i(Q).length&&i(B)?(r(),l(R,{key:1,option:{tip:"暂无浏览的商品"}})):f("v-if",!0)])),_:1},8,["onInit"]),i(Q).length&&$.value?(r(),l(h,{key:0,class:"fixed left-0 right-0 bottom-0 z-200 bg-[#fff] pb-ios"},{default:a((()=>[i(q)?(r(),l(h,{key:0,class:"h-[66rpx] flex items-center justify-between pl-[30rpx] pr-[20rpx] border-0 border-b-[1rpx] border-solid border-[#f6f6f6]"},{default:a((()=>[o(h,{class:"text-[24rpx]"},{default:a((()=>[o(s,null,{default:a((()=>[c("已选")])),_:1}),o(s,{class:"text-primary"},{default:a((()=>[c(d(i(q)),1)])),_:1}),o(s,null,{default:a((()=>[c("件宝贝")])),_:1})])),_:1}),o(h,{class:"text-[24rpx] text-[#999]",onClick:L},{default:a((()=>[c("一键清空宝贝足迹")])),_:1})])),_:1})):f("v-if",!0),o(h,{class:"flex h-[100rpx] items-center justify-between pl-[30rpx] pr-[20rpx]"},{default:a((()=>[o(h,{class:"flex items-center",onClick:J},{default:a((()=>[o(s,{class:x(["self-center iconfont text-primary text-[34rpx] mr-[10rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden flex-shrink-0",{iconxuanze1:G.value,"bg-color":!G.value}])},null,8,["class"]),o(s,{class:"font-400 text-[#303133] text-[26rpx]"},{default:a((()=>[c("全选")])),_:1})])),_:1}),o(I,{class:"w-[180rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] m-0 rounded-full primary-btn-bg remove-border",onClick:K},{default:a((()=>[c("删除")])),_:1})])),_:1})])),_:1})):f("v-if",!0),f(" 时间选择 "),o(S,{ref_key:"selectDateRef",ref:T,onConfirm:X},null,512)])),_:1},8,["style"])}}}),[["__scopeId","data-v-f76a2f33"]]);export{O as default};
