import{d as a,h as e,l as t,r,m as l,p as o,q as s,s as c,o as u,c as n,w as i,g as b,b as p,t as f,x as d,y as v,F as h,z as m,e as k,k as y,i as x,j as g,n as _,A as T,B as C,C as j,a as w,D as A,E as B,G as E,H as O}from"./index-3caf046d.js";import{_ as P}from"./u-tabbar-item.31141540.js";import{_ as S}from"./u-tabbar.38f37e13.js";import{_ as $}from"./top-tabbar.f4fde406.js";import{_ as z}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-safe-bottom.98e092c5.js";import"./manifest.ed582bbb.js";const H=[],I=z(a({__name:"tabbar",setup(a){e();const z=t(),I=r(0),L=r({}),q=r({}),D=l(),F=o((()=>D.currTabbar.path)),G=s({}),U=o((()=>{if(D.currTabbar.path){let a=D.currTabbar.path.split("/"),e=a[0]?0:1;return"addon"==a[e]?a[e+1]:"app"}return""})),J=r({}),K=r(null),M=r([]),N=()=>{let a=A(C().tabbarList);if(1==a.length)Object.assign(G,a[0]);else{let t=!1;for(let e=0;e<a.length;e++)if(a[e].key==U.value){Object.assign(G,a[e]),t=!0;break}if(!t){let t=0,r={};try{a&&a.forEach((a=>{"app"==a.info.type&&(t++,r=a)})),1==t&&Object.assign(G,r)}catch(e){}}}},Q=()=>{if(D.currTabbar.path)if(M.value.includes(D.currTabbar.path)||M.value.push(D.currTabbar.path),J.value[D.currTabbar.path]&&J.value[D.currTabbar.path].$)if(J.value[D.currTabbar.path].$.root.onLoad.forEach((a=>{a(D.currTabbar.query||{})})),J.value[D.currTabbar.path].$.root.onShow.forEach((a=>{a()})),L.value[D.currTabbar.path]=q.value[D.currTabbar.path]||0,z[D.currTabbar.path])K.value=null;else{let a=D.currTabbar.path.replace("/app/","").replace("/addon/","").replaceAll("/",".");switch(B()){case"zh-Hans":O[a]&&(a=O[a]);break;case"en":E[a]&&(a=E[a])}K.value={data:{title:a,topStatusBar:{bgColor:"#ffffff",rollBgColor:"#ffffff",style:"style-1",textColor:"#333333",rollTextColor:"#333333",textAlign:"center",imgUrl:"",link:{name:""}}}}}else setTimeout((()=>{I.value++,Q()}),300)};c((()=>C().tabbarList),((a,e)=>{a&&N()}),{deep:!0,immediate:!0}),c((()=>U),((a,e)=>{a&&N()}),{deep:!0,immediate:!0});const R=a=>{const e=j(a);H.includes(e.path)?l().$patch((a=>{a.currTabbar=e})):w({url:a,mode:"navigateTo"})};return c((()=>D.currTabbar),((a,e)=>{Q()}),{deep:!0,immediate:!0}),(a,e)=>{const t=y,r=x(g("u-tabbar-item"),P),l=x(g("u-tabbar"),S);return u(),n(t,{class:"w-screen h-screen flex flex-col"},{default:i((()=>[K.value?(u(),n($,{key:0,data:K.value.data,"is-back":!1},null,8,["data"])):b("v-if",!0),p(t,{class:"hidden"},{default:i((()=>[f(d(I.value),1)])),_:1}),G&&Object.keys(G).length?(u(),v(h,{key:1},[p(l,{value:k(F),zIndex:"9999",fixed:!0,placeholder:!0,safeAreaInsetBottom:!0,"inactive-color":G.value.textColor,"active-color":G.value.textHoverColor},{default:i((()=>[(u(!0),v(h,null,m(G.value.list,(a=>(u(),v(h,null,[1==G.value.type?(u(),n(r,{key:0,class:"py-[5rpx]",style:_({"background-color":G.value.backgroundColor}),text:a.text,icon:k(T)(k(F)==a.link.url?a.iconSelectPath:a.iconPath),name:a.link,onClick:e=>R(a.link.url)},null,8,["style","text","icon","name","onClick"])):b("v-if",!0),2==G.value.type?(u(),n(r,{key:1,class:"py-[5rpx]",style:_({"background-color":G.value.backgroundColor}),icon:k(T)(k(F)==a.link.url?a.iconSelectPath:a.iconPath),name:a.link,onClick:e=>R(a.link.url)},null,8,["style","icon","name","onClick"])):b("v-if",!0),3==G.value.type?(u(),n(r,{key:2,class:"py-[5rpx]",style:_({"background-color":G.value.backgroundColor}),text:a.text,name:a.link.url,onClick:e=>R(a.link.url)},null,8,["style","text","name","onClick"])):b("v-if",!0)],64)))),256))])),_:1},8,["value","inactive-color","active-color"]),p(t,{class:"tab-bar-placeholder"})],64)):b("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-2172c28d"]]);export{I as default};
