import{at as e,d as a,r as l,p as t,s,J as o,_ as c,a2 as u,M as n,N as d,ar as i,o as r,c as v,w as _,g as f,b as p,t as m,e as g,x as y,$ as h,y as k,a_ as b,O as w,z as x,F as C,be as I,bP as z,S as D,k as A,Q as L,i as $,j as S,al as M,A as j}from"./index-3caf046d.js";import{_ as F}from"./u-icon.ba193921.js";import{a as H}from"./voucher.db23b7c0.js";import{j as P}from"./brand.3bc41d81.js";import{c as E}from"./recycle_order.a252d983.js";import{a as q,g as U}from"./quote.9b84c391.js";import{_ as V}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const R=V(a({__name:"detail",setup(a){const V=l({id:"",name:"",code:"",image:"",category_id:"",brand:null}),R=l({new:0,light:0,obvious:0}),B=l(!1),N=l({}),T=l(""),O=l("new"),J=l(0),Q=l(!1),G=l(-1),K=l(null),W=l([]),X=l(!1),Y=l("pickup"),Z=l(!1),ee=l(!1),ae=l(""),le=l("");l(!1);const te=l(null),se=l(!1),oe=l("尽快上门"),ce=l("today"),ue=l("today-urgent"),ne=l(""),de=l(""),ie=l(""),re=["顺丰速运","京东物流","德邦快递","中通快递","韵达速递","圆通速递","申通快递","极兔速递"],ve={"today-urgent":"尽快上门","today-afternoon":"今天 14:00-16:00","today-evening":"今天 16:00-18:00","tomorrow-morning":"明天 09:00-11:00","tomorrow-noon":"明天 11:00-13:00","tomorrow-afternoon":"明天 14:00-16:00","tomorrow-evening":"明天 16:00-18:00","dayafter-morning":"后天 09:00-11:00","dayafter-noon":"后天 11:00-13:00","dayafter-afternoon":"后天 14:00-16:00","dayafter-evening":"后天 16:00-18:00"},_e=l([]),fe=l(!1),pe=l(!1),me=l(!1),ge=l(null),ye=t((()=>R.value[O.value]||0)),he=t((()=>Array.isArray(W.value)&&W.value.some((e=>De(e,ye.value))))),ke=t((()=>Array.isArray(W.value)?W.value.filter((e=>De(e,ye.value))):[])),be=t((()=>K.value&&K.value.can_use&&K.value.discount_amount||0)),we=t((()=>(ye.value||0)+(be.value||0)));t((()=>V.value.image?j(V.value.image):""));const xe=e=>{O.value=e,G.value>=0?Ae():Ce()},Ce=()=>{if(!Array.isArray(W.value)||0===W.value.length)return;const e=W.value.filter((e=>De(e,ye.value)));if(e.length>0){const a=e.sort(((e,a)=>a.price-e.price))[0],l=W.value.findIndex((e=>e.id===a.id));l>=0&&(G.value=l,Ae())}},Ie=async e=>{if(e)try{B.value=!0;const a=(await P(parseInt(e))).data;if(!a)throw new Error("商品数据为空");V.value={id:a.id,name:a.name||"商品名称",code:a.code||"",image:a.image||"",category_id:a.category_id||"",brand:a.brand||null},console.log("商品信息已更新:",V.value),console.log("品牌信息:",a.brand),R.value={new:parseFloat(a.price_new||0),light:parseFloat(a.price_used||0),obvious:parseFloat(a.price_damaged||0)},console.log("商品信息加载完成，价格数据:",R.value),console.log("当前选中价格:",ye.value),await ze()}catch(a){console.error("加载商品信息失败:",a),n({title:"商品信息加载失败",icon:"error",duration:2e3}),n({title:"API调用失败，使用测试数据",icon:"none",duration:2e3})}finally{B.value=!1}},ze=async()=>{try{X.value=!0;const e=await H({status:1,product_price:ye.value,product_id:V.value.id});1===e.code&&(W.value=e.data.data||[],console.log("加载的优惠券列表:",W.value),console.log("当前价格:",ye.value),console.log("可用优惠券数量:",W.value.filter((e=>De(e,ye.value))).length),G.value<0&&Ce())}catch(e){console.error("获取加价券列表失败:",e),W.value=[]}finally{X.value=!1}},De=(e,a)=>{if(1!==e.status)return!1;return!(Math.floor(Date.now()/1e3)>parseInt(e.expire_time))&&!(a<e.min_condition_money)},Ae=async()=>{if(G.value<0||!W.value[G.value])return void(K.value=null);const e=W.value[G.value],a=De(e,ye.value);K.value=a?{can_use:!0,discount_amount:e.price,final_price:ye.value+e.price,voucher_info:e,message:"可以使用"}:{can_use:!1,discount_amount:0,final_price:ye.value,message:"不满足使用条件"}},Le=async()=>{if(console.log("开始获取回收标准，当前商品信息:",V.value),!V.value.category_id){if(console.log("没有分类ID，无法获取回收标准。商品信息:",V.value),!V.value.id)return console.log("商品ID也为空，无法获取商品信息"),void n({title:"商品信息缺失",icon:"none"});if(console.log("尝试重新加载商品信息以获取分类ID..."),await Ie(V.value.id),!V.value.category_id)return console.log("重新加载后仍然没有分类ID"),void n({title:"商品分类信息缺失",icon:"none"})}fe.value=!0;try{console.log(`正在获取分类ID ${V.value.category_id} 的回收标准...`);const l=await(a=V.value.category_id,e.get(`yz_she/recycle_standard/${a}`));console.log("回收标准API响应:",l),l&&l.data&&Array.isArray(l.data)?(_e.value=l.data,console.log("获取到的回收标准:",_e.value),console.log("获取到回收标准数据，共",_e.value.length,"项"),0===_e.value.length&&console.log("该分类暂无回收标准")):l&&Array.isArray(l)?(_e.value=l,console.log("获取到的回收标准:",_e.value)):(console.error("获取回收标准失败: 数据格式错误",l),_e.value=[],n({title:"数据格式错误",icon:"none"}))}catch(l){console.error("获取回收标准异常:",l),_e.value=[],n({title:"网络错误，请重试",icon:"none"})}var a;fe.value=!1},$e=async()=>{if(console.log("点击显示回收标准弹窗"),!V.value.id)return console.log("没有商品信息，无法显示回收标准"),void n({title:"请先选择商品",icon:"none"});ee.value=!0,await Le()},Se=()=>{ee.value=!1},Me=()=>{ee.value=!1,console.log("确认回收标准")},je=()=>{pe.value=!0},Fe=()=>{pe.value=!1},He=()=>{if(console.log("从detail页面跳转到拍照估价"),console.log("当前商品信息:",V.value),!V.value.id)return void n({title:"商品信息缺失",icon:"none"});let e=null;V.value.brand&&(e=V.value.brand),console.log("使用品牌信息:",e);const a={from:"detail",productId:V.value.id,productName:V.value.name||"",productImage:V.value.image||"",categoryId:V.value.category_id||"",brandId:e.id,brandName:e.name||"",brandLogo:e.logo||""};console.log("存储跳转数据:",a),uni.setStorageSync("photoEvaluateData",a),I({url:"/addon/yz_she/pages/evaluate/photo?from=detail"})},Pe=e=>e&&e.image?j(e.image):"",Ee=()=>{const e=T.value?`/addon/yz_she/pages/evaluate/detail?id=${T.value}`:"/addon/yz_she/pages/evaluate/detail";uni.setStorage({key:"selectAddressCallback",data:{back:e,productId:T.value}}),I({url:"/addon/yz_she/pages/address/index"})},qe=()=>{te.value?se.value=!0:n({title:"请先选择取件地址",icon:"none",duration:2e3})},Ue=()=>{se.value=!1},Ve=e=>{ce.value=e},Re=e=>{ue.value=e;const a=ve[e]||e;oe.value=a,se.value=!1,console.log("选择时间:",a)},Be=e=>{Y.value=e,console.log("选择配送方式:",e)},Ne=async()=>{var e,a;if(O.value&&ye.value)try{const l={category_id:V.value.category_id||0,brand_id:(null==(e=V.value.brand)?void 0:e.id)||0,product_id:V.value.id||0,product_name:V.value.name,product_code:V.value.code,product_image:V.value.image,quote_price:parseFloat(we.value),condition_type:O.value,voucher_id:(null==(a=K.value)?void 0:a.id)||0,voucher_amount:parseFloat(be.value)||0,note:`成色：${Oe(O.value)}`},t=await q(l);if(1!==t.code)throw new Error(t.msg||"添加失败");n({title:"已添加到购物车",icon:"success"}),await Te(),setTimeout((()=>{I({url:"/addon/yz_she/pages/order/quote-list?status=3"})}),1500)}catch(l){console.error("添加到购物车失败:",l),n({title:l.message||"添加失败",icon:"none"})}else n({title:"请先选择商品成色",icon:"none"})},Te=async()=>{try{const e=await U();1===e.code&&(J.value=e.data.count||0)}catch(e){console.error("获取购物车数量失败:",e),J.value=0}},Oe=e=>{switch(e){case"new":return"全新未穿着";case"light":return"轻微穿着";case"obvious":return"明显穿着";default:return"未知成色"}},Je=l(!1),Qe=l("🎫"),Ge=l(""),Ke=l(""),We=e=>{e?(Qe.value="✓",Ge.value="已使用加价券",Ke.value="恭喜！您已成功使用加价券，回收价格已提升。请注意加价券有效期，及时完成交易。"):(Qe.value="!",Ge.value="未使用加价券",Ke.value='您当前未使用加价券，可以点击"选择加价券"来提升回收价格，获得更多收益。'),Je.value=!0},Xe=()=>{Je.value=!1},Ye=async()=>{await ze(),Q.value=!0},Ze=()=>{Q.value=!1},ea=()=>{W.value.length>0&&G.value>=0?(W.value[G.value],setTimeout((()=>{We(!0)}),300)):W.value.length>0&&(G.value=-1,K.value=null,setTimeout((()=>{We(!1)}),300)),Q.value=!1},aa=()=>{Z.value=!0},la=()=>{Z.value=!1},ta=async()=>{if(console.log("开始提交回收订单"),!me.value)try{if(!sa())return;me.value=!0;const e=oa();console.log("订单数据:",e);const a=await E(e);if(1!==a.code)throw new Error(a.msg||"订单提交失败");n({title:"订单提交成功",icon:"success",duration:2e3}),setTimeout((()=>{z({url:`/addon/yz_she/pages/order/success/detail-success?recycleOrderId=${a.data.id}`})}),1e3)}catch(e){console.error("提交订单失败:",e),n({title:e.message||"提交失败，请重试",icon:"none",duration:3e3})}finally{me.value=!1}},sa=()=>{if(!V.value.id)return n({title:"商品信息缺失",icon:"none"}),!1;if(!ye.value||ye.value<=0)return n({title:"请选择商品价格",icon:"none"}),!1;if("pickup"===Y.value){if(!te.value)return n({title:"请选择取件地址",icon:"none"}),!1;if(!oe.value)return n({title:"请选择上门时间",icon:"none"}),!1}if("self"===Y.value){if(!ae.value)return n({title:"请选择快递公司",icon:"none"}),!1;if(!le.value||!le.value.trim())return n({title:"请输入快递单号",icon:"none"}),!1}return!0},oa=()=>{var e,a,l;const t=ye.value||0,s=be.value||0,o=we.value||t,c={category_id:parseInt(V.value.category_id)||0,brand_id:(null==(e=V.value.brand)?void 0:e.id)||null,product_id:parseInt(V.value.id)||null,product_name:V.value.name||"",product_code:V.value.code||"",product_image:V.value.image||"",expected_price:t,voucher_amount:s,final_price:o,total_amount:o,express_fee:0,source_type:2,delivery_type:"pickup"===Y.value?1:2,quantity:1,voucher_id:(null==(l=null==(a=K.value)?void 0:a.voucher_info)?void 0:l.id)||null,quote_order_id:null};return"pickup"===Y.value&&te.value&&(c.pickup_address_id=te.value.id,c.pickup_contact_name=te.value.name,c.pickup_contact_phone=te.value.mobile,c.pickup_address_detail=te.value.full_address||te.value.address_detail||"",c.pickup_time=ca(),console.log("快递上门地址信息:",{address_id:te.value.id,contact_name:te.value.name,contact_phone:te.value.mobile,address_detail:c.pickup_address_detail,pickup_time:c.pickup_time})),"self"===Y.value&&(c.pickup_contact_name="放心星仓库",c.pickup_contact_phone="13060000687",c.admin_note=`用户自寄，快递公司：${ae.value}，快递单号：${le.value}`,c.express_company=ae.value,c.express_number=le.value,c.status=2,console.log("自行寄出订单信息:",{express_company:c.express_company,express_number:c.express_number,status:c.status})),console.log("构建的订单数据:",{pickup_address_detail:c.pickup_address_detail,voucher_id:c.voucher_id,selectedVoucherInfo:K.value,selectedAddress:te.value}),c},ca=()=>ue.value&&ve[ue.value]?ve[ue.value]:oe.value||"尽快上门";return s(ye,(()=>{G.value=-1,K.value=null,Q.value&&ze()})),o((e=>{console.log("=== onLoad获取到的参数 ===",e),N.value=e||{}})),c((async()=>{console.log("=== 开始获取页面参数 ==="),await Te();let e="";try{const a=window.location.href;console.log("当前完整URL:",a),console.log("window.location.search:",window.location.search),console.log("window.location.hash:",window.location.hash);let l=null;if(window.location.search){l=new URLSearchParams(window.location.search).get("id"),console.log("从search解析的ID:",l)}if(!l&&window.location.hash){const e=window.location.hash.split("?")[1];if(e){l=new URLSearchParams(e).get("id"),console.log("从hash解析的ID:",l)}}if(!l){const e=a.match(/[?&]id=([^&]+)/);e&&(l=e[1],console.log("手动解析的ID:",l))}l&&(e=l,console.log("从URL直接解析获取到商品ID:",e))}catch(a){console.log("URL直接解析失败:",a)}if(!e)try{const a=u(),l=a[a.length-1].options||{};console.log("getCurrentPages获取的参数:",l),l.id&&(e=l.id,console.log("从getCurrentPages获取到商品ID:",e))}catch(a){console.log("getCurrentPages获取失败:",a)}if(!e&&N.value.id&&(e=N.value.id,console.log("从pageParams获取到商品ID:",e)),N.value.quote_order_id&&(ge.value=parseInt(N.value.quote_order_id),console.log("获取到估价订单ID:",ge.value)),!e){const a=uni.getStorageSync("currentProductId");a&&(e=a,console.log("从本地存储获取到商品ID:",e))}if(e){if(!e){const a=window.location.href.match(/[?&]id=(\d+)/);a&&a[1]&&(e=a[1],console.log("通用URL解析获取到商品ID:",e))}console.log("=== 最终使用的商品ID:",e,"==="),T.value=e,uni.setStorageSync("currentProductId",e),await Ie(e),(()=>{const e=new Date,a=new Date(e);a.setDate(e.getDate()+1);const l=new Date(e);l.setDate(e.getDate()+2),ne.value=`${e.getMonth()+1}月${e.getDate()}日`,de.value=`${a.getMonth()+1}月${a.getDate()}日`,ie.value=`${l.getMonth()+1}月${l.getDate()}日`})()}else n({title:"缺少商品ID参数",icon:"error",duration:2e3})})),d((()=>{const e=uni.getStorageSync("selectAddressCallback");e&&e.address_id&&(e.productId&&(T.value=e.productId,V.value.id||Ie(e.productId)),e.address_info?te.value=e.address_info:te.value=null,uni.removeStorage({key:"selectAddressCallback"})),!V.value.id&&T.value&&Ie(T.value)})),i((()=>{})),(e,a)=>{const l=D,t=A,s=L,o=$(S("u-icon"),F),c=M;return r(),v(t,{class:"evaluate-detail-page"},{default:_((()=>[f(" 顶部提示栏和商品卡片组合 "),p(t,{class:"top-card-container"},{default:_((()=>[f(" 顶部提示栏 "),p(t,{class:"top-notice"},{default:_((()=>[p(l,{class:"notice-text"},{default:_((()=>[m("极速回收 质检完成后秒到账")])),_:1}),p(l,{class:"notice-right"},{default:_((()=>[m("极速专属")])),_:1})])),_:1}),f(" 商品信息卡片 "),p(t,{class:"product-card"},{default:_((()=>[p(t,{class:"product-info"},{default:_((()=>[p(t,{class:"product-image"},{default:_((()=>[V.value.image?(r(),v(s,{key:0,src:g(j)(V.value.image),class:"product-img",mode:"aspectFill"},null,8,["src"])):(r(),v(t,{key:1,class:"image-placeholder"},{default:_((()=>[p(l,{class:"placeholder-text"},{default:_((()=>[m(y(B.value?"加载中...":"商品图片"),1)])),_:1})])),_:1}))])),_:1}),p(t,{class:"product-details"},{default:_((()=>[p(l,{class:"product-name"},{default:_((()=>[m(y(V.value.name||(B.value?"加载中...":"商品名称")),1)])),_:1}),V.value.code?(r(),v(l,{key:0,class:"product-code"},{default:_((()=>[m(y(V.value.code),1)])),_:1})):f("v-if",!0)])),_:1})])),_:1}),f(" 价格选项 "),p(t,{class:"price-options-container"},{default:_((()=>[p(t,{class:"price-options-header"},{default:_((()=>[p(l,{class:"options-title"},{default:_((()=>[m("选择成色")])),_:1}),p(t,{class:"condition-explain-btn",onClick:je},{default:_((()=>[p(l,{class:"explain-icon"},{default:_((()=>[m("?")])),_:1}),p(l,{class:"explain-text"},{default:_((()=>[m("成色说明")])),_:1})])),_:1})])),_:1}),p(t,{class:"price-options"},{default:_((()=>[p(t,{class:h(["price-option",{active:"new"===O.value,disabled:B.value}]),onClick:a[0]||(a[0]=e=>!B.value&&xe("new"))},{default:_((()=>[p(l,{class:"option-label"},{default:_((()=>[m("全新未穿着")])),_:1}),B.value?(r(),v(l,{key:1,class:"loading-text"},{default:_((()=>[m("加载中...")])),_:1})):(r(),v(t,{key:0,class:"price-display"},{default:_((()=>["new"===O.value&&g(be)>0?(r(),v(t,{key:0,class:"voucher-price"},{default:_((()=>[p(l,{class:"voucher-label"},{default:_((()=>[m("券后")])),_:1}),p(l,{class:"voucher-amount"},{default:_((()=>[m("¥"+y(g(we)),1)])),_:1})])),_:1})):(r(),v(l,{key:1,class:"normal-price"},{default:_((()=>[m("¥"+y(R.value.new),1)])),_:1}))])),_:1}))])),_:1},8,["class"]),p(t,{class:h(["price-option",{active:"light"===O.value,disabled:B.value}]),onClick:a[1]||(a[1]=e=>!B.value&&xe("light"))},{default:_((()=>[p(l,{class:"option-label"},{default:_((()=>[m("轻微穿着")])),_:1}),B.value?(r(),v(l,{key:1,class:"loading-text"},{default:_((()=>[m("加载中...")])),_:1})):(r(),v(t,{key:0,class:"price-display"},{default:_((()=>["light"===O.value&&g(be)>0?(r(),v(t,{key:0,class:"voucher-price"},{default:_((()=>[p(l,{class:"voucher-label"},{default:_((()=>[m("券后")])),_:1}),p(l,{class:"voucher-amount"},{default:_((()=>[m("¥"+y(g(we)),1)])),_:1})])),_:1})):(r(),v(l,{key:1,class:"normal-price"},{default:_((()=>[m("¥"+y(R.value.light),1)])),_:1}))])),_:1}))])),_:1},8,["class"]),p(t,{class:h(["price-option",{active:"obvious"===O.value,disabled:B.value}]),onClick:a[2]||(a[2]=e=>!B.value&&xe("obvious"))},{default:_((()=>[p(l,{class:"option-label"},{default:_((()=>[m("明显穿着")])),_:1}),B.value?(r(),v(l,{key:1,class:"loading-text"},{default:_((()=>[m("加载中...")])),_:1})):(r(),v(t,{key:0,class:"price-display"},{default:_((()=>["obvious"===O.value&&g(be)>0?(r(),v(t,{key:0,class:"voucher-price"},{default:_((()=>[p(l,{class:"voucher-label"},{default:_((()=>[m("券后")])),_:1}),p(l,{class:"voucher-amount"},{default:_((()=>[m("¥"+y(g(we)),1)])),_:1})])),_:1})):(r(),v(l,{key:1,class:"normal-price"},{default:_((()=>[m("¥"+y(R.value.obvious),1)])),_:1}))])),_:1}))])),_:1},8,["class"])])),_:1})])),_:1}),f(" 价格明细 "),p(t,{class:"price-summary"},{default:_((()=>[p(l,{class:"summary-text"},{default:_((()=>[m("评估金额：¥"+y(g(ye)),1)])),_:1}),g(be)>0?(r(),v(t,{key:0,class:"voucher-addition"},{default:_((()=>{var e,a;return[p(l,{class:"plus-symbol"},{default:_((()=>[m("+")])),_:1}),p(l,{class:"bonus-text"},{default:_((()=>[m("加价券：¥"+y(g(be)),1)])),_:1}),(null==(a=null==(e=K.value)?void 0:e.voucher_info)?void 0:a.is_limited)?(r(),v(t,{key:0,class:"time-limit"},{default:_((()=>[p(l,{class:"limit-text"},{default:_((()=>[m("限时")])),_:1})])),_:1})):f("v-if",!0)]})),_:1})):f("v-if",!0)])),_:1}),f(" 优惠券选择 "),g(he)||W.value.length>0?(r(),v(t,{key:0,class:"voucher-select-btn",onClick:Ye},{default:_((()=>[p(t,{class:"btn-content"},{default:_((()=>[g(be)>0?(r(),v(l,{key:0,class:"btn-title"},{default:_((()=>[m("已选择加价券")])),_:1})):g(ke).length>0?(r(),v(l,{key:1,class:"btn-title"},{default:_((()=>[m("选择加价券")])),_:1})):(r(),v(l,{key:2,class:"btn-title"},{default:_((()=>[m("查看加价券")])),_:1})),g(be)>0?(r(),v(l,{key:3,class:"btn-desc"},{default:_((()=>{var e,a,l,t;return[m(y((null==(a=null==(e=K.value)?void 0:e.voucher_info)?void 0:a.title)||(null==(t=null==(l=K.value)?void 0:l.voucher_info)?void 0:t.name)),1)]})),_:1})):g(ke).length>0?(r(),v(l,{key:4,class:"btn-desc"},{default:_((()=>[m(y(g(ke).length)+"张可用，最高可加价¥"+y(Math.max(...g(ke).map((e=>e.price)))),1)])),_:1})):(r(),v(l,{key:5,class:"btn-desc"},{default:_((()=>[m("暂无可用券")])),_:1}))])),_:1}),p(l,{class:"btn-arrow"},{default:_((()=>[m(">")])),_:1})])),_:1})):f("v-if",!0),f(" 警告提示 "),p(t,{class:"warning-notice"},{default:_((()=>[p(t,{class:"warning-icon"},{default:_((()=>[m("⚠")])),_:1}),p(l,{class:"warning-text"},{default:_((()=>[m("请以最终质检回收价格为准，如质检金额大于等于询价金额，将自动确认交易并打款")])),_:1})])),_:1})])),_:1})])),_:1}),p(t,{class:"content-area"},{default:_((()=>[f(" 三步换钱 "),p(t,{class:"process-section"},{default:_((()=>[p(l,{class:"section-title"},{default:_((()=>[m("三步换钱")])),_:1}),p(t,{class:"process-steps"},{default:_((()=>[p(t,{class:"step-item"},{default:_((()=>[p(t,{class:"step-icon"},{default:_((()=>[m("📦")])),_:1}),p(l,{class:"step-text"},{default:_((()=>[m(y("pickup"===Y.value?"包邮寄出":"自行寄出"),1)])),_:1})])),_:1}),p(t,{class:"step-arrow"},{default:_((()=>[m(">")])),_:1}),p(t,{class:"step-item"},{default:_((()=>[p(t,{class:"step-icon"},{default:_((()=>[m("📋")])),_:1}),p(l,{class:"step-text"},{default:_((()=>[m("专业质检估价")])),_:1})])),_:1}),p(t,{class:"step-arrow"},{default:_((()=>[m(">")])),_:1}),p(t,{class:"step-item"},{default:_((()=>[p(t,{class:"step-icon"},{default:_((()=>[m("💰")])),_:1}),p(l,{class:"step-text"},{default:_((()=>[m("确认回收秒到账")])),_:1})])),_:1})])),_:1})])),_:1}),f(" 配送方式 "),p(t,{class:"delivery-section"},{default:_((()=>[p(t,{class:"delivery-options"},{default:_((()=>[p(t,{class:h(["delivery-option",{active:"pickup"===Y.value}]),onClick:a[3]||(a[3]=e=>Be("pickup"))},{default:_((()=>[p(t,{class:"option-content"},{default:_((()=>[p(l,{class:"option-name"},{default:_((()=>[m("快递上门")])),_:1}),p(t,{class:"option-tag"},{default:_((()=>[p(l,{class:"tag-text"},{default:_((()=>[m("免费")])),_:1})])),_:1})])),_:1})])),_:1},8,["class"]),p(t,{class:h(["delivery-option",{active:"self"===Y.value}]),onClick:a[4]||(a[4]=e=>Be("self"))},{default:_((()=>[p(l,{class:"option-name"},{default:_((()=>[m("自行寄出")])),_:1})])),_:1},8,["class"])])),_:1}),f(" 快递上门内容 "),"pickup"===Y.value?(r(),v(t,{key:0,class:"pickup-content"},{default:_((()=>[f(" 地址选择 "),p(t,{class:"address-item",onClick:Ee},{default:_((()=>[p(t,{class:"address-icon"},{default:_((()=>[p(o,{name:"map",color:"#333",size:"18"})])),_:1}),p(t,{class:"address-content"},{default:_((()=>[te.value?(r(),v(t,{key:1,class:"selected-address"},{default:_((()=>[p(l,{class:"address-name"},{default:_((()=>[m(y(te.value.name)+" "+y(te.value.mobile),1)])),_:1}),p(l,{class:"address-detail"},{default:_((()=>[m(y(te.value.full_address),1)])),_:1})])),_:1})):(r(),v(l,{key:0,class:"address-text"},{default:_((()=>[m("请选择取件地址")])),_:1}))])),_:1}),p(t,{class:"divider-line"}),p(l,{class:"address-action"},{default:_((()=>[m("地址簿")])),_:1})])),_:1}),f(" 预约时间 "),p(t,{class:h(["time-item",{disabled:!te.value}]),onClick:qe},{default:_((()=>[p(t,{class:"time-icon"},{default:_((()=>[(r(),k("svg",{viewBox:"0 0 1024 1024",width:"32",height:"32"},[b("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m176.5 585.7l-28.6 39c-4.4 6.1-11.9 9.3-19.4 9.3-4.7 0-9.5-1.4-13.6-4.3L512 609.7l-114.9 84c-9.9 7.2-23.8 5.1-31-4.9l-28.6-39c-7.2-9.9-5.1-23.8 4.9-31L457 515.3V264c0-11 9-20 20-20h70c11 0 20 9 20 20v251.3l114.6 103.5c9.9 7.1 12 21 4.9 30.9z",fill:te.value?"#333":"#ccc"},null,8,["fill"])]))])),_:1}),p(l,{class:"time-text"},{default:_((()=>[m("期望上门时间")])),_:1}),p(l,{class:h(["time-action",{disabled:!te.value}])},{default:_((()=>[m(y(te.value?oe.value||"尽快上门":"请先选择地址")+" > ",1)])),_:1},8,["class"])])),_:1},8,["class"])])),_:1})):f("v-if",!0),f(" 自行寄出内容 "),"self"===Y.value?(r(),v(t,{key:1,class:"self-content"},{default:_((()=>[f(" 收货地址 "),p(t,{class:"address-item"},{default:_((()=>[p(t,{class:"address-icon orange-bg"},{default:_((()=>[p(l,{class:"address-text-icon"},{default:_((()=>[m("收")])),_:1})])),_:1}),p(t,{class:"address-info"},{default:_((()=>[p(l,{class:"address-name"},{default:_((()=>[m("放心星仓库 13060000687")])),_:1}),p(l,{class:"address-detail"},{default:_((()=>[m("四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递")])),_:1})])),_:1}),p(l,{class:"copy-btn"},{default:_((()=>[m("复制")])),_:1})])),_:1}),f(" 快递公司 "),p(t,{class:"express-item",onClick:aa},{default:_((()=>[p(t,{class:"express-icon"},{default:_((()=>[p(o,{name:"car",color:"#333",size:"32"})])),_:1}),p(l,{class:"express-text"},{default:_((()=>[m("快递公司")])),_:1}),p(l,{class:"express-action"},{default:_((()=>[m(y(ae.value||"请选择快递公司")+" >",1)])),_:1})])),_:1}),f(" 快递单号 "),p(t,{class:"tracking-item"},{default:_((()=>[p(t,{class:"tracking-icon"},{default:_((()=>[p(o,{name:"order",color:"#333",size:"32"})])),_:1}),p(l,{class:"tracking-text"},{default:_((()=>[m("快递单号")])),_:1}),p(c,{class:"tracking-input",modelValue:le.value,"onUpdate:modelValue":a[5]||(a[5]=e=>le.value=e),placeholder:"请输入快递单号",maxlength:"30"},null,8,["modelValue"])])),_:1})])),_:1})):f("v-if",!0)])),_:1})])),_:1}),f(" 底部操作 "),p(t,{class:"bottom-action"},{default:_((()=>[p(t,{class:"cart-section"},{default:_((()=>[p(t,{class:"cart-icon",onClick:Ne},{default:_((()=>[p(t,{class:"cart-badge"},{default:_((()=>[m(y(J.value),1)])),_:1}),(r(),k("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24"},[b("path",{d:"M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12.1 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 0 0-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-17.4-28-34.6-28H96.5a35.3 35.3 0 1 0 0 70.6h125.9L246 312.8l58.1 281.3-74.8 122.1a34.96 34.96 0 0 0-3 36.8c6.1 11.9 18.4 19.4 31.5 19.4h62.8a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 0 0-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6z",fill:"currentColor"})])),p(l,{class:"cart-text"},{default:_((()=>[m("稍后发货")])),_:1})])),_:1})])),_:1}),p(t,{class:h(["submit-button",{submitting:me.value}]),onClick:ta},{default:_((()=>[me.value?(r(),v(l,{key:1,class:"submit-text"},{default:_((()=>[m("提交中...")])),_:1})):(r(),v(l,{key:0,class:"submit-text"},{default:_((()=>[m("确认回收")])),_:1}))])),_:1},8,["class"])])),_:1}),f(" 加价券使用提示弹窗 "),Je.value?(r(),v(t,{key:0,class:"voucher-tip-modal",onClick:Xe},{default:_((()=>[p(t,{class:"tip-modal-content",onClick:a[6]||(a[6]=w((()=>{}),["stop"]))},{default:_((()=>[p(t,{class:"tip-content"},{default:_((()=>[p(t,{class:"tip-icon"},{default:_((()=>[p(l,{class:"icon-text"},{default:_((()=>[m(y(Qe.value),1)])),_:1})])),_:1}),p(l,{class:"tip-title"},{default:_((()=>[m(y(Ge.value),1)])),_:1}),p(l,{class:"tip-message"},{default:_((()=>[m(y(Ke.value),1)])),_:1})])),_:1}),p(t,{class:"tip-actions"},{default:_((()=>[p(t,{class:"tip-btn",onClick:Xe},{default:_((()=>[p(l,{class:"btn-text"},{default:_((()=>[m("知道了")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):f("v-if",!0),f(" 加价券弹窗 "),Q.value?(r(),v(t,{key:1,class:"coupon-modal",onClick:Ze},{default:_((()=>[p(t,{class:"coupon-modal-content",onClick:a[7]||(a[7]=w((()=>{}),["stop"]))},{default:_((()=>[p(t,{class:"coupon-header"},{default:_((()=>[p(l,{class:"coupon-title"},{default:_((()=>[m("我的加价券")])),_:1}),p(t,{class:"coupon-close-btn",onClick:Ze},{default:_((()=>[(r(),k("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24"},[b("path",{d:"M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3.1-3.6-7.6-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3.1 3.6 7.6 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z",fill:"currentColor"})]))])),_:1})])),_:1}),f(" 加价券列表 "),p(t,{class:"coupon-container"},{default:_((()=>[f(" 加载状态 "),X.value?(r(),v(t,{key:0,class:"loading-state"},{default:_((()=>[p(l,{class:"loading-text"},{default:_((()=>[m("加载中...")])),_:1})])),_:1})):W.value.length>0?(r(),v(t,{key:1,class:"coupon-list"},{default:_((()=>[(r(!0),k(C,null,x(W.value,((e,a)=>(r(),v(t,{class:h(["coupon-card",{selected:G.value===a,disabled:!De(e,g(ye))}]),key:e.id,onClick:l=>De(e,g(ye))?(e=>{G.value=G.value===e?-1:e,Ae()})(a):null},{default:_((()=>[p(t,{class:"coupon-left-section"},{default:_((()=>[p(t,{class:"coupon-amount"},{default:_((()=>[m("¥"+y(e.price),1)])),_:2},1024),p(t,{class:"coupon-condition"},{default:_((()=>[m("满"+y(e.min_condition_money)+"可用",1)])),_:2},1024)])),_:2},1024),p(t,{class:"coupon-right-section"},{default:_((()=>[p(t,{class:"coupon-name"},{default:_((()=>[m(y(e.title),1)])),_:2},1024),p(t,{class:"coupon-desc"},{default:_((()=>[m(y(e.type_name),1)])),_:2},1024),De(e,g(ye))?(r(),v(t,{key:0,class:"coupon-benefit"},{default:_((()=>[p(l,{class:"benefit-text"},{default:_((()=>[m("使用后可得：¥"+y(g(ye)+e.price),1)])),_:2},1024)])),_:2},1024)):f("v-if",!0),p(t,{class:"coupon-time"},{default:_((()=>[m("有效期至 "+y(e.expire_time_text||e.expire_time),1)])),_:2},1024),De(e,g(ye))?f("v-if",!0):(r(),v(t,{key:1,class:"coupon-disabled-reason"},{default:_((()=>[g(ye)<e.min_condition_money?(r(),v(l,{key:0},{default:_((()=>[m(" 需满"+y(e.min_condition_money)+"元 ",1)])),_:2},1024)):(r(),v(l,{key:1},{default:_((()=>[m("不可用")])),_:1}))])),_:2},1024))])),_:2},1024),G.value===a?(r(),v(t,{key:0,class:"coupon-select-icon"},{default:_((()=>[(r(),k("svg",{viewBox:"0 0 1024 1024",width:"20",height:"20"},[b("path",{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z",fill:"currentColor"})]))])),_:1})):f("v-if",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})):(r(),k(C,{key:2},[f(" 无加价券状态 "),p(t,{class:"empty-coupon"},{default:_((()=>[p(t,{class:"empty-icon"},{default:_((()=>[(r(),k("svg",{viewBox:"0 0 1024 1024",width:"80",height:"80"},[b("path",{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zM224 896V128h576v768H224z m128-640h320v64H352v-64z m0 128h320v64H352v-64z m0 128h320v64H352v-64z m0 128h160v64H352v-64z",fill:"#ddd"})]))])),_:1}),p(l,{class:"empty-text"},{default:_((()=>[m("暂无加价券")])),_:1}),p(l,{class:"empty-desc"},{default:_((()=>[m("完成更多回收任务可获得加价券奖励")])),_:1})])),_:1})],2112))])),_:1}),f(" 底部按钮 "),p(t,{class:"coupon-footer"},{default:_((()=>[G.value>=0?(r(),v(t,{key:0,class:"selected-info"},{default:_((()=>[p(l,{class:"selected-text"},{default:_((()=>{var e;return[m("已选择："+y(null==(e=W.value[G.value])?void 0:e.title),1)]})),_:1}),p(l,{class:"selected-benefit"},{default:_((()=>{var e;return[m("+¥"+y(null==(e=W.value[G.value])?void 0:e.price),1)]})),_:1})])),_:1})):f("v-if",!0),p(t,{class:"coupon-confirm-btn",onClick:ea},{default:_((()=>[p(l,{class:"coupon-confirm-text"},{default:_((()=>[m(y(G.value>=0?"确认使用":W.value.length>0?"不使用优惠券":"我知道了"),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):f("v-if",!0),f(" 快递公司选择弹窗 "),Z.value?(r(),v(t,{key:2,class:"express-modal",onClick:la},{default:_((()=>[p(t,{class:"modal-content",onClick:a[8]||(a[8]=w((()=>{}),["stop"]))},{default:_((()=>[p(t,{class:"modal-header"},{default:_((()=>[p(l,{class:"modal-title"},{default:_((()=>[m("选择快递公司")])),_:1}),p(t,{class:"close-btn",onClick:la},{default:_((()=>[m("×")])),_:1})])),_:1}),p(t,{class:"express-list"},{default:_((()=>[(r(),k(C,null,x(re,(e=>p(t,{class:"express-option",key:e,onClick:a=>(e=>{ae.value=e,Z.value=!1,n({title:`已选择${e}`,icon:"success"})})(e)},{default:_((()=>[p(l,{class:"express-name"},{default:_((()=>[m(y(e),1)])),_:2},1024),ae.value===e?(r(),v(t,{key:0,class:"express-check"},{default:_((()=>[m("✓")])),_:1})):f("v-if",!0)])),_:2},1032,["onClick"]))),64))])),_:1})])),_:1})])),_:1})):f("v-if",!0),f(" 右侧固定回收标准按钮 "),p(t,{class:"fixed-standard-btn",onClick:$e},{default:_((()=>[p(l,{class:"fixed-standard-text"},{default:_((()=>[m("回")])),_:1}),p(l,{class:"fixed-standard-text"},{default:_((()=>[m("收")])),_:1}),p(l,{class:"fixed-standard-text"},{default:_((()=>[m("标")])),_:1}),p(l,{class:"fixed-standard-text"},{default:_((()=>[m("准")])),_:1})])),_:1}),f(" 回收标准弹窗 "),ee.value?(r(),v(t,{key:3,class:"standard-modal",onClick:Se},{default:_((()=>[p(t,{class:"standard-modal-content",onClick:a[9]||(a[9]=w((()=>{}),["stop"]))},{default:_((()=>[p(t,{class:"standard-header"},{default:_((()=>[p(l,{class:"standard-title"},{default:_((()=>[m("回收标准")])),_:1}),p(t,{class:"close-btn",onClick:Se},{default:_((()=>[m("×")])),_:1})])),_:1}),p(l,{class:"standard-subtitle"},{default:_((()=>[m("以下情况请使用拍照估价进行精准价回收")])),_:1}),f(" 加载状态 "),fe.value?(r(),v(t,{key:0,class:"standards-loading"},{default:_((()=>[p(l,{class:"loading-text"},{default:_((()=>[m("正在加载回收标准...")])),_:1})])),_:1})):_e.value.length>0?(r(),k(C,{key:1},[f(" 回收标准列表 "),p(t,{class:"standard-grid"},{default:_((()=>[(r(!0),k(C,null,x(_e.value,(e=>(r(),v(t,{class:"standard-item",key:e.id},{default:_((()=>[p(t,{class:"standard-image"},{default:_((()=>[e.image&&!e.imageError?(r(),v(s,{key:0,src:Pe(e),mode:"aspectFill",class:"standard-img",onError:()=>(e=>{console.error("回收标准图片加载失败:",e.title),e.imageError=!0})(e),onLoad:()=>(e=>{e.imageError&&(e.imageError=!1)})(e)},null,8,["src","onError","onLoad"])):(r(),v(t,{key:1,class:"standard-placeholder"},{default:_((()=>[p(l,{class:"placeholder-text"},{default:_((()=>[m(y(e.image?"图片加载失败":"暂无图片"),1)])),_:2},1024)])),_:2},1024))])),_:2},1024),p(l,{class:"standard-label"},{default:_((()=>[m(y(e.title),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})],2112)):(r(),k(C,{key:2},[f(" 无数据状态 "),p(t,{class:"no-standards"},{default:_((()=>[p(l,{class:"no-standards-text"},{default:_((()=>[m("该分类暂无回收标准")])),_:1})])),_:1})],2112)),p(t,{class:"standard-buttons"},{default:_((()=>[p(t,{class:"photo-estimate-btn",onClick:He},{default:_((()=>[p(l,{class:"photo-estimate-text"},{default:_((()=>[m("拍照估价")])),_:1})])),_:1}),p(t,{class:"standard-confirm-btn",onClick:Me},{default:_((()=>[p(l,{class:"confirm-text"},{default:_((()=>[m("我知道了")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):f("v-if",!0),f(" 时间选择弹窗 "),se.value?(r(),v(t,{key:4,class:"time-modal",onClick:Ue},{default:_((()=>[p(t,{class:"time-modal-content",onClick:a[24]||(a[24]=w((()=>{}),["stop"]))},{default:_((()=>[p(t,{class:"time-header"},{default:_((()=>[p(l,{class:"time-title"},{default:_((()=>[m("期望上门时间")])),_:1}),p(t,{class:"close-btn",onClick:Ue},{default:_((()=>[m("×")])),_:1})])),_:1}),f(" 左右分栏布局 "),p(t,{class:"time-container"},{default:_((()=>[f(" 左侧日期选择 "),p(t,{class:"date-sidebar"},{default:_((()=>[p(t,{class:h(["date-item",{active:"today"===ce.value}]),onClick:a[10]||(a[10]=e=>Ve("today"))},{default:_((()=>[p(l,{class:"date-name"},{default:_((()=>[m("今天")])),_:1}),p(l,{class:"date-desc"},{default:_((()=>[m(y(ne.value),1)])),_:1})])),_:1},8,["class"]),p(t,{class:h(["date-item",{active:"tomorrow"===ce.value}]),onClick:a[11]||(a[11]=e=>Ve("tomorrow"))},{default:_((()=>[p(l,{class:"date-name"},{default:_((()=>[m("明天")])),_:1}),p(l,{class:"date-desc"},{default:_((()=>[m(y(de.value),1)])),_:1})])),_:1},8,["class"]),p(t,{class:h(["date-item",{active:"dayafter"===ce.value}]),onClick:a[12]||(a[12]=e=>Ve("dayafter"))},{default:_((()=>[p(l,{class:"date-name"},{default:_((()=>[m("后天")])),_:1}),p(l,{class:"date-desc"},{default:_((()=>[m(y(ie.value),1)])),_:1})])),_:1},8,["class"])])),_:1}),f(" 右侧时间选择 "),p(t,{class:"time-content"},{default:_((()=>[f(" 今天的时间选项 "),"today"===ce.value?(r(),v(t,{key:0,class:"time-slots"},{default:_((()=>[p(t,{class:h(["time-slot urgent",{active:"today-urgent"===ue.value}]),onClick:a[13]||(a[13]=e=>Re("today-urgent"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("尽快上门")])),_:1}),p(l,{class:"slot-desc"},{default:_((()=>[m("工作日2小时内")])),_:1})])),_:1},8,["class"]),p(t,{class:h(["time-slot",{active:"today-afternoon"===ue.value}]),onClick:a[14]||(a[14]=e=>Re("today-afternoon"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("14:00-16:00")])),_:1})])),_:1},8,["class"]),p(t,{class:h(["time-slot",{active:"today-evening"===ue.value}]),onClick:a[15]||(a[15]=e=>Re("today-evening"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("16:00-18:00")])),_:1})])),_:1},8,["class"])])),_:1})):f("v-if",!0),f(" 明天的时间选项 "),"tomorrow"===ce.value?(r(),v(t,{key:1,class:"time-slots"},{default:_((()=>[p(t,{class:h(["time-slot",{active:"tomorrow-morning"===ue.value}]),onClick:a[16]||(a[16]=e=>Re("tomorrow-morning"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("09:00-11:00")])),_:1})])),_:1},8,["class"]),p(t,{class:h(["time-slot",{active:"tomorrow-noon"===ue.value}]),onClick:a[17]||(a[17]=e=>Re("tomorrow-noon"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("11:00-13:00")])),_:1})])),_:1},8,["class"]),p(t,{class:h(["time-slot",{active:"tomorrow-afternoon"===ue.value}]),onClick:a[18]||(a[18]=e=>Re("tomorrow-afternoon"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("14:00-16:00")])),_:1})])),_:1},8,["class"]),p(t,{class:h(["time-slot",{active:"tomorrow-evening"===ue.value}]),onClick:a[19]||(a[19]=e=>Re("tomorrow-evening"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("16:00-18:00")])),_:1})])),_:1},8,["class"])])),_:1})):f("v-if",!0),f(" 后天的时间选项 "),"dayafter"===ce.value?(r(),v(t,{key:2,class:"time-slots"},{default:_((()=>[p(t,{class:h(["time-slot",{active:"dayafter-morning"===ue.value}]),onClick:a[20]||(a[20]=e=>Re("dayafter-morning"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("09:00-11:00")])),_:1})])),_:1},8,["class"]),p(t,{class:h(["time-slot",{active:"dayafter-noon"===ue.value}]),onClick:a[21]||(a[21]=e=>Re("dayafter-noon"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("11:00-13:00")])),_:1})])),_:1},8,["class"]),p(t,{class:h(["time-slot",{active:"dayafter-afternoon"===ue.value}]),onClick:a[22]||(a[22]=e=>Re("dayafter-afternoon"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("14:00-16:00")])),_:1})])),_:1},8,["class"]),p(t,{class:h(["time-slot",{active:"dayafter-evening"===ue.value}]),onClick:a[23]||(a[23]=e=>Re("dayafter-evening"))},{default:_((()=>[p(l,{class:"slot-title"},{default:_((()=>[m("16:00-18:00")])),_:1})])),_:1},8,["class"])])),_:1})):f("v-if",!0)])),_:1})])),_:1})])),_:1})])),_:1})):f("v-if",!0),f(" 成色说明弹窗 "),pe.value?(r(),v(t,{key:5,class:"condition-modal",onClick:Fe},{default:_((()=>[p(t,{class:"condition-modal-content",onClick:a[25]||(a[25]=w((()=>{}),["stop"]))},{default:_((()=>[p(t,{class:"condition-header"},{default:_((()=>[p(l,{class:"condition-title"},{default:_((()=>[m("成色说明")])),_:1}),p(t,{class:"close-btn",onClick:Fe},{default:_((()=>[m("×")])),_:1})])),_:1}),p(t,{class:"condition-content"},{default:_((()=>[p(t,{class:"condition-item"},{default:_((()=>[p(t,{class:"condition-item-header"},{default:_((()=>[p(t,{class:"condition-badge new"},{default:_((()=>[m("未使用")])),_:1})])),_:1}),p(t,{class:"condition-item-body"},{default:_((()=>[p(t,{class:"condition-description"},{default:_((()=>[p(l,{class:"condition-text"},{default:_((()=>[m("整体品相极佳，仅存在放置痕迹且无严重做工瑕疵")])),_:1})])),_:1}),p(t,{class:"condition-images"},{default:_((()=>[p(s,{class:"condition-img",src:g(j)("addon/yz_she/cs/1.png"),mode:"aspectFill"},null,8,["src"])])),_:1})])),_:1})])),_:1}),p(t,{class:"condition-item"},{default:_((()=>[p(t,{class:"condition-item-header"},{default:_((()=>[p(t,{class:"condition-badge light"},{default:_((()=>[m("轻微使用")])),_:1})])),_:1}),p(t,{class:"condition-item-body"},{default:_((()=>[p(t,{class:"condition-description"},{default:_((()=>[p(l,{class:"condition-text"},{default:_((()=>[m("整体品相佳，仅试穿或缺少重要零部件(吊牌等)、做工瑕疵少")])),_:1})])),_:1}),p(t,{class:"condition-images"},{default:_((()=>[p(s,{class:"condition-img",src:g(j)("addon/yz_she/cs/2.png"),mode:"aspectFill"},null,8,["src"])])),_:1})])),_:1})])),_:1}),p(t,{class:"condition-item"},{default:_((()=>[p(t,{class:"condition-item-header"},{default:_((()=>[p(t,{class:"condition-badge obvious"},{default:_((()=>[m("明显使用")])),_:1})])),_:1}),p(t,{class:"condition-item-body"},{default:_((()=>[p(t,{class:"condition-description"},{default:_((()=>[p(l,{class:"condition-text"},{default:_((()=>[m("存在轻微磨损/氧化/褶皱等瑕疵或水洗/护理，但无严重瑕疵(如严重破损/变形/氧化等)")])),_:1})])),_:1}),p(t,{class:"condition-images"},{default:_((()=>[p(s,{class:"condition-img",src:g(j)("addon/yz_she/cs/3.png"),mode:"aspectFill"},null,8,["src"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):f("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-a2b90570"]]);export{R as default};
