import{d as e,r as a,I as l,p as o,q as t,_ as r,P as s,o as i,c as n,w as u,b as d,t as p,x as m,e as c,O as g,g as b,$ as x,M as f,Y as _,a1 as v,U as h,k as y,al as k,i as S,j,S as w,R as V,a as C,B as A}from"./index-3caf046d.js";import{_ as B,a as O}from"./u-form.49dbb57f.js";import{_ as P}from"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import{_ as T,a as I}from"./u-checkbox-group.0328273c.js";import{_ as U}from"./u-popup.1b30ffa7.js";import{_ as M}from"./_plugin-vue_export-helper.1b428a4d.js";const q=M(e({__name:"bind-mobile",setup(e,{expose:M}){const q=a(!1),z=l(),E=o((()=>z.info)),L=o((()=>A().login)),R=a(!1),$=a(!1),J=t({mobile:"",mobile_code:"",mobile_key:""}),Y=a(!0);r((()=>{setTimeout((()=>{Y.value=!1}),800),uni.getStorageSync("pid")&&Object.assign(J,{pid:uni.getStorageSync("pid")}),uni.getStorageSync("openid")&&Object.assign(J,{openid:uni.getStorageSync("openid")}),uni.getStorageSync("unionid")&&Object.assign(J,{unionid:uni.getStorageSync("unionid")}),uni.getStorageSync("nickname")&&Object.assign(J,{nickname:uni.getStorageSync("nickname")}),uni.getStorageSync("avatar")&&Object.assign(J,{headimg:uni.getStorageSync("avatar")})}));const D={mobile:[{type:"string",required:!0,message:s("mobilePlaceholder"),trigger:["blur","change"]},{validator(e,a,l){uni.$u.test.mobile(a)?l():l(new Error("请输入正确的手机号"))},message:s("mobileError"),trigger:["change","blur"]}],mobile_code:{type:"string",required:!0,message:s("codePlaceholder"),trigger:["blur","change"]}},F=()=>{$.value=!$.value},G=a(null),H=()=>{G.value.validate().then((()=>{if(!$.value&&L.value.agreement_show)return void f({title:s("isAgreeTips"),icon:"none"});if(R.value)return;R.value=!0;(E.value?_:v)(J).then((e=>{E.value?(z.getMemberInfo(),E.value.mobile&&uni.removeStorageSync("isBindMobile")):(z.setToken(e.data.token),h().handleLoginBack()),q.value=!1})).catch((()=>{R.value=!1}))}))};return M({open:()=>{q.value=!0}}),(e,a)=>{const l=y,o=k,t=S(j("u-form-item"),B),r=S(j("sms-code"),P),f=S(j("u-checkbox"),T),_=S(j("u-checkbox-group"),I),v=w,h=V,A=S(j("u-form"),O),M=S(j("u-popup"),U);return i(),n(M,{show:q.value,onClose:a[7]||(a[7]=e=>q.value=!1),mode:"center",round:10,closeable:!0,safeAreaInsetBottom:!1,zIndex:"10086"},{default:u((()=>[d(l,{onTouchmove:a[6]||(a[6]=g((()=>{}),["prevent","stop"])),class:"max-w-[600rpx] w-[600rpx] box-border"},{default:u((()=>[d(l,{class:"text-center py-[var(--pad-top-m)] text-[32rpx] font-500 leading-[46rpx]"},{default:u((()=>[p(m(c(s)("bindMobile")),1)])),_:1}),d(l,{class:"px-[var(--pad-sidebar-m)] pb-[var(--pad-top-m)]"},{default:u((()=>[d(A,{labelPosition:"left",model:J,errorType:"toast",rules:D,ref_key:"formRef",ref:G},{default:u((()=>[d(t,{label:"",prop:"mobile",borderBottom:!0},{default:u((()=>[d(o,{modelValue:J.mobile,"onUpdate:modelValue":a[0]||(a[0]=e=>J.mobile=e),type:"number",maxlength:"11",placeholder:c(s)("mobilePlaceholder"),class:"w-full h-[50rpx] leading-[50rpx] !bg-transparent !px-[20rpx] text-[26rpx] text-[#333]",disabled:Y.value,"placeholder-class":"bind-mobile"},null,8,["modelValue","placeholder","disabled"])])),_:1}),d(l,{class:"mt-[20rpx]"},{default:u((()=>[d(t,{label:"",prop:"mobile_code",borderBottom:!0},{right:u((()=>[c(L).agreement_show?(i(),n(r,{key:0,mobile:J.mobile,type:"login",modelValue:J.mobile_key,"onUpdate:modelValue":a[2]||(a[2]=e=>J.mobile_key=e),isAgree:$.value},null,8,["mobile","modelValue","isAgree"])):(i(),n(r,{key:1,mobile:J.mobile,type:"login",modelValue:J.mobile_key,"onUpdate:modelValue":a[3]||(a[3]=e=>J.mobile_key=e)},null,8,["mobile","modelValue"]))])),default:u((()=>[d(o,{modelValue:J.mobile_code,"onUpdate:modelValue":a[1]||(a[1]=e=>J.mobile_code=e),type:"number",maxlength:"6",placeholder:c(s)("codePlaceholder"),class:"box-border w-full h-[50rpx] leading-[50rpx] !bg-transparent !px-[20rpx] text-[26rpx] text-[#333]",disabled:Y.value,"placeholder-class":"bind-mobile"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),c(L).agreement_show?(i(),n(l,{key:0,class:"flex items-center mt-[30rpx] pl-[10rpx] py-[10rpx]",onClick:g(F,["stop"])},{default:u((()=>[d(_,{onChange:F},{default:u((()=>[d(f,{activeColor:"var(--primary-color)",checked:$.value,shape:"circle",size:"28rpx"},null,8,["checked"])])),_:1}),d(l,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:u((()=>[d(v,null,{default:u((()=>[p(m(c(s)("agreeTips")),1)])),_:1}),d(v,{onClick:a[4]||(a[4]=g((e=>c(C)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:u((()=>[p("《"+m(c(s)("privacyAgreement"))+"》",1)])),_:1}),d(v,null,{default:u((()=>[p(m(c(s)("and")),1)])),_:1}),d(v,{onClick:a[5]||(a[5]=g((e=>c(C)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:u((()=>[p("《"+m(c(s)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])):b("v-if",!0),d(l,{class:"mt-[100rpx]"},{default:u((()=>[d(h,{class:x(["primary-btn-bg text-[26rpx] !text-[#fff] !h-[80rpx] leading-[80rpx] rounded-full font-500",{"opacity-50":R.value}]),disabled:R.value,onClick:H},{default:u((()=>[p(m(c(s)("bind")),1)])),_:1},8,["class","disabled"])])),_:1})])),_:1},8,["model"])])),_:1})])),_:1})])),_:1},8,["show"])}}}),[["__scopeId","data-v-6fde1d0f"]]);export{q as b};
