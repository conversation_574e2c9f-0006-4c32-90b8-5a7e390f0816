import{at as t}from"./index-3caf046d.js";function r(r){return t.post("yz_she/quote/order",r)}function e(r){const e={};return r.order_no&&r.order_no.trim()&&(e.order_no=r.order_no.trim()),void 0!==r.status&&null!==r.status&&""!==r.status&&(e.status=Number(r.status)),r.category_id&&r.category_id>0&&(e.category_id=Number(r.category_id)),r.brand_id&&r.brand_id>0&&(e.brand_id=Number(r.brand_id)),r.page&&r.page>0&&(e.page=Number(r.page)),r.limit&&r.limit>0&&(e.limit=Number(r.limit)),t.get("yz_she/quote/order/list",e)}function o(r){return t.get(`yz_she/quote/order/${r}`)}function s(r){return t.post(`yz_she/quote/order/${r}/confirm`)}function n(r,e){return t.post(`yz_she/quote/order/${r}/cancel`,{reason:e})}function u(r,e){return t.post(`yz_she/quote/order/${r}/status`,{status:e})}function a(r){return t.post("yz_she/quote/order/batch/detail",{order_ids:r})}function i(r){return t.post("yz_she/quote/order/batch/ship",r)}function d(){return t.get("yz_she/quote/cart/count")}function c(r){return t.post("yz_she/quote/cart/add",r)}export{c as a,n as b,r as c,o as d,e,s as f,d as g,a as h,i,u};
