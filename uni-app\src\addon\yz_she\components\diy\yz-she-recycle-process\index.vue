<template>
    <view :style="warpCss">
        <view class="px-[30rpx] py-[30rpx]">
            <!-- 标题栏 -->
            <view class="flex items-center justify-between mb-[40rpx]">
                <view class="flex items-center">
                    <view class="font-600" :style="{ fontSize: diyComponent.titleSize * 2 + 'rpx', color: diyComponent.titleColor }">{{ diyComponent.title }}</view>
                </view>
                <view v-if="diyComponent.showMore && diyComponent.moreText" class="flex items-center" @click="handleMoreClick">
                    <text class="text-[26rpx]" :style="{ color: diyComponent.moreColor }">{{ diyComponent.moreText }}</text>
                    <text class="nc-iconfont nc-icon-youV6xx text-[26rpx] ml-[8rpx]" :style="{ color: diyComponent.moreColor }"></text>
                </view>
            </view>

            <!-- 流程步骤 -->
            <view class="flex items-start justify-between relative">
                <view v-for="(step, index) in diyComponent.steps" :key="index" class="flex flex-col items-center flex-1 relative">
                    <!-- 步骤图标 -->
                    <view class="w-[100rpx] h-[100rpx] mb-[24rpx] flex items-center justify-center relative z-10">
                        <!-- 自定义图标 -->
                        <image v-if="step.icon" class="w-[80rpx] h-[80rpx]" :src="img(step.icon)" mode="aspectFit" />
                        <!-- 默认图标 -->
                        <view v-else class="w-[80rpx] h-[80rpx] bg-[#F8F9FA] rounded-full flex items-center justify-center border-[2rpx] border-[#E9ECEF]">
                            <text class="nc-iconfont text-[40rpx] text-[#6C757D] font-600" :class="step.defaultIcon"></text>
                        </view>
                    </view>

                    <!-- 步骤文字 -->
                    <view class="text-center font-500" :style="{ fontSize: (diyComponent.stepTextSize + 2) * 2 + 'rpx', color: diyComponent.stepTextColor }">{{ step.title }}</view>

                    <!-- 箭头 -->
                    <view v-if="index < diyComponent.steps.length - 1" class="absolute top-[50rpx] left-[calc(100%-20rpx)] w-[40rpx] h-[40rpx] flex items-center justify-center z-20">
                        <text class="nc-iconfont nc-icon-youV6xx text-[38rpx] text-[#999999] font-700"></text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import useDiyStore from '@/app/stores/diy';
import { img } from '@/utils/common';

const props = defineProps(['component', 'index']);
const diyStore = useDiyStore();

const diyComponent = computed(() => {
    if (diyStore.mode == 'decorate') {
        return diyStore.value[props.index];
    } else {
        return props.component;
    }
})

const warpCss = computed(() => {
    var style = '';
    if (diyComponent.value.componentStartBgColor) {
        if (diyComponent.value.componentStartBgColor && diyComponent.value.componentEndBgColor) style += `background:linear-gradient(${ diyComponent.value.componentGradientAngle },${ diyComponent.value.componentStartBgColor },${ diyComponent.value.componentEndBgColor });`;
        else style += 'background-color:' + diyComponent.value.componentStartBgColor + ';';
    }
    if (diyComponent.value.componentBgUrl) {
        style += 'background-image:url(' + img(diyComponent.value.componentBgUrl) + ');';
        style += 'background-size: 100% 100%;';
        style += 'background-repeat: no-repeat;';
    }

    if (diyComponent.value.topRounded) style += 'border-top-left-radius:' + diyComponent.value.topRounded * 2 + 'rpx;';
    if (diyComponent.value.topRounded) style += 'border-top-right-radius:' + diyComponent.value.topRounded * 2 + 'rpx;';
    if (diyComponent.value.bottomRounded) style += 'border-bottom-left-radius:' + diyComponent.value.bottomRounded * 2 + 'rpx;';
    if (diyComponent.value.bottomRounded) style += 'border-bottom-right-radius:' + diyComponent.value.bottomRounded * 2 + 'rpx;';
    return style;
})

const handleMoreClick = () => {
    if (diyComponent.value.moreLink && diyComponent.value.moreLink.name) {
        diyStore.toRedirect(diyComponent.value.moreLink)
    }
}
</script>

<style>
</style>
