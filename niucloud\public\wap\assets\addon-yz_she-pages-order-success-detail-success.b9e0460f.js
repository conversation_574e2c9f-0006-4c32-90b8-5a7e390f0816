import{d as e,r as a,J as s,M as c,A as t,o as l,c as o,w as r,g as d,b as u,t as i,x as n,be as _,Q as p,S as m,k as f,i as v,j as g}from"./index-3caf046d.js";import{_ as b}from"./u-icon.ba193921.js";import{d as x}from"./recycle_order.a252d983.js";import{_ as y}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const k=y(e({__name:"detail-success",setup(e){const y=a({}),k=a({name:"",code:"",image:"",price:"0.00",evaluatePrice:"0.00",bonus:"0"}),h=a({orderNo:"",status:"",createTime:""}),w=a({});s((e=>{console.log("成功页面接收到的参数:",e),y.value=e||{},e.recycleOrderId?j(parseInt(e.recycleOrderId)):c({title:"订单信息缺失",icon:"none"})}));const j=async e=>{var a,s;try{console.log("加载回收订单详情:",e);const c=await x(e);if(1!==c.code)throw new Error(c.msg||"获取回收订单详情失败");w.value=c.data,console.log("回收订单详情:",c.data),h.value={orderNo:c.data.order_no||"",status:c.data.status_text||"",createTime:c.data.create_time_text||""},k.value={name:c.data.product_name||"",code:c.data.product_code||"",image:c.data.product_image?t(c.data.product_image):"",price:(c.data.expected_price+(c.data.voucher_amount||0)).toFixed(2),evaluatePrice:(null==(a=c.data.expected_price)?void 0:a.toFixed(2))||"0.00",bonus:(null==(s=c.data.voucher_amount)?void 0:s.toFixed(2))||"0"}}catch(l){console.error("加载订单详情失败:",l),c({title:l.message||"加载订单信息失败",icon:"none"})}},F=()=>{w.value.id?_({url:`/addon/yz_she/pages/recycle_order/detail?id=${w.value.id}`}):c({title:"订单信息缺失",icon:"none"})};return(e,a)=>{const s=p,c=m,t=f,_=v(g("u-icon"),b);return l(),o(t,{class:"recycle-success-page"},{default:r((()=>[d(" 商品信息卡片 "),u(t,{class:"product-card"},{default:r((()=>[u(t,{class:"product-info"},{default:r((()=>[u(s,{src:k.value.image||"/static/images/default-product.png",class:"product-image",mode:"aspectFit",onError:e.onImageError},null,8,["src","onError"]),u(t,{class:"product-details"},{default:r((()=>[u(c,{class:"product-name"},{default:r((()=>[i(n(k.value.name),1)])),_:1}),u(c,{class:"product-code"},{default:r((()=>[i(n(k.value.code),1)])),_:1})])),_:1}),u(s,{src:"/wap/static/images/gucci-logo.png",class:"brand-logo",mode:"aspectFit"})])),_:1}),u(t,{class:"price-section"},{default:r((()=>[u(t,{class:"price-info"},{default:r((()=>[u(c,{class:"price-label"},{default:r((()=>[i("预估回收")])),_:1}),u(t,{class:"price-amount"},{default:r((()=>[u(c,{class:"currency"},{default:r((()=>[i("¥")])),_:1}),u(c,{class:"price-value"},{default:r((()=>[i(n(k.value.price),1)])),_:1})])),_:1})])),_:1}),u(t,{class:"price-breakdown"},{default:r((()=>[u(c,{class:"breakdown-item"},{default:r((()=>[i("评估金额: ¥"+n(k.value.evaluatePrice),1)])),_:1}),u(c,{class:"breakdown-item"},{default:r((()=>[i("加价券: +¥"+n(k.value.bonus),1)])),_:1})])),_:1})])),_:1})])),_:1}),d(" 成功状态区域 "),u(t,{class:"success-section"},{default:r((()=>[u(t,{class:"success-icon"},{default:r((()=>[u(t,{class:"clipboard-bg"},{default:r((()=>[u(t,{class:"clipboard-body"},{default:r((()=>[u(t,{class:"clipboard-lines"},{default:r((()=>[u(t,{class:"line"}),u(t,{class:"line"}),u(t,{class:"line"})])),_:1})])),_:1}),u(t,{class:"clipboard-clip"})])),_:1}),u(t,{class:"check-mark"},{default:r((()=>[u(_,{name:"checkmark",color:"#fff",size:"16"})])),_:1})])),_:1}),u(c,{class:"success-title"},{default:r((()=>[i("订单创建成功")])),_:1}),u(c,{class:"order-number"},{default:r((()=>[i("订单号: "+n(h.value.orderNo),1)])),_:1}),u(c,{class:"success-desc"},{default:r((()=>[i('您可以在我的"我的订单"中查看该订单信息')])),_:1}),d(" 查看订单按钮 "),u(t,{class:"view-order-button",onClick:F},{default:r((()=>[u(c,{class:"button-text"},{default:r((()=>[i("查看订单")])),_:1})])),_:1})])),_:1})])),_:1})}}}),[["__scopeId","data-v-67af7ac6"]]);export{k as default};
