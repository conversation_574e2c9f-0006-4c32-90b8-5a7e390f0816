import{ab as t,ac as e,ad as i,ae as o,af as r,i as s,j as a,o as l,c as d,w as u,b as p,t as n,n as m,x as _,g as c,S as j,k as y,d as x,r as f,J as h,e as v,P as g}from"./index-3caf046d.js";import{_ as S}from"./u-line.69c0c00f.js";import{_ as b}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as k}from"./index.vue_vue_type_script_setup_true_lang.7e3f8767.js";import"./index.9de114a1.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-popup.1b30ffa7.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-safe-bottom.98e092c5.js";import"./top-tabbar.f4fde406.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.abe3938e.js";import"./u-checkbox-group.0328273c.js";import"./u-loading-icon.255170b9.js";import"./u-datetime-picker.a5259774.js";import"./u-input.2d8dc7a4.js";import"./u-upload.83871903.js";import"./u-radio-group.63482a1c.js";import"./diy_form.9eef685a.js";import"./u-avatar.30e31e9c.js";import"./u-text.f02e6497.js";import"./u-parse.406d0731.js";import"./tabbar.2c31519d.js";import"./u-tabbar-item.31141540.js";import"./u-tabbar.38f37e13.js";import"./index.32583a71.js";import"./u--image.eb573bce.js";import"./u-image.04cba9a2.js";/* empty css                                                                */import"./goods.6a81cb49.js";import"./useGoods.9c8f1c51.js";import"./coupon.2f3f2d3d.js";import"./point.0698952c.js";import"./rank.7a4c9318.js";import"./bind-mobile.25318c0e.js";import"./u-form.49dbb57f.js";import"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import"./u-modal.8624728a.js";import"./voucher.db23b7c0.js";import"./quote.9b84c391.js";import"./newcomer.6993dfef.js";import"./order.5c5c6bee.js";const C=b({name:"u-divider",mixins:[e,i,{props:{dashed:{type:Boolean,default:()=>t.divider.dashed},hairline:{type:Boolean,default:()=>t.divider.hairline},dot:{type:Boolean,default:()=>t.divider.dot},textPosition:{type:String,default:()=>t.divider.textPosition},text:{type:[String,Number],default:()=>t.divider.text},textSize:{type:[String,Number],default:()=>t.divider.textSize},textColor:{type:String,default:()=>t.divider.textColor},lineColor:{type:String,default:()=>t.divider.lineColor}}}],computed:{textStyle(){const t={};return t.fontSize=o(this.textSize),t.color=this.textColor,t},leftLineStyle(){const t={};return"left"===this.textPosition?t.width="80rpx":t.flex=1,t},rightLineStyle(){const t={};return"right"===this.textPosition?t.width="80rpx":t.flex=1,t}},emits:["click"],methods:{addStyle:r,click(){this.$emit("click")}}},[["render",function(t,e,i,o,r,x){const f=s(a("u-line"),S),h=j,v=y;return l(),d(v,{class:"u-divider",style:m([x.addStyle(t.customStyle)]),onClick:x.click},{default:u((()=>[p(f,{color:t.lineColor,customStyle:x.leftLineStyle,hairline:t.hairline,dashed:t.dashed},null,8,["color","customStyle","hairline","dashed"]),t.dot?(l(),d(h,{key:0,class:"u-divider__dot"},{default:u((()=>[n("●")])),_:1})):t.text?(l(),d(h,{key:1,class:"u-divider__text",style:m([x.textStyle])},{default:u((()=>[n(_(t.text),1)])),_:1},8,["style"])):c("v-if",!0),p(f,{color:t.lineColor,customStyle:x.rightLineStyle,hairline:t.hairline,dashed:t.dashed},null,8,["color","customStyle","hairline","dashed"])])),_:1},8,["style","onClick"])}],["__scopeId","data-v-14eab0c7"]]),w=x({__name:"diy_form_detail",setup(t){const e=f(0);return h((t=>{e.value=t.record_id})),(t,i)=>{const o=y,r=s(a("u-divider"),C);return l(),d(o,{style:m(t.themeColor())},{default:u((()=>[p(o,{class:"w-screen h-screen bg-[var(--page-bg-color)] min-h-[100vh]"},{default:u((()=>[p(o,{class:"bg-white p-3"},{default:u((()=>[p(o,{class:"text-[30rpx] font-500 leading-[45rpx]"},{default:u((()=>[n(_(v(g)("diyForm.detailInformation")),1)])),_:1}),p(r,{text:""}),c(" 动态渲染表单组件详情 "),p(k,{record_id:e.value,completeLayout:"style-1"},null,8,["record_id"])])),_:1})])),_:1})])),_:1},8,["style"])}}});export{w as default};
