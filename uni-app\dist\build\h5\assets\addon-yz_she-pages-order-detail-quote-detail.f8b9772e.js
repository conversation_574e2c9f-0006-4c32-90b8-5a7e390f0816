import{d as e,r as a,p as t,J as l,M as s,_ as c,N as u,o,c as d,w as n,g as i,b as r,t as _,y as f,x as p,e as v,$ as m,F as g,z as y,a_ as k,O as b,a3 as h,be as x,bU as S,k as C,S as q,Q as w,i as $,j as D,au as I,al as z,A as M,aB as F}from"./index-3caf046d.js";import{_ as j}from"./u-icon.ba193921.js";import{b as R,u as T,d as U}from"./quote.9b84c391.js";import{c as A}from"./recycle_order.a252d983.js";import{_ as E}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const Q=E(e({__name:"quote-detail",setup(e){const E=a({}),Q=a({id:"",order_no:"",product_name:"",product_code:"",product_image:"",status:1,quote_price:null,user_note:"",create_time:"",quote_time:""}),N=a([]),O=a([]),V=a(""),H=a(""),B=a(!1),J=a(!1),L=a(!1),P=a("pickup"),Y=a(null),G=a("尽快上门"),K=a(""),W=a(""),X=a(!1),Z=a(!1),ee=a(0),ae=a(-1),te=a(!1),le=a([]),se=a([]),ce=t((()=>{var e;return ae.value>=0&&!(null==(e=se.value[ae.value])?void 0:e.disabled)})),ue=["顺丰速运","京东物流","德邦快递","中通快递","韵达速递","圆通速递","申通快递","极兔速递"],oe=t((()=>[1,2,3,5].includes(Q.value.status))),de=1,ne=2,ie=3,re=4,_e=5,fe=e=>({[de]:"估价中",[ne]:"待确认",[ie]:"待发货",[re]:"已完成",[_e]:"已取消"}[e]||"未知状态"),pe=e=>{if(!e)return"";const a=new Date(e);return`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")} ${String(a.getHours()).padStart(2,"0")}:${String(a.getMinutes()).padStart(2,"0")}`},ve=()=>{J.value=!0},me=()=>{J.value=!1},ge=async()=>{if(!L.value)try{L.value=!0,await R(Q.value.id,"用户主动取消"),s({title:"订单已取消",icon:"success",duration:2e3}),Q.value.status=_e,J.value=!1,setTimeout((()=>{h()}),1500)}catch(e){console.error("取消订单失败:",e),s({title:e.message||"取消失败，请重试",icon:"none",duration:3e3})}finally{L.value=!1}},ye=async()=>{try{console.log("确认回收，订单ID:",Q.value.id);const e=await T(Q.value.id,ie);if(1!==e.code)throw new Error(e.msg||"确认失败");s({title:"确认成功",icon:"success",duration:2e3}),Q.value.status=ie,await Re(Q.value.id.toString())}catch(e){console.error("确认回收失败:",e),s({title:e.message||"确认失败，请重试",icon:"none",duration:3e3})}},ke=()=>{x({url:"/addon/yz_she/pages/brand/index"})},be=()=>{x({url:"/addon/yz_she/pages/order/order-list"})},he=e=>{if(e&&e.auto_cancel_time){const a=e.auto_cancel_time;if(a.length>=16){return`${a.substring(5,10)} ${a.substring(11,16)}`}return a}if(e&&e.quote_time)try{const a=new Date(e.quote_time);if(isNaN(a.getTime()))return"";const t=new Date(a.getTime()+1728e5),l=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0"),c=String(t.getHours()).padStart(2,"0");return`${l}-${s} ${c}:${String(t.getMinutes()).padStart(2,"0")}`}catch(a){return console.error("quote_time 解析错误:",a),""}return""},xe=()=>{const e=new Date,a=new Date(e);a.setDate(e.getDate()+1);const t=new Date(e);t.setDate(e.getDate()+2),le.value=[{label:`${String(e.getMonth()+1).padStart(2,"0")}月${String(e.getDate()).padStart(2,"0")}日(今日)`,date:e,isToday:!0},{label:`${String(a.getMonth()+1).padStart(2,"0")}月${String(a.getDate()).padStart(2,"0")}日(明日)`,date:a,isToday:!1},{label:`${String(t.getMonth()+1).padStart(2,"0")}月${String(t.getDate()).padStart(2,"0")}日(后日)`,date:t,isToday:!1}],ee.value=0,Se(),ae.value=0},Se=()=>{const e=le.value[ee.value];e&&(se.value=((e,a=!1)=>{const t=new Date,l=t.getHours()+t.getMinutes()/60,s=[];return s.push({label:"尽快上门",desc:"",value:"urgent",disabled:!1,isUrgent:!0}),[{start:9,end:11},{start:11,end:13},{start:13,end:15},{start:15,end:17},{start:17,end:19}].forEach((e=>{const t=`${e.start.toString().padStart(2,"0")}:00-${e.end.toString().padStart(2,"0")}:00`;let c=!1,u="";a&&l>=e.start&&(c=!0,u=""),s.push({label:t,desc:u,value:`${e.start}-${e.end}`,disabled:c,isUrgent:!1})})),s})(e.date,e.isToday),ae.value=0)},Ce=e=>{P.value=e,console.log("选择配送方式:",e)},qe=()=>{const e=Q.value.id?`/addon/yz_she/pages/order/detail/quote-detail?id=${Q.value.id}`:"/addon/yz_she/pages/order/detail/quote-detail";uni.setStorage({key:"selectAddressCallback",data:{back:e,orderId:Q.value.id}}),x({url:"/addon/yz_she/pages/address/index"})},we=()=>{S({data:"四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递",success:()=>{s({title:"地址已复制",icon:"success"})}})},$e=()=>{Y.value?(xe(),Z.value=!0):s({title:"请先选择取件地址",icon:"none",duration:2e3})},De=()=>{Z.value=!1},Ie=()=>{if(!ce.value)return void s({title:"请选择有效的时间段",icon:"none"});const e=le.value[ee.value],a=se.value[ae.value];if(e&&a){let t="";if(a.isUrgent)t="尽快上门";else{t=`${e.label.replace(/\(.*\)/,"")} ${a.label}`}G.value=t,Z.value=!1,s({title:"时间选择成功",icon:"success"}),console.log("选择时间:",t)}},ze=()=>{X.value=!0},Me=()=>{X.value=!1},Fe=()=>{const e=parseFloat(Q.value.quote_price)||0,a=parseInt(Q.value.category_id)||0,t=Q.value.brand_id?parseInt(Q.value.brand_id):null,l=void 0!==Q.value.product_id&&null!==Q.value.product_id?parseInt(Q.value.product_id):null;console.log("原始订单信息:",{category_id:Q.value.category_id,brand_id:Q.value.brand_id,product_id:Q.value.product_id,product_name:Q.value.product_name});const s={category_id:a,brand_id:t,product_id:l,product_name:Q.value.product_name||"",product_code:Q.value.product_code||"",product_image:Q.value.product_image||"",expected_price:e.toFixed(2),voucher_amount:0,final_price:e.toFixed(2),total_amount:e.toFixed(2),express_fee:0,source_type:1,delivery_type:"pickup"===P.value?1:2,quantity:1,voucher_id:null,quote_order_id:parseInt(Q.value.id)};return console.log("构建订单数据 - 处理后的参数:",{category_id:s.category_id,brand_id:s.brand_id,product_id:s.product_id,product_name:s.product_name,quote_price:e,quote_order_id:s.quote_order_id}),"pickup"===P.value&&Y.value&&(s.pickup_address_id=Y.value.id,s.pickup_contact_name=Y.value.name,s.pickup_contact_phone=Y.value.mobile,s.pickup_address_detail=Y.value.full_address||Y.value.address_detail||"",s.pickup_time=G.value&&"尽快上门"!==G.value?G.value:"尽快上门",console.log("快递上门地址信息:",{address_id:Y.value.id,contact_name:Y.value.name,contact_phone:Y.value.mobile,address_detail:s.pickup_address_detail,pickup_time:s.pickup_time})),"self"===P.value&&(s.pickup_contact_name="放心星仓库",s.pickup_contact_phone="13060000687",s.admin_note=`用户自寄，快递公司：${K.value}，快递单号：${W.value}`,s.express_company=K.value,s.express_number=W.value,s.status=2,console.log("自行寄出订单信息:",{express_company:s.express_company,express_number:s.express_number,status:s.status})),s},je=async()=>{if(console.log("开始提交回收订单"),!te.value)try{if(!(()=>{if(!Q.value.id)return s({title:"订单信息缺失",icon:"none"}),!1;if(!Q.value.quote_price||Q.value.quote_price<=0)return s({title:"估价金额异常",icon:"none"}),!1;if("pickup"===P.value){if(!Y.value)return s({title:"请选择取件地址",icon:"none"}),!1;if(!G.value)return s({title:"请选择上门时间",icon:"none"}),!1}if("self"===P.value){if(!K.value)return s({title:"请选择快递公司",icon:"none"}),!1;if(!W.value||!W.value.trim())return s({title:"请输入快递单号",icon:"none"}),!1}return!0})())return;te.value=!0,console.log("第一步：更新估价订单状态为已完成");const e=await T(Q.value.id,re);if(1!==e.code)throw new Error(e.msg||"更新估价订单状态失败");console.log("第二步：构建回收订单数据");const a=Fe();console.log("完整订单数据:",a),0===a.category_id&&console.warn("警告：category_id为0，可能获取失败"),null===a.brand_id&&console.warn("警告：brand_id为null，可能获取失败"),null===a.product_id&&console.warn("警告：product_id为null，可能获取失败"),console.log("第三步：提交回收订单"),console.log("即将提交的订单数据:",JSON.stringify(a,null,2));const t=await A(a);if(1!==t.code)throw new Error(t.msg||"订单提交失败");s({title:"订单提交成功",icon:"success",duration:2e3}),Q.value.status=re,H.value=t.data.order_no||`RC${Date.now().toString().slice(-8)}`,console.log("订单提交成功，刷新页面状态"),await Re(Q.value.id.toString())}catch(e){console.error("提交订单失败:",e),s({title:e.message||"提交失败，请重试",icon:"none",duration:3e3})}finally{te.value=!1}},Re=async e=>{var a;if(e)try{B.value=!0;const t=await U(parseInt(e));if(1!==t.code)throw new Error(t.msg||"获取订单详情失败");{const e=t.data;console.log("API返回的原始数据:",{id:e.id,category_id:e.category_id,brand_id:e.brand_id,product_id:e.product_id,product_name:e.product_name,product_code:e.product_code,product_image:e.product_image,auto_cancel_time:e.auto_cancel_time,quote_time:e.quote_time}),Q.value={id:e.id,order_no:e.order_no||"",category_id:e.category_id||0,brand_id:e.brand_id||null,product_id:e.product_id||null,product_name:e.product_name||(null==(a=e.brand)?void 0:a.name)||"估价商品",product_code:e.product_code||"",product_image:e.product_image||"",status:e.status||de,quote_price:e.quote_price||null,user_note:e.user_note||"",create_time:e.create_time||"",quote_time:e.quote_time||"",auto_cancel_time:e.auto_cancel_time||null},console.log("设置后的订单信息 - 关键参数:",{category_id:Q.value.category_id,brand_id:Q.value.brand_id,product_id:Q.value.product_id,product_name:Q.value.product_name}),N.value=(e.photos||[]).map((e=>({...e,photo_url:e.photo_url}))),O.value=e.accessories||[],console.log("估价记录数据:",e.latestQuoteRecord),e.latestQuoteRecord&&e.latestQuoteRecord.quote_note?(V.value=e.latestQuoteRecord.quote_note,console.log("设置估价说明:",V.value)):(V.value="",console.log("没有估价说明数据")),e.status===re&&e.recycle_order_no?H.value=e.recycle_order_no:e.status===re&&(H.value=`RC${e.order_no.substring(2)}`),e.status===re&&e.recycle_order_no?H.value=e.recycle_order_no:e.status===re&&(H.value=`RC${e.order_no.substring(2)}`)}}catch(t){console.error("加载订单详情失败:",t),s({title:t.message||"加载失败，请重试",icon:"none",duration:3e3})}finally{B.value=!1}else s({title:"订单ID不能为空",icon:"none"})};return l((e=>{console.log("页面参数:",e),E.value=e,e.id?Re(e.id):s({title:"缺少订单ID参数",icon:"none"})})),c((()=>{console.log("页面挂载完成"),xe()})),u((()=>{const e=uni.getStorageSync("selectAddressCallback");e&&e.address_id&&(e.orderId&&(Q.value.id||Re(e.orderId)),e.address_info?(Y.value=e.address_info,s({title:"地址选择成功",icon:"success"})):Y.value=null,uni.removeStorage({key:"selectAddressCallback"}))})),(e,a)=>{const t=C,l=q,c=w,u=$(D("u-icon"),j),h=I,x=z;return o(),d(t,{class:"quote-detail-page"},{default:n((()=>[i(" 加载状态 "),B.value?(o(),d(t,{key:0,class:"loading-container"},{default:n((()=>[r(t,{class:"loading-content"},{default:n((()=>[r(t,{class:"loading-spinner"}),r(l,{class:"loading-text"},{default:n((()=>[_("加载中...")])),_:1})])),_:1})])),_:1})):(o(),f(g,{key:1},[i(" 主要内容 "),r(t,{class:"main-content"},{default:n((()=>[i(" 商品信息卡片（融合订单信息和评估图片） "),r(t,{class:"product-card"},{default:n((()=>[i(" 待发货状态的顶部提示栏 "),3===Q.value.status?(o(),d(t,{key:0,class:"shipping-notice"},{default:n((()=>[r(l,{class:"notice-text"},{default:n((()=>[_("单次估价有效时间，请在"+p(he(Q.value))+"前 寄出，超时可能导致订单失效",1)])),_:1}),r(l,{class:"notice-right"},{default:n((()=>[_("限时有效")])),_:1})])),_:1})):i("v-if",!0),i(" 商品基本信息 "),r(t,{class:"product-info"},{default:n((()=>[Q.value.product_image?(o(),d(c,{key:0,src:v(M)(Q.value.product_image),class:"product-image",mode:"aspectFit"},null,8,["src"])):(o(),d(t,{key:1,class:"product-image no-image"},{default:n((()=>[r(u,{name:"image",color:"#ccc",size:"40"})])),_:1})),r(t,{class:"product-details"},{default:n((()=>[r(l,{class:"product-name"},{default:n((()=>[_(p(Q.value.product_name||"商品名称"),1)])),_:1}),Q.value.product_code?(o(),d(l,{key:0,class:"product-code"},{default:n((()=>[_(p(Q.value.product_code),1)])),_:1})):i("v-if",!0),r(l,{class:"order-no"},{default:n((()=>[_("估价订单号："+p(Q.value.order_no),1)])),_:1})])),_:1})])),_:1}),i(" 订单状态和价格 - 非已完成状态显示订单状态 "),r(t,{class:"status-section"},{default:n((()=>[4!==Q.value.status?(o(),d(t,{key:0,class:"status-info"},{default:n((()=>{return[r(l,{class:"status-label"},{default:n((()=>[_("订单状态")])),_:1}),r(t,{class:m(["status-badge",(e=Q.value.status,{[de]:"quoting",[ne]:"pending-confirm",[ie]:"pending-ship",[re]:"completed",[_e]:"cancelled"}[e]||"default")])},{default:n((()=>[r(l,{class:"status-text"},{default:n((()=>[_(p(fe(Q.value.status)),1)])),_:1})])),_:1},8,["class"])];var e})),_:1})):i("v-if",!0),i(" 价格信息 "),Q.value.quote_price?(o(),d(t,{key:1,class:"price-info"},{default:n((()=>[r(l,{class:"price-label"},{default:n((()=>[_("估价金额")])),_:1}),r(t,{class:"price-amount"},{default:n((()=>[r(l,{class:"currency"},{default:n((()=>[_("¥")])),_:1}),r(l,{class:"price-value"},{default:n((()=>[_(p(Q.value.quote_price),1)])),_:1})])),_:1})])),_:1})):(o(),d(t,{key:2,class:"price-info"},{default:n((()=>[r(l,{class:"price-label"},{default:n((()=>[_("估价金额")])),_:1}),r(l,{class:"price-pending"},{default:n((()=>[_("估价中...")])),_:1})])),_:1}))])),_:1}),i(" 订单时间信息 - 非已完成状态显示 "),r(t,{class:"time-info"},{default:n((()=>[r(t,{class:"time-item"},{default:n((()=>[r(l,{class:"time-label"},{default:n((()=>[_("创建时间")])),_:1}),r(l,{class:"time-value"},{default:n((()=>[_(p(pe(Q.value.create_time)),1)])),_:1})])),_:1}),Q.value.quote_time?(o(),d(t,{key:0,class:"time-item"},{default:n((()=>[r(l,{class:"time-label"},{default:n((()=>[_("估价时间")])),_:1}),r(l,{class:"time-value"},{default:n((()=>[_(p(pe(Q.value.quote_time)),1)])),_:1})])),_:1})):i("v-if",!0)])),_:1}),i(" 用户上传的评估图片（融合在商品卡片中） "),N.value.length>0?(o(),d(t,{key:1,class:"upload-photos"},{default:n((()=>[r(l,{class:"photos-title"},{default:n((()=>[_("评估图片")])),_:1}),r(h,{class:"photos-scroll","scroll-x":"true","show-scrollbar":"false"},{default:n((()=>[r(t,{class:"photos-list"},{default:n((()=>[(o(!0),f(g,null,y(N.value,(e=>(o(),d(t,{class:"photo-item",key:e.id},{default:n((()=>[r(c,{src:v(M)(e.photo_url),class:"photo-image",mode:"aspectFill",onClick:a=>(e=>{const a=N.value.map((e=>M(e.photo_url))),t=a.indexOf(M(e));F({urls:a,current:t})})(e.photo_url)},null,8,["src","onClick"]),r(l,{class:"photo-label"},{default:n((()=>[_(p(e.photo_name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):i("v-if",!0)])),_:1}),i(" 选择的配件（并排显示） - 非已完成状态且非待发货状态显示 "),O.value.length>0&&4!==Q.value.status&&3!==Q.value.status?(o(),d(t,{key:0,class:"accessories-card"},{default:n((()=>[r(l,{class:"card-title"},{default:n((()=>[_("选择配件")])),_:1}),r(t,{class:"accessories-list"},{default:n((()=>[(o(!0),f(g,null,y(O.value,(e=>(o(),d(t,{class:"accessory-tag",key:e.id},{default:n((()=>[r(l,{class:"accessory-name"},{default:n((()=>[_(p(e.accessory_name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})):i("v-if",!0),i(" 估价说明 - 非已完成状态且非待发货状态显示 "),V.value&&V.value.trim()&&4!==Q.value.status&&3!==Q.value.status?(o(),d(t,{key:1,class:"quote-note-card"},{default:n((()=>[r(l,{class:"card-title"},{default:n((()=>[_("估价说明")])),_:1}),r(t,{class:"note-content"},{default:n((()=>[r(l,{class:"note-text"},{default:n((()=>[_(p(V.value),1)])),_:1})])),_:1})])),_:1})):i("v-if",!0),i(" 用户备注 - 非已完成状态且非待发货状态显示 "),Q.value.user_note&&4!==Q.value.status&&3!==Q.value.status?(o(),d(t,{key:2,class:"user-note-card"},{default:n((()=>[r(l,{class:"card-title"},{default:n((()=>[_("用户备注")])),_:1}),r(t,{class:"note-content"},{default:n((()=>[r(l,{class:"note-text"},{default:n((()=>[_(p(Q.value.user_note),1)])),_:1})])),_:1})])),_:1})):i("v-if",!0),i(" 待发货状态的配送方式选择 "),3===Q.value.status?(o(),d(t,{key:3,class:"shipping-section"},{default:n((()=>[i(" 配送方式 "),r(t,{class:"delivery-section"},{default:n((()=>[r(t,{class:"delivery-options"},{default:n((()=>[r(t,{class:m(["delivery-option",{active:"pickup"===P.value}]),onClick:a[0]||(a[0]=e=>Ce("pickup"))},{default:n((()=>[r(t,{class:"option-content"},{default:n((()=>[r(l,{class:"option-name"},{default:n((()=>[_("快递上门")])),_:1}),r(t,{class:"option-tag"},{default:n((()=>[r(l,{class:"tag-text"},{default:n((()=>[_("免费")])),_:1})])),_:1})])),_:1})])),_:1},8,["class"]),r(t,{class:m(["delivery-option",{active:"self"===P.value}]),onClick:a[1]||(a[1]=e=>Ce("self"))},{default:n((()=>[r(l,{class:"option-name"},{default:n((()=>[_("自行寄出")])),_:1})])),_:1},8,["class"])])),_:1}),i(" 快递上门内容 "),"pickup"===P.value?(o(),d(t,{key:0,class:"pickup-content"},{default:n((()=>[i(" 地址选择 "),r(t,{class:"address-item",onClick:qe},{default:n((()=>[r(t,{class:"address-icon"},{default:n((()=>[r(u,{name:"map",color:"#333",size:"18"})])),_:1}),r(t,{class:"address-content"},{default:n((()=>[Y.value?(o(),d(t,{key:1,class:"selected-address"},{default:n((()=>[r(l,{class:"address-name"},{default:n((()=>[_(p(Y.value.name)+" "+p(Y.value.mobile),1)])),_:1}),r(l,{class:"address-detail"},{default:n((()=>[_(p(Y.value.full_address),1)])),_:1})])),_:1})):(o(),d(l,{key:0,class:"address-text"},{default:n((()=>[_("请选择取件地址")])),_:1}))])),_:1}),r(t,{class:"divider-line"}),r(l,{class:"address-action"},{default:n((()=>[_("地址簿")])),_:1})])),_:1}),i(" 预约时间 "),r(t,{class:m(["time-item",{disabled:!Y.value}]),onClick:$e},{default:n((()=>[r(t,{class:"time-icon"},{default:n((()=>[(o(),f("svg",{viewBox:"0 0 1024 1024",width:"32",height:"32"},[k("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m176.5 585.7l-28.6 39c-4.4 6.1-11.9 9.3-19.4 9.3-4.7 0-9.5-1.4-13.6-4.3L512 609.7l-114.9 84c-9.9 7.2-23.8 5.1-31-4.9l-28.6-39c-7.2-9.9-5.1-23.8 4.9-31L457 515.3V264c0-11 9-20 20-20h70c11 0 20 9 20 20v251.3l114.6 103.5c9.9 7.1 12 21 4.9 30.9z",fill:Y.value?"#333":"#ccc"},null,8,["fill"])]))])),_:1}),r(l,{class:"time-text"},{default:n((()=>[_("期望上门时间")])),_:1}),r(l,{class:m(["time-action",{disabled:!Y.value}])},{default:n((()=>[_(p(Y.value?G.value||"尽快上门":"请先选择地址")+" > ",1)])),_:1},8,["class"])])),_:1},8,["class"])])),_:1})):i("v-if",!0),i(" 自行寄出内容 "),"self"===P.value?(o(),d(t,{key:1,class:"self-content"},{default:n((()=>[i(" 收货地址 "),r(t,{class:"address-item"},{default:n((()=>[r(t,{class:"address-icon orange-bg"},{default:n((()=>[r(l,{class:"address-text-icon"},{default:n((()=>[_("收")])),_:1})])),_:1}),r(t,{class:"address-info"},{default:n((()=>[r(l,{class:"address-name"},{default:n((()=>[_("放心星仓库 13060000687")])),_:1}),r(l,{class:"address-detail"},{default:n((()=>[_("四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递")])),_:1})])),_:1}),r(l,{class:"copy-btn",onClick:we},{default:n((()=>[_("复制")])),_:1})])),_:1}),i(" 快递公司 "),r(t,{class:"express-item",onClick:ze},{default:n((()=>[r(t,{class:"express-icon"},{default:n((()=>[r(u,{name:"car",color:"#333",size:"32"})])),_:1}),r(l,{class:"express-text"},{default:n((()=>[_("快递公司")])),_:1}),r(l,{class:"express-action"},{default:n((()=>[_(p(K.value||"请选择快递公司")+" >",1)])),_:1})])),_:1}),i(" 快递单号 "),r(t,{class:"tracking-item"},{default:n((()=>[r(t,{class:"tracking-icon"},{default:n((()=>[r(u,{name:"order",color:"#333",size:"32"})])),_:1}),r(l,{class:"tracking-text"},{default:n((()=>[_("快递单号")])),_:1}),r(x,{class:"tracking-input",modelValue:W.value,"onUpdate:modelValue":a[2]||(a[2]=e=>W.value=e),placeholder:"请输入快递单号",maxlength:"30"},null,8,["modelValue"])])),_:1})])),_:1})):i("v-if",!0)])),_:1})])),_:1})):i("v-if",!0),i(" 已完成状态的成功展示区域 "),4===Q.value.status?(o(),d(t,{key:4,class:"success-section"},{default:n((()=>[r(t,{class:"success-icon"},{default:n((()=>[r(t,{class:"clipboard-bg"},{default:n((()=>[r(t,{class:"clipboard-body"},{default:n((()=>[r(t,{class:"clipboard-lines"},{default:n((()=>[r(t,{class:"line"}),r(t,{class:"line"}),r(t,{class:"line"})])),_:1})])),_:1}),r(t,{class:"clipboard-clip"})])),_:1}),r(t,{class:"check-mark"},{default:n((()=>[r(l,{class:"check-icon"},{default:n((()=>[_("✓")])),_:1})])),_:1})])),_:1}),r(l,{class:"success-title"},{default:n((()=>[_("订单创建成功")])),_:1}),r(l,{class:"order-number"},{default:n((()=>[_("回收订单号: "+p(H.value||"生成中..."),1)])),_:1}),r(l,{class:"success-desc"},{default:n((()=>[_('您可以在"我的订单"中查看该订单信息')])),_:1}),i(" 查看订单按钮 "),r(t,{class:"view-order-button",onClick:be},{default:n((()=>[r(l,{class:"button-text"},{default:n((()=>[_("查看订单")])),_:1})])),_:1})])),_:1})):i("v-if",!0)])),_:1})],2112)),i(" 底部操作按钮 "),v(oe)?(o(),d(t,{key:2,class:"bottom-actions"},{default:n((()=>[i(" 估价中状态：取消订单 "),1===Q.value.status?(o(),d(t,{key:0,class:"single-button"},{default:n((()=>[r(t,{class:"cancel-button",onClick:ve},{default:n((()=>[r(l,{class:"button-text"},{default:n((()=>[_("取消订单")])),_:1})])),_:1})])),_:1})):i("v-if",!0),i(" 待确认状态：取消订单、确认回收 "),2===Q.value.status?(o(),d(t,{key:1,class:"double-buttons"},{default:n((()=>[r(t,{class:"cancel-button secondary",onClick:ve},{default:n((()=>[r(l,{class:"button-text"},{default:n((()=>[_("取消订单")])),_:1})])),_:1}),r(t,{class:"confirm-button primary",onClick:ye},{default:n((()=>[r(l,{class:"button-text"},{default:n((()=>[_("确认回收")])),_:1})])),_:1})])),_:1})):i("v-if",!0),i(" 待发货状态：确认回收 "),3===Q.value.status?(o(),d(t,{key:2,class:"shipping-bottom-actions"},{default:n((()=>[r(t,{class:m(["confirm-recycle-button",{submitting:te.value}]),onClick:je},{default:n((()=>[r(t,{class:"button-content"},{default:n((()=>[te.value?(o(),d(t,{key:1,class:"loading-content"},{default:n((()=>[r(t,{class:"loading-spinner"}),r(l,{class:"button-text"},{default:n((()=>[_("提交中...")])),_:1})])),_:1})):(o(),d(t,{key:0,class:"normal-content"},{default:n((()=>[r(l,{class:"button-text"},{default:n((()=>[_("确认回收")])),_:1})])),_:1}))])),_:1}),r(t,{class:"button-shine"})])),_:1},8,["class"])])),_:1})):i("v-if",!0),i(" 已取消状态：重新回收 "),5===Q.value.status?(o(),d(t,{key:3,class:"single-button"},{default:n((()=>[r(t,{class:"recycle-button",onClick:ke},{default:n((()=>[r(l,{class:"button-text"},{default:n((()=>[_("重新回收")])),_:1})])),_:1})])),_:1})):i("v-if",!0)])),_:1})):i("v-if",!0),i(" 取消订单确认弹窗 "),J.value?(o(),d(t,{key:3,class:"cancel-modal",onClick:me},{default:n((()=>[r(t,{class:"cancel-modal-content",onClick:a[3]||(a[3]=b((()=>{}),["stop"]))},{default:n((()=>[r(t,{class:"cancel-header"},{default:n((()=>[r(l,{class:"cancel-title"},{default:n((()=>[_("取消订单")])),_:1})])),_:1}),r(t,{class:"cancel-body"},{default:n((()=>[r(l,{class:"cancel-message"},{default:n((()=>[_("确定要取消这个估价订单吗？")])),_:1}),r(l,{class:"cancel-note"},{default:n((()=>[_("取消后将无法恢复，需要重新提交估价申请。")])),_:1})])),_:1}),r(t,{class:"cancel-actions"},{default:n((()=>[r(t,{class:"cancel-btn secondary",onClick:me},{default:n((()=>[r(l,{class:"btn-text"},{default:n((()=>[_("再想想")])),_:1})])),_:1}),r(t,{class:"cancel-btn primary",onClick:ge},{default:n((()=>[r(l,{class:"btn-text"},{default:n((()=>[_("确认取消")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):i("v-if",!0),i(" 快递公司选择弹窗 "),X.value?(o(),d(t,{key:4,class:"express-modal",onClick:Me},{default:n((()=>[r(t,{class:"modal-content",onClick:a[4]||(a[4]=b((()=>{}),["stop"]))},{default:n((()=>[r(t,{class:"modal-header"},{default:n((()=>[r(l,{class:"modal-title"},{default:n((()=>[_("选择快递公司")])),_:1}),r(t,{class:"close-btn",onClick:Me},{default:n((()=>[_("×")])),_:1})])),_:1}),r(t,{class:"express-list"},{default:n((()=>[(o(),f(g,null,y(ue,(e=>r(t,{class:"express-option",key:e,onClick:a=>(e=>{K.value=e,X.value=!1,s({title:`已选择${e}`,icon:"success"})})(e)},{default:n((()=>[r(l,{class:"express-name"},{default:n((()=>[_(p(e),1)])),_:2},1024),K.value===e?(o(),d(t,{key:0,class:"express-check"},{default:n((()=>[_("✓")])),_:1})):i("v-if",!0)])),_:2},1032,["onClick"]))),64))])),_:1})])),_:1})])),_:1})):i("v-if",!0),i(" 时间选择弹窗 "),Z.value?(o(),d(t,{key:5,class:"time-modal",onClick:De},{default:n((()=>[r(t,{class:"time-modal-content",onClick:a[5]||(a[5]=b((()=>{}),["stop"]))},{default:n((()=>[r(t,{class:"time-header"},{default:n((()=>[r(l,{class:"time-title"},{default:n((()=>[_("选择上门时间")])),_:1}),r(t,{class:"close-btn",onClick:De},{default:n((()=>[_("×")])),_:1})])),_:1}),i(" 时间选择内容 "),r(t,{class:"time-picker-container"},{default:n((()=>[i(" 左侧日期列表 "),r(t,{class:"date-list"},{default:n((()=>[(o(!0),f(g,null,y(le.value,((e,a)=>(o(),d(t,{class:m(["date-item",{active:ee.value===a}]),key:a,onClick:e=>(e=>{ee.value=e,Se()})(a)},{default:n((()=>[r(l,{class:"date-text"},{default:n((()=>[_(p(e.label),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),i(" 右侧时间段列表 "),r(t,{class:"time-list"},{default:n((()=>[(o(!0),f(g,null,y(se.value,((e,a)=>(o(),d(t,{class:m(["time-item",{active:ae.value===a,disabled:e.disabled,urgent:e.isUrgent}]),key:a,onClick:e=>(e=>{const a=se.value[e];a&&!a.disabled&&(ae.value=e)})(a)},{default:n((()=>[r(l,{class:"time-text"},{default:n((()=>[_(p(e.label),1)])),_:2},1024),e.desc?(o(),d(l,{key:0,class:"time-desc"},{default:n((()=>[_(p(e.desc),1)])),_:2},1024)):i("v-if",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),i(" 确认按钮 "),r(t,{class:"time-confirm"},{default:n((()=>[r(t,{class:m(["confirm-btn",{disabled:!v(ce)}]),onClick:Ie},{default:n((()=>[r(l,{class:"confirm-text"},{default:n((()=>[_("确认")])),_:1})])),_:1},8,["class"])])),_:1})])),_:1})])),_:1})):i("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-0943ffce"]]);export{Q as default};
