import{d as e,r as a,J as s,V as t,W as l,X as n,o as r,c as o,w as u,b as c,e as p,t as i,g as m,n as d,i as v,j as g,k as _,Q as f,A as y}from"./index-3caf046d.js";import{_ as h}from"./u-parse.406d0731.js";import"./_plugin-vue_export-helper.1b428a4d.js";const k=e({__name:"agreement",setup(e){const k=a(null),x=a(!0);return s((e=>{if(e.key)t(e.key).then((e=>{k.value=e.data,x.value=!1,l({title:e.data.agreement_key_name})})).catch((()=>{x.value=!1}));else{n({url:"/app/pages/index/index",title:"协议类型不存在",mode:"reLaunch"})}})),(e,a)=>{const s=v(g("u-parse"),h),t=_,l=f;return r(),o(t,{style:d(e.themeColor())},{default:u((()=>[k.value&&k.value.content?(r(),o(t,{key:0,class:"py-[var(--top-m)] px-[var(--sidebar-m)]"},{default:u((()=>[c(s,{content:k.value.content,tagStyle:{img:"vertical-align: top;"}},null,8,["content"])])),_:1})):x.value?m("v-if",!0):(r(),o(t,{key:1,class:"min-h-[100vh] bg-[var(--page-bg-color)] overflow-hidden"},{default:u((()=>[c(t,{class:"empty-page"},{default:u((()=>[c(l,{class:"img",src:p(y)("static/resource/images/empty.png"),model:"aspectFit"},null,8,["src"]),c(t,{class:"desc"},{default:u((()=>[i("暂无协议")])),_:1})])),_:1})])),_:1}))])),_:1},8,["style"])}}});export{k as default};
