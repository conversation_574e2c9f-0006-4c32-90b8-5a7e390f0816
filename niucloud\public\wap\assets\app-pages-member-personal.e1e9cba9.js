import{d as e,I as a,p as l,r as t,J as o,q as n,e as r,o as s,c as i,w as u,b as d,t as p,x as m,g as c,O as f,n as x,M as v,P as _,ca as h,aC as y,i as b,j as k,k as g,R as j,al as w,A as C,cb as z,a as S}from"./index-3caf046d.js";import{_ as V}from"./u-avatar.30e31e9c.js";import{_ as D}from"./u-upload.83871903.js";import{_ as I,a as A,b as O}from"./u-action-sheet.3d896de8.js";import{_ as P}from"./u-popup.1b30ffa7.js";import{_ as $}from"./u-datetime-picker.a5259774.js";import{_ as B}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-text.f02e6497.js";import"./u-loading-icon.255170b9.js";import"./u-line.69c0c00f.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-safe-bottom.98e092c5.js";import"./u-input.2d8dc7a4.js";const q=B(e({__name:"personal",setup(e){const B=a(),q=l((()=>B.info));t(null),o((()=>{}));const M=n({modal:!1,value:q.nickname||""}),R=e=>{M.value=e.detail.value},U=()=>{uni.$u.test.isEmpty(M.value)?v({title:_("nicknamePlaceholder"),icon:"none"}):h({field:"nickname",value:M.value}).then((e=>{B.info.nickname=M.value,M.modal=!1}))},E=t(!1),J=l((()=>[{name:_("man"),value:1},{name:_("woman"),value:2}])),K=e=>{h({field:"sex",value:e.value}).then((a=>{B.info.sex_name=e.name}))},N=e=>{y({filePath:e.file.url,name:"file"}).then((e=>{h({field:"headimg",value:e.data.url}).then((()=>{B.info.headimg=e.data.url}))})).catch((()=>{}))},T=t(!1),W=e=>{h({field:"birthday",value:uni.$u.date(e.value,"yyyy-mm-dd")}).then((()=>{B.info.birthday=uni.$u.date(e.value||e.value+1,"yyyy-mm-dd"),T.value=!1}))};return(e,a)=>{const l=b(k("u-avatar"),V),t=b(k("u-upload"),D),o=b(k("u-cell"),I),n=g,v=j,h=b(k("u-cell-group"),A),y=w,B=b(k("u-popup"),P),F=b(k("u-action-sheet"),O),G=b(k("u-datetime-picker"),$);return r(q)?(s(),i(n,{key:0,class:"w-full h-screen bg-page personal-wrap overflow-hidden",style:x(e.themeColor())},{default:u((()=>[d(n,{class:"my-[var(--top-m)] sidebar-margin overflow-hidden card-template py-[20rpx]"},{default:u((()=>[d(h,{border:!1,class:"cell-group"},{default:u((()=>{return[d(o,{title:r(_)("headimg"),titleStyle:{"font-size":"28rpx"},"is-link":!0},{value:u((()=>[d(t,{onAfterRead:N,maxCount:1},{default:u((()=>[d(l,{src:r(C)(r(q).headimg),"default-url":r(C)("static/resource/images/default_headimg.png"),size:"40",leftIcon:"none"},null,8,["src","default-url"])])),_:1})])),_:1},8,["title"]),d(o,{title:r(_)("nickname"),titleStyle:{"font-size":"28rpx"},"is-link":!0,value:r(q).nickname,onClick:a[0]||(a[0]=e=>M.modal=!0)},null,8,["title","value"]),d(o,{title:r(_)("sex"),titleStyle:{"font-size":"28rpx"},"is-link":!0,value:r(q).sex_name||r(_)("unknown"),onClick:a[1]||(a[1]=e=>E.value=!0)},null,8,["title","value"]),d(o,{title:r(_)("mobile"),titleStyle:{"font-size":"28rpx"}},{value:u((()=>[r(q).mobile?(s(),i(n,{key:0,class:"mr-[10rpx]"},{default:u((()=>[p(m(r(z)(r(q).mobile)),1)])),_:1})):(s(),i(n,{key:1},{default:u((()=>[d(v,{onClick:a[2]||(a[2]=e=>r(S)({url:"/app/pages/auth/bind"})),class:"bg-transparent w-[170rpx] p-[0] rounded-[100rpx] text-[var(--primary-color)] !border-[2rpx] !border-solid border-[var(--primary-color)] text-[24rpx] h-[54rpx] flex-center"},{default:u((()=>[p(m(r(_)("bindMobile")),1)])),_:1})])),_:1}))])),_:1},8,["title"]),d(o,{title:r(_)("birthday"),titleStyle:{"font-size":"28rpx"},"is-link":!0,value:(e=r(q).birthday,(e?uni.$u.date(new Date(e),"yyyy-mm-dd"):"")||r(_)("unknown")),onClick:a[3]||(a[3]=e=>T.value=!0)},null,8,["title","value"])];var e})),_:1})])),_:1}),c(" 修改昵称 "),d(B,{class:"popup-type",safeAreaInsetBottom:!1,round:"var(--rounded-big)",show:M.modal,mode:"center",onClose:a[6]||(a[6]=e=>M.modal=!1)},{default:u((()=>[d(n,{class:"w-[620rpx] popup-common pb-[40rpx]",onTouchmove:a[5]||(a[5]=f((()=>{}),["prevent","stop"]))},{default:u((()=>[d(n,{class:"title !pt-[50rpx] !pb-[60rpx]"},{default:u((()=>[p(m(r(_)("updateNickname")),1)])),_:1}),d(n,{class:"mx-[50rpx] border-0 border-b border-[#eee] border-solid"},{default:u((()=>[d(y,{type:"nickname",class:"h-[88rpx] text-[26rpx]",modelValue:M.value,"onUpdate:modelValue":a[4]||(a[4]=e=>M.value=e),placeholder:r(_)("nicknamePlaceholder"),placeholderClass:"text-[26rpx] h-[88rpx] flex items-center",onBlur:R},null,8,["modelValue","placeholder"])])),_:1}),d(n,{class:"px-[60rpx] pt-[70rpx]"},{default:u((()=>[d(v,{"hover-class":"none",class:"primary-btn-bg text-[#fff] h-[80rpx] font-500 leading-[80rpx] rounded-[100rpx] text-[26rpx]",onClick:U},{default:u((()=>[p(m(r(_)("confirm")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"]),c(" 修改性别 "),d(F,{actions:r(J),show:E.value,closeOnClickOverlay:!0,safeAreaInsetBottom:!0,onClose:a[7]||(a[7]=e=>E.value=!1),onSelect:K},null,8,["actions","show"]),c(" 修改生日 "),d(G,{modelValue:r(q).birthday,"onUpdate:modelValue":a[8]||(a[8]=e=>r(q).birthday=e),show:T.value,mode:"date","confirm-text":r(_)("confirm"),maxDate:(new Date).valueOf(),minDate:0,"cancel-text":r(_)("cancel"),onCancel:a[9]||(a[9]=e=>T.value=!1),onConfirm:W},null,8,["modelValue","show","confirm-text","maxDate","cancel-text"])])),_:1},8,["style"])):c("v-if",!0)}}}),[["__scopeId","data-v-a930bcab"]]);export{q as default};
