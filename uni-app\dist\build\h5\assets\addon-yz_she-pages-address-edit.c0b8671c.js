import{d as e,r as a,J as l,bS as t,p as r,b9 as s,M as d,o as i,c as o,w as u,b as n,t as c,x as p,O as v,e as m,$ as f,n as _,bR as x,bT as g,a as y,i as b,j as h,k,S,R as j}from"./index-3caf046d.js";import{_ as w}from"./u-input.2d8dc7a4.js";import{_ as V,a as C}from"./u-form.49dbb57f.js";import{_ as q}from"./u-switch.4d9ca48d.js";import{_ as $}from"./area-select.vue_vue_type_script_setup_true_lang.abe3938e.js";import{m as z}from"./manifest.ed582bbb.js";import{_ as A}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-line.69c0c00f.js";import"./u-loading-icon.255170b9.js";import"./u-popup.1b30ffa7.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-safe-bottom.98e092c5.js";const I=A(e({__name:"edit",setup(e){const A=a({id:0,name:"",mobile:"",province_id:0,city_id:0,district_id:0,lat:"",lng:"",address:"",address_name:"",full_address:"",is_default:0,area:""}),I=a(),M=a(null),R=a(""),T=a(!1),U=a(!1),W=a("address"),E=a(2);a(null),l((e=>{E.value=e.isSelectMap||"";const a=uni.getStorageSync("selectAddressCallback");if(e.id)t(e.id).then((e=>{e.data&&Object.assign(A.value,e.data),a&&(W.value="express"==a.delivery?"address":"locationAddress")}));else if(e.name){uni.getStorageSync("addressInfo")&&Object.assign(A.value,uni.getStorageSync("addressInfo")),A.value.address=e.name,F(e.latng);var l=G("latng").split(",");A.value.lat=l[0],A.value.lng=l[1]}R.value=e.source||"",a&&(W.value="express"==a.delivery?"address":"locationAddress")}));const O=r((()=>({name:{type:"string",required:!0,message:"请输入收货人姓名",trigger:["blur","change"]},mobile:[{type:"string",required:!0,message:"请输入手机号",trigger:["blur","change"]},{validator(e,a,l){/^1[3-9]\d{9}$/.test(a)?l():l(new Error("请输入正确的手机号"))}}],area:{validator(){let e=!0;return uni.$u.test.isEmpty(A.value.area)&&uni.$u.test.isEmpty(A.value.address_name)&&(e=!1),e},message:"请选择省市区"},address:{type:"string",required:!0,message:"请输入详细地址",trigger:["blur","change"]}}))),J=()=>{U.value=!0,I.value.open()},K=e=>{var a,l,t,r,s,d,i,o,u;!U.value||A.value.province_id!=(null==(a=e.province)?void 0:a.id)&&A.value.city_id==(null==(l=e.city)?void 0:l.id)&&A.value.district_id==(null==(t=e.district)?void 0:t.id)||(A.value.lat="",A.value.lng=""),A.value.province_id=(null==(r=e.province)?void 0:r.id)||0,A.value.city_id=(null==(s=e.city)?void 0:s.id)||0,A.value.district_id=(null==(d=e.district)?void 0:d.id)||0,A.value.area=`${(null==(i=e.province)?void 0:i.name)||""}${(null==(o=e.city)?void 0:o.name)||""}${(null==(u=e.district)?void 0:u.name)||""}`,U.value=!1},P=a(!1),B=()=>{const e=A.value.id?x:g;M.value.validate().then((()=>{if(!P.value){if(P.value=!0,T.value=!0,A.value.full_address=A.value.area+A.value.address,1==E.value&&!A.value.lat&&!A.value.lng)return d({title:"缺少经纬度，请在地图上重新选点",icon:"none"}),P.value=!1,T.value=!1,!1;e(A.value).then((e=>{P.value=!1,setTimeout((()=>{if(T.value=!1,"shop_order_payment"==R.value){const a=uni.getStorageSync("selectAddressCallback");a&&(a.address_id=e.data.id||A.value.id,uni.setStorage({key:"selectAddressCallback",data:a,success(){y({url:a.back,mode:"redirectTo"})}}))}else y({url:"/app/pages/member/address",mode:"redirectTo",param:{source:R.value}})}),1e3)})).catch((()=>{P.value=!1,T.value=!1}))}}))},D=()=>{var e=A.value;uni.setStorageSync("addressInfo",e);let a=location.origin+location.pathname+"?source="+R.value;E.value&&(a=a+"&isSelectMap="+E.value),window.location.href="https://apis.map.qq.com/tools/locpicker?search=1&type=0&backurl="+encodeURIComponent(a)+"&key="+z.h5.sdkConfigs.maps.qqmap.key+"&referer=myapp"},F=e=>{s({latlng:e}).then((e=>{e.data?(A.value.full_address="",A.value.full_address+=null!=e.data.province?e.data.province:"",A.value.full_address+=null!=e.data.city?""+e.data.city:"",A.value.full_address+=null!=e.data.district?""+e.data.district:"",A.value.address_name=A.value.full_address.replace(/-/g,""),A.value.area=e.data.province+e.data.city+e.data.district||e.data.full_address,A.value.province_id=null!=e.data.province_id?e.data.province_id:0,A.value.city_id=null!=e.data.city_id?e.data.city_id:0,A.value.district_id=null!=e.data.district_id?e.data.district_id:0):d({title:e.msg,icon:"none"})}))},G=e=>{for(var a=window.location.search.substring(1).split("&"),l=0;l<a.length;l++){var t=a[l].split("=");if(t[0]==e)return t[1]}return!1};return(e,a)=>{const l=b(h("u-input"),w),t=b(h("u-form-item"),V),r=k,s=S,d=b(h("u-switch"),q),x=b(h("u-form"),C),g=j,y=b(h("area-select"),$);return i(),o(r,{class:"bg-[var(--page-bg-color)] min-h-[100vh] overflow-hidden address-edit",style:_(e.themeColor())},{default:u((()=>[n(x,{labelPosition:"left",model:A.value,errorType:"toast",rules:m(O),ref_key:"formRef",ref:M},{default:u((()=>[n(r,{class:"sidebar-margin card-template mt-[var(--top-m)] py-[20rpx]"},{default:u((()=>[n(r,null,{default:u((()=>[n(t,{label:"收货人",prop:"name",labelWidth:"200rpx"},{default:u((()=>[n(l,{fontSize:"28rpx",modelValue:A.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>A.value.name=e),modelModifiers:{trim:!0},border:"none",clearable:"",maxlength:"25",placeholderStyle:"color: #888",placeholder:"请输入收货人姓名"},null,8,["modelValue"])])),_:1})])),_:1}),n(r,{class:"mt-[16rpx]"},{default:u((()=>[n(t,{label:"手机号",prop:"mobile",labelWidth:"200rpx"},{default:u((()=>[n(l,{fontSize:"28rpx",modelValue:A.value.mobile,"onUpdate:modelValue":a[1]||(a[1]=e=>A.value.mobile=e),modelModifiers:{trim:!0},maxlength:"11",border:"none",clearable:"",placeholder:"请输入手机号",placeholderStyle:"color: #888"},null,8,["modelValue"])])),_:1})])),_:1}),n(r,{class:"mt-[16rpx]"},{default:u((()=>[n(t,{label:"所在地区",prop:"area",labelWidth:"200rpx"},{default:u((()=>["address"==W.value&&1!=E.value?(i(),o(r,{key:0,class:"flex w-full items-center h-[52rpx]",onClick:J},{default:u((()=>[A.value.area?(i(),o(r,{key:1,class:"text-[28rpx] flex-1 leading-[1.4]"},{default:u((()=>[c(p(A.value.area),1)])),_:1})):(i(),o(r,{key:0,class:"text-[#888] text-[28rpx] flex-1"},{default:u((()=>[c("请选择省市区")])),_:1})),n(r,{onClick:v(D,["stop"]),class:"flex items-center"},{default:u((()=>[n(s,{class:"nc-iconfont nc-icon-dizhiguanliV6xx mr-[4rpx] text-[32rpx] text-[var(--primary-color)]"}),n(s,{class:"text-[24rpx] whitespace-nowrap text-[var(--primary-color)]"},{default:u((()=>[c("定位")])),_:1})])),_:1},8,["onClick"])])),_:1})):(i(),o(r,{key:1,class:"flex justify-between items-center flex-1 h-[52rpx]",onClick:D},{default:u((()=>[A.value.area||A.value.address_name?(i(),o(r,{key:0,class:"text-[28rpx] text-[#303133] leading-[1.4]"},{default:u((()=>[c(p(A.value.area||A.value.address_name),1)])),_:1})):(i(),o(r,{key:1,class:"text-[#888] text-[28rpx]"},{default:u((()=>[c("请选择地址")])),_:1})),n(r,{class:"flex items-center"},{default:u((()=>[n(s,{class:"nc-iconfont nc-icon-dizhiguanliV6xx text-[32rpx] mr-[4rpx] text-[var(--primary-color)]"}),n(s,{class:"text-[24rpx] whitespace-nowrap text-[var(--primary-color)]"},{default:u((()=>[c("定位")])),_:1})])),_:1})])),_:1}))])),_:1})])),_:1}),n(r,{class:"mt-[16rpx]"},{default:u((()=>[n(t,{label:"详细地址",prop:"address",labelWidth:"200rpx"},{default:u((()=>[n(l,{fontSize:"28rpx",modelValue:A.value.address,"onUpdate:modelValue":a[2]||(a[2]=e=>A.value.address=e),border:"none",clearable:"",maxlength:"120",placeholder:"请输入详细地址",placeholderStyle:"color: #888"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(r,{class:"sidebar-margin card-template mt-[var(--top-m)] py-[10rpx]"},{default:u((()=>[n(t,{label:"设为默认地址",prop:"name","border-bottom":!1,labelWidth:"200rpx"},{default:u((()=>[n(d,{modelValue:A.value.is_default,"onUpdate:modelValue":a[3]||(a[3]=e=>A.value.is_default=e),size:"20",activeValue:1,inactiveValue:0,activeColor:"var(--primary-color)",inactiveColor:"var(--temp-bg)"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["model","rules"]),n(r,{class:"w-full footer"},{default:u((()=>[n(r,{class:"py-[var(--top-m)] px-[var(--sidebar-m)] footer w-full fixed bottom-0 left-0 right-0 box-border"},{default:u((()=>[n(g,{"hover-class":"none",class:f(["primary-btn-bg !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",{"opacity-50":T.value}]),onClick:B,disabled:T.value,loading:P.value},{default:u((()=>[c("保存 ")])),_:1},8,["disabled","loading","class"])])),_:1})])),_:1}),n(y,{ref_key:"areaRef",ref:I,onComplete:K,"area-id":A.value.district_id||A.value.city_id},null,8,["area-id"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-566df9ad"]]);export{I as default};
