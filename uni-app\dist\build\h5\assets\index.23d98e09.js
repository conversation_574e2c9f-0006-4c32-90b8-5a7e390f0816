import{d as e,r as o,p as t,q as a,_ as r,s as n,o as i,c as l,w as s,g as m,f as d,v as f,e as u,b as p,n as c,M as _,k as g,b0 as y,b1 as v}from"./index-3caf046d.js";import{u as S}from"./useDiyForm.26962b36.js";import{d as b}from"./index.9de114a1.js";import{_ as h}from"./_plugin-vue_export-helper.1b428a4d.js";const T=h(e({__name:"index",props:["form_id","relate_id","storage_name","form_border"],setup(e,{expose:h}){const T=e,F=S({form_id:T.form_id,needLogin:!1}),x=o(null),k=t((()=>F.requestData)),N=a({});r((()=>{F.getData((()=>{var e;if(N.status=F.data.status,N.status){N.title=F.data.title,N.global=F.data.global,N.global&&(N.global.topStatusBar.isShow=!1,N.global.bottomTabBarSwitch=!1);let o=[];"none"==T.form_border&&(N.global.borderControl=!1),F.data.value.forEach((e=>{"diy_form"==e.componentType&&"FormSubmit"!=e.componentName&&o.push(e)})),N.value=o,N.componentRefs=null,null==(e=x.value)||e.refresh(),R()}}))}));const R=()=>{n((()=>N.value),((e,o)=>{if(e){let o={validTime:y(5),components:[]};e.forEach((e=>{if("diy_form"==e.componentType&&"FormSubmit"!=e.componentName&&e.field.cache){let t=v(e.field);delete t.remark,delete t.detailComponent,delete t.default,o.components.push({id:e.id,componentName:e.componentName,componentType:e.componentType,componentTitle:e.componentTitle,isHidden:e.isHidden,field:t})}})),o.components.length&&uni.setStorageSync("diyFormStorage_"+T.form_id,o)}}),{deep:!0})};return F.onHide(),F.onUnload(),h({verify:()=>{if(!N.status)return!0;if(!N.value)return!0;let e=!0,o=x.value.getFormRef().componentRefs;for(let a=0;a<N.value.length;a++){let t=N.value[a];if(t.field.required||t.field.value){let a=`diy${t.componentName}Ref`,r=!1;if(o[a]){for(let e=0;e<o[a].length;e++){let t=o[a][e].verify();if(t&&!t.code){r=!0,_({title:t.message,icon:"none"});break}}if(r){e=!1;break}}}}if(!e)return!1;const t={form_id:T.form_id,value:uni.getStorageSync("diyFormStorage_"+T.form_id),relate_id:T.relate_id||0};return T.storage_name&&uni.setStorageSync(T.storage_name,t),e},getData:()=>({form_id:T.form_id,value:N.value,relate_id:T.relate_id||0}),clearStorage:(e=[])=>{uni.removeStorageSync("diyFormStorage_"+T.form_id),T.storage_name&&uni.removeStorageSync(T.storage_name),e&&e.forEach((e=>{uni.removeStorageSync(e)}))}}),(e,o)=>{const t=g;return i(),l(t,{style:c(e.themeColor())},{default:s((()=>[m(" 自定义组件渲染 "),d(p(t,{class:"diy-template-wrap"},{default:s((()=>[p(b,{ref_key:"diyGroupRef",ref:x,data:N},null,8,["data"])])),_:1},512),[[f,1==u(k).status&&!u(F).getLoading()]])])),_:1},8,["style"])}}}),[["__scopeId","data-v-cfbaf382"]]);export{T as d};
