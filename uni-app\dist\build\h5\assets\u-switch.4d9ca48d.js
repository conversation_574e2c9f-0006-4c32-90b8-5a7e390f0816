import{ab as e,ac as t,ad as i,ae as s,af as a,i as l,j as o,o as c,c as n,w as r,b as h,n as d,$ as u,k as f}from"./index-3caf046d.js";import{_ as m}from"./u-loading-icon.255170b9.js";import{_ as g}from"./_plugin-vue_export-helper.1b428a4d.js";const p=g({name:"u-switch",mixins:[t,i,{props:{loading:{type:Boolean,default:()=>e.switch.loading},disabled:{type:Boolean,default:()=>e.switch.disabled},size:{type:[String,Number],default:()=>e.switch.size},activeColor:{type:String,default:()=>e.switch.activeColor},inactiveColor:{type:String,default:()=>e.switch.inactiveColor},modelValue:{type:[Boolean,String,Number],default:()=>e.switch.value},activeValue:{type:[String,Number,Boolean],default:()=>e.switch.activeValue},inactiveValue:{type:[String,Number,Boolean],default:()=>e.switch.inactiveValue},asyncChange:{type:Boolean,default:()=>e.switch.asyncChange},space:{type:[String,Number],default:()=>e.switch.space}}}],watch:{modelValue:{immediate:!0,handler(e){e!==this.inactiveValue&&this.activeValue}}},data:()=>({bgColor:"#ffffff"}),computed:{isActive(){return this.modelValue===this.activeValue},switchStyle(){let e={};return e.width=s(2*this.size+2),e.height=s(Number(this.size)+2),this.customInactiveColor&&(e.borderColor="rgba(0, 0, 0, 0)"),e.backgroundColor=this.isActive?this.activeColor:this.inactiveColor,e},nodeStyle(){let e={};e.width=s(this.size-this.space),e.height=s(this.size-this.space);const t=this.isActive?s(this.space):s(this.size);return e.transform=`translateX(-${t})`,e},bgStyle(){let e={};return e.width=s(2*Number(this.size)-this.size/2),e.height=s(this.size),e.backgroundColor=this.inactiveColor,e.transform=`scale(${this.isActive?0:1})`,e},customInactiveColor(){return"#fff"!==this.inactiveColor&&"#ffffff"!==this.inactiveColor}},emits:["update:modelValue","change"],methods:{addStyle:a,clickHandler(){if(!this.disabled&&!this.loading){const e=this.isActive?this.inactiveValue:this.activeValue;this.asyncChange||this.$emit("update:modelValue",e),this.$nextTick((()=>{this.$emit("change",e)}))}}}},[["render",function(e,t,i,s,a,g){const p=f,v=l(o("u-loading-icon"),m);return c(),n(p,{class:u(["u-switch cursor-pointer",[e.disabled&&"u-switch--disabled"]]),style:d([g.switchStyle,g.addStyle(e.customStyle)]),onClick:g.clickHandler},{default:r((()=>[h(p,{class:"u-switch__bg",style:d([g.bgStyle])},null,8,["style"]),h(p,{class:u(["u-switch__node",[e.modelValue&&"u-switch__node--on"]]),style:d([g.nodeStyle]),ref:"u-switch__node"},{default:r((()=>[h(v,{show:e.loading,mode:"circle",timingFunction:"linear",color:e.modelValue?e.activeColor:"#AAABAD",size:.6*e.size},null,8,["show","color","size"])])),_:1},8,["class","style"])])),_:1},8,["class","style","onClick"])}],["__scopeId","data-v-09bf48b9"]]);export{p as _};
