import{bA as o,bB as t,o as e,c as n,$ as s,n as i,g as l,Q as r,b6 as p,a9 as c,av as a,ax as h,bC as d,bD as u,a8 as w,w as g,b as f,t as m,x as y,a7 as S,f as D,v as T,k as x}from"./index-3caf046d.js";import{m as U,G as b}from"./mescroll-i18n.e7c22011.js";import{_ as v}from"./_plugin-vue_export-helper.1b428a4d.js";function B(o,t){let e=this;e.version="1.3.7",e.options=o||{},e.isScrollBody=t||!1,e.isDownScrolling=!1,e.isUpScrolling=!1;let n=e.options.down&&e.options.down.callback;e.initDownScroll(),e.initUpScroll(),setTimeout((function(){(e.optDown.use||e.optDown.native)&&e.optDown.auto&&n&&(e.optDown.autoShowLoading?e.triggerDownScroll():e.optDown.callback&&e.optDown.callback(e)),e.isUpAutoLoad||setTimeout((function(){e.optUp.use&&e.optUp.auto&&!e.isUpAutoLoad&&e.triggerUpScroll()}),100)}),30)}B.prototype.extendDownScroll=function(o){B.extend(o,{use:!0,auto:!0,native:!1,autoShowLoading:!1,isLock:!1,offset:80,startTop:100,inOffsetRate:1,outOffsetRate:.2,bottomOffset:20,minAngle:45,textInOffset:"下拉刷新",textOutOffset:"释放更新",textLoading:"加载中 ...",textSuccess:"加载成功",textErr:"加载失败",beforeEndDelay:0,bgColor:"transparent",textColor:"gray",inited:null,inOffset:null,outOffset:null,onMoving:null,beforeLoading:null,showLoading:null,afterLoading:null,beforeEndDownScroll:null,endDownScroll:null,afterEndDownScroll:null,callback:function(o){o.resetUpScroll()}})},B.prototype.extendUpScroll=function(o){B.extend(o,{use:!0,auto:!0,isLock:!1,isBoth:!0,callback:null,page:{num:0,size:10,time:null},noMoreSize:5,offset:150,textLoading:"加载中 ...",textNoMore:"",bgColor:"transparent",textColor:"gray",inited:null,showLoading:null,showNoMore:null,hideUpScroll:null,errDistance:60,toTop:{src:null,offset:1e3,duration:300,btnClick:null,onShow:null,zIndex:9990,left:null,right:20,bottom:120,safearea:!1,width:72,radius:"50%"},empty:{use:!0,icon:null,tip:"~ 暂无相关数据 ~",btnText:"",btnClick:null,onShow:null,fixed:!1,top:"100rpx",zIndex:99},onScroll:!1})},B.extend=function(o,t){if(!o)return t;for(let e in t)if(null==o[e]){let n=t[e];o[e]=null!=n&&"object"==typeof n?B.extend({},n):n}else"object"==typeof o[e]&&B.extend(o[e],t[e]);return o},B.prototype.hasColor=function(o){if(!o)return!1;let t=o.toLowerCase();return"#fff"!=t&&"#ffffff"!=t&&"transparent"!=t&&"white"!=t},B.prototype.initDownScroll=function(){let o=this;o.optDown=o.options.down||{},!o.optDown.textColor&&o.hasColor(o.optDown.bgColor)&&(o.optDown.textColor="#fff"),o.extendDownScroll(o.optDown),o.isScrollBody&&o.optDown.native?o.optDown.use=!1:o.optDown.native=!1,o.downHight=0,o.optDown.use&&o.optDown.inited&&setTimeout((function(){o.optDown.inited(o)}),0)},B.prototype.touchstartEvent=function(o){this.optDown.use&&(this.startPoint=this.getPoint(o),this.startTop=this.getScrollTop(),this.startAngle=0,this.lastPoint=this.startPoint,this.maxTouchmoveY=this.getBodyHeight()-this.optDown.bottomOffset,this.inTouchend=!1)},B.prototype.touchmoveEvent=function(o){if(!this.optDown.use)return;let t=this,e=t.getScrollTop(),n=t.getPoint(o);if(n.y-t.startPoint.y>0&&(t.isScrollBody&&e<=0||!t.isScrollBody&&(e<=0||e<=t.optDown.startTop&&e===t.startTop))&&!t.inTouchend&&!t.isDownScrolling&&!t.optDown.isLock&&(!t.isUpScrolling||t.isUpScrolling&&t.optUp.isBoth)){if(t.startAngle||(t.startAngle=t.getAngle(t.lastPoint,n)),t.startAngle<t.optDown.minAngle)return;if(t.maxTouchmoveY>0&&n.y>=t.maxTouchmoveY)return t.inTouchend=!0,void t.touchendEvent();t.preventDefault(o);let e=n.y-t.lastPoint.y;t.downHight<t.optDown.offset?(1!==t.movetype&&(t.movetype=1,t.isDownEndSuccess=null,t.optDown.inOffset&&t.optDown.inOffset(t),t.isMoveDown=!0),t.downHight+=e*t.optDown.inOffsetRate):(2!==t.movetype&&(t.movetype=2,t.optDown.outOffset&&t.optDown.outOffset(t),t.isMoveDown=!0),t.downHight+=e>0?e*t.optDown.outOffsetRate:e),t.downHight=Math.round(t.downHight);let s=t.downHight/t.optDown.offset;t.optDown.onMoving&&t.optDown.onMoving(t,s,t.downHight)}t.lastPoint=n},B.prototype.touchendEvent=function(o){if(this.optDown.use)if(this.isMoveDown)this.downHight>=this.optDown.offset?this.triggerDownScroll():(this.downHight=0,this.endDownScrollCall(this)),this.movetype=0,this.isMoveDown=!1;else if(!this.isScrollBody&&this.getScrollTop()===this.startTop){if(this.getPoint(o).y-this.startPoint.y<0){this.getAngle(this.getPoint(o),this.startPoint)>80&&this.triggerUpScroll(!0)}}},B.prototype.getPoint=function(o){return o?o.touches&&o.touches[0]?{x:o.touches[0].pageX,y:o.touches[0].pageY}:o.changedTouches&&o.changedTouches[0]?{x:o.changedTouches[0].pageX,y:o.changedTouches[0].pageY}:{x:o.clientX,y:o.clientY}:{x:0,y:0}},B.prototype.getAngle=function(o,t){let e=Math.abs(o.x-t.x),n=Math.abs(o.y-t.y),s=Math.sqrt(e*e+n*n),i=0;return 0!==s&&(i=Math.asin(n/s)/Math.PI*180),i},B.prototype.triggerDownScroll=function(){this.optDown.beforeLoading&&this.optDown.beforeLoading(this)||(this.showDownScroll(),!this.optDown.native&&this.optDown.callback&&this.optDown.callback(this))},B.prototype.showDownScroll=function(){this.isDownScrolling=!0,this.optDown.native?(o(),this.showDownLoadingCall(0)):(this.downHight=this.optDown.offset,this.showDownLoadingCall(this.downHight))},B.prototype.showDownLoadingCall=function(o){this.optDown.showLoading&&this.optDown.showLoading(this,o),this.optDown.afterLoading&&this.optDown.afterLoading(this,o)},B.prototype.onPullDownRefresh=function(){this.isDownScrolling=!0,this.showDownLoadingCall(0),this.optDown.callback&&this.optDown.callback(this)},B.prototype.endDownScroll=function(){if(this.optDown.native)return this.isDownScrolling=!1,this.endDownScrollCall(this),void t();let o=this,e=function(){o.downHight=0,o.isDownScrolling=!1,o.endDownScrollCall(o),o.isScrollBody||(o.setScrollHeight(0),o.scrollTo(0,0))},n=0;o.optDown.beforeEndDownScroll&&(n=o.optDown.beforeEndDownScroll(o),null==o.isDownEndSuccess&&(n=0)),"number"==typeof n&&n>0?setTimeout(e,n):e()},B.prototype.endDownScrollCall=function(){this.optDown.endDownScroll&&this.optDown.endDownScroll(this),this.optDown.afterEndDownScroll&&this.optDown.afterEndDownScroll(this)},B.prototype.lockDownScroll=function(o){null==o&&(o=!0),this.optDown.isLock=o},B.prototype.lockUpScroll=function(o){null==o&&(o=!0),this.optUp.isLock=o},B.prototype.initUpScroll=function(){let o=this;o.optUp=o.options.up||{use:!1},!o.optUp.textColor&&o.hasColor(o.optUp.bgColor)&&(o.optUp.textColor="#fff"),o.extendUpScroll(o.optUp),!1!==o.optUp.use&&(o.optUp.hasNext=!0,o.startNum=o.optUp.page.num+1,o.optUp.inited&&setTimeout((function(){o.optUp.inited(o)}),0))},B.prototype.onReachBottom=function(){this.isScrollBody&&!this.isUpScrolling&&!this.optUp.isLock&&this.optUp.hasNext&&this.triggerUpScroll()},B.prototype.onPageScroll=function(o){this.isScrollBody&&(this.setScrollTop(o.scrollTop),o.scrollTop>=this.optUp.toTop.offset?this.showTopBtn():this.hideTopBtn())},B.prototype.scroll=function(o,t){this.setScrollTop(o.scrollTop),this.setScrollHeight(o.scrollHeight),null==this.preScrollY&&(this.preScrollY=0),this.isScrollUp=o.scrollTop-this.preScrollY>0,this.preScrollY=o.scrollTop,this.isScrollUp&&this.triggerUpScroll(!0),o.scrollTop>=this.optUp.toTop.offset?this.showTopBtn():this.hideTopBtn(),this.optUp.onScroll&&t&&t()},B.prototype.triggerUpScroll=function(o){if(!this.isUpScrolling&&this.optUp.use&&this.optUp.callback){if(!0===o){let o=!1;if(!this.optUp.hasNext||this.optUp.isLock||this.isDownScrolling||this.getScrollBottom()<=this.optUp.offset&&(o=!0),!1===o)return}this.showUpScroll(),this.optUp.page.num++,this.isUpAutoLoad=!0,this.num=this.optUp.page.num,this.size=this.optUp.page.size,this.time=this.optUp.page.time,this.optUp.callback(this)}},B.prototype.showUpScroll=function(){this.isUpScrolling=!0,this.optUp.showLoading&&this.optUp.showLoading(this)},B.prototype.showNoMore=function(){this.optUp.hasNext=!1,this.optUp.showNoMore&&this.optUp.showNoMore(this)},B.prototype.hideUpScroll=function(){this.optUp.hideUpScroll&&this.optUp.hideUpScroll(this)},B.prototype.endUpScroll=function(o){null!=o&&(o?this.showNoMore():this.hideUpScroll()),this.isUpScrolling=!1},B.prototype.resetUpScroll=function(o){if(this.optUp&&this.optUp.use){let t=this.optUp.page;this.prePageNum=t.num,this.prePageTime=t.time,t.num=this.startNum,t.time=null,this.isDownScrolling||!1===o||(null==o?(this.removeEmpty(),this.showUpScroll()):this.showDownScroll()),this.isUpAutoLoad=!0,this.num=t.num,this.size=t.size,this.time=t.time,this.optUp.callback&&this.optUp.callback(this)}},B.prototype.setPageNum=function(o){this.optUp.page.num=o-1},B.prototype.setPageSize=function(o){this.optUp.page.size=o},B.prototype.endByPage=function(o,t,e){let n;this.optUp.use&&null!=t&&(n=this.optUp.page.num<t),this.endSuccess(o,n,e)},B.prototype.endBySize=function(o,t,e){let n;if(this.optUp.use&&null!=t){n=(this.optUp.page.num-1)*this.optUp.page.size+o<t}this.endSuccess(o,n,e)},B.prototype.endSuccess=function(o,t,e){let n=this;if(n.isDownScrolling&&(n.isDownEndSuccess=!0,n.endDownScroll()),n.optUp.use){let s;if(null!=o){let i=n.optUp.page.num,l=n.optUp.page.size;if(1===i&&e&&(n.optUp.page.time=e),o<l||!1===t)if(n.optUp.hasNext=!1,0===o&&1===i)s=!1,n.showEmpty();else{s=!((i-1)*l+o<n.optUp.noMoreSize),n.removeEmpty()}else s=!1,n.optUp.hasNext=!0,n.removeEmpty()}n.endUpScroll(s)}},B.prototype.endErr=function(o){if(this.isDownScrolling){this.isDownEndSuccess=!1;let o=this.optUp.page;o&&this.prePageNum&&(o.num=this.prePageNum,o.time=this.prePageTime),this.endDownScroll()}this.isUpScrolling&&(this.optUp.page.num--,this.endUpScroll(!1),this.isScrollBody&&0!==o&&(o||(o=this.optUp.errDistance),this.scrollTo(this.getScrollTop()-o,0)))},B.prototype.showEmpty=function(){this.optUp.empty.use&&this.optUp.empty.onShow&&this.optUp.empty.onShow(!0)},B.prototype.removeEmpty=function(){this.optUp.empty.use&&this.optUp.empty.onShow&&this.optUp.empty.onShow(!1)},B.prototype.showTopBtn=function(){this.topBtnShow||(this.topBtnShow=!0,this.optUp.toTop.onShow&&this.optUp.toTop.onShow(!0))},B.prototype.hideTopBtn=function(){this.topBtnShow&&(this.topBtnShow=!1,this.optUp.toTop.onShow&&this.optUp.toTop.onShow(!1))},B.prototype.getScrollTop=function(){return this.scrollTop||0},B.prototype.setScrollTop=function(o){this.scrollTop=o},B.prototype.scrollTo=function(o,t){this.myScrollTo&&this.myScrollTo(o,t)},B.prototype.resetScrollTo=function(o){this.myScrollTo=o},B.prototype.getScrollBottom=function(){return this.getScrollHeight()-this.getClientHeight()-this.getScrollTop()},B.prototype.getStep=function(o,t,e,n,s){let i=t-o;if(0===n||0===i)return void(e&&e(t));let l=(n=n||300)/(s=s||30),r=i/l,p=0,c=setInterval((function(){p<l-1?(o+=r,e&&e(o,c),p++):(e&&e(t,c),clearInterval(c))}),s)},B.prototype.getClientHeight=function(o){let t=this.clientHeight||0;return 0===t&&!0!==o&&(t=this.getBodyHeight()),t},B.prototype.setClientHeight=function(o){this.clientHeight=o},B.prototype.getScrollHeight=function(){return this.scrollHeight||0},B.prototype.setScrollHeight=function(o){this.scrollHeight=o},B.prototype.getBodyHeight=function(){return this.bodyHeight||0},B.prototype.setBodyHeight=function(o){this.bodyHeight=o},B.prototype.preventDefault=function(o){o&&o.cancelable&&!o.defaultPrevented&&o.preventDefault()};const L=v({props:{option:Object,value:!1},computed:{mOption(){return this.option||{}},left(){return this.mOption.left?this.addUnit(this.mOption.left):"auto"},right(){return this.mOption.left?"auto":this.addUnit(this.mOption.right)}},methods:{addUnit:o=>o?"number"==typeof o?o+"rpx":o:0,toTopClick(){this.$emit("input",!1),this.$emit("click")}}},[["render",function(o,t,p,c,a,h){const d=r;return h.mOption.src?(e(),n(d,{key:0,class:s(["mescroll-totop",[p.value?"mescroll-totop-in":"mescroll-totop-out",{"mescroll-totop-safearea":h.mOption.safearea}]]),style:i({"z-index":h.mOption.zIndex,left:h.left,right:h.right,bottom:h.addUnit(h.mOption.bottom),width:h.addUnit(h.mOption.width),"border-radius":h.addUnit(h.mOption.radius)}),src:h.mOption.src,mode:"widthFix",onClick:h.toTopClick},null,8,["class","style","src","onClick"])):l("v-if",!0)}],["__scopeId","data-v-335a2c6e"]]),H={data:()=>({wxsProp:{optDown:{},scrollTop:0,bodyHeight:0,isDownScrolling:!1,isUpScrolling:!1,isScrollBody:!0,isUpBoth:!0,t:0},callProp:{callType:"",t:0}}),methods:{wxsCall(o){"setWxsProp"===o.type?this.wxsProp={optDown:this.mescroll.optDown,scrollTop:this.mescroll.getScrollTop(),bodyHeight:this.mescroll.getBodyHeight(),isDownScrolling:this.mescroll.isDownScrolling,isUpScrolling:this.mescroll.isUpScrolling,isUpBoth:this.mescroll.optUp.isBoth,isScrollBody:this.mescroll.isScrollBody,t:Date.now()}:"setLoadType"===o.type?(this.downLoadType=o.downLoadType,this.$set(this.mescroll,"downLoadType",this.downLoadType),this.$set(this.mescroll,"isDownEndSuccess",null)):"triggerDownScroll"===o.type?this.mescroll.triggerDownScroll():"endDownScroll"===o.type?this.mescroll.endDownScroll():"triggerUpScroll"===o.type&&this.mescroll.triggerUpScroll(!0)}},mounted(){this.mescroll.optDown.afterLoading=()=>{this.callProp={callType:"showLoading",t:Date.now()}},this.mescroll.optDown.afterEndDownScroll=()=>{this.callProp={callType:"endDownScroll",t:Date.now()};let o=300+(this.mescroll.optDown.beforeEndDelay||0);setTimeout((()=>{4!==this.downLoadType&&0!==this.downLoadType||(this.callProp={callType:"clearTransform",t:Date.now()}),this.$set(this.mescroll,"downLoadType",this.downLoadType)}),o)},this.wxsCall({type:"setWxsProp"})}};var P={};function k(o,t){if(P.isMoveDown)P.downHight>=P.optDown.offset?(P.downHight=P.optDown.offset,P.callMethod(t,{type:"triggerDownScroll"})):(P.downHight=0,P.callMethod(t,{type:"endDownScroll"})),P.movetype=0,P.isMoveDown=!1;else if(!P.isScrollBody&&P.getScrollTop()===P.startTop){if(P.getPoint(o).y-P.startPoint.y<0)P.getAngle(P.getPoint(o),P.startPoint)>80&&P.callMethod(t,{type:"triggerUpScroll"})}P.callMethod(t,{type:"setWxsProp"})}P.onMoving=function(o,t,e){o.requestAnimationFrame((function(){o.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"transform",transform:"translateY("+e+"px)",transition:""});var n=o.selectComponent(".mescroll-wxs-progress");n&&n.setStyle({transform:"rotate("+360*t+"deg)"})}))},P.showLoading=function(o){P.downHight=P.optDown.offset,o.requestAnimationFrame((function(){o.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"auto",transform:"translateY("+P.downHight+"px)",transition:"transform 300ms"})}))},P.endDownScroll=function(o){P.downHight=0,P.isDownScrolling=!1,o.requestAnimationFrame((function(){o.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"auto",transform:"translateY(0)",transition:"transform 300ms"})}))},P.clearTransform=function(o){o.requestAnimationFrame((function(){o.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"",transform:"",transition:""})}))},P.disabled=function(){return!P.optDown||!P.optDown.use||P.optDown.native},P.getPoint=function(o){return o?o.touches&&o.touches[0]?{x:o.touches[0].pageX,y:o.touches[0].pageY}:o.changedTouches&&o.changedTouches[0]?{x:o.changedTouches[0].pageX,y:o.changedTouches[0].pageY}:{x:o.clientX,y:o.clientY}:{x:0,y:0}},P.getAngle=function(o,t){var e=Math.abs(o.x-t.x),n=Math.abs(o.y-t.y),s=Math.sqrt(e*e+n*n),i=0;return 0!==s&&(i=Math.asin(n/s)/Math.PI*180),i},P.getScrollTop=function(){return P.scrollTop||0},P.getBodyHeight=function(){return P.bodyHeight||0},P.callMethod=function(o,t){o&&o.callMethod("wxsCall",t)};const O={propObserver:function(o){P.optDown=o.optDown,P.scrollTop=o.scrollTop,P.bodyHeight=o.bodyHeight,P.isDownScrolling=o.isDownScrolling,P.isUpScrolling=o.isUpScrolling,P.isUpBoth=o.isUpBoth,P.isScrollBody=o.isScrollBody,P.startTop=o.scrollTop},callObserver:function(o,t,e){P.disabled()||o.callType&&("showLoading"===o.callType?P.showLoading(e):"endDownScroll"===o.callType?P.endDownScroll(e):"clearTransform"===o.callType&&P.clearTransform(e))},touchstartEvent:function(o,t){P.downHight=0,P.startPoint=P.getPoint(o),P.startTop=P.getScrollTop(),P.startAngle=0,P.lastPoint=P.startPoint,P.maxTouchmoveY=P.getBodyHeight()-P.optDown.bottomOffset,P.inTouchend=!1,P.callMethod(t,{type:"setWxsProp"})},touchmoveEvent:function(o,t){var e=!0;if(P.disabled())return e;var n=P.getScrollTop(),s=P.getPoint(o);if(s.y-P.startPoint.y>0&&(P.isScrollBody&&n<=0||!P.isScrollBody&&(n<=0||n<=P.optDown.startTop&&n===P.startTop))&&!P.inTouchend&&!P.isDownScrolling&&!P.optDown.isLock&&(!P.isUpScrolling||P.isUpScrolling&&P.isUpBoth)){if(P.startAngle||(P.startAngle=P.getAngle(P.lastPoint,s)),P.startAngle<P.optDown.minAngle)return e;if(P.maxTouchmoveY>0&&s.y>=P.maxTouchmoveY)return P.inTouchend=!0,k(o,t),e;e=!1;var i=s.y-P.lastPoint.y;P.downHight<P.optDown.offset?(1!==P.movetype&&(P.movetype=1,P.callMethod(t,{type:"setLoadType",downLoadType:1}),P.isMoveDown=!0),P.downHight+=i*P.optDown.inOffsetRate):(2!==P.movetype&&(P.movetype=2,P.callMethod(t,{type:"setLoadType",downLoadType:2}),P.isMoveDown=!0),P.downHight+=i>0?i*P.optDown.outOffsetRate:i),P.downHight=Math.round(P.downHight);var l=P.downHight/P.optDown.offset;P.onMoving(t,l,P.downHight)}return P.lastPoint=s,e},touchendEvent:k},C=o=>{o.$wxs||(o.$wxs=[]),o.$wxs.push("wxsBiz"),o.mixins||(o.mixins=[]),o.mixins.push({beforeCreate(){this.wxsBiz=O}})};var M={};function E(o){M.optDown=o.optDown,M.scrollTop=o.scrollTop,M.isDownScrolling=o.isDownScrolling,M.isUpScrolling=o.isUpScrolling,M.isUpBoth=o.isUpBoth}window&&!window.$mescrollRenderInit&&(window.$mescrollRenderInit=!0,window.addEventListener("touchstart",(function(o){M.disabled()||(M.startPoint=M.getPoint(o))}),{passive:!0}),window.addEventListener("touchmove",(function(o){if(!M.disabled()&&(!(M.getScrollTop()>0)&&M.getPoint(o).y-M.startPoint.y>0&&!M.isDownScrolling&&!M.optDown.isLock&&(!M.isUpScrolling||M.isUpScrolling&&M.isUpBoth))){for(var t=o.target,e=!1;t&&t.tagName&&"UNI-PAGE-BODY"!==t.tagName&&"BODY"!=t.tagName;){var n=t.classList;if(n&&n.contains("mescroll-render-touch")){e=!0;break}t=t.parentNode}e&&o.cancelable&&!o.defaultPrevented&&o.preventDefault()}}),{passive:!1})),M.getScrollTop=function(){return M.scrollTop||0},M.disabled=function(){return!M.optDown||!M.optDown.use||M.optDown.native},M.getPoint=function(o){return o?o.touches&&o.touches[0]?{x:o.touches[0].pageX,y:o.touches[0].pageY}:o.changedTouches&&o.changedTouches[0]?{x:o.changedTouches[0].pageX,y:o.changedTouches[0].pageY}:{x:o.clientX,y:o.clientY}:{x:0,y:0}};const N={mixins:[{data:()=>({propObserver:E})}]},z=o=>{o.$renderjs||(o.$renderjs=[]),o.$renderjs.push("renderBiz"),o.mixins||(o.mixins=[]),o.mixins.push({beforeCreate(){this.renderBiz=this},mounted(){this.$ownerInstance=this.$gcd(this,!0)}}),o.mixins.push(N)},A={name:"mescroll-body",mixins:[H],components:{MescrollTop:L},props:{down:Object,up:Object,i18n:Object,top:[String,Number],topbar:[Boolean,String],bottom:[String,Number],safearea:Boolean,height:[String,Number],bottombar:{type:Boolean,default:!0},sticky:Boolean},data:()=>({mescroll:{optDown:{},optUp:{}},downHight:0,downRate:0,downLoadType:0,upLoadType:0,isShowEmpty:!1,isShowToTop:!1,windowHeight:0,windowBottom:0,statusBarHeight:0}),computed:{minHeight(){return this.toPx(this.height||"100%")+"px"},numTop(){return this.toPx(this.top)},padTop(){return this.numTop+"px"},numBottom(){return this.toPx(this.bottom)},padBottom(){return this.numBottom+"px"},isDownReset(){return 3===this.downLoadType||4===this.downLoadType},transition(){return this.isDownReset?"transform 300ms":""},translateY(){return this.downHight>0?"translateY("+this.downHight+"px)":""},isDownLoading(){return 3===this.downLoadType},downRotate(){return"rotate("+360*this.downRate+"deg)"},downText(){if(!this.mescroll)return"";switch(this.downLoadType){case 1:default:return this.mescroll.optDown.textInOffset;case 2:return this.mescroll.optDown.textOutOffset;case 3:return this.mescroll.optDown.textLoading;case 4:return this.mescroll.isDownEndSuccess?this.mescroll.optDown.textSuccess:0==this.mescroll.isDownEndSuccess?this.mescroll.optDown.textErr:this.mescroll.optDown.textInOffset}}},methods:{toPx(o){if("string"==typeof o)if(-1!==o.indexOf("px"))if(-1!==o.indexOf("rpx"))o=o.replace("rpx","");else{if(-1===o.indexOf("upx"))return Number(o.replace("px",""));o=o.replace("upx","")}else if(-1!==o.indexOf("%")){let t=Number(o.replace("%",""))/100;return this.windowHeight*t}return o?p(Number(o)):0},emptyClick(){this.$emit("emptyclick",this.mescroll)},toTopClick(){this.mescroll.scrollTo(0,this.mescroll.optUp.toTop.duration),this.$emit("topclick",this.mescroll)}},created(){let o=this,t={down:{inOffset(){o.downLoadType=1},outOffset(){o.downLoadType=2},onMoving(t,e,n){o.downHight=n,o.downRate=e},showLoading(t,e){o.downLoadType=3,o.downHight=e},beforeEndDownScroll:t=>(o.downLoadType=4,t.optDown.beforeEndDelay),endDownScroll(){o.downLoadType=4,o.downHight=0,o.downResetTimer&&(clearTimeout(o.downResetTimer),o.downResetTimer=null),o.downResetTimer=setTimeout((()=>{4===o.downLoadType&&(o.downLoadType=0)}),300)},callback:function(t){o.$emit("down",t)}},up:{showLoading(){o.upLoadType=1},showNoMore(){o.upLoadType=2},hideUpScroll(t){o.upLoadType=t.optUp.hasNext?0:3},empty:{onShow(t){o.isShowEmpty=t}},toTop:{onShow(t){o.isShowToTop=t}},callback:function(t){o.$emit("up",t)}}},e=U.getType(),n={type:e};B.extend(n,o.i18n),B.extend(n,b.i18n),B.extend(t,n[e]),B.extend(t,{down:b.down,up:b.up});let s=JSON.parse(JSON.stringify({down:o.down,up:o.up}));B.extend(s,t),o.mescroll=new B(s,!0),o.mescroll.i18n=n,o.$emit("init",o.mescroll);const i=c();i.windowHeight&&(o.windowHeight=i.windowHeight),i.windowBottom&&(o.windowBottom=i.windowBottom),i.statusBarHeight&&(o.statusBarHeight=i.statusBarHeight),o.mescroll.setBodyHeight(i.windowHeight),o.mescroll.resetScrollTo(((t,e)=>{"string"==typeof t?setTimeout((()=>{let n;-1==t.indexOf("#")&&-1==t.indexOf(".")?n="#"+t:(n=t,-1!=t.indexOf(">>>")&&(n=t.split(">>>")[1].trim())),a().select(n).boundingClientRect((function(t){if(t){let n=t.top;n+=o.mescroll.getScrollTop(),h({scrollTop:n,duration:e})}else console.error(n+" does not exist")})).exec()}),30):h({scrollTop:t,duration:e})})),o.up&&o.up.toTop&&null!=o.up.toTop.safearea||(o.mescroll.optUp.toTop.safearea=o.safearea),d("setMescrollGlobalOption",(t=>{if(!t)return;let e=t.i18n?t.i18n.type:null;if(e&&o.mescroll.i18n.type!=e&&(o.mescroll.i18n.type=e,U.setType(e),B.extend(t,o.mescroll.i18n[e])),t.down){let e=B.extend({},t.down);o.mescroll.optDown=B.extend(e,o.mescroll.optDown)}if(t.up){let e=B.extend({},t.up);o.mescroll.optUp=B.extend(e,o.mescroll.optUp)}}))},destroyed(){u("setMescrollGlobalOption")}};C(A),z(A);const Y=v(A,[["render",function(o,t,r,p,c,a){const h=x,d=w("mescroll-empty"),u=w("mescroll-top");return o.wxsProp?(e(),n(h,{key:0,class:s(["mescroll-body mescroll-render-touch",{"mescorll-sticky":r.sticky}]),style:i({minHeight:a.minHeight,"padding-top":a.padTop,"padding-bottom":a.padBottom}),onTouchstart:o.wxsBiz.touchstartEvent,onTouchmove:o.wxsBiz.touchmoveEvent,onTouchend:o.wxsBiz.touchendEvent,onTouchcancel:o.wxsBiz.touchendEvent,"change:prop":o.wxsBiz.propObserver,prop:o.wxsProp},{default:g((()=>[l(" 状态栏 "),r.topbar&&c.statusBarHeight?(e(),n(h,{key:0,class:"mescroll-topbar",style:i({height:c.statusBarHeight+"px",background:r.topbar})},null,8,["style"])):l("v-if",!0),f(h,{class:"mescroll-body-content mescroll-wxs-content",style:i({transform:a.translateY,transition:a.transition}),"change:prop":o.wxsBiz.callObserver,prop:o.callProp},{default:g((()=>[l(" 下拉加载区域 (支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-down组件实现)"),l(' <mescroll-down :option="mescroll.optDown" :type="downLoadType" :rate="downRate"></mescroll-down> '),c.mescroll.optDown.use?(e(),n(h,{key:0,class:"mescroll-downwarp",style:i({background:c.mescroll.optDown.bgColor,color:c.mescroll.optDown.textColor})},{default:g((()=>[f(h,{class:"downwarp-content"},{default:g((()=>[f(h,{class:s(["downwarp-progress mescroll-wxs-progress",{"mescroll-rotate":a.isDownLoading}]),style:i({"border-color":c.mescroll.optDown.textColor,transform:a.downRotate})},null,8,["class","style"]),f(h,{class:"downwarp-tip"},{default:g((()=>[m(y(a.downText),1)])),_:1})])),_:1})])),_:1},8,["style"])):l("v-if",!0),l(" 列表内容 "),S(o.$slots,"default",{},void 0,!0),l(" 空布局 "),c.isShowEmpty?(e(),n(d,{key:1,option:c.mescroll.optUp.empty,onEmptyclick:a.emptyClick},null,8,["option","onEmptyclick"])):l("v-if",!0),l(" 上拉加载区域 (下拉刷新时不显示, 支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-up组件实现)"),l(' <mescroll-up v-if="mescroll.optUp.use && !isDownLoading && upLoadType!==3" :option="mescroll.optUp" :type="upLoadType"></mescroll-up> '),c.mescroll.optUp.use&&!a.isDownLoading&&3!==c.upLoadType?(e(),n(h,{key:2,class:"mescroll-upwarp",style:i({background:c.mescroll.optUp.bgColor,color:c.mescroll.optUp.textColor})},{default:g((()=>[l(" 加载中 (此处不能用v-if,否则android小程序快速上拉可能会不断触发上拉回调) "),D(f(h,null,{default:g((()=>[f(h,{class:"upwarp-progress mescroll-rotate",style:i({"border-color":c.mescroll.optUp.textColor})},null,8,["style"]),f(h,{class:"upwarp-tip"},{default:g((()=>[m(y(c.mescroll.optUp.textLoading),1)])),_:1})])),_:1},512),[[T,1===c.upLoadType]]),l(" 无数据 "),2===c.upLoadType?(e(),n(h,{key:0,class:"upwarp-nodata"},{default:g((()=>[m(y(c.mescroll.optUp.textNoMore),1)])),_:1})):l("v-if",!0)])),_:1},8,["style"])):l("v-if",!0)])),_:3},8,["style","change:prop","prop"]),l(" 底部是否偏移TabBar的高度(默认仅在H5端的tab页生效) "),r.bottombar&&c.windowBottom>0?(e(),n(h,{key:1,class:"mescroll-bottombar",style:i({height:c.windowBottom+"px"})},null,8,["style"])):l("v-if",!0),l(" 适配iPhoneX "),r.safearea?(e(),n(h,{key:2,class:"mescroll-safearea"})):l("v-if",!0),l(" 回到顶部按钮 (fixed元素需写在transform外面,防止降级为absolute)"),f(u,{modelValue:c.isShowToTop,"onUpdate:modelValue":t[0]||(t[0]=o=>c.isShowToTop=o),option:c.mescroll.optUp.toTop,onClick:a.toTopClick},null,8,["modelValue","option","onClick"]),l(" renderjs的数据载体,不可写在mescroll-downwarp内部,避免use为false时,载体丢失,无法更新数据 "),f(h,{"change:prop":o.renderBiz.propObserver,prop:o.wxsProp},null,8,["change:prop","prop"])])),_:3},8,["class","style","onTouchstart","onTouchmove","onTouchend","onTouchcancel","change:prop","prop"])):l("v-if",!0)}],["__scopeId","data-v-8f805502"]]);export{Y as M};
