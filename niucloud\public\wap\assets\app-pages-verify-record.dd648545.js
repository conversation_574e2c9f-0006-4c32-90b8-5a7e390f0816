import{d as e,r as t,J as a,o as l,c as s,w as r,b as o,O as c,g as n,y as i,z as p,F as x,e as u,n as d,am as m,an as f,S as _,al as v,k as g,i as h,j as y,t as b,x as j,ao as k,A as w,a as C,Q as V}from"./index-3caf046d.js";import{_ as z}from"./u--image.eb573bce.js";import{M as I}from"./mescroll-body.36f14dc3.js";import{M}from"./mescroll-empty.d02c7bd6.js";import{u as S}from"./useMescroll.26ccf5de.js";import{c as U}from"./verify.8c605f2d.js";import{s as F}from"./select-date.b993e54c.js";import{_ as A}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./mescroll-i18n.e7c22011.js";import"./u-popup.1b30ffa7.js";import"./u-safe-bottom.98e092c5.js";const R=A(e({__name:"record",setup(e){const A=t(""),R=t([]),B=t([]),D=t(!1),{mescrollInit:E,downCallback:J,getMescroll:O}=S(f,m);t(null),a((()=>{}));const Q=e=>{D.value=!1;let t={page:e.num,limit:e.size,keyword:A.value,create_time:R.value};U(t).then((t=>{let a=t.data.data;1==e.num&&(B.value=[]),B.value=B.value.concat(a),e.endSuccess(a.length),D.value=!0})).catch((()=>{D.value=!0,e.endErr()}))},X=()=>{O().resetUpScroll()},Y=t(),Z=()=>{Y.value.show=!0},$=e=>{R.value=e,B.value=[],O().resetUpScroll()};return(e,t)=>{const a=_,m=v,f=g,S=V,U=h(y("u--image"),z);return l(),s(f,{class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden",style:d(e.themeColor())},{default:r((()=>[o(f,{class:"fixed left-0 right-0 top-0 z-99 bg-[#fff]"},{default:r((()=>[o(f,{class:"py-[14rpx] flex items-center justify-between px-[20rpx]"},{default:r((()=>[o(f,{class:"flex-1 search-input mr-[20rpx]"},{default:r((()=>[o(a,{onClick:t[0]||(t[0]=c((e=>X()),["stop"])),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"}),o(m,{class:"input",maxlength:"50",type:"text",modelValue:A.value,"onUpdate:modelValue":t[1]||(t[1]=e=>A.value=e),placeholder:"请输入搜索关键词",placeholderClass:"text-[var(--text-color-light9)] text-[24rpx]","confirm-type":"search",onConfirm:t[2]||(t[2]=e=>X())},null,8,["modelValue"]),A.value?(l(),s(a,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[3]||(t[3]=e=>A.value="")})):n("v-if",!0)])),_:1}),o(f,{class:"nc-iconfont nc-icon-a-riliV6xx-36 !text-[30rpx] leading-[36rpx] text-[var(--text-color-light6)]",onClick:Z})])),_:1})])),_:1}),o(I,{ref:"mescrollRef",top:"88rpx",onInit:u(E),down:{use:!1},onUp:Q},{default:r((()=>[B.value.length?(l(),s(f,{key:0,class:"sidebar-margin pt-[var(--top-m)]"},{default:r((()=>[(l(!0),i(x,null,p(B.value,((e,t)=>(l(),s(f,{key:e.id,class:"w-full flex flex-col mb-[var(--top-m)] card-template",onClick:t=>{C({url:"/app/pages/verify/detail",param:{code:e.code}})}},{default:r((()=>[o(f,{class:"flex items-center mb-[30rpx] leading-[1] text-[26rpx]"},{default:r((()=>[o(f,{class:"nc-iconfont nc-icon-hexiaotaiV6xx !text-[26rpx] pr-[10rpx]"}),o(a,{class:"truncate text-[#303133] text-[26rpx]"},{default:r((()=>[b("核销码:")])),_:1}),o(a,{class:"ml-[10rpx] max-w-[494rpx]"},{default:r((()=>[b(j(e.code),1)])),_:2},1024),o(a,{class:"text-[#303133] text-[26rpx] font-400 nc-iconfont nc-icon-fuzhiV6xx1 ml-[11rpx]",onClick:c((t=>u(k)(e.code)),["stop"])},null,8,["onClick"])])),_:2},1024),(l(!0),i(x,null,p(e.value.list,((t,a)=>(l(),s(f,{class:"flex flex-1 mb-2",key:a},{default:r((()=>[o(U,{width:"130rpx",height:"130rpx",radius:"var(--goods-rounded-big)",src:u(w)(t.cover?t.cover:""),mode:"aspectFill"},{error:r((()=>[o(S,{class:"w-[130rpx] h-[130rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:u(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["radius","src"]),o(f,{class:"flex flex-col flex-1 ml-[20rpx] py-[4rpx]"},{default:r((()=>[o(f,{class:"max-w-[490rpx] leading-[1.3] truncate text-[28rpx] text-[#303133]"},{default:r((()=>[b(j(t.name),1)])),_:2},1024),e.sub_name?(l(),s(f,{key:0,class:"mt-[14rpx] truncate text-[24rpx] text-[var(--text-color-light9)] max-w-[490rpx]"},{default:r((()=>[b(j(e.sub_name),1)])),_:2},1024)):n("v-if",!0),o(f,{class:"text-[24rpx] mt-[10rpx] text-[var(--text-color-light9)]"},{default:r((()=>[b("x"+j(t.verify_num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128)),o(f,{class:"flex bg-[var(--temp-bg)] py-[20rpx] px-[20rpx] rounded-[12rpx] mt-[20rpx]"},{default:r((()=>[o(f,{class:"flex-1"},{default:r((()=>[o(f,{class:"text-[22rpx] text-[var(--text-color-light9)] mb-[10rpx] leading-[30rpx]"},{default:r((()=>[b("核销时间")])),_:1}),o(f,{class:"text-[26rpx] text-[#303133] leading-[36rpx]"},{default:r((()=>[b(j(e.create_time),1)])),_:2},1024)])),_:2},1024),o(f,{class:"flex-1"},{default:r((()=>[o(f,{class:"text-[22rpx] text-[var(--text-color-light9)] mb-[10rpx] leading-[30rpx]"},{default:r((()=>[b("核销员")])),_:1}),o(f,{class:"text-[26rpx] text-[#303133] leading-[36rpx]"},{default:r((()=>[b(j(e.member?e.member.nickname:"--"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})):n("v-if",!0),!B.value.length&&D.value?(l(),s(M,{key:1,option:{tip:"暂无核销记录"}})):n("v-if",!0)])),_:1},8,["onInit"]),n(" 时间选择 "),o(F,{ref_key:"selectDateRef",ref:Y,onConfirm:$},null,512)])),_:1},8,["style"])}}}),[["__scopeId","data-v-0c14380a"]]);export{R as default};
