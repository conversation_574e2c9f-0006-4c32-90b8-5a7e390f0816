import{d as e,r as a,p as s,a9 as t,q as l,J as r,o,c as n,w as d,g as c,b as i,e as u,n as p,y as x,z as _,F as f,t as m,x as g,O as v,am as h,an as y,Q as b,k,au as w,S as j,R as C,i as F,j as T,A as E,P as I,$ as S,a as P,T as R,as as z,av as M,b6 as $}from"./index-3caf046d.js";import{_ as A}from"./u-popup.1b30ffa7.js";import{a as H,b as O,c as U}from"./rank.7a4c9318.js";import{M as W}from"./mescroll-body.36f14dc3.js";import{u as q}from"./useMescroll.26ccf5de.js";import{M as G}from"./mescroll-empty.d02c7bd6.js";import{t as J}from"./topTabbar.9217e319.js";import{u as N}from"./useGoods.9c8f1c51.js";import{_ as Q}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-safe-bottom.98e092c5.js";import"./mescroll-i18n.e7c22011.js";const V=Q(e({__name:"rank",setup(e){const Q=N(),{mescrollInit:V,downCallback:Y,getMescroll:Z}=q(y,h),B=a(null),D=a(!1);let K={};J().setTopTabbarParam({title:""});const L=s((()=>R(Number(K.height)+K.top+8)+30+"rpx;")),X=t(),ee=X.windowHeight/X.screenWidth*750,ae=s((()=>`${ee-450}rpx`)),se=a(!1),te=()=>{se.value=!1},le=a([]),re=a([]),oe=a(!1),ne=(e=!1)=>{H().then((a=>{if(le.value=a.data,ie.value){for(let e=0;e<le.value.length;e++)if(ie.value==le.value[e].rank_id){pe(le.value[e],e);break}}else e&&le.value&&le.value.length?pe(le.value[0],0):le.value.length||(D.value=!0);z((()=>{M().selectAll(".category-btn").boundingClientRect((e=>{if(e&&e.length>0){const a=$(20)*(e.length-1),s=e.reduce(((e,a)=>e+a.width),0)+a,l=t().windowWidth;oe.value=s<=.93*l}else console.error("Failed to get .category-btn elements.")})).exec()}))})).catch((e=>{console.error("加载分类数据失败",e)}))},de=l({}),ce=a(0),ie=a(0),ue=a();function pe(e,a){var s;D.value=!1,re.value=[],ce.value=a,ie.value=e.rank_id,ue.value=e.goods_source,null==(s=Z())||s.resetUpScroll()}const xe=e=>{if(0==le.value.length)return;D.value=!1;let a={page:e.num,limit:e.size,rank_id:ie.value};U(a).then((a=>{let s=a.data.data.map((e=>e)),t=!0;1==e.num&&(re.value=[]),re.value=re.value.concat(s),t="goods"!=ue.value,e.endSuccess(s.length,t),D.value=!0})).catch((()=>{D.value=!0,e.endErr()}))};function _e(e){switch(e){case 1:return E("addon/shop/rank/rank_first.png");case 2:return E("addon/shop/rank/rank_second.png");case 3:return E("addon/shop/rank/rank_third.png");default:return E("addon/shop/rank/rank.png")}}return r((async e=>{ie.value=e.rank_id||0,O().then((e=>{Object.assign(de,e.data)})),ne(!0)})),(e,a)=>{const s=b,t=k,l=w,r=j,h=C,y=F(T("u-popup"),A);return o(),n(t,{class:"min-h-[100vh]",style:p(e.themeColor())},{default:d((()=>[c(" 顶部图片 "),i(t,{class:"rank-head"},{default:d((()=>[i(s,{class:"w-[100%] h-[435rpx]",src:u(E)(de.rank_images),mode:"aspectFill"},null,8,["src"]),i(t,{class:"content-box"},{default:d((()=>[c(" 榜单分类按钮 "),i(l,{"scroll-x":"true",class:"category-slider","scroll-with-animation":"","scroll-into-view":"id"+ce.value},{default:d((()=>[i(t,{class:"category-con",style:p({justifyContent:oe.value?"center":"flex-start"})},{default:d((()=>[(o(!0),x(f,null,_(le.value,((e,a)=>(o(),n(t,{class:"category-btn",key:a,id:"id"+a,onClick:s=>pe(e,a),style:p({color:ce.value===a?de.select_color:de.no_color,background:ce.value===a?`linear-gradient(to right, ${de.select_bg_color_start}, ${de.select_bg_color_end})`:"transparent"})},{default:d((()=>[i(t,null,{default:d((()=>[m(g(e.name),1)])),_:2},1024)])),_:2},1032,["id","onClick","style"])))),128))])),_:1},8,["style"])])),_:1},8,["scroll-into-view"]),c(' <view class="content">\n                  <text class="text-[26rpx]">{{rankConfig.rank_name}}</text>\n                </view> ')])),_:1}),de.rank_remark?(o(),n(t,{key:0,class:"side-tab",style:p({top:u(L)}),onClick:a[0]||(a[0]=e=>se.value=!0)},{default:d((()=>[i(r,{class:"iconfont icona-paihangbangpc30 icon"}),i(r,{class:"desc"},{default:d((()=>[m(g(u(I)("rankingRules")),1)])),_:1})])),_:1},8,["style"])):c("v-if",!0)])),_:1}),i(t,{class:"rank-list p-[20rpx] relative -mt-[42rpx]"},{default:d((()=>[c(" 列表 "),i(W,{ref_key:"mescrollRef",ref:B,height:u(ae),onInit:u(V),down:{use:!1},onUp:xe},{default:d((()=>[re.value.length?(o(!0),x(f,{key:0},_(re.value,((e,a)=>(o(),n(t,{class:S(["bg-[#fff] flex rounded-[var(--rounded-mid)] p-[20rpx]",{"mb-[20rpx]":re.value.length-1!=a}]),key:e.goods_id,onClick:a=>{return s=e.goods_id,void P({url:"/addon/shop/pages/goods/detail",param:{goods_id:s}});var s}},{default:d((()=>[i(t,{class:"w-[240rpx] h-[240rpx] flex items-center justify-center relative"},{default:d((()=>[c(" 榜单排名图片 "),a<5?(o(),n(s,{key:0,class:"absolute top-[7rpx] left-[10rpx] w-[50rpx] h-[58rpx]",style:{zIndex:9},src:_e(e.rank_num),mode:"aspectFill"},null,8,["src"])):c("v-if",!0),a<5?(o(),n(t,{key:1,class:"absolute top-[15rpx] left-[10rpx] flex items-center justify-center w-[50rpx] h-[50rpx]",style:{zIndex:10}},{default:d((()=>[i(r,{class:"text-[24rpx] font-bold text-[#fff]"},{default:d((()=>[m(g(a+1),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0),e.goods_cover_thumb_mid?(o(),n(s,{key:2,class:"w-[250rpx] h-[250rpx] rounded-[var(--rounded-mid)]",src:u(E)(e.goods_cover_thumb_mid),mode:"aspectFill",onError:a=>e.goods_cover_thumb_mid="static/resource/images/diy/shop_default.jpg"},null,8,["src","onError"])):(o(),n(s,{key:3,class:"w-[240rpx] h-[240rpx] rounded-[var(--rounded-mid)]",src:u(E)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"]))])),_:2},1024),i(t,{class:"flex flex-col flex-1 justify-between ml-[20rpx] pt-[4rpx]"},{default:d((()=>[i(t,{class:"text-[28rpx] text-[#333] leading-[40rpx] multi-hidden mb-[10rpx]"},{default:d((()=>[e.goods_brand?(o(),n(t,{key:0,class:"brand-tag",style:p(u(Q).baseTagStyle(e.goods_brand))},{default:d((()=>[m(g(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):c("v-if",!0),m(" "+g(e.goods_name),1)])),_:2},1024),e.goods_label_name&&e.goods_label_name.length?(o(),n(t,{key:0,class:"flex flex-wrap"},{default:d((()=>[(o(!0),x(f,null,_(e.goods_label_name,((e,a)=>(o(),x(f,null,["icon"==e.style_type&&e.icon?(o(),n(s,{key:0,class:"img-tag",src:u(E)(e.icon),mode:"heightFix",onError:a=>u(Q).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?c("v-if",!0):(o(),n(t,{key:1,class:"base-tag",style:p(u(Q).baseTagStyle(e))},{default:d((()=>[m(g(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):c("v-if",!0),i(t,{class:"flex items-center"},{default:d((()=>[i(t,{class:"text-[var(--price-text-color)] price-font flex items-baseline"},{default:d((()=>[i(r,{class:"text-[24rpx] font-500"},{default:d((()=>[m("￥")])),_:1}),i(r,{class:"text-[40rpx] font-500"},{default:d((()=>[m(g(u(Q).goodsPrice(e).toFixed(2).split(".")[0]),1)])),_:2},1024),i(r,{class:"text-[24rpx] font-500"},{default:d((()=>[m("."+g(u(Q).goodsPrice(e).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),"member_price"==u(Q).priceType(e)?(o(),n(s,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:u(E)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==u(Q).priceType(e)?(o(),n(s,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:u(E)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==u(Q).priceType(e)?(o(),n(s,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:u(E)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):c("v-if",!0),i(t,{id:"itemCart"+a,class:"w-[102rpx] box-border ml-auto text-center text-[#fff] primary-btn-bg h-[46rpx] text-[22rpx] leading-[46rpx] rounded-[100rpx]"},{default:d((()=>[m("去购买")])),_:2},1032,["id"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128)):c("v-if",!0),!re.value.length&&D.value?(o(),n(G,{key:1,option:{tip:"暂无商品",btnText:"去逛逛"},onEmptyclick:a[1]||(a[1]=e=>u(P)({url:"/addon/shop/pages/goods/list"}))})):c("v-if",!0)])),_:1},8,["height","onInit"]),i(t,{onTouchmove:a[3]||(a[3]=v((()=>{}),["prevent","stop"]))},{default:d((()=>[i(y,{show:se.value,onClose:te,mode:"center",round:"var(--rounded-big)"},{default:d((()=>[i(t,{class:"w-[570rpx] px-[32rpx] popup-common center"},{default:d((()=>[i(t,{class:"title"},{default:d((()=>[m(g(u(I)("rankingRules")),1)])),_:1}),i(l,{"scroll-y":!0,class:"px-[30rpx] box-border max-h-[260rpx]"},{default:d((()=>[i(t,{class:"text-[28rpx] leading-[40rpx] mb-[20rpx]"},{default:d((()=>[m(g(de.rank_remark),1)])),_:1})])),_:1}),i(t,{class:"btn-wrap !pt-[40rpx]"},{default:d((()=>[i(h,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:a[2]||(a[2]=e=>se.value=!1)},{default:d((()=>[m("我知道了")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-87f8720f"]]);export{V as default};
