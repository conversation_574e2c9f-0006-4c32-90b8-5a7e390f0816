import{d as e,I as t,r,J as l,e as a,o as s,c as o,w as p,b as i,y as n,z as x,F as u,g as c,n as d,am as f,an as m,k as _,au as v,i as g,j as h,$ as y,t as b,x as w,a as k,S as j,R as C}from"./index-3caf046d.js";import{_ as F}from"./pay.e8ba1ab9.js";import{f as S,h as z,a as I}from"./coupon.2f3f2d3d.js";import{M}from"./mescroll-body.36f14dc3.js";import{M as B}from"./mescroll-empty.d02c7bd6.js";import{u as R}from"./useMescroll.26ccf5de.js";import{_ as U}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-popup.1b30ffa7.js";import"./u-safe-bottom.98e092c5.js";import"./pay.1a29db5c.js";import"./mescroll-i18n.e7c22011.js";const Y=U(e({__name:"my_coupon",setup(e){const U=t(),{mescrollInit:Y,downCallback:D,getMescroll:E}=R(m,f),J=r([]),V=r(!1),Z=r([]),$=r(1),q=e=>{V.value=!1;let t={page:e.num,limit:e.size,status:$.value,type:G.value};z(t).then((t=>{let r=t.data.data;1==e.num&&(J.value=[]),J.value=J.value.concat(r),e.endSuccess(r.length),V.value=!0})).catch((()=>{V.value=!0,e.endErr()}))},A=r(0),G=r("all"),H=r([]);l((()=>{I().then((e=>{H.value.push({label:"全部",value:"all"}),H.value=H.value.concat(e.data)})),S().then((e=>{Z.value=e.data.filter((e=>4!=e.status))}))}));return(e,t)=>{const r=_,l=v,f=j,m=C,S=g(h("pay"),F);return a(U).info?(s(),o(r,{key:0,class:"bg-[#f8f8f8] min-h-screen overflow-hidden",style:d(e.themeColor())},{default:p((()=>[i(r,{class:"fixed left-0 top-0 right-0 z-10"},{default:p((()=>[i(l,{"scroll-x":!0,class:"scroll-Y box-border px-[var(--sidebar-m)] bg-white"},{default:p((()=>[i(r,{class:"flex whitespace-nowrap justify-around items-center h-[88rpx]"},{default:p((()=>[(s(!0),n(u,null,x(Z.value,((e,t)=>(s(),o(r,{class:y(["text-[28rpx] text-[#333] h-[88rpx] leading-[88rpx] font-400",{"class-select !text-primary":$.value===e.status}]),onClick:t=>{return r=e.status,$.value=r,J.value=[],void E().resetUpScroll();var r}},{default:p((()=>[b(w(e.status_name)+"("+w(e.count)+")",1)])),_:2},1032,["class","onClick"])))),256))])),_:1})])),_:1}),i(l,{"scroll-x":!0,"scroll-with-animation":"","scroll-into-view":"id"+(A.value?A.value-1:0),class:"px-[var(--sidebar-m)] box-border bg-white"},{default:p((()=>[i(r,{class:"items-center flex h-[88rpx]"},{default:p((()=>[(s(!0),n(u,null,x(H.value,((e,t)=>(s(),o(f,{class:y(["flex-shrink-0 w-[126rpx] h-[54rpx] text-[24rpx] flex-center text-center text-[#333] bg-[var(--temp-bg)] rounded-[30rpx] box-border mr-[20rpx] border-[2rpx] border-solid border-[#F8F9FD]",{"!text-primary !border-primary font-500 !bg-[var(--primary-color-light)]":e.value==G.value}]),key:t,id:"id"+t,onClick:r=>((e,t)=>{A.value=e,G.value=t,J.value=[],E().resetUpScroll()})(t,e.value)},{default:p((()=>[b(w(e.label),1)])),_:2},1032,["class","id","onClick"])))),128))])),_:1})])),_:1},8,["scroll-into-view"])])),_:1}),i(M,{ref:"mescrollRef",top:"176rpx",onInit:a(Y),down:{use:!1},onUp:q},{default:p((()=>[J.value.length?(s(),o(r,{key:0,class:"py-[var(--top-m)] px-[var(--sidebar-m)]"},{default:p((()=>[(s(!0),n(u,null,x(J.value,((e,t)=>(s(),n(u,null,[1!=$.value?(s(),o(r,{key:0,class:y(["flex items-center relative w-[100%] rounded-[var(--rounded-small)] overflow-hidden bg-[#fff]",{"mt-[var(--top-m)]":t}])},{default:p((()=>[i(r,{class:y(["w-[186rpx] h-[160rpx] flex flex-col items-center justify-center rounded-[var(--rounded-small)] relative coupon-item",{"bg-[var(--primary-help-color4)]":2==$.value,"bg-[var(--primary-color-light)]":3==$.value}])},{default:p((()=>[i(r,{class:y(["price-font flex items-baseline",{"text-[#fff]":2==$.value,"text-[#FFB4B1]":3==$.value}])},{default:p((()=>[i(f,{class:"text-[30rpx] leading-[34rpx] mr-[2rpx] text-center price-font font-500"},{default:p((()=>[b("￥ ")])),_:1}),i(f,{class:"text-[54rpx] font-500 leading-[58rpx] price-font truncate"},{default:p((()=>[b(w(e.coupon_price),1)])),_:2},1024)])),_:2},1032,["class"]),i(f,{class:y(["truncate max-w-[176rpx] mt-[6rpx] text-[24rpx] h-[32rpx] leading-[32rpx]",{"text-[#fff]":2==$.value,"text-[var(--primary-help-color4)]":3==$.value}])},{default:p((()=>[b(w(e.title),1)])),_:2},1032,["class"])])),_:2},1032,["class"]),i(r,{class:"ml-[30rpx] flex-1 h-[100%] box-border py-[20rpx]"},{default:p((()=>[i(r,{class:"text-[26rpx] leading-[40rpx] text-left font-500"},{default:p((()=>["0.00"===e.min_condition_money?(s(),o(f,{key:0},{default:p((()=>[b("无门槛")])),_:1})):(s(),o(f,{key:1},{default:p((()=>[b("满"+w(e.coupon_min_price)+"元可用",1)])),_:2},1024))])),_:2},1024),i(r,{class:"mt-[10rpx] flex items-center"},{default:p((()=>[i(f,{class:"w-[80rpx] text-center bg-[var(--primary-color-light)] whitespace-nowrap text-[var(--primary-color)] text-[18rpx] h-[30rpx] leading-[30rpx] rounded-[15rpx] mr-[10rpx] flex-shrink-0"},{default:p((()=>[b(w(e.type_name),1)])),_:2},1024),i(f,{class:"truncate max-w-[226rpx] text-[24rpx] text-[var(--text-color-light6)] leading-[34rpx]"},{default:p((()=>[b(w(e.title),1)])),_:2},1024)])),_:2},1024),i(r,{class:"w-[100%] mt-[6rpx] text-[20rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:p((()=>[i(f,null,{default:p((()=>[b("有效期至 "),i(f,null,{default:p((()=>[b(w(e.expire_time?e.expire_time.slice(0,10):""),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),i(r,{class:"px-[20rpx]"},{default:p((()=>[2==$.value?(s(),o(m,{key:0,class:"flex-center rounded-full remove-border",style:d({width:"150rpx",height:"60rpx",color:"#fff",fontSize:"24rpx",padding:"0",border:"none",backgroundColor:"var(--primary-help-color4)"})},{default:p((()=>[b("已使用")])),_:1},8,["style"])):c("v-if",!0),3==$.value?(s(),o(m,{key:1,class:"flex-center rounded-full remove-border",style:d({width:"150rpx",height:"60rpx",color:"var(--primary-help-color4)",fontSize:"24rpx",padding:"0",border:"none",backgroundColor:"var(--primary-color-light)"})},{default:p((()=>[b("已过期")])),_:1},8,["style"])):c("v-if",!0)])),_:1})])),_:2},1032,["class"])):(s(),o(r,{key:1,class:y(["flex items-center relative w-[100%] rounded-[var(--rounded-small)] overflow-hidden bg-[#fff]",{"mt-[var(--top-m)]":t}])},{default:p((()=>[i(r,{class:"coupon-bg w-[186rpx] h-[160rpx] flex flex-col items-center justify-center rounded-[var(--rounded-small)] relative coupon-item"},{default:p((()=>[i(r,{class:"price-font flex items-baseline text-[#fff]"},{default:p((()=>[i(f,{class:"text-[30rpx] leading-[34rpx] mr-[2rpx] text-center price-font font-500"},{default:p((()=>[b("￥")])),_:1}),i(f,{class:"text-[54rpx] font-500 leading-[58rpx] price-font truncate"},{default:p((()=>[b(w(e.coupon_price),1)])),_:2},1024)])),_:2},1024),i(f,{class:"truncate max-w-[176rpx] mt-[6rpx] text-[22rpx] text-[#fff] h-[32rpx] leading-[32rpx]"},{default:p((()=>[b(w(e.title),1)])),_:2},1024)])),_:2},1024),i(r,{class:"ml-[30rpx] flex-1 h-[100%] box-border py-[20rpx]"},{default:p((()=>[i(r,{class:"text-[26rpx] leading-[40rpx] text-left font-500"},{default:p((()=>["0.00"===e.min_condition_money?(s(),o(f,{key:0},{default:p((()=>[b("无门槛")])),_:1})):(s(),o(f,{key:1},{default:p((()=>[b("满"+w(e.coupon_min_price)+"元可用",1)])),_:2},1024))])),_:2},1024),i(r,{class:"text-[20rpx] mt-[10rpx] flex items-center"},{default:p((()=>[i(f,{class:"w-[80rpx] text-center bg-[var(--primary-color-light)] whitespace-nowrap text-[var(--primary-color)] text-[18rpx] h-[30rpx] leading-[30rpx] rounded-[15rpx] mr-[10rpx] flex-shrink-0"},{default:p((()=>[b(w(e.type_name),1)])),_:2},1024),i(f,{class:"truncate max-w-[226rpx] text-[24rpx] text-[var(--text-color-light9)] leading-[34rpx]"},{default:p((()=>[b(w(e.title),1)])),_:2},1024)])),_:2},1024),i(r,{class:"w-[100%] mt-[6rpx] text-[20rpx] leading-[34rpx] text-[var(--text-color-light9)]"},{default:p((()=>[i(f,null,{default:p((()=>[b("有效期至 "),i(f,null,{default:p((()=>[b(w(e.expire_time?e.expire_time.slice(0,10):""),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),1===$.value?(s(),o(r,{key:0,class:"px-[20rpx]"},{default:p((()=>[i(m,{"hover-class":"none",class:"flex-center rounded-full remove-border primary-btn-bg",style:{width:"150rpx",height:"60rpx",color:"#fff",fontSize:"24rpx",padding:"0",border:"none"},onClick:t=>{return r=e.coupon_id,void k({url:"/addon/shop/pages/goods/list",param:{coupon_id:r}});var r}},{default:p((()=>[b("去使用")])),_:2},1032,["onClick"])])),_:2},1024)):c("v-if",!0)])),_:2},1032,["class"]))],64)))),256))])),_:1})):c("v-if",!0),!J.value.length&&V.value?(s(),o(B,{key:1,option:{tip:"暂无优惠券"}})):c("v-if",!0)])),_:1},8,["onInit"]),i(S,{ref:"payRef"},null,512)])),_:1},8,["style"])):c("v-if",!0)}}}),[["__scopeId","data-v-3adb0e8b"]]);export{Y as default};
