import{d as e,p as t,r,B as a,I as l,m as s,J as o,K as i,L as n,M as p,a as u,N as c,o as x,c as d,w as f,b as _,e as m,t as g,x as b,g as v,O as y,n as h,P as k,Q as w,k as j,R as C,i as S,j as L,S as T,A,T as F,U as I}from"./index-3caf046d.js";import{_ as R,a as M}from"./u-checkbox-group.0328273c.js";import{_ as N}from"./uni-popup.a1c93ccb.js";import{b as O}from"./bind-mobile.25318c0e.js";import{t as P}from"./topTabbar.9217e319.js";import{_ as z}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-form.49dbb57f.js";import"./u-line.69c0c00f.js";import"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import"./u-input.2d8dc7a4.js";import"./u-modal.8624728a.js";import"./u-loading-icon.255170b9.js";import"./u-popup.1b30ffa7.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-safe-bottom.98e092c5.js";const B=z(e({__name:"index",setup(e){let z={};P().setTopTabbarParam({title:"",topStatusBar:{textColor:"#333"}}),t((()=>Object.keys(z).length?F(Number(z.height))+F(z.top)+F(8)+"rpx":"auto"));const B=r(""),J=r(!1),K=a(),q=t((()=>K.login)),Q=I();l(),t((()=>l().info));const U=s();t((()=>!J.value&&K.login.agreement_show?"":"getPhoneNumber")),r(null);const V=t((()=>!K.login.is_auth_register)),D=r(!1);r(!1);const E=r(),G=()=>{E.value.close()},H=()=>{J.value=!0,E.value.close(),Z()},W=r(null),X=()=>{W.value.open()};o((async()=>{await U.getSiteInfoFn(),await K.getLoginConfig();let e=!K.login.is_username&&!K.login.is_mobile&&!K.login.is_bind_mobile;i()?!n()&&e&&V.value&&(p({title:"商家未开启登录注册",icon:"none"}),setTimeout((()=>{u({url:"/app/pages/index/index",mode:"reLaunch"})}),100)):!n()&&e&&(p({title:"商家未开启登录注册",icon:"none"}),setTimeout((()=>{u({url:"/app/pages/index/index",mode:"reLaunch"})}),100))})),c((()=>{D.value=!1}));const Y=t((()=>{var e="";return K.login.bg_url&&(e+="background-image:url("+A(K.login.bg_url)+");",e+="background-size: 100%;",e+="background-position: top;",e+="background-repeat: no-repeat;"),e})),Z=(e=null,t=null)=>{((e="")=>!(J.value||!K.login.agreement_show||(e?p({title:k("isAgreeTips"),icon:"none"}):E.value.open(),0)))()||D.value||(D.value=!0,e||(e=()=>{D.value=!1}),$())},$=()=>{if(i()){let e=uni.getStorageSync("login_config");if(uni.getStorageSync("member_lock"))return p({title:k("memberLock"),icon:"none"}),void setTimeout((()=>{uni.removeStorageSync("member_lock"),u({url:"/app/pages/index/index",mode:"reLaunch"})}),1e3);if(e.wechat_error)return D.value=!1,void p({title:e.wechat_error,icon:"none"});B.value=uni.getStorageSync("wap_member_mobile"),B.value||(B.value=uni.getStorageSync("wap_member_not_control_mobile")),e.is_auth_register?!B.value&&e.is_bind_mobile?X():e.is_force_access_user_info?Q.getAuthCode({scopes:"snsapi_userinfo"}):e.is_force_access_user_info||Q.getAuthCode({scopes:"snsapi_base"}):!B.value&&e.is_bind_mobile?X():Q.getAuthCode({scopes:"snsapi_base"}),D.value=!1}},ee=()=>{J.value=!J.value};return(e,t)=>{const r=w,a=j,l=C,s=S(L("u-checkbox"),R),o=S(L("u-checkbox-group"),M),n=T,p=S(L("uni-popup"),N),c=S(L("bind-mobile"),O);return x(),d(a,{class:"w-screen h-screen",style:h(e.themeColor())},{default:f((()=>[_(a,{class:"w-screen h-screen",style:h(m(Y))},{default:f((()=>[_(a,{class:"mx-[var(--sidebar-m)] px-[var(--pad-sidebar-m)]"},{default:f((()=>[_(a,{class:"pt-[154rpx] flex justify-center"},{default:f((()=>{var e,t;return[(null==(e=m(U).site)?void 0:e.front_end_logo)?(x(),d(a,{key:0,class:"h-[90rpx] w-[300rpx]"},{default:f((()=>{var e;return[_(r,{class:"h-[90rpx] w-[300rpx]",src:m(A)(null==(e=m(U).site)?void 0:e.front_end_logo),mode:"aspectFit"},null,8,["src"])]})),_:1})):(null==(t=m(U).site)?void 0:t.front_end_icon)?(x(),d(a,{key:1,class:"h-[250rpx] w-[250rpx]"},{default:f((()=>{var e;return[_(r,{class:"h-[250rpx] w-[250rpx]",src:m(A)(null==(e=m(U).site)?void 0:e.front_end_icon),mode:"aspectFit"},null,8,["src"])]})),_:1})):(x(),d(a,{key:2,class:"h-[90rpx] w-[300rpx]"}))]})),_:1}),_(a,{class:"text-[var(--text-color-light6)]] text-[28rpx] text-center leading-[34rpx] min-h-[34rpx] mt-[40rpx]"},{default:f((()=>[g(b(m(q).desc),1)])),_:1}),_(a,{class:"mt-[181rpx]"},{default:f((()=>[v(" 微信公众号快捷登录，开启自动注册的情况下才能使用 "),m(i)()&&m(q).is_auth_register?(x(),d(a,{key:0,class:"w-full flex items-center justify-center mb-[40rpx]"},{default:f((()=>[_(l,{class:"w-[630rpx] h-[88rpx] !mx-[0] !bg-[var(--primary-color)] text-[26rpx] rounded-[44rpx] leading-[88rpx] font-500 !text-[#fff]",onClick:t[0]||(t[0]=e=>Z())},{default:f((()=>[g(b(m(k)("quickLoginOrLogout")),1)])),_:1})])),_:1})):v("v-if",!0),v(" 手机号登录 "),m(q).is_mobile?(x(),d(a,{key:1,class:"mb-[40rpx] w-full flex items-center justify-center"},{default:f((()=>[_(l,{class:"w-[630rpx] h-[88rpx] !mx-[0] !bg-[#fff] border-[var(--primary-color)] border-solid border-[2rpx] text-[26rpx] rounded-[44rpx] leading-[84rpx] !text-[var(--primary-color)]",onClick:t[1]||(t[1]=e=>m(u)({url:"/app/pages/auth/login",param:{type:"mobile"}}))},{default:f((()=>[g(b(m(k)("mobileLogin")),1)])),_:1})])),_:1})):!m(q).is_mobile&&m(q).is_username?(x(),d(a,{key:2,class:"w-full flex items-center justify-center"},{default:f((()=>[_(l,{class:"w-[630rpx] h-[88rpx] !mx-[0] !bg-[#fff] !border-[var(--primary-color)] border-solid border-[2rpx] text-[26rpx] rounded-[44rpx] leading-[84rpx] !text-[var(--primary-color)]",onClick:t[2]||(t[2]=e=>m(u)({url:"/app/pages/auth/login",param:{type:"username"}}))},{default:f((()=>[g(b(m(k)("accountLogin")),1)])),_:1})])),_:1})):v("v-if",!0),m(q).agreement_show?(x(),d(a,{key:3,class:"w-full flex items-center justify-center mt-[28rpx]"},{default:f((()=>[_(a,{class:"flex items-center justify-center mt-[28rpx] py-[14rpx] px-[50rpx]",onClick:y(ee,["stop"])},{default:f((()=>[_(o,{onChange:ee},{default:f((()=>[_(s,{activeColor:"var(--primary-color)",checked:J.value,shape:"circle",size:"30rpx"},null,8,["checked"])])),_:1}),_(a,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap leading-[30rpx]"},{default:f((()=>[_(n,null,{default:f((()=>[g(b(m(k)("agreeTips")),1)])),_:1}),_(n,{onClick:t[3]||(t[3]=y((e=>m(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:f((()=>[g("《"+b(m(k)("privacyAgreement"))+"》",1)])),_:1}),_(n,null,{default:f((()=>[g(b(m(k)("and")),1)])),_:1}),_(n,{onClick:t[4]||(t[4]=y((e=>m(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:f((()=>[g("《"+b(m(k)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])])),_:1})):v("v-if",!0),m(q).is_mobile&&m(q).is_username?(x(),d(a,{key:4,class:"footer w-full"},{default:f((()=>[_(a,{class:"text-[26rpx] leading-[36rpx] text-[333] text-center mb-[30rpx] font-400"},{default:f((()=>[g(b(m(k)("otherLogin")),1)])),_:1}),_(a,{class:"flex justify-center"},{default:f((()=>[_(a,{class:"h-[80rpx] w-[80rpx] text-center leading-[78rpx] border-[2rpx] text-[#FF7100] rounded-[50%] border-solid border-[#ddd] nc-iconfont nc-icon-wodeV6mm3 text-[46rpx] overflow-hidden",onClick:t[5]||(t[5]=e=>m(u)({url:"/app/pages/auth/login",param:{type:"username"}}))})])),_:1}),_(a,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)] text-center font-400 mt-[30rpx]"},{default:f((()=>[g(b(m(k)("accountLogin")),1)])),_:1})])),_:1})):v("v-if",!0)])),_:1})])),_:1})])),_:1},8,["style"]),_(p,{ref_key:"popupRef",ref:E,type:"dialog"},{default:f((()=>[_(a,{class:"bg-[#fff] flex flex-col justify-between w-[600rpx] min-h-[280rpx] rounded-[var(--rounded-big)] box-border px-[35rpx] pt-[35rpx] pb-[8rpx] relative"},{default:f((()=>[_(a,{class:"flex justify-center"},{default:f((()=>[_(n,{class:"text-[33rpx] font-700"},{default:f((()=>[g(" 用户协议及隐私保护")])),_:1})])),_:1}),_(a,{class:"flex items-center mb-[20rpx] mt-[20rpx] py-[20rpx]",onClick:y(ee,["stop"])},{default:f((()=>[_(a,{class:"text-[26rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:f((()=>[_(n,null,{default:f((()=>[g(b(m(k)("agreeTips")),1)])),_:1}),_(n,{onClick:t[6]||(t[6]=y((e=>m(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:f((()=>[g("《"+b(m(k)("privacyAgreement"))+"》",1)])),_:1}),_(n,null,{default:f((()=>[g(b(m(k)("and")),1)])),_:1}),_(n,{onClick:t[7]||(t[7]=y((e=>m(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:f((()=>[g("《"+b(m(k)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"]),_(a,null,{default:f((()=>[_(a,{class:"w-[100%] flex justify-center bg-[var(--primary-color)] h-[70rpx] leading-[70rpx] text-[#fff] text-[26rpx] border-[0] font-500 rounded-[50rpx]",onClick:H},{default:f((()=>[g("同意并登录")])),_:1}),_(a,{class:"w-[100%] flex justify-center h-[70rpx] leading-[70rpx] text-[#999] text-[24rpx] border-[0] font-500 rounded-[50rpx]",onClick:G},{default:f((()=>[g("不同意")])),_:1})])),_:1})])),_:1})])),_:1},512),v(" 强制绑定手机号 "),_(c,{ref_key:"bindMobileRef",ref:W},null,512)])),_:1},8,["style"])}}}),[["__scopeId","data-v-f9ad1904"]]);export{B as default};
