import{at as a,d as e,r as s,o as t,c as p,w as r,b as o,t as n,x as l,y as u,z as x,F as i,g as c,O as d,k as f,au as _,R as m,i as v,j as y}from"./index-3caf046d.js";import{_ as g}from"./u-popup.1b30ffa7.js";function b(e,s){return a.get(`pay/friendspay/info/${e}/${s}`,{},{showErrorMessage:!0})}const h=e({__name:"message",setup(a,{expose:e}){const b=s(!1),h=s({}),w=()=>{b.value=!1};return e({open:a=>{h.value=a,b.value=!0}}),(a,e)=>{const s=f,k=_,j=m,C=v(y("u-popup"),g);return t(),p(s,{onTouchmove:e[0]||(e[0]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[o(C,{show:b.value,onClose:w,mode:"center",round:"var(--rounded-big)"},{default:r((()=>[Object.keys(h.value).length?(t(),p(s,{key:0,class:"w-[570rpx] px-[32rpx] popup-common center"},{default:r((()=>[o(s,{class:"title"},{default:r((()=>[n(l(h.value.pay_explain_title),1)])),_:1}),h.value.pay_explain_content?(t(),p(k,{key:0,"scroll-y":!0,class:"px-[30rpx] box-border max-h-[260rpx]"},{default:r((()=>[(t(!0),u(i,null,x(h.value.pay_explain_content.split("\n"),(a=>(t(),p(s,{class:"text-[28rpx] leading-[40rpx] mb-[20rpx]"},{default:r((()=>[n(l(a),1)])),_:2},1024)))),256))])),_:1})):c("v-if",!0),o(s,{class:"btn-wrap !pt-[40rpx]"},{default:r((()=>[o(j,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:w},{default:r((()=>[n("我知道了")])),_:1})])),_:1})])),_:1})):c("v-if",!0)])),_:1},8,["show"])])),_:1})}}});export{h as _,b as g};
