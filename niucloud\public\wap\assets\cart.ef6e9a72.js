import{at as t,bI as s,L as i,M as o}from"./index-3caf046d.js";function e(s){return t.post("shop/cart",s)}function d(s){return t.put("shop/cart",s)}function a(s){return t.put("shop/cart/delete",s)}function c(s){return t.get("shop/cart/goods",s)}function u(s){return t.get("shop/cart/calculate",s)}const r=s("cart",{state:()=>({cartList:{},totalNum:0,totalMoney:0,isRepeat:!1}),actions:{getList(s=null){if(!i()){for(let t in this.cartList)delete this.cartList[t];return this.totalNum=0,void(this.totalMoney=0)}var o;(o={},t.get("shop/cart",o)).then((t=>{let i=t.data;for(let s in this.cartList)delete this.cartList[s];i&&i.forEach((t=>{if(1==t.goods.status&&0==t.goods.delete_time&&t.goodsSku){let s={id:t.id,goods_id:t.goods_id,sku_id:t.sku_id,stock:t.goodsSku.stock,num:t.num,sale_price:t.goodsSku.show_price};this.cartList["goods_"+s.goods_id]||(this.cartList["goods_"+s.goods_id]={}),this.cartList["goods_"+s.goods_id]["sku_"+s.sku_id]=s}})),this.calculateNum(),s&&"function"==typeof s&&s()}))},increase(t,s=1,a=null){if(!t||t&&0==Object.keys(t).length||!t.goods_id||!t.sku_id)return;if(!i())return;let c=(t.num||0)+s,u=t.id?d:e;c>parseInt(t.stock)?o({title:"商品库存不足",icon:"none"}):this.isRepeat||(this.isRepeat=!0,t.id?this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id].num=c:(this.cartList["goods_"+t.goods_id]||(this.cartList["goods_"+t.goods_id]={}),this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]={id:t.id,goods_id:t.goods_id,sku_id:t.sku_id,stock:t.stock,num:c,sale_price:t.sale_price}),this.calculateNum(),u({id:t.id,goods_id:t.goods_id,sku_id:t.sku_id,num:c}).then((t=>{this.getList(a),this.isRepeat=!1})).catch((t=>{this.isRepeat=!1})))},reduce(t,s=1,o=null){if(!t||t&&0==Object.keys(t).length||!t.goods_id||!t.sku_id)return;if(!i())return;let e=(t.num||0)-s,c=e>0?d:a;this.isRepeat||(this.isRepeat=!0,e>0?this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id].num=e:(delete this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id],0==Object.keys(this.cartList["goods_"+t.goods_id]).length&&delete this.cartList["goods_"+t.goods_id]),this.calculateNum(),c({ids:t.id,id:t.id,goods_id:t.goods_id,sku_id:t.sku_id,num:e}).then((t=>{this.getList(o),this.isRepeat=!1})).catch((t=>{this.isRepeat=!1})))},delete(t,s=null){t&&a({ids:t}).then((t=>{this.getList(),this.isRepeat=!1,s&&s()})).catch((t=>{this.isRepeat=!1}))},calculateNum(){if(this.totalNum=0,this.totalMoney=0,Object.keys(this.cartList).length)for(let t in this.cartList){let s=0,i=0;for(let o in this.cartList[t])"object"==typeof this.cartList[t][o]&&(s+=this.cartList[t][o].num,i+=this.cartList[t][o].num*this.cartList[t][o].sale_price);this.cartList[t].totalNum=s,this.cartList[t].totalMoney=i.toFixed(2),this.totalNum+=s,this.totalMoney+=i}this.totalMoney=this.totalMoney.toFixed(2)}}});export{u as a,c as g,r as u};
