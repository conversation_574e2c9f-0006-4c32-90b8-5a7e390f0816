<template>
    <!-- 内容 -->
    <div class="content-wrap" v-show="diyStore.editTab == 'content'">
        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">标题设置</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="标题">
                    <el-input v-model.trim="diyStore.editComponent.title" placeholder="请输入标题" clearable maxlength="10" show-word-limit />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">更多设置</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="显示更多">
                    <el-switch v-model="diyStore.editComponent.showMore" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]" v-if="diyStore.editComponent.showMore">
                <el-form-item label="更多文字">
                    <el-input v-model.trim="diyStore.editComponent.moreText" placeholder="请输入更多文字" clearable maxlength="8" show-word-limit />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]" v-if="diyStore.editComponent.showMore">
                <el-form-item label="更多链接">
                    <diy-link v-model="diyStore.editComponent.moreLink" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">滚动设置</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="自动滚动">
                    <el-switch v-model="diyStore.editComponent.autoScroll" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]" v-if="diyStore.editComponent.autoScroll">
                <el-form-item label="滚动速度">
                    <el-slider v-model="diyStore.editComponent.scrollSpeed" :min="2000" :max="5000" :step="500" show-input />
                    <el-text type="info" size="small">单位：毫秒，数值越小滚动越快</el-text>
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">背景渐变</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="开始颜色">
                    <el-color-picker v-model="diyStore.editComponent.bgGradient.startColor" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="结束颜色">
                    <el-color-picker v-model="diyStore.editComponent.bgGradient.endColor" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">评价列表</h3>
            <div class="mb-[10px] flex justify-between items-center">
                <span>共 {{ diyStore.editComponent.reviews.length }} 条评价</span>
                <el-button type="primary" size="small" @click="addReview">添加评价</el-button>
            </div>
            
            <div v-for="(review, index) in diyStore.editComponent.reviews" :key="index" class="mb-[20px] p-[15px] border border-gray-200 rounded">
                <div class="mb-[10px] flex justify-between items-center">
                    <h4>评价{{ index + 1 }}</h4>
                    <el-button type="danger" size="small" @click="removeReview(index)">删除</el-button>
                </div>
                
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="头像">
                        <upload-image v-model="review.avatar" :limit="1" />
                    </el-form-item>
                </el-form>
                
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="昵称">
                        <el-input v-model.trim="review.nickname" placeholder="请输入昵称" clearable maxlength="10" show-word-limit />
                    </el-form-item>
                </el-form>
                
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="时间">
                        <el-input v-model.trim="review.time" placeholder="请输入时间描述" clearable maxlength="20" show-word-limit />
                    </el-form-item>
                </el-form>
                
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="评价内容">
                        <el-input v-model.trim="review.content" type="textarea" :rows="3" placeholder="请输入评价内容" maxlength="100" show-word-limit />
                    </el-form-item>
                </el-form>
                
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="预估价格">
                        <el-input v-model.trim="review.originalPrice" placeholder="请输入预估价格" clearable />
                    </el-form-item>
                </el-form>
                
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="最终价格">
                        <el-input v-model.trim="review.finalPrice" placeholder="请输入最终价格" clearable />
                    </el-form-item>
                </el-form>
                
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="印章图片">
                        <upload-image v-model="review.stampImage" :limit="1" />
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="diyStore.editTab == 'style'">
        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">标题样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="文字颜色">
                    <el-color-picker v-model="diyStore.editComponent.titleColor" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="字体大小">
                    <el-slider v-model="diyStore.editComponent.titleSize" :min="12" :max="20" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap" v-if="diyStore.editComponent.showMore">
            <h3 class="mb-[10px]">更多样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="文字颜色">
                    <el-color-picker v-model="diyStore.editComponent.moreColor" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">价格样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="价格颜色">
                    <el-color-picker v-model="diyStore.editComponent.reviews[0].priceColor" @change="updateAllPriceColors" />
                </el-form-item>
            </el-form>
        </div>

        <!-- 组件样式 -->
        <slot name="style"></slot>
    </div>
</template>

<script lang="ts" setup>
import useDiyStore from '@/stores/modules/diy'

const diyStore = useDiyStore()
diyStore.editComponent.ignore = ['componentBgUrl'] // 忽略公共属性

const addReview = () => {
    diyStore.editComponent.reviews.push({
        avatar: "",
        nickname: "用户昵称",
        time: "刚刚鉴定结果",
        content: "请输入评价内容",
        originalPrice: "1.00",
        finalPrice: "10.00",
        priceColor: "#FF5722",
        stampImage: ""
    })
}

const removeReview = (index: number) => {
    diyStore.editComponent.reviews.splice(index, 1)
}

const updateAllPriceColors = (color: string) => {
    diyStore.editComponent.reviews.forEach(review => {
        review.priceColor = color
    })
}

defineExpose({})

</script>

<style lang="scss" scoped></style>
