import{ab as t,ac as a,ad as e,o as s,c as o,w as n,a7 as i,k as r}from"./index-3caf046d.js";import{_ as p}from"./_plugin-vue_export-helper.1b428a4d.js";const c=p({name:"u-swipe-action",mixins:[a,e,{props:{autoClose:{type:Boolean,default:()=>t.swipeAction.autoClose}}}],data:()=>({}),provide(){return{swipeAction:this}},computed:{parentData(){return[this.autoClose]}},watch:{parentData(){this.children.length&&this.children.map((t=>{"function"==typeof t.updateParentData&&t.updateParentData()}))}},created(){this.children=[]},methods:{closeOther(t){this.autoClose&&this.children.map(((a,e)=>{t!==a&&a.closeHandler()}))}}},[["render",function(t,a,e,p,c,l){const u=r;return s(),o(u,{class:"u-swipe-action"},{default:n((()=>[i(t.$slots,"default")])),_:3})}]]);export{c as _};
