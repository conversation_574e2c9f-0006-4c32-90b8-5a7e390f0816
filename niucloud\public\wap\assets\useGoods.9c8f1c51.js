import"./index-3caf046d.js";function o(o={}){return{baseTagStyle:o=>{let r="";return o.color_json&&o.color_json.text_color&&(r+=`color:${o.color_json.text_color};`),o.color_json&&o.color_json.border_color&&(r+=`border-color: ${o.color_json.border_color};`),o.color_json&&o.color_json.bg_color&&(r+=`background-color: ${o.color_json.bg_color};`),r},goodsPrice:o=>{let r="0.00";return r=o.goodsSku.show_price,parseFloat(r)},priceType:o=>{let r="";return r=o.goodsSku.show_type,r},error:(o,r)=>{o[r]=""}}}export{o as u};
