import{d as s,h as a,m as e,s as t,o,c as l,w as c,b as n,e as r,t as i,x as d,n as u,Q as m,k as p,A as g,P as f,a as h}from"./index-3caf046d.js";const x=s({__name:"nosite",setup(s){a();const x=e();return t((()=>x.site),((s,a)=>{s&&1==s.status&&h({url:"/app/pages/index/index",mode:"reLaunch"})})),(s,a)=>{const e=m,t=p;return o(),l(t,{class:"min-h-[100vh] bg-[var(--page-bg-color)] overflow-hidden",style:u(s.themeColor())},{default:c((()=>[n(t,{class:"empty-page"},{default:c((()=>[n(e,{class:"img",src:r(g)("static/resource/images/site/close.png"),model:"aspectFit"},null,8,["src"]),n(t,{class:"desc"},{default:c((()=>[i(d(r(f)("noSite")),1)])),_:1})])),_:1})])),_:1},8,["style"])}}});export{x as default};
