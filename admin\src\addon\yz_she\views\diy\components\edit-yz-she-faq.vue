<template>
    <!-- 内容 -->
    <div class="content-wrap" v-show="diyStore.editTab == 'content'">
        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">标题设置</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="标题">
                    <el-input v-model.trim="diyStore.editComponent.title" placeholder="请输入标题" clearable maxlength="10" show-word-limit />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">更多设置</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="显示更多">
                    <el-switch v-model="diyStore.editComponent.showMore" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]" v-if="diyStore.editComponent.showMore">
                <el-form-item label="更多文字">
                    <el-input v-model.trim="diyStore.editComponent.moreText" placeholder="请输入更多文字" clearable maxlength="8" show-word-limit />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]" v-if="diyStore.editComponent.showMore">
                <el-form-item label="更多链接">
                    <diy-link v-model="diyStore.editComponent.moreLink" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">显示设置</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="默认显示">
                    <el-input-number v-model="diyStore.editComponent.defaultShowCount" :min="1" :max="20" />
                    <el-text type="info" size="small" class="ml-[10px]">条问题</el-text>
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="展开文字">
                    <el-input v-model.trim="diyStore.editComponent.expandText" placeholder="请输入展开文字" clearable maxlength="6" show-word-limit />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="收起文字">
                    <el-input v-model.trim="diyStore.editComponent.collapseText" placeholder="请输入收起文字" clearable maxlength="6" show-word-limit />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">问题列表</h3>
            <div class="mb-[10px] flex justify-between items-center">
                <span>共 {{ diyStore.editComponent.faqList.length }} 个问题</span>
                <el-button type="primary" size="small" @click="addFaq">添加问题</el-button>
            </div>
            
            <div v-for="(faq, index) in diyStore.editComponent.faqList" :key="index" class="mb-[20px] p-[15px] border border-gray-200 rounded">
                <div class="mb-[10px] flex justify-between items-center">
                    <h4>问题{{ index + 1 }}</h4>
                    <el-button type="danger" size="small" @click="removeFaq(index)">删除</el-button>
                </div>
                
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="问题">
                        <el-input v-model.trim="faq.question" placeholder="请输入问题" clearable maxlength="50" show-word-limit />
                    </el-form-item>
                </el-form>
                
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="回答">
                        <el-input v-model.trim="faq.answer" type="textarea" :rows="3" placeholder="请输入回答内容" maxlength="200" show-word-limit />
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="diyStore.editTab == 'style'">
        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">标题样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="文字颜色">
                    <el-color-picker v-model="diyStore.editComponent.titleColor" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="字体大小">
                    <el-slider v-model="diyStore.editComponent.titleSize" :min="12" :max="20" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap" v-if="diyStore.editComponent.showMore">
            <h3 class="mb-[10px]">更多样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="文字颜色">
                    <el-color-picker v-model="diyStore.editComponent.moreColor" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">问题样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="问题颜色">
                    <el-color-picker v-model="diyStore.editComponent.questionColor" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="问题大小">
                    <el-slider v-model="diyStore.editComponent.questionSize" :min="12" :max="18" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">回答样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="回答颜色">
                    <el-color-picker v-model="diyStore.editComponent.answerColor" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="回答大小">
                    <el-slider v-model="diyStore.editComponent.answerSize" :min="10" :max="16" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">卡片样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="背景颜色">
                    <el-color-picker v-model="diyStore.editComponent.itemBgColor" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="边框颜色">
                    <el-color-picker v-model="diyStore.editComponent.borderColor" />
                </el-form-item>
            </el-form>
        </div>

        <!-- 组件样式 -->
        <slot name="style"></slot>
    </div>
</template>

<script lang="ts" setup>
import useDiyStore from '@/stores/modules/diy'

const diyStore = useDiyStore()
diyStore.editComponent.ignore = ['componentBgUrl'] // 忽略公共属性

const addFaq = () => {
    diyStore.editComponent.faqList.push({
        question: "请输入问题",
        answer: "请输入回答内容"
    })
}

const removeFaq = (index: number) => {
    diyStore.editComponent.faqList.splice(index, 1)
}

defineExpose({})

</script>

<style lang="scss" scoped></style>
