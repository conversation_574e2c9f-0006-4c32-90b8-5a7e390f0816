import{d as e,r as a,p as t,J as l,_ as s,o as c,c as u,w as o,g as n,b as d,y as r,z as i,F as f,e as v,t as _,x as p,$ as g,M as b,be as m,S as y,k as h,au as k,O as x,A as C,bG as w,Q as q}from"./index-3caf046d.js";import{e as $,b as z,f as F}from"./quote.9b84c391.js";import{_ as I}from"./_plugin-vue_export-helper.1b428a4d.js";const N=I(e({__name:"quote-list",setup(e){const I=a([{label:"全部",value:""},{label:"估价中",value:1},{label:"待确认",value:2},{label:"待发货",value:3},{label:"已完成",value:4},{label:"已取消",value:5}]),N=a(""),O=a(!1),j=a(!1),S=a({page:1,limit:10}),J=a([]),A=a([]),D=t((()=>{const e=A.value.filter((e=>3===e.status));return e.length>0&&J.value.length===e.length})),G=t((()=>A.value.filter((e=>J.value.includes(e.id))).reduce(((e,a)=>e+parseFloat(a.quote_price||0)),0).toFixed(2))),L=async(e=!1)=>{if(!O.value){e&&(S.value.page=1,A.value=[]),O.value=!0;try{const a={status:N.value,page:S.value.page,limit:S.value.limit},t=await $(a);if(1===t.code){const a=t.data.data||[];e?A.value=a:A.value.push(...a),j.value=a.length===S.value.limit,j.value&&S.value.page++}else b({title:t.msg||"加载失败",icon:"none"})}catch(a){console.error("加载订单列表失败:",a),b({title:"加载失败",icon:"none"})}finally{O.value=!1}}},M=t((()=>A.value)),Q=()=>{const e=A.value.filter((e=>3===e.status));D.value?J.value=[]:J.value=e.map((e=>e.id))},R=()=>{if(0===J.value.length)return void b({title:"请选择要发货的订单",icon:"none"});const e=encodeURIComponent(JSON.stringify(J.value));m({url:`/addon/yz_she/pages/order/detail/quote-order?orderIds=${e}`})},U=e=>e.product_image?C(e.product_image):e.brand&&e.brand.logo?C(e.brand.logo):"/static/images/default-product.png",B=e=>e.product_name?e.product_name:e.brand&&e.brand.brand_name?e.brand.brand_name:e.category&&e.category.name?e.category.name:"未知商品",E=e=>e.product_code?e.product_code:e.brand&&e.brand.brand_code?e.brand.brand_code:"",H=e=>({"":"全部",1:"估价中",2:"待确认",3:"待发货",4:"已完成",5:"已取消"}[e]||e),K=e=>({1:[{type:"cancel",text:"取消订单"}],2:[{type:"cancel",text:"取消订单"},{type:"confirm",text:"确认回收"}]}[e]||[]),P=async e=>{w({title:"确认取消",content:"确定要取消这个订单吗？",success:async a=>{if(a.confirm)try{const a=await z(e.id,"用户主动取消");1===a.code?(b({title:"取消成功",icon:"success"}),L(!0)):b({title:a.msg||"取消失败",icon:"none"})}catch(t){console.error("取消订单失败:",t),b({title:"取消失败",icon:"none"})}}})},T=async e=>{w({title:"确认回收",content:`确认以¥${e.quote_price||"待估价"}的价格回收此商品？`,success:async a=>{if(a.confirm)try{const a=await F(e.id);1===a.code?(b({title:"确认成功，请寄出商品",icon:"success"}),L(!0)):b({title:a.msg||"确认失败",icon:"none"})}catch(t){console.error("确认订单失败:",t),b({title:"确认失败",icon:"none"})}}})},V=e=>{m({url:`/addon/yz_she/pages/logistics/detail?orderNo=${e.orderNo}`})};return l((e=>{if(console.log("页面参数:",e),e.status){const a=parseInt(e.status);[1,2,3,4,5].includes(a)&&(N.value=a,console.log("设置当前状态为:",a))}})),s((()=>{console.log("报价单列表页面加载完成，当前状态:",N.value),L(!0)})),(e,a)=>{const t=y,l=h,s=k,b=q;return c(),u(l,{class:"evaluate-orders-page"},{default:o((()=>[n(" 顶部状态筛选 "),d(s,{class:"status-tabs-container","scroll-x":"true","show-scrollbar":"false"},{default:o((()=>[d(l,{class:"status-tabs"},{default:o((()=>[(c(!0),r(f,null,i(I.value,(e=>(c(),u(l,{class:g(["tab-item",{active:N.value===e.value}]),key:e.value,onClick:a=>{return t=e.value,N.value=t,J.value=[],void L(!0);var t}},{default:o((()=>[d(t,{class:"tab-text"},{default:o((()=>[_(p(e.label),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),n(" 订单列表 "),d(l,{class:"order-list"},{default:o((()=>[(c(!0),r(f,null,i(v(M),(e=>(c(),u(l,{class:"order-item",key:e.id,onClick:a=>(e=>{m({url:`/addon/yz_she/pages/order/detail/quote-detail?id=${e.id}&status=${e.status}`})})(e)},{default:o((()=>[d(l,{class:g(["order-content",{"with-checkbox":3===N.value}])},{default:o((()=>{return[d(l,{class:"order-header"},{default:o((()=>{return[n(" 待发货状态的勾选框与订单号对齐 "),d(l,{class:"order-header-left"},{default:o((()=>[3===N.value?(c(),u(l,{key:0,class:g(["checkbox",{checked:J.value.includes(e.id)}]),onClick:x((a=>(e=>{const a=J.value.indexOf(e);a>-1?J.value.splice(a,1):J.value.push(e)})(e.id)),["stop"])},{default:o((()=>[J.value.includes(e.id)?(c(),u(t,{key:0,class:"checkbox-icon"},{default:o((()=>[_("✓")])),_:1})):n("v-if",!0)])),_:2},1032,["class","onClick"])):n("v-if",!0),d(t,{class:"order-no"},{default:o((()=>[_("订单号: "+p(e.order_no),1)])),_:2},1024)])),_:2},1024),d(l,{class:g(["order-status",(a=e.status,{1:"status-evaluating",2:"status-pending",3:"status-shipping",4:"status-completed",5:"status-cancelled"}[a]||"")])},{default:o((()=>[d(t,{class:"status-text"},{default:o((()=>[_(p(H(e.status)),1)])),_:2},1024)])),_:2},1032,["class"])];var a})),_:2},1024),d(l,{class:"order-time"},{default:o((()=>[d(t,{class:"time-text"},{default:o((()=>{return[_("创建时间: "+p((a=e.create_time,a?new Date(a).toLocaleString():"")),1)];var a})),_:2},1024)])),_:2},1024),d(l,{class:"product-info"},{default:o((()=>[n(" 根据商品信息显示逻辑：优先显示商品信息，没有则显示品牌信息 "),d(b,{src:U(e),class:"product-image",mode:"aspectFit"},null,8,["src"]),d(l,{class:"product-details"},{default:o((()=>[d(t,{class:"product-name"},{default:o((()=>[_(p(B(e)),1)])),_:2},1024),E(e)?(c(),u(t,{key:0,class:"product-code"},{default:o((()=>[_(p(E(e)),1)])),_:2},1024)):n("v-if",!0)])),_:2},1024),d(l,{class:"price-info"},{default:o((()=>[d(t,{class:"price-label"},{default:o((()=>{return[_(p((a=e.status,{1:"预估价格",2:"估价金额",3:"预估价格",4:"预估价格",5:"估价金额"}[a]||"估价金额")),1)];var a})),_:2},1024),d(l,{class:"price-amount"},{default:o((()=>[d(t,{class:"currency"},{default:o((()=>[_("¥")])),_:1}),d(t,{class:"price-value"},{default:o((()=>[_(p(e.quote_price||"待估价"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),n(" 操作按钮 (待发货状态不显示查看物流按钮) "),(a=e.status,[1,2,3].includes(a)&&3!==N.value?(c(),u(l,{key:0,class:"order-actions"},{default:o((()=>[(c(!0),r(f,null,i(K(e.status),(a=>(c(),u(l,{class:g(["action-btn",a.type]),key:a.type,onClick:x((t=>((e,a)=>{switch(console.log("处理操作:",e.orderNo,a),a){case"cancel":P(e);break;case"confirm":T(e);break;case"logistics":V(e)}})(e,a.type)),["stop"])},{default:o((()=>[d(t,{class:"action-text"},{default:o((()=>[_(p(a.text),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)):n("v-if",!0))];var a})),_:2},1032,["class"])])),_:2},1032,["onClick"])))),128)),n(" 空状态 "),0===v(M).length?(c(),u(l,{key:0,class:"empty-state"},{default:o((()=>[d(l,{class:"empty-icon"},{default:o((()=>[_("📋")])),_:1}),d(t,{class:"empty-text"},{default:o((()=>[_("暂无"+p(H(N.value))+"订单",1)])),_:1})])),_:1})):n("v-if",!0)])),_:1}),n(" 底部加载更多 "),j.value&&v(M).length>0&&3!==N.value?(c(),u(l,{key:0,class:"load-more",onClick:a[0]||(a[0]=e=>L())},{default:o((()=>[d(t,{class:"load-text"},{default:o((()=>[_(p(O.value?"加载中...":"加载更多..."),1)])),_:1})])),_:1})):n("v-if",!0),n(" 待发货批量操作底部栏 "),3===N.value&&v(M).length>0?(c(),u(l,{key:1,class:"batch-actions-bar"},{default:o((()=>[d(l,{class:"select-all-container"},{default:o((()=>[d(l,{class:g(["checkbox",{checked:v(D)}]),onClick:Q},{default:o((()=>[v(D)?(c(),u(t,{key:0,class:"checkbox-icon"},{default:o((()=>[_("✓")])),_:1})):n("v-if",!0)])),_:1},8,["class"]),d(l,{class:"select-text-container"},{default:o((()=>[d(t,{class:"select-all-text"},{default:o((()=>[_("全选")])),_:1}),J.value.length>0?(c(),u(t,{key:0,class:"selected-count"},{default:o((()=>[_(" 已选"+p(J.value.length)+"件，预计可获得 ¥"+p(v(G)),1)])),_:1})):n("v-if",!0)])),_:1})])),_:1}),d(l,{class:g(["batch-ship-btn",{disabled:0===J.value.length}]),onClick:R},{default:o((()=>[d(t,{class:"batch-ship-text"},{default:o((()=>[_("批量发货")])),_:1})])),_:1},8,["class"])])),_:1})):n("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-a27da158"]]);export{N as default};
