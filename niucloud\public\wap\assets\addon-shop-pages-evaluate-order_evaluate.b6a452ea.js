import{d as e,p as t,i as a,j as l,o as r,c as o,w as s,y as u,F as n,z as d,e as i,$ as p,b as c,A as x,O as m,f as _,t as f,x as v,v as g,M as h,aC as b,aB as y,S as j,k as w,r as V,J as C,X as k,a as F,n as z,R as U,g as A,aD as B}from"./index-3caf046d.js";import{_ as E}from"./u-icon.ba193921.js";import{_ as I}from"./u--image.eb573bce.js";import{s as q,_ as M}from"./evaluate.556b3376.js";import{_ as R}from"./u-tabbar.38f37e13.js";import{_ as S}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{g as $}from"./order.5c5c6bee.js";import{_ as D}from"./u-upload.83871903.js";import{_ as J}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */import"./u-image.04cba9a2.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-safe-bottom.98e092c5.js";import"./u-loading-icon.255170b9.js";const N=e({__name:"upload-img",props:{modelValue:{type:String||Array},maxCount:{type:Number,default:9},multiple:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{emit:V}){const C=e,k=t({get:()=>C.modelValue,set(e){V("update:modelValue",e)}}),F=t((()=>C.maxCount)),z=e=>{C.multiple?e.file.forEach((e=>{U({file:e})})):U(e)},U=e=>{var t;if((null==(t=k.value)?void 0:t.length)>=F.value)return h({title:`最多允许上传${F.value}张图片`,icon:"none"}),!1;b({filePath:e.file.url,name:"file"}).then((e=>{var t;(null==(t=k.value)?void 0:t.length)<F.value&&k.value.push(e.data.url)})).catch((()=>{}))};return(e,t)=>{const h=a(l("u-icon"),E),b=a(l("u--image"),I),V=j,U=w,A=a(l("u-upload"),D);return r(),o(U,{class:"flex flex-wrap"},{default:s((()=>[(r(!0),u(n,null,d(i(k),((e,t)=>(r(),o(U,{class:p(["mb-[18rpx] relative",{"mr-[18rpx]":(t+1)%4!=0}])},{default:s((()=>[c(b,{class:"rounded-[10rpx] overflow-hidden",width:"140rpx",height:"140rpx",src:i(x)(e||""),model:"aspectFill",onClick:t=>(e=>{if(""===e)return!1;var t=[];t.push(x(e)),y({indicator:"number",loop:!0,urls:t})})(e)},{error:s((()=>[c(h,{name:"photo",color:"#999",size:"50"})])),_:2},1032,["src","onClick"]),c(U,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:m((e=>(e=>{k.value.splice(e,1)})(t)),["stop"])},{default:s((()=>[c(V,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1032,["class"])))),256)),_(c(U,{class:"w-[140rpx] h-[140rpx]"},{default:s((()=>[c(A,{onAfterRead:z,maxCount:i(F),multiple:C.multiple},{default:s((()=>[c(U,{class:"flex items-center justify-center w-[140rpx] h-[140rpx] border-[2rpx] border-dashed border-[#ddd] text-center text-[var(--text-color-light9)] rounded-[var(--goods-rounded-big)]"},{default:s((()=>[c(U,null,{default:s((()=>[c(U,{class:"nc-iconfont nc-icon-xiangjiV6xx text-[50rpx]"}),c(U,{class:"text-[24rpx] mt-[12rpx]"},{default:s((()=>[f(v(i(k).length)+"/"+v(i(F)),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["maxCount","multiple"])])),_:1},512),[[g,i(k).length<i(F)]])])),_:1})}}}),O=J(e({__name:"order_evaluate",setup(e){const t=V([]),m=V([]),_=V("2"),g=V(!1),b=V("");V(null),C((e=>{if(e.order_id)b.value=e.order_id,y(b.value);else{k({url:"/addon/shop/pages/order/list",param:{status:5},title:"缺少订单id"})}}));const y=e=>{g.value=!0,$(e).then((e=>{if(e.data.is_evaluate)return O(b.value),!1;e.data.order_goods.forEach((e=>{1==e.status&&(t.value.push(e),m.value.push({order_id:e.order_id,order_goods_id:e.order_goods_id,goods_id:e.goods_id,content:"",images:[],scores:5}))})),g.value=!1})).catch((()=>{g.value=!1}))},D=()=>{_.value="1"===_.value?"2":"1"},J=()=>{if(m.value.some((e=>""==e.content)))return h({title:"请输入你的评价",icon:"none"}),!1;for(let e=0;e<m.value.length;e++){let t=m.value[e];t.content.length>200&&(t.content=t.content.substr(0,200))}m.value.forEach((e=>e.is_anonymous=_.value)),g.value=!0,q({evaluate_array:m.value}).then((e=>{g.value=!1,O(b.value)})).catch((()=>{g.value=!1}))},O=e=>{F({url:"/addon/shop/pages/evaluate/order_evaluate_view",param:{order_id:e},mode:"redirectTo"})};return(e,h)=>{const b=a(l("u-icon"),E),y=a(l("u--image"),I),V=w,C=j,k=a(l("u-rate"),M),F=B,q=U,$=a(l("u-tabbar"),R),O=a(l("loading-page"),S);return r(),o(V,{class:"bg-[var(--page-bg-color)] min-h-screen",style:z(e.themeColor())},{default:s((()=>[c(V,{class:"px-[var(--sidebar-m)] py-[var(--top-m)]"},{default:s((()=>[(r(!0),u(n,null,d(t.value,((e,t)=>(r(),o(V,{key:t,class:"card-template mb-[var(--top-m)]"},{default:s((()=>[c(V,{class:"bg-[var(--temp-bg)] p-[20rpx] rounded-[var(--rounded-mid)] flex"},{default:s((()=>[c(y,{radius:"var(--goods-rounded-mid)",width:"150rpx",height:"150rpx",src:i(x)(e.goods_image_thumb_small?e.goods_image_thumb_small:""),model:"aspectFill"},{error:s((()=>[c(b,{name:"photo",color:"#999",size:"50"})])),_:2},1032,["src"]),c(V,{class:"flex-1 flex flex-wrap ml-[20rpx] my-[4rpx]"},{default:s((()=>[c(V,null,{default:s((()=>[c(V,{class:"text-[26rpx] leading-[40rpx] max-w-[450rpx] truncate"},{default:s((()=>[f(v(e.goods_name),1)])),_:2},1024),e.sku_name?(r(),o(V,{key:0,class:"max-w-[450rpx] mt-[14rpx] truncate text-[22rpx] text-[var(--text-color-light9)] leading-[28rpx]"},{default:s((()=>[f(v(e.sku_name),1)])),_:2},1024)):A("v-if",!0)])),_:2},1024),c(V,{class:"mt-auto w-full flex justify-between items-center"},{default:s((()=>[c(V,{class:"flex items-baseline price-font"},{default:s((()=>[c(C,{class:"text-[24rpx] font-500"},{default:s((()=>[f("￥")])),_:1}),c(C,{class:"text-[40rpx] font-500"},{default:s((()=>[f(v(parseFloat(e.price).toFixed(2).split(".")[0]),1)])),_:2},1024),c(C,{class:"text-[24rpx] font-500"},{default:s((()=>[f("."+v(parseFloat(e.price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),c(C,{class:"font-400 text-[28rpx] text-[#333]"},{default:s((()=>[f("x"+v(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),c(V,{class:"flex items-center mt-[30rpx]"},{default:s((()=>[c(k,{count:5,modelValue:m.value[t].scores,"onUpdate:modelValue":e=>m.value[t].scores=e,"active-color":"var(--primary-color)",size:"36rpx",gutter:"1"},null,8,["modelValue","onUpdate:modelValue"]),c(C,{class:"ml-[16rpx] text-[28rpx] pt-[2rpx] text-[var(--primary-color)]"},{default:s((()=>[f(v(1===m.value[t].scores?"差评":2===m.value[t].scores||3===m.value[t].scores?"中评":"好评"),1)])),_:2},1024)])),_:2},1024),c(F,{class:"!text-[26rpx] px-[2rpx] mt-[16rpx] w-[100%] !text-[#333] !leading-[1.5]",modelValue:m.value[t].content,"onUpdate:modelValue":e=>m.value[t].content=e,modelModifiers:{trim:!0},placeholder:"请在此处输入你的评价",placeholderClass:"text-[26rpx] text-[var(--text-color-light9)]",maxlength:"200"},null,8,["modelValue","onUpdate:modelValue"]),A(' <view class="text-right text-[24rpx] text-[var(--text-color-light6)]">{{ form[index].content.length >= 200 ? 200 : form[index].content.length }}/200</view> '),c(i(N),{class:"mt-[20rpx]",modelValue:m.value[t].images,"onUpdate:modelValue":e=>m.value[t].images=e,"max-count":9,multiple:!0},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)))),128))])),_:1}),c($,{fixed:!0,placeholder:!0,safeAreaInsetBottom:!0,zIndex:"9999"},{default:s((()=>[c(V,{class:"flex items-center pl-[30rpx] pr-[20rpx] box-border justify-between w-[100%]"},{default:s((()=>[c(V,{class:"flex items-center",onClick:D},{default:s((()=>[c(C,{class:p(["iconfont text-color text-[30rpx] mr-[12rpx] text-[var(--text-color-light9)]",{"iconxuanze1 text-[var(--primary-color)]":"1"===_.value,"nc-iconfont nc-icon-yuanquanV6xx":"1"!==_.value}])},null,8,["class"]),c(C,{class:p(["text-[28rpx] leading-[34rpx]",{"text-[var(--primary-color)]":"1"===_.value,"text-[var(--text-color-light6)]":"1"!==_.value}])},{default:s((()=>[f("匿名")])),_:1},8,["class"])])),_:1}),c(q,{class:"!w-[240rpx] !h-[70rpx] text-[26rpx] !m-0 flex-center rounded-full text-white primary-btn-bg remove-border font-500","hover-class":"none",onClick:J},{default:s((()=>[f("提交")])),_:1})])),_:1})])),_:1}),c(O,{loading:g.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-cc39ae82"]]);export{O as default};
