import{d as e,r as t,J as a,X as l,A as r,p as s,o,c as d,w as i,b as u,t as p,x,e as n,g as c,y as f,F as _,z as m,$ as v,n as y,a as g,bJ as b,k as h,Q as k,S as j,i as w,j as F,aN as C,ao as T,O as N,P as q,D as z,bG as M,B as L,aM as P}from"./index-3caf046d.js";import{_ as V}from"./u--image.eb573bce.js";import{_ as B}from"./u-avatar.30e31e9c.js";import{_ as E}from"./pay.e8ba1ab9.js";import{_ as G}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{g as O,o as R,b as I}from"./order.5c5c6bee.js";import{g as J}from"./shop.547718ad.js";import{d as S}from"./verify.8c605f2d.js";import{l as $}from"./logistics-tracking.f83b11ce.js";import{n as A}from"./ns-goods-manjian.50c99c6a.js";import{t as D}from"./topTabbar.9217e319.js";import{_ as Q}from"./index.vue_vue_type_script_setup_true_lang.7e3f8767.js";import{_ as U}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-text.f02e6497.js";import"./u-popup.1b30ffa7.js";import"./u-safe-bottom.98e092c5.js";import"./pay.1a29db5c.js";import"./u-loading-icon.255170b9.js";import"./u-steps.e6c2645d.js";import"./index.9de114a1.js";import"./top-tabbar.f4fde406.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.abe3938e.js";import"./u-checkbox-group.0328273c.js";import"./u-datetime-picker.a5259774.js";import"./u-input.2d8dc7a4.js";import"./u-upload.83871903.js";import"./u-radio-group.63482a1c.js";import"./diy_form.9eef685a.js";import"./u-parse.406d0731.js";import"./tabbar.2c31519d.js";import"./u-tabbar-item.31141540.js";import"./u-tabbar.38f37e13.js";import"./index.32583a71.js";import"./goods.6a81cb49.js";import"./useGoods.9c8f1c51.js";import"./coupon.2f3f2d3d.js";import"./point.0698952c.js";import"./rank.7a4c9318.js";import"./bind-mobile.25318c0e.js";import"./u-form.49dbb57f.js";import"./u-line.69c0c00f.js";import"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import"./u-modal.8624728a.js";import"./voucher.db23b7c0.js";import"./quote.9b84c391.js";import"./newcomer.6993dfef.js";const X=U(e({__name:"detail",setup(e){D().setTopTabbarParam({title:"订单详情"});const U=t({}),X=t(!0);t("");const H=t(""),K=t({}),W=t(!0),Y=t(null),Z=t("");t("");const ee=t("");t(null),a((e=>{if(e.order_id)H.value=e.order_id,te(H.value);else{l({url:"/addon/shop/pages/order/list",title:"缺少订单id"})}})),J().then((({data:e})=>{K.value=e}));const te=e=>{X.value=!0,O(e).then((e=>{if(U.value=e.data,e.data.order_goods&&e.data.order_goods.length&&ue.value){let t={};t.order_goods_id=e.data.order_goods[0].order_goods_id,xe(t)}if(e.data.order_goods&&e.data.order_goods.length&&ne.value){let t={};t.order_id=e.data.order_id,fe(t)}U.value.goods=[],U.value.gift_goods=[],U.value.order_goods.forEach(((e,t)=>{e.is_gift?U.value.gift_goods.push(e):U.value.goods.push(e)}));let t=0;for(let a=0;a<U.value.order_goods.length;a++)1!=U.value.order_goods[a].status&&t++;t==U.value.order_goods.length&&(W.value=!1),Z.value=U.value.order_goods[0].goods_name,ee.value=r(U.value.order_goods[0].goods_image_thumb_small||""),X.value=!1})).catch((()=>{X.value=!1}))},ae=e=>{"exchange"==U.value.activity_type?g({url:"/addon/shop/pages/point/detail",param:{id:U.value.relate_id}}):g({url:"/addon/shop/pages/goods/detail",param:{goods_id:e}})},le=t(null),re=t(null),se=(e="")=>{var t,a;if("pay"==e)null==(t=le.value)||t.open(U.value.order_type,U.value.order_id,`/addon/shop/pages/order/detail?order_id=${U.value.order_id}`);else if("close"==e)a=U.value,M({title:"提示",content:"您确定要关闭该订单吗？",confirmColor:L().themeColor["--primary-color"],success:e=>{e.confirm&&R(a.order_id).then((e=>{te(a.order_id)}))}});else if("finish"==e)(e=>{M({title:"提示",content:"您确定物品已收到吗？",confirmColor:L().themeColor["--primary-color"],success:t=>{t.confirm&&I(e.order_id).then((t=>{te(e.order_id)}))}})})(U.value);else if("index"==e)g({url:"/addon/shop/pages/index",mode:"reLaunch"});else if("logistics"==e){if(U.value.order_delivery.length>0){let e={id:U.value.order_delivery[0].id,mobile:U.value.taker_mobile},t=[];U.value.order_delivery.forEach(((e,a)=>{e.name=`包裹${a+1}`,t.push(e)})),re.value.open(e),re.value.packageList=t}}else"evaluate"==e&&(U.value.is_evaluate?g({url:"/addon/shop/pages/evaluate/order_evaluate_view",param:{order_id:U.value.order_id}}):g({url:"/addon/shop/pages/evaluate/order_evaluate",param:{order_id:U.value.order_id}}))},oe=()=>{b({latitude:Number(U.value.taker_latitude),longitude:Number(U.value.taker_longitude),success:function(){}})},de=e=>{let t=!1;if("express"!=e.delivery_type)return!1;for(let a=0;a<e.order_delivery.length;a++){let l=e.order_delivery[a];if("express"===l.sub_delivery_type&&"3"===e.status){t=!0;break}if("express"===l.sub_delivery_type&&"5"===e.status){t=!0;break}t=!1}return t},ie=t({}),ue=s((()=>{let e=!1;if(1==U.value.order_goods.length){ie.value=U.value.order_goods[0];let t=U.value.order_goods[0];e=1==t.is_verify&&"virtual"==t.goods_type&&"delivery_finish"==t.delivery_status&&3==U.value.status}return e})),pe=t([]),xe=e=>{pe.value=[],S("shopVirtualGoods",e).then((e=>{pe.value=e.data}))},ne=s((()=>{let e=!1;return e="store"==U.value.delivery_type&&2==U.value.status,e})),ce=t([]),fe=e=>{ce.value=[],S("shopPickUpOrder",e).then((e=>{ce.value=e.data}))},_e=t([]),me=e=>{_e.value=e},ve=t([]),ye=e=>{ve.value=e};return(e,t)=>{const a=h,l=k,s=j,b=w(F("u--image"),V),M=P,L=C,O=w(F("u-avatar"),B),R=w(F("pay"),E),I=w(F("loading-page"),G);return o(),d(a,{style:y(e.themeColor())},{default:i((()=>[X.value?c("v-if",!0):(o(),d(a,{key:0,class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden"},{default:i((()=>[X.value?c("v-if",!0):(o(),d(a,{key:0,class:"pb-20rpx"},{default:i((()=>[U.value.status_name?(o(),d(a,{key:0,class:"pl-[40rpx] pr-[50rpx] bg-linear pb-[100rpx]"},{default:i((()=>[u(a,{class:"flex justify-between items-center pt-[40rpx]"},{default:i((()=>[u(a,{class:"text-[#fff] text-[36rpx] font-500 leading-[42rpx]"},{default:i((()=>[p(x(U.value.status_name.name),1)])),_:1}),1==U.value.status?(o(),d(l,{key:0,class:"w-[180rpx] h-[140rpx]",src:n(r)("addon/shop/detail/payment.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0),2==U.value.status?(o(),d(l,{key:1,class:"w-[180rpx] h-[140rpx]",src:n(r)("addon/shop/detail/deliver_goods.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0),3==U.value.status?(o(),d(l,{key:2,class:"w-[180rpx] h-[140rpx]",src:n(r)("addon/shop/detail/receive.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0),5==U.value.status?(o(),d(l,{key:3,class:"w-[180rpx] h-[140rpx]",src:n(r)("addon/shop/detail/complete.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0),-1==U.value.status?(o(),d(l,{key:4,class:"w-[180rpx] h-[140rpx]",src:n(r)("addon/shop/detail/close.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0)])),_:1})])),_:1})):c("v-if",!0),"virtual"!=U.value.delivery_type?(o(),d(a,{key:1,class:"sidebar-margin mt-[-86rpx] card-template"},{default:i((()=>["express"==U.value.delivery_type?(o(),d(a,{key:0},{default:i((()=>[u(a,{class:"text-[#303133] flex"},{default:i((()=>[u(s,{class:"nc-iconfont nc-icon-dizhiguanliV6xx text-[40rpx] pt-[12rpx] mr-[20rpx]"}),u(a,{class:"flex flex-col"},{default:i((()=>[u(a,{class:"text-[30rpx] leading-[38rpx] overflow-hidden"},{default:i((()=>[u(s,null,{default:i((()=>[p(x(U.value.taker_name),1)])),_:1}),u(s,{class:"ml-[15rpx]"},{default:i((()=>[p(x(U.value.taker_mobile),1)])),_:1})])),_:1}),u(a,{class:"mt-[12rpx] text-[24rpx] text-[var(--text-color-light6)] using-hidden leading-[26rpx]"},{default:i((()=>[p(x(U.value.taker_full_address.split(U.value.taker_address)[0])+x(U.value.taker_address),1)])),_:1})])),_:1})])),_:1})])),_:1})):c("v-if",!0),"store"==U.value.delivery_type?(o(),d(a,{key:1},{default:i((()=>[u(a,{class:"flex items-center mb-3"},{default:i((()=>[u(a,null,{default:i((()=>[u(b,{class:"overflow-hidden",radius:"var(--goods-rounded-mid)",width:"100rpx",height:"100rpx",src:n(r)(U.value.store.store_logo?U.value.store.store_logo:""),model:"aspectFill"},{error:i((()=>[u(l,{class:"w-[100rpx] h-[100rpx] rounded-[var(--goods-rounded-mid)] overflow-hidden",src:n(r)("addon/shop/store_default.png"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["src"])])),_:1}),u(a,{class:"flex flex-col ml-[20rpx]"},{default:i((()=>[u(s,{class:"text-[30rpx] font-500 text-[#303133] mb-[20rpx]"},{default:i((()=>[p(x(U.value.store.store_name),1)])),_:1}),u(s,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[14rpx]"},{default:i((()=>[p(x(U.value.store.trade_time),1)])),_:1}),u(s,{class:"text-[24rpx] text-[var(--text-color-light6)] leading-[1.4]"},{default:i((()=>[p(x(U.value.store.full_address),1)])),_:1})])),_:1})])),_:1}),U.value.taker_name?(o(),d(a,{key:0,class:"justify-between card-template-item"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p("姓名")])),_:1}),u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.taker_name),1)])),_:1})])),_:1})):c("v-if",!0),U.value.taker_mobile?(o(),d(a,{key:1,class:"justify-between card-template-item"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p("预留手机")])),_:1}),u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.taker_mobile),1)])),_:1})])),_:1})):c("v-if",!0),U.value.buyer_ask_delivery_time?(o(),d(a,{key:2,class:"justify-between card-template-item"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p("提货时间")])),_:1}),u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.buyer_ask_delivery_time),1)])),_:1})])),_:1})):c("v-if",!0)])),_:1})):c("v-if",!0),"local_delivery"==U.value.delivery_type?(o(),d(a,{key:2,class:"flex"},{default:i((()=>[u(s,{onClick:oe,class:"nc-iconfont nc-icon-dizhiguanliV6xx text-[40rpx] pt-[12rpx] mr-[20rpx]"}),u(a,{class:"flex flex-col"},{default:i((()=>[u(a,{class:"flex leading-[38rpx] overflow-hidden"},{default:i((()=>[u(s,{class:"text-[30rpx]"},{default:i((()=>[p(x(U.value.taker_name),1)])),_:1}),u(s,{class:"text-[30rpx] ml-[15rpx]"},{default:i((()=>[p(x(U.value.taker_mobile),1)])),_:1})])),_:1}),u(s,{class:"text-[24rpx] mt-[12rpx] leading-[26rpx]"},{default:i((()=>[p(x(U.value.taker_full_address),1)])),_:1})])),_:1})])),_:1})):c("v-if",!0)])),_:1})):c("v-if",!0),c(" 自提核销"),n(ne)?(o(),f(_,{key:2},[ce.value&&ce.value.length?(o(),d(a,{key:0,class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[ce.value.length>1?(o(),d(L,{key:0,class:"h-[450rpx]",circular:"","indicator-dots":"true"},{default:i((()=>[(o(!0),f(_,null,m(ce.value,((e,t)=>(o(),d(M,{key:t},{default:i((()=>[u(a,{class:"flex flex-col items-center justify-center"},{default:i((()=>[u(l,{src:e.qrcode,class:"w-[300rpx] h-[auto]",mode:"widthFix"},null,8,["src"])])),_:2},1024),u(a,{class:"flex items-center justify-center mt-[30rpx]"},{default:i((()=>[u(s,{class:"text-[28rpx] font-500"},{default:i((()=>[p(x(e.code),1)])),_:2},1024),u(s,{class:"text-[var(--text-color-light6)] text-[24rpx] ml-[10rpx] border-[2rpx] border-solid border-[#666] bg-[#f7f7f7] px-[12rpx] py-[6rpx] rounded",onClick:t=>n(T)(e.code)},{default:i((()=>[p("复制")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)))),128))])),_:1})):(o(),f(_,{key:1},[u(a,{class:"flex flex-col items-center justify-center"},{default:i((()=>[u(l,{src:ce.value[0].qrcode,class:"w-[300rpx] h-[auto]",mode:"widthFix"},null,8,["src"])])),_:1}),u(a,{class:"flex items-center justify-center mt-[30rpx]"},{default:i((()=>[u(s,{class:"text-[28rpx] font-500"},{default:i((()=>[p(x(ce.value[0].code),1)])),_:1}),u(s,{class:"text-[var(--text-color-light6)] text-[24rpx] ml-[10rpx] border-[2rpx] border-solid border-[#666] bg-[#f7f7f7] px-[12rpx] py-[6rpx] rounded",onClick:t[0]||(t[0]=e=>n(T)(ce.value[0].code))},{default:i((()=>[p("复制")])),_:1})])),_:1})],64))])),_:1})):c("v-if",!0)],64)):c("v-if",!0),u(a,{class:v(["sidebar-margin card-template p-[0] py-[var(--pad-top-m)] overflow-hidden",{"pb-[var(--pad-top-m)]":U.value.gift_goods.length<=0}]),style:y("virtual"==U.value.delivery_type?"margin-top: -86rpx":"margin-top: 20rpx")},{default:i((()=>[(o(!0),f(_,null,m(U.value.goods,((e,t)=>(o(),d(a,{key:t,class:"px-[var(--pad-sidebar-m)]"},{default:i((()=>[u(a,{class:"order-goods-item flex justify-between flex-wrap mb-[20rpx]"},{default:i((()=>[u(a,{class:"w-[150rpx] h-[150rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",onClick:t=>ae(e.goods_id)},{default:i((()=>[u(b,{class:"overflow-hidden",radius:"var(--goods-rounded-big)",width:"150rpx",height:"150rpx",src:n(r)(e.goods_image_thumb_small?e.goods_image_thumb_small:""),model:"aspectFill"},{error:i((()=>[u(l,{class:"w-[150rpx] h-[150rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:n(r)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1032,["onClick"]),u(a,{class:"ml-[20rpx] flex flex-1 flex-col justify-between"},{default:i((()=>[u(a,null,{default:i((()=>[u(a,{class:"text-[28rpx] max-w-[490rpx] truncate leading-[40rpx] text-[#333]"},{default:i((()=>[p(x(e.goods_name),1)])),_:2},1024),e.sku_name?(o(),d(a,{key:0},{default:i((()=>[u(a,{class:"text-[22rpx] mt-[14rpx] text-[var(--text-color-light9)] truncate max-w-[490rpx] leading-[28rpx]"},{default:i((()=>[p(x(e.sku_name),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0)])),_:2},1024),e.manjian_info&&Object.keys(e.manjian_info).length?(o(),d(a,{key:0,class:"flex items-center mt-[10rpx] mb-[auto]",onClick:N((t=>(e=>{let t={};t.condition_type=z(e).condition_type,t.rule_json=[z(e).rule],t.name=z(e).manjian_name,Y.value.open(t)})(e.manjian_info)),["stop"])},{default:i((()=>[u(a,{class:"bg-[var(--primary-color-light)] text-[var(--primary-color)] rounded-[6rpx] text-[20rpx] flex items-center justify-center w-[88rpx] h-[36rpx] mr-[6rpx]"},{default:i((()=>[p("满减送")])),_:1}),u(s,{class:"text-[22rpx] text-[#999]"},{default:i((()=>[p(x(e.manjian_info.manjian_name),1)])),_:2},1024)])),_:2},1032,["onClick"])):c("v-if",!0),u(a,{class:"flex justify-between items-baseline leading-[28rpx] text-[#333]"},{default:i((()=>[u(a,{class:"price-font"},{default:i((()=>[e.extend&&parseFloat(e.extend.point)>0?(o(),d(a,{key:0,class:"text-[40rpx] inline-block"},{default:i((()=>[u(s,{class:"text-[40rpx] font-200"},{default:i((()=>[p(x(e.extend.point),1)])),_:2},1024),u(s,{class:"text-[32rpx] ml-[4rpx]"},{default:i((()=>[p("积分")])),_:1})])),_:2},1024)):c("v-if",!0),parseFloat(e.price)&&e.extend&&parseFloat(e.extend.point)>0?(o(),d(s,{key:1,class:"mx-[4rpx] text-[32rpx]"},{default:i((()=>[p("+")])),_:1})):c("v-if",!0),parseFloat(e.price)&&e.extend&&parseFloat(e.extend.point)>0?(o(),f(_,{key:2},[u(s,{class:"text-[40rpx] font-200"},{default:i((()=>[p(x(parseFloat(e.price).toFixed(2)),1)])),_:2},1024),u(s,{class:"text-[32rpx] ml-[4rpx]"},{default:i((()=>[p("元")])),_:1})],64)):c("v-if",!0),e.extend&&e.extend.is_newcomer?(o(),f(_,{key:3},[u(s,{class:"text-[24rpx]"},{default:i((()=>[p("￥")])),_:1}),u(s,{class:"text-[40rpx] font-500"},{default:i((()=>[p(x(parseFloat(e.price).toFixed(2).split(".")[0]),1)])),_:2},1024),u(s,{class:"text-[24rpx] font-500"},{default:i((()=>[p("."+x(parseFloat(e.price).toFixed(2).split(".")[1]),1)])),_:2},1024)],64)):c("v-if",!0),e.extend&&e.extend.is_impulse_buy?(o(),f(_,{key:4},[u(s,{class:"text-[24rpx]"},{default:i((()=>[p("￥")])),_:1}),u(s,{class:"text-[40rpx] font-500"},{default:i((()=>[p(x(parseFloat(e.goods_money).toFixed(2).split(".")[0]),1)])),_:2},1024),u(s,{class:"text-[24rpx] font-500"},{default:i((()=>[p("."+x(parseFloat(e.goods_money).toFixed(2).split(".")[1]),1)])),_:2},1024)],64)):c("v-if",!0),e.extend?c("v-if",!0):(o(),f(_,{key:5},[u(s,{class:"text-[24rpx]"},{default:i((()=>[p("￥")])),_:1}),u(s,{class:"text-[40rpx] font-500"},{default:i((()=>[p(x(parseFloat(e.price).toFixed(2).split(".")[0]),1)])),_:2},1024),u(s,{class:"text-[24rpx] font-500"},{default:i((()=>[p("."+x(parseFloat(e.price).toFixed(2).split(".")[1]),1)])),_:2},1024)],64))])),_:2},1024),u(s,{class:"text-right text-[26rpx]"},{default:i((()=>[p("x"+x(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),e.extend&&e.extend.is_impulse_buy&&e.num>1?(o(),d(a,{key:0,class:"flex items-center box-border mt-[8rpx]"},{default:i((()=>[u(l,{class:"h-[24rpx] w-[56rpx]",src:n(r)("addon/shop/impulse_buy.png"),mode:"heightFix"},null,8,["src"]),u(a,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:i((()=>[p(x(e.impulse_buy_tips),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0),e.extend&&e.extend.is_newcomer&&e.num>1?(o(),d(a,{key:1,class:"flex items-center box-border mt-[8rpx]"},{default:i((()=>[u(l,{class:"h-[24rpx] w-[56rpx]",src:n(r)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"]),u(a,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:i((()=>[p(" 第1"+x(e.unit)+"，￥"+x(parseFloat(e.extend.newcomer_price).toFixed(2))+"/"+x(e.unit)+"；第"+x(e.num>2?"2~"+e.num:"2")+x(e.unit)+"，￥"+x(parseFloat(e.price).toFixed(2))+"/"+x(e.unit),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0),"1"!=e.status||1==e.is_enable_refund?(o(),d(a,{key:2,class:"flex justify-end w-[100%] mt-[30rpx] mb-[20rpx]"},{default:i((()=>["1"!=e.status?(o(),d(a,{key:0,class:"text-[22rpx] text-[#303133] leading-[50rpx] px-[20rpx] border-[2rpx] border-solid border-[#999] rounded-full",onClick:t=>n(g)({url:"/addon/shop/pages/refund/detail",param:{order_refund_no:e.order_refund_no}})},{default:i((()=>[p("查看退款")])),_:2},1032,["onClick"])):1==e.is_enable_refund?(o(),d(a,{key:1,class:"text-[22rpx] text-[#303133] leading-[50rpx] px-[20rpx] border-[2rpx] border-solid border-[#999] rounded-full ml-[20rpx]",onClick:t=>{return a=e.order_goods_id,void g({url:"/addon/shop/pages/refund/apply",param:{order_id:U.value.order_id,order_goods_id:a}});var a}},{default:i((()=>[p("申请退款")])),_:2},1032,["onClick"])):c("v-if",!0)])),_:2},1024)):c("v-if",!0),c(" 商品的万能表单信息 "),e.form_record_id?(o(),d(a,{key:3,class:v({"diy-form-wrap":_e.value.length})},{default:i((()=>[u(Q,{record_id:e.form_record_id,completeLayout:"style-2",onCallback:me},null,8,["record_id"])])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1024)))),128)),U.value.gift_goods.length?(o(),d(a,{key:0,class:"pt-[20rpx] bg-[#f9f9f9] mt-[20rpx] mx-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)]"},{default:i((()=>[(o(!0),f(_,null,m(U.value.gift_goods,((e,t)=>(o(),d(a,{class:"order-goods-item flex justify-between flex-wrap px-[var(--pad-sidebar-m)] pb-[20rpx]",key:t},{default:i((()=>[u(a,{class:"w-[120rpx] h-[120rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",onClick:t=>ae(e.goods_id)},{default:i((()=>[u(b,{class:"overflow-hidden",radius:"var(--goods-rounded-big)",width:"120rpx",height:"120rpx",src:n(r)(e.goods_image_thumb_small?e.goods_image_thumb_small:""),model:"aspectFill"},{error:i((()=>[u(l,{class:"w-[120rpx] h-[120rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:n(r)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1032,["onClick"]),u(a,{class:"ml-[16rpx] py-[8rpx] flex flex-1 flex-col justify-between"},{default:i((()=>[u(a,{class:"flex items-center"},{default:i((()=>[u(a,{class:"bg-[var(--primary-color-light)] whitespace-nowrap text-[var(--primary-color)] rounded-[6rpx] text-[22rpx] flex items-center justify-center w-[64rpx] h-[34rpx] mr-[6rpx]"},{default:i((()=>[p(" 赠品 ")])),_:1}),u(a,{class:"text-[26rpx] max-w-[400rpx] truncate leading-[40rpx] text-[#333]"},{default:i((()=>[p(x(e.goods_name),1)])),_:2},1024)])),_:2},1024),u(a,{class:"flex items-center"},{default:i((()=>[e.sku_name?(o(),d(a,{key:0,class:"text-[22rpx] text-[var(--text-color-light9)] truncate max-w-[400rpx] leading-[28rpx]"},{default:i((()=>[p(x(e.sku_name),1)])),_:2},1024)):c("v-if",!0),u(a,{class:"ml-[auto] font-400 text-[26rpx] text-[#303133]"},{default:i((()=>[u(s,null,{default:i((()=>[p("x")])),_:1}),u(s,null,{default:i((()=>[p(x(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):c("v-if",!0)])),_:1},8,["class","style"]),u(a,{class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[u(a,{class:"justify-between card-template-item"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("orderNo")),1)])),_:1}),u(a,{class:"flex items-center text-[28rpx]"},{default:i((()=>[u(s,null,{default:i((()=>[p(x(U.value.order_no),1)])),_:1}),u(s,{class:"w-[2rpx] h-[20rpx] bg-[#999] mx-[10rpx]"}),u(s,{class:"text-[var(--primary-color)]",onClick:t[1]||(t[1]=e=>n(T)(U.value.order_no))},{default:i((()=>[p("复制")])),_:1})])),_:1})])),_:1}),U.value.out_trade_no?(o(),d(a,{key:0,class:"justify-between card-template-item"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("orderTradeNo")),1)])),_:1}),u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.out_trade_no),1)])),_:1})])),_:1})):c("v-if",!0),u(a,{class:"justify-between card-template-item"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("createTime")),1)])),_:1}),u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.create_time),1)])),_:1})])),_:1}),U.value.member_remark?(o(),d(a,{key:1,class:"justify-between card-template-item"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("memberRemark")),1)])),_:1}),u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.member_remark),1)])),_:1})])),_:1})):c("v-if",!0),u(a,{class:"card-template-item justify-between"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("deliveryType")),1)])),_:1}),u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.delivery_type_name),1)])),_:1})])),_:1}),U.value.pay?(o(),d(a,{key:2,class:v(["card-template-item justify-between",{"!mb-[18rpx]":U.value.member_id!==U.value.pay.main_id&&2==U.value.pay.status}])},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("payTypeName")),1)])),_:1}),u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.pay.type_name),1)])),_:1})])),_:1},8,["class"])):c("v-if",!0),U.value.pay&&U.value.member_id!==U.value.pay.main_id&&2==U.value.pay.status?(o(),d(a,{key:3,class:"card-template-item justify-end"},{default:i((()=>[u(a,{class:"friend-pay relative px-[20rpx] py-[12rpx] bg-[#F2F2F2] rounded-[10rpx] flex items-center"},{default:i((()=>[u(O,{src:n(r)(U.value.pay.pay_member_headimg),size:"20",leftIcon:"none","default-url":n(r)("static/resource/images/default_headimg.png")},null,8,["src","default-url"]),u(s,{class:"ml-[14rpx] text-[24rpx] using-hidden"},{default:i((()=>[p(x(U.value.pay.pay_member)+x(n(q)("helpPay")),1)])),_:1})])),_:1})])),_:1})):c("v-if",!0),U.value.pay?(o(),d(a,{key:4,class:"card-template-item justify-between"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("payTime")),1)])),_:1}),u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.pay.pay_time),1)])),_:1})])),_:1})):c("v-if",!0)])),_:1}),c(" 核销码 "),n(ue)?(o(),f(_,{key:3},[pe.value&&pe.value.length?(o(),d(a,{key:0,class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[pe.value.length>1?(o(),d(L,{key:0,class:"h-[450rpx]",circular:"","indicator-dots":"true"},{default:i((()=>[(o(!0),f(_,null,m(pe.value,((e,t)=>(o(),d(M,{key:t},{default:i((()=>[u(a,{class:"flex flex-col items-center justify-center"},{default:i((()=>[u(l,{src:e.qrcode,class:"w-[300rpx] h-[auto]",mode:"widthFix"},null,8,["src"])])),_:2},1024),u(a,{class:"flex items-center justify-center mt-[30rpx]"},{default:i((()=>[u(s,{class:"text-[28rpx] font-500"},{default:i((()=>[p(x(e.code),1)])),_:2},1024),u(s,{class:"text-[var(--text-color-light6)] text-[24rpx] ml-[10rpx] border-[2rpx] border-solid border-[#666] bg-[#f7f7f7] px-[12rpx] py-[6rpx] rounded",onClick:t=>n(T)(e.code)},{default:i((()=>[p("复制")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)))),128))])),_:1})):(o(),f(_,{key:1},[u(a,{class:"flex flex-col items-center justify-center"},{default:i((()=>[u(l,{src:pe.value[0].qrcode,class:"w-[300rpx] h-[auto]",mode:"widthFix"},null,8,["src"])])),_:1}),u(a,{class:"flex items-center justify-center mt-[30rpx]"},{default:i((()=>[u(s,{class:"text-[28rpx] font-500"},{default:i((()=>[p(x(pe.value[0].code),1)])),_:1}),u(s,{class:"text-[var(--text-color-light6)] text-[24rpx] ml-[10rpx] border-[2rpx] border-solid border-[#666] bg-[#f7f7f7] px-[12rpx] py-[6rpx] rounded",onClick:t[2]||(t[2]=e=>n(T)(pe.value[0].code))},{default:i((()=>[p("复制")])),_:1})])),_:1})],64))])),_:1})):c("v-if",!0),u(a,{class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[u(a,{class:"title"},{default:i((()=>[p("核销信息")])),_:1}),u(a,{class:"card-template-item justify-between"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p("核销次数")])),_:1}),u(a,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[p(x("剩余"+(ie.value.num-ie.value.verify_count)+"次")+"/"+x("共"+ie.value.num+"次"),1)])),_:1})])),_:1}),u(a,{class:"card-template-item justify-between"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p("有效期")])),_:1}),u(a,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[p(x(ie.value.verify_expire_time?ie.value.verify_expire_time:"永久"),1)])),_:1})])),_:1})])),_:1})],64)):c("v-if",!0),c(" 待付款订单的万能表单信息 "),U.value.form_record_id?(o(),d(a,{key:4,class:v({"sidebar-margin mt-[var(--top-m)] card-template":ve.value.length})},{default:i((()=>[u(Q,{record_id:U.value.form_record_id,completeLayout:"style-2",onCallback:ye},null,8,["record_id"])])),_:1},8,["class"])):c("v-if",!0),u(a,{class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[u(a,{class:"card-template-item justify-between"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("goodsMoney")),1)])),_:1}),u(a,{class:"price-font font-500"},{default:i((()=>[parseFloat(U.value.point)>0?(o(),d(s,{key:0,class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.point)+"积分",1)])),_:1})):c("v-if",!0),parseFloat(U.value.point)>0&&parseFloat(U.value.goods_money)?(o(),d(s,{key:1,class:"mx-[4rpx] text-[28rpx]"},{default:i((()=>[p("+")])),_:1})):c("v-if",!0),parseFloat(U.value.goods_money)||!parseFloat(U.value.point)?(o(),f(_,{key:2},[u(s,{class:"text-[28rpx]"},{default:i((()=>[p("￥")])),_:1}),u(s,{class:"text-[28rpx]"},{default:i((()=>[p(x(parseFloat(U.value.goods_money).toFixed(2).split(".")[0]),1)])),_:1}),u(s,{class:"text-[28rpx]"},{default:i((()=>[p("."+x(parseFloat(U.value.goods_money).toFixed(2).split(".")[1]),1)])),_:1})],64)):c("v-if",!0)])),_:1})])),_:1}),parseFloat(U.value.delivery_money)?(o(),d(a,{key:0,class:"card-template-item justify-between"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("deliveryMoney")),1)])),_:1}),u(a,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[p("￥"+x(parseFloat(U.value.delivery_money).toFixed(2)),1)])),_:1})])),_:1})):c("v-if",!0),c(' <view class=" card-template-item justify-between">\n                      <view class="text-[28rpx]">{{ t(\'discountMoney\') }}</view>\n                      <view class="price-font font-500 text-[28rpx]">\n                        -￥{{ parseFloat(detail.discount_money).toFixed(2) }}\n                      </view>\n                    </view> '),parseFloat(U.value.coupon_money)?(o(),d(a,{key:1,class:"card-template-item justify-between"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p("优惠券优惠")])),_:1}),u(a,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[p("-￥"+x(parseFloat(U.value.coupon_money).toFixed(2)),1)])),_:1})])),_:1})):c("v-if",!0),parseFloat(U.value.manjian_discount_money)?(o(),d(a,{key:2,class:"card-template-item justify-between"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p("满减优惠")])),_:1}),u(a,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[p("-￥"+x(parseFloat(U.value.manjian_discount_money).toFixed(2)),1)])),_:1})])),_:1})):c("v-if",!0),u(a,{class:"card-template-item justify-between items-baseline"},{default:i((()=>[u(a,{class:"text-[28rpx]"},{default:i((()=>[p(x(n(q)("orderMoney")),1)])),_:1}),u(a,{class:"text-[var(--price-text-color)] price-font"},{default:i((()=>[parseFloat(U.value.point)>0?(o(),d(s,{key:0,class:"text-[28rpx]"},{default:i((()=>[p(x(U.value.point)+"积分",1)])),_:1})):c("v-if",!0),parseFloat(U.value.point)>0&&parseFloat(U.value.order_money)?(o(),d(s,{key:1,class:"mx-[4rpx] text-[28rpx]"},{default:i((()=>[p("+")])),_:1})):c("v-if",!0),parseFloat(U.value.order_money)||!parseFloat(U.value.point)?(o(),d(s,{key:2,class:"text-[28rpx]"},{default:i((()=>[p("￥"+x(parseFloat(U.value.order_money).toFixed(2)),1)])),_:1})):c("v-if",!0)])),_:1})])),_:1})])),_:1}),u(a,{class:"flex z-2 justify-between items-center bg-[#fff] fixed left-0 right-0 bottom-0 min-h-[100rpx] pl-[30rpx] pr-[20rpx] flex-wrap pb-ios"},{default:i((()=>[u(a,{class:"flex"},{default:i((()=>[u(a,{class:"flex mr-[34rpx] flex-col justify-center items-center",onClick:t[3]||(t[3]=e=>se("index"))},{default:i((()=>[u(a,{class:"nc-iconfont nc-icon-shouyeV6xx11 text-[36rpx]"}),u(s,{class:"text-[20rpx] mt-[10rpx]"},{default:i((()=>[p(x(n(q)("index")),1)])),_:1})])),_:1})])),_:1}),u(a,{class:"flex justify-end"},{default:i((()=>[de(U.value)?(o(),d(a,{key:0,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center border-[2rpx] border-solid border-[#999] rounded-full ml-[20rpx] text-[var(--text-color-light6)]",onClick:t[4]||(t[4]=e=>se("logistics"))},{default:i((()=>[p(x(n(q)("logisticsTracking")),1)])),_:1})):c("v-if",!0),1==U.value.status?(o(),d(a,{key:1,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center text-center border-[2rpx] border-solid border-[#999] rounded-full ml-[20rpx] text-[var(--text-color-light6)]",onClick:t[5]||(t[5]=e=>se("close"))},{default:i((()=>[p(x(n(q)("orderClose")),1)])),_:1})):c("v-if",!0),1==U.value.status?(o(),d(a,{key:2,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center text-center text-[#fff] primary-btn-bg rounded-full ml-[20rpx]",onClick:t[6]||(t[6]=e=>se("pay"))},{default:i((()=>[p(x(n(q)("topay")),1)])),_:1})):c("v-if",!0),3==U.value.status?(o(),d(a,{key:3,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center text-center text-[#fff] primary-btn-bg rounded-full ml-[20rpx]",onClick:t[7]||(t[7]=e=>se("finish"))},{default:i((()=>[p(x(n(q)("orderFinish")),1)])),_:1})):c("v-if",!0),5==U.value.status&&W.value?(o(),f(_,{key:4},[1==U.value.is_evaluate||1!=U.value.is_evaluate&&1==K.value.is_evaluate?(o(),d(a,{key:0,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center border-[2rpx] border-solid border-[#999] rounded-full ml-[20rpx] !text-[var(--text-color-light6)]",onClick:t[8]||(t[8]=e=>se("evaluate"))},{default:i((()=>[p(x(1==U.value.is_evaluate?n(q)("selectedEvaluate"):n(q)("evaluate")),1)])),_:1})):c("v-if",!0)],64)):c("v-if",!0)])),_:1})])),_:1})])),_:1})),u(a,{class:"tab-bar-placeholder"}),u(R,{ref_key:"payRef",ref:le,onClose:e.payClose},null,8,["onClose"]),u($,{ref_key:"materialRef",ref:re},null,512),c(" 满减 "),u(A,{ref_key:"manjianShowRef",ref:Y},null,512)])),_:1})),u(I,{loading:X.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-f4884f33"]]);export{X as default};
