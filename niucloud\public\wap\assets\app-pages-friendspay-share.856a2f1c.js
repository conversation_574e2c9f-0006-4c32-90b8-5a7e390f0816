import{d as e,r as a,u as t,I as l,p as s,J as r,N as i,aq as u,ar as o,W as n,as as p,X as d,o as _,c as f,w as m,b as c,e as x,t as v,x as g,n as y,g as b,y as h,F as j,z as F,i as k,j as w,S as T,k as P,R as O,A as S,P as I,a as $,Q as B}from"./index-3caf046d.js";import{_ as C}from"./u-avatar.30e31e9c.js";import{_ as J}from"./u--image.eb573bce.js";import{_ as N}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{g as U,_ as R}from"./message.vue_vue_type_script_setup_true_lang.dd3641a4.js";import{t as z}from"./topTabbar.9217e319.js";import{s as A}from"./share-poster.0fbc73fb.js";import{_ as V}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-text.f02e6497.js";import"./u-image.04cba9a2.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-loading-icon.255170b9.js";import"./u-popup.1b30ffa7.js";import"./u-safe-bottom.98e092c5.js";const q=V(e({__name:"share",setup(e){const V=z();V.setTopTabbarParam({title:""});const q=a(!0),L=a(!1),M=a(0),Q=a(""),W=a({}),X=a(!0),D=a(null),{setShare:E}=t(),G=l(),H=s((()=>G.info));r((e=>{M.value=e.id||0,Q.value=e.type||""})),i((()=>{M.value&&Q.value&&K(Q.value,M.value)})),u((()=>{D.value&&(clearTimeout(D.value),D.value=null)})),o((()=>{D.value&&(clearTimeout(D.value),D.value=null)}));const K=(e,a)=>{X.value&&(q.value=!0,X.value=!1),U(e,a).then((t=>{W.value=t.data,q.value=!1,n({title:W.value.config.pay_page_name}),V.setTopTabbarParam({title:W.value.config.pay_page_name});let l="",s=location.pathname,r=["/app/","/addon/"];for(let e=0;e<r.length;e++)-1!=s.indexOf(r[e])&&(s=s.substr(0,s.indexOf(r[e])));l=location.origin+s+`/app/pages/friendspay/money?id=${W.value.trade_id}&type=${W.value.trade_type}`;let i={desc:W.value.config.pay_leave_message,path:`/app/pages/friendspay/money?id=${W.value.trade_id}&type=${W.value.trade_type}`,link:l};if(W.value.member){let e=W.value.member.nickname;e=e.length>15?e=e.substring(0,15)+"...":e,i.title=`${e}希望你帮他付${W.value.money}元`}"[]"!==JSON.stringify(W.value.trade_info)&&W.value.trade_info.item_list.length?i.url=W.value.trade_info.item_list[0].item_image?W.value.trade_info.item_list[0].item_image:W.value.config.pay_wechat_share_image:i.url=W.value.config.pay_wechat_share_image,E({wechat:{...i},weapp:{...i}}),se(),p((()=>{setTimeout((()=>{ee.value&&(le.id=W.value.trade_id,le.type=W.value.trade_type,H.value&&H.value.member_id&&(le.member_id=H.value.member_id),ee.value.loadPoster())}),400)})),2!=W.value.status&&1!=W.value.status&&-1!=W.value.status?D.value=setTimeout((()=>{K(e,a)}),3e3):(clearTimeout(D.value),D.value=null)})).catch((e=>{D.value&&(clearTimeout(D.value),D.value=null),q.value=!1;d({title:"未找到帮付订单信息",url:"/app/pages/index/index",mode:"reLaunch"})}))},Y=a(null),Z=()=>{Y.value.open(W.value.config)},ee=a(null),ae=a("/app/pages/friendspay/money"),te=a("");let le={};const se=()=>{te.value="?id="+W.value.trade_id,te.value+="&type="+W.value.trade_type},re=()=>{le.id=W.value.trade_id,le.type=W.value.trade_type,H.value&&H.value.member_id&&(le.member_id=H.value.member_id),ee.value.openShare()};return(e,a)=>{const t=k(w("u-avatar"),C),l=T,s=P,r=O,i=B,u=k(w("u--image"),J),o=k(w("loading-page"),N);return _(),f(s,{style:y(e.themeColor())},{default:m((()=>[Object.keys(W.value).length&&!q.value?(_(),f(s,{key:0,class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden"},{default:m((()=>[c(s,{style:y({background:"url("+x(S)("static/resource/images/app/friendpay_bg.png")+") left bottom /100% no-repeat"}),class:"pb-[168rpx] overflow-hidden"},{default:m((()=>[c(s,{class:"mt-[20rpx] flex flex-col items-center"},{default:m((()=>[c(t,{src:x(S)(W.value.member.headimg),size:"50",leftIcon:"none","default-url":x(S)("static/resource/images/default_headimg.png")},null,8,["src","default-url"]),c(s,{class:"flex items-center mt-[20rpx] text-[#fff] text-[26rpx] leading-[36rpx]"},{default:m((()=>[c(l,{class:"font-bold mr-[10rpx] max-w-[250rpx] truncate"},{default:m((()=>[v(g(W.value.member.nickname),1)])),_:1}),c(l,null,{default:m((()=>[v("发起了订单帮付请求~")])),_:1})])),_:1})])),_:1})])),_:1},8,["style"]),c(s,{class:"mt-[-128rpx] card-template sidebar-margin mb-[var(--top-m)]"},{default:m((()=>[c(s,{class:"text-[24rpx] text-center text-[#333] mb-[10rpx]"},{default:m((()=>[v(g(x(I)("payMoney")),1)])),_:1}),c(s,{class:"text-center mb-[50rpx]"},{default:m((()=>[c(l,{class:"text-[32rpx] font-500 price-font text-[#FF4142]"},{default:m((()=>[v("￥")])),_:1}),c(l,{class:"text-[56rpx] font-bold price-font text-[#FF4142]"},{default:m((()=>[v(g(parseFloat(W.value.money).toFixed(2).split(".")[0]),1)])),_:1}),c(l,{class:"text-[32rpx] font-500 price-font text-[#FF4142]"},{default:m((()=>[v("."+g(parseFloat(W.value.money).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),c(s,{class:"px-[20rpx] box-border"},{default:m((()=>[2==W.value.status?(_(),f(r,{key:0,class:"bg-[#FFB4B1] !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none"},{default:m((()=>[v(g(x(I)("finish")),1)])),_:1})):-1==W.value.status?(_(),f(r,{key:1,class:"bg-[#FFB4B1] !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none"},{default:m((()=>[v(g(x(I)("close")),1)])),_:1})):(_(),f(r,{key:2,class:"botton-color !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none",loading:L.value,onClick:re},{default:m((()=>[v(g(W.value.config.pay_type_name?W.value.config.pay_type_name:x(I)("friendPay")),1)])),_:1},8,["loading"]))])),_:1}),2==W.value.status&&"[]"!==JSON.stringify(W.value.trade_info)&&W.value.trade_info.detail_url?(_(),f(s,{key:0,class:"mt-[20rpx] flex items-baseline justify-center text-[var(--text-color-light9)]",onClick:a[0]||(a[0]=e=>x($)({url:W.value.trade_info.detail_url}))},{default:m((()=>[c(l,{class:"text-[24rpx] mr-[6rpx]"},{default:m((()=>[v("查看订单")])),_:1}),c(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx]"})])),_:1})):b("v-if",!0)])),_:1}),c(s,{class:"card-template sidebar-margin mb-[var(--top-m)]"},{default:m((()=>[c(s,{class:"flex justify-between items-center mb-[30rpx]"},{default:m((()=>[c(s,{class:"text-[30rpx] text-[#333] font-500"},{default:m((()=>[v(g(x(I)("friendPayOrderInfo")),1)])),_:1}),W.value.config.pay_explain_switch?(_(),f(s,{key:0,class:"text-[#666] leading-[1]",onClick:Z},{default:m((()=>[c(l,{class:"mr-[8rpx] text-[24rpx]"},{default:m((()=>[v(g(W.value.config.pay_explain_title),1)])),_:1}),c(l,{class:"nc-iconfont nc-icon-jichuxinxiV6xx text-[26rpx]"})])),_:1})):b("v-if",!0)])),_:1}),"[]"!==JSON.stringify(W.value.trade_info)?(_(),h(j,{key:0},[W.value.trade_info.item_list.length?(_(),h(j,{key:0},[c(s,{class:"border-0 border-solid border-b-[1rpx] border-[#f6f6f6] mb-[20rpx]"},{default:m((()=>[(_(!0),h(j,null,F(W.value.trade_info.item_list,((e,a)=>(_(),f(s,{class:"flex justify-between mb-[30rpx]"},{default:m((()=>[c(s,{class:"w-[170rpx] h-[170rpx] rounded-[var(--goods-rounded-big)] overflow-hidden flex-shrink-0"},{default:m((()=>[c(u,{class:"overflow-hidden",radius:"var(--goods-rounded-big)",width:"170rpx",height:"170rpx",src:x(S)(e.item_image?e.item_image:""),model:"aspectFill"},{error:m((()=>[c(i,{class:"w-[170rpx] h-[170rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:x(S)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1024),c(s,{class:"ml-[20rpx] flex flex-1 flex-col justify-between"},{default:m((()=>[c(s,null,{default:m((()=>[c(s,{class:"text-[28rpx] using-hidden leading-[40rpx] text-[#333]"},{default:m((()=>[v(g(e.item_name),1)])),_:2},1024),e.item_sub_name?(_(),f(s,{key:0,class:"text-[24rpx] mt-[14rpx] text-[var(--text-color-light9)] using-hidden leading-[28rpx]"},{default:m((()=>[v(g(e.item_sub_name),1)])),_:2},1024)):b("v-if",!0)])),_:2},1024),c(s,{class:"flex justify-between items-baseline"},{default:m((()=>[c(s,{class:"price-font text-[#FF4142]"},{default:m((()=>[c(l,{class:"text-[24rpx]"},{default:m((()=>[v("￥")])),_:1}),c(l,{class:"text-[40rpx] font-500"},{default:m((()=>[v(g(parseFloat(e.item_price).toFixed(2).split(".")[0]),1)])),_:2},1024),c(l,{class:"text-[24rpx] font-500"},{default:m((()=>[v("."+g(parseFloat(e.item_price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),c(l,{class:"text-right text-[26rpx]"},{default:m((()=>[v("x"+g(e.item_num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1}),c(s,{class:"text-[26rpx] text-right"},{default:m((()=>[v(g(W.value.trade_info.item_total),1)])),_:1})],64)):b("v-if",!0)],64)):(_(),f(s,{key:1,class:"text-[28rpx] leading-[40rpx] text-[#333]"},{default:m((()=>[v(g(W.value.body),1)])),_:1}))])),_:1}),c(A,{ref_key:"sharePosterRef",ref:ee,posterType:"friendspay",posterId:W.value.poster_id,posterParam:x(le),copyUrl:ae.value,copyUrlParam:te.value},null,8,["posterId","posterParam","copyUrl","copyUrlParam"]),b(" 帮付说明 "),c(R,{ref_key:"messageRef",ref:Y},null,512)])),_:1})):b("v-if",!0),c(o,{loading:q.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-81015a81"]]);export{q as default};
