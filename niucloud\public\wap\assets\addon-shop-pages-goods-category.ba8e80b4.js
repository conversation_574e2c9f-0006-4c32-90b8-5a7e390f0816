import{d as e,r as t,_ as l,o,c as r,w as a,e as s,b as i,O as u,g as d,y as c,z as n,F as p,$ as x,a as f,S as v,al as g,k as _,au as m,i as b,j as h,t as y,x as k,A as w,Q as C,p as j,s as S,M as I,R as V,D as F,bH as N,I as B,f as M,v as E,n as R,am as L,an as U,U as $,J as z,N as O,W as T}from"./index-3caf046d.js";import{_ as H}from"./u--image.eb573bce.js";import{_ as D}from"./tabbar.2c31519d.js";import{_ as P}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{c as A,o as W,d as J,p as Y}from"./goods.6a81cb49.js";import{M as K}from"./mescroll-empty.d02c7bd6.js";import{_ as Q}from"./_plugin-vue_export-helper.1b428a4d.js";import{M as Z}from"./mescroll-body.36f14dc3.js";import{u as q}from"./useMescroll.26ccf5de.js";import{_ as G}from"./u-number-box.8a6aafb5.js";import{_ as X}from"./u-popup.1b30ffa7.js";import{u as ee}from"./cart.ef6e9a72.js";import{b as te}from"./bind-mobile.25318c0e.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-tabbar-item.31141540.js";import"./u-tabbar.38f37e13.js";import"./u-safe-bottom.98e092c5.js";import"./u-loading-icon.255170b9.js";import"./mescroll-i18n.e7c22011.js";import"./u-form.49dbb57f.js";import"./u-line.69c0c00f.js";import"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import"./u-input.2d8dc7a4.js";import"./u-modal.8624728a.js";import"./u-checkbox-group.0328273c.js";const le=Q(e({__name:"category-template-two-one",props:{config:{type:Object,default:()=>({})},categoryId:{type:[String,Number],default:0}},setup(e){const j=e;let S=j.config,I=j.categoryId;const V=t(""),F=t(!0);l((()=>{B()}));const N=t([]),B=()=>{F.value=!0,A().then((e=>{if(N.value=e.data,I)for(let t=0;t<N.value.length;t++){if(N.value[t].category_id==I){M.value=t;break}if(N.value[t].child_list)for(let e=0;e<N.value[t].child_list.length;e++)if(N.value[t].child_list[e].category_id==I){M.value=t;break}}F.value=!1})).catch((()=>{F.value=!1}))},M=t(0),E=()=>{f({url:"/addon/shop/pages/goods/list",param:{goods_name:encodeURIComponent(V.value)}})};return(e,t)=>{const l=v,j=g,I=_,B=m,R=C,L=b(h("u--image"),H),U=b(h("tabbar"),D),$=b(h("loading-page"),P);return o(),r(I,{class:"bg-[var(--page-bg-color)] overflow-hidden min-h-screen"},{default:a((()=>[N.value.length?(o(),r(I,{key:0,class:"mescroll-box"},{default:a((()=>[s(S).search.control?(o(),r(I,{key:0,class:"search-box box-border z-10 fixed top-0 left-0 right-0 h-[100rpx] bg-[#fff]"},{default:a((()=>[i(I,{class:"flex-1 search-input"},{default:a((()=>[i(l,{onClick:u(E,["stop"]),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"},null,8,["onClick"]),i(j,{class:"input",type:"text",modelValue:V.value,"onUpdate:modelValue":t[0]||(t[0]=e=>V.value=e),modelModifiers:{trim:!0},placeholder:s(S).search.title,placeholderClass:"text-[var(--text-color-light9)]",onConfirm:E},null,8,["modelValue","placeholder"]),V.value?(o(),r(l,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[1]||(t[1]=e=>V.value="")})):d("v-if",!0)])),_:1})])),_:1})):d("v-if",!0),i(I,{class:x(["tabs-box z-2 fixed left-0 bg-[#fff] bottom-[50px] top-0",{"!top-[100rpx]":s(S).search.control}])},{default:a((()=>[i(B,{"scroll-y":!0,class:"scroll-height"},{default:a((()=>[i(I,{class:"bg-[var(--temp-bg)]"},{default:a((()=>[(o(!0),c(p,null,n(N.value,((e,t)=>(o(),r(I,{class:x(["tab-item",{"tab-item-active":t==M.value,"rounded-br-[12rpx]":M.value-1===t,"rounded-tr-[12rpx]":M.value+1===t}]),key:t,onClick:e=>((e,t)=>{M.value=e})(t)},{default:a((()=>[i(I,{class:"text-box text-left leading-[1.3] break-words px-[16rpx]"},{default:a((()=>[y(k(e.category_name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["class"]),i(B,{class:"h-[100vh]","scroll-y":!0},{default:a((()=>[i(I,{class:x(["pl-[188rpx] scroll-ios pt-[20rpx] pr-[20rpx]",{"!pt-[120rpx]":s(S).search.control}])},{default:a((()=>{var e,t;return[(null==(e=N.value[M.value])?void 0:e.child_list)&&!F.value?(o(),r(I,{key:0,class:"bg-[#fff] grid grid-cols-3 gap-x-[50rpx] gap-y-[32rpx] py-[32rpx] px-[24rpx] rounded-[var(--rounded-big)]"},{default:a((()=>{var e;return[(o(!0),c(p,null,n(null==(e=N.value[M.value])?void 0:e.child_list,((e,t)=>(o(),r(I,{key:e.category_id,onClick:t=>{return l=e.category_id,void f({url:"/addon/shop/pages/goods/list",param:{curr_goods_category:l}});var l},class:"flex items-center justify-center flex-col"},{default:a((()=>[i(L,{radius:"var(--goods-rounded-big)",width:"120rpx",height:"120rpx",src:s(w)(e.image?e.image:""),model:"aspectFill"},{error:a((()=>[i(R,{class:"rounded-[var(--goods-rounded-big)] overflow-hidden w-[120rpx] h-[120rpx]",src:s(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),i(I,{class:"text-[24rpx] text-center mt-[16rpx] leading-[34rpx]"},{default:a((()=>[y(k(e.category_name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))]})),_:1})):d("v-if",!0),(null==(t=N.value[M.value])?void 0:t.child_list)||F.value?d("v-if",!0):(o(),r(K,{key:1,class:"part",option:{tip:"暂无商品分类"}}))]})),_:1},8,["class"])])),_:1})])),_:1})):d("v-if",!0),i(U),N.value.length||F.value?d("v-if",!0):(o(),r(K,{key:1,option:{tip:"暂无商品分类"}})),i($,{loading:F.value},null,8,["loading"])])),_:1})}}}),[["__scopeId","data-v-09e06f5f"]]),oe=Q(e({__name:"add-cart-popup",setup(e,{expose:l}){const f=ee(),B=j((()=>f.cartList)),M=t(!1),E=t({skuId:"",name:[]}),R=t({}),L=t({}),U=t(1),$=t(0),z=t(0),O=t(0),T=t(0),D=()=>{setTimeout((()=>{(!U.value||U.value<=z.value)&&(U.value=z.value||1),U.value>=$.value&&(U.value=$.value),z.value>U.value&&(U.value=0)}),0)},P=()=>{setTimeout((()=>{(!U.value||U.value<=z.value)&&(U.value=z.value||1),U.value>=$.value&&(U.value=$.value),z.value>U.value&&(U.value=0,I({title:"库存数量小于起购数量",icon:"none"}))}),0)},A=()=>{M.value=!1},J=j((()=>{let e=F(R.value);if(Object.keys(e).length){if(Object.keys(E.value.name).length||(E.value.name=e.sku_spec_format.split(",")),e.goodsSpec.forEach(((e,t)=>{let l=e.spec_values.split(",");e.values=[],l.forEach(((l,o)=>{e.values[o]={},e.values[o].name=l,e.values[o].selected=!1,e.values[o].disabled=!1,E.value.name.forEach(((r,a)=>{a==t&&r==l&&(e.values[o].selected=!0)}))}))})),Y(),e.skuList&&Object.keys(e.skuList).length&&e.skuList.forEach((e=>{e.sku_id==E.value.skuId&&(L.value=e)})),$.value=L.value.stock,e.goods.is_limit){if(e.goods&&e.goods.max_buy){let t=0;if(1==e.goods.limit_type)t=e.goods.max_buy;else{let l=e.goods.max_buy-(e.has_buy||0);t=l>0?l:0}t>L.value.stock?$.value=L.value.stock:t<=L.value.stock&&($.value=t)}O.value=e.goods.max_buy,0==$.value&&(U.value=0)}z.value=e.goods.min_buy,U.value=z.value>0?e.goods.min_buy:1,T.value=e.goods.min_buy,z.value>L.value.stock&&(U.value=0)}return e}));S((()=>L.value),((e,t)=>{if(B.value["goods_"+L.value.goods_id]&&B.value["goods_"+L.value.goods_id]["sku_"+L.value.sku_id])U.value=N(B.value["goods_"+L.value.goods_id]["sku_"+L.value.sku_id].num),L.value.cart_id=N(B.value["goods_"+L.value.goods_id]["sku_"+L.value.sku_id].id);else{let e=1;z.value>0&&(e=z.value),z.value>L.value.stock&&(e=0),U.value=e,L.value.cart_id=""}}));const Y=()=>{R.value.skuList.forEach(((e,t)=>{let l=e.sku_spec_format.split(",");E.value.name.every((e=>l.includes(e)))&&(E.value.skuId=e.sku_id)}))},K=()=>{if(z.value&&z.value>L.value.stock)I({title:"商品库存小于起购数量",icon:"none"});else if(J.value.goods.is_limit){let e=`该商品单次限购${J.value.goods.max_buy}件`;1!=J.value.goods.limit_type&&(e=`该商品每人限购${J.value.goods.max_buy}件`),U.value>=J.value.goods.max_buy&&I({title:e,icon:"none"})}},Q=()=>{if(J.value.goods.is_limit&&z.value){let e=`该商品起购${z.value}件`;U.value<=z.value&&I({title:e,icon:"none"})}},Z=()=>{0==U.value?f.reduce({id:L.value.cart_id||"",goods_id:L.value.goods_id,sale_price:L.value.show_price,sku_id:L.value.sku_id}):f.increase({id:L.value.cart_id||"",goods_id:L.value.goods_id,sku_id:L.value.sku_id,stock:L.value.stock,sale_price:L.value.show_price,num:U.value},0,(()=>{I({title:"加入购物车成功",icon:"none"})})),M.value=!1},q=e=>{let t="0.00";return t=e.show_price,t},te=e=>{let t="";return t=e.show_type,t};return l({open:e=>{(e=>{W(e).then((t=>{R.value=t.data,E.value.sku_id=e,R.value.skuList&&Object.keys(R.value.skuList).length&&R.value.skuList.forEach((t=>{t.sku_id==e&&(L.value=t,E.value.name=t.sku_spec_format.split(","))})),M.value=!0}))})(e)}}),(e,t)=>{const l=C,f=b(h("u--image"),H),j=v,S=_,I=g,F=b(h("u-number-box"),G),N=m,R=V,W=b(h("u-popup"),X);return o(),r(S,{onTouchmove:t[9]||(t[9]=u((()=>{}),["prevent","stop"]))},{default:a((()=>[i(W,{show:M.value,onClose:A,mode:"bottom"},{default:a((()=>[Object.keys(s(J)).length?(o(),r(S,{key:0,onTouchmove:t[8]||(t[8]=u((()=>{}),["prevent","stop"])),class:"rounded-t-[20rpx] overflow-hidden bg-[#fff] py-[32rpx] relative"},{default:a((()=>[i(S,{class:"flex px-[32rpx] mb-[58rpx]"},{default:a((()=>[i(f,{width:"180rpx",height:"180rpx",radius:"var(--goods-rounded-big)",src:s(w)(L.value.sku_image),model:"aspectFill"},{error:a((()=>[i(l,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:s(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["radius","src"]),i(S,{class:"flex flex-1 flex-col justify-between ml-[20rpx] py-[10rpx]"},{default:a((()=>[i(S,{class:"w-[100%]"},{default:a((()=>[i(S,{class:"text-[var(--price-text-color)] flex items-baseline"},{default:a((()=>[i(j,{class:"text-[32rpx] font-bold price-font mr-[4rpx]"},{default:a((()=>[y("￥")])),_:1}),i(j,{class:"text-[48rpx] price-font"},{default:a((()=>[y(k(parseFloat(q(L.value)).toFixed(2).split(".")[0]),1)])),_:1}),i(j,{class:"text-[32rpx] price-font"},{default:a((()=>[y("."+k(parseFloat(q(L.value)).toFixed(2).split(".")[1]),1)])),_:1}),"member_price"==te(L.value)?(o(),r(l,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:s(w)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==te(L.value)?(o(),r(l,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:s(w)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==te(L.value)?(o(),r(l,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:s(w)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):d("v-if",!0)])),_:1}),i(S,{class:"text-[26rpx] leading-[32rpx] text-[#303133] mt-[12rpx]"},{default:a((()=>[y("库存"+k(L.value.stock)+k(s(J).goods.unit),1)])),_:1})])),_:1}),s(J).goodsSpec&&s(J).goodsSpec.length?(o(),r(S,{key:0,class:"w-[100%] text-[26rpx] leading-[30rpx] text-[var(--text-color-light6)] multi-hidden max-h-[60rpx]"},{default:a((()=>[y(" 已选规格："+k(L.value.sku_spec_format),1)])),_:1})):d("v-if",!0)])),_:1})])),_:1}),i(N,{class:"h-[500rpx] box-border px-[32rpx] mb-[30rpx]","scroll-y":"true"},{default:a((()=>[(o(!0),c(p,null,n(s(J).goodsSpec,((e,t)=>(o(),r(S,{class:x({"mt-[36rpx]":0!=t}),key:t},{default:a((()=>[i(S,{class:"text-[26rpx] leading-[36rpx] mb-[24rpx]"},{default:a((()=>[y(k(e.spec_name),1)])),_:2},1024),i(S,{class:"flex flex-wrap"},{default:a((()=>[(o(!0),c(p,null,n(e.values,((e,l)=>(o(),r(S,{class:x(["box-border bg-[#f2f2f2] text-[24rpx] px-[44rpx] text-center h-[56rpx] leading-[52rpx] mr-[20rpx] mb-[20rpx] border-1 border-solid rounded-[50rpx] border-[#f2f2f2]",{"!border-[var(--primary-color)] text-[var(--primary-color)] !bg-[var(--primary-color-light)]":e.selected}]),key:l,onClick:l=>((e,t)=>{E.value.name[t]=e.name})(e,t)},{default:a((()=>[y(k(e.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1032,["class"])))),128)),i(S,{class:"flex justify-between items-center mt-[8rpx]"},{default:a((()=>[i(S,{class:"text-[26rpx]"},{default:a((()=>[y("购买数量")])),_:1}),O.value>0&&T.value>1?(o(),r(j,{key:0,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:a((()=>[y("("+k(T.value)+k(s(J).goods.unit)+"起售，限购"+k(O.value)+k(s(J).goods.unit)+")",1)])),_:1})):O.value>0?(o(),r(j,{key:1,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:a((()=>[y("(限购"+k(O.value)+k(s(J).goods.unit)+")",1)])),_:1})):T.value>1?(o(),r(j,{key:2,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:a((()=>[y("("+k(T.value)+k(s(J).goods.unit)+"起售)",1)])),_:1})):d("v-if",!0),s(B)["goods_"+L.value.goods_id]&&s(B)["goods_"+L.value.goods_id]["sku_"+L.value.sku_id]?(o(),r(F,{key:3,modelValue:U.value,"onUpdate:modelValue":t[3]||(t[3]=e=>U.value=e),min:z.value,max:$.value,integer:"",step:1,"input-width":"98rpx","input-height":"54rpx"},{minus:a((()=>[i(S,{class:"relative w-[34rpx] h-[34rpx]",onClick:t[0]||(t[0]=e=>Q())},{default:a((()=>[i(j,{class:x(["text-[34rpx] nc-iconfont nc-icon-jianV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":U.value<=z.value}])},null,8,["class"])])),_:1})])),input:a((()=>[i(I,{class:"text-[#303133] text-[28rpx] mx-[10rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:D,onBlur:P,modelValue:U.value,"onUpdate:modelValue":t[1]||(t[1]=e=>U.value=e)},null,8,["modelValue"])])),plus:a((()=>[i(S,{class:"relative w-[34rpx] h-[34rpx]",onClick:t[2]||(t[2]=e=>K())},{default:a((()=>[i(j,{class:x(["text-[34rpx] nc-iconfont nc-icon-jiahaoV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":U.value>=$.value}])},null,8,["class"])])),_:1})])),_:1},8,["modelValue","min","max"])):(o(),r(F,{key:4,modelValue:U.value,"onUpdate:modelValue":t[7]||(t[7]=e=>U.value=e),min:z.value,max:$.value,integer:"",step:1,"input-width":"98rpx","input-height":"54rpx"},{minus:a((()=>[i(S,{class:"relative w-[34rpx] h-[34rpx]",onClick:t[4]||(t[4]=e=>Q())},{default:a((()=>[i(j,{class:x(["text-[34rpx] nc-iconfont nc-icon-jianV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":U.value<=z.value}])},null,8,["class"])])),_:1})])),input:a((()=>[i(I,{class:"text-[#303133] text-[28rpx] mx-[10rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:D,onBlur:P,modelValue:U.value,"onUpdate:modelValue":t[5]||(t[5]=e=>U.value=e)},null,8,["modelValue"])])),plus:a((()=>[i(S,{class:"relative w-[34rpx] h-[34rpx]",onClick:t[6]||(t[6]=e=>K())},{default:a((()=>[i(j,{class:x(["text-[34rpx] nc-iconfont nc-icon-jiahaoV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":U.value>=$.value}])},null,8,["class"])])),_:1})])),_:1},8,["modelValue","min","max"]))])),_:1})])),_:1}),i(S,{class:"px-[20rpx]"},{default:a((()=>[L.value.stock>0?(o(),r(R,{key:0,class:"!h-[80rpx] font-500 primary-btn-bg leading-[80rpx] text-[26rpx] rounded-[50rpx]",type:"primary",onClick:Z},{default:a((()=>[y("确定")])),_:1})):(o(),r(R,{key:1,class:"!h-[80rpx] leading-[80rpx] font-500 text-[26rpx] text-[#fff] bg-[#ccc] rounded-[50rpx]"},{default:a((()=>[y("已售罄")])),_:1}))])),_:1})])),_:1})):d("v-if",!0)])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-8d946487"]]),re=Q(e({__name:"category-template-one-one",props:{config:{type:Object,default:()=>({})},categoryId:{type:[String,Number],default:0}},setup(e){const S=e,N=ee();N.getList();const z=j((()=>N.cartList)),O=j((()=>N.totalNum)),T=j((()=>N.totalMoney)),W=B(),Y=j((()=>W.info)),{mescrollInit:Q,getMescroll:G}=q(U,L);let X=S.config,le=S.categoryId;const re=t([]),ae=t(""),se=t(!0),ie=t(!1);t([]).value=uni.getStorageSync("shopCart")||[];const ue=e=>{ie.value=!1,J({page:e.num,limit:e.size,goods_category:le}).then((t=>{let l=t.data.data;1==e.num&&(re.value=[]),re.value=re.value.concat(l),de(),se.value=!1,e.endSuccess(l.length),re.value.length||(ie.value=!0)})).catch((()=>{se.value=!1,ie.value=!0,e.endErr()}))},de=()=>{re.value.forEach(((e,t)=>{e.isMaxBuy=!1;let l=-1;if(e.is_limit&&e.max_buy){let t=0;if(1==e.limit_type)t=e.max_buy;else{let l=e.max_buy-(e.has_buy||0);t=l>0?l:0}t>e.stock?l=e.stock:t<=e.stock&&(l=t)}0==l&&(e.isMaxBuy=!0)}))},ce=e=>{f({url:"/addon/shop/pages/goods/detail",param:{goods_id:e}})};l((()=>{ge()}));const ne=t(""),pe=t(!1),xe=t(!1),fe=(e,t)=>{if(pe.value||xe.value)return!1;pe.value=!0,xe.value=!0;let l={goods_id:e.goodsSku.goods_id,sku_id:e.goodsSku.sku_id,sale_price:je(e),stock:e.goodsSku.stock};e.id&&(l.num=e.num,l.id=e.id);let o=1;o=e.min_buy>0&&!e.num?e.min_buy:1,N.increase(l,o,(()=>{xe.value=!1})),setTimeout((()=>{const e=window.document.getElementById("animation-end"),l=e.getBoundingClientRect().left,o=e.getBoundingClientRect().top,r=window.document.getElementById(t),a=r.getBoundingClientRect().left,s=r.getBoundingClientRect().top;ne.value=`top: ${s}px; left: ${a}px;`,setTimeout((()=>{ne.value=`top: ${o+e.offsetHeight/2-r.offsetHeight/3}px; left: ${l+e.offsetWidth/2-r.offsetHeight/3}px; transition: all 0.8s; transform: rotate(-720deg);`}),20),setTimeout((()=>{ne.value="",pe.value=!1}),1020)}),100)},ve=t([]),ge=()=>{se.value=!0,A().then((e=>{if(ve.value=e.data,le)for(let t=0;t<ve.value.length;t++){if(ve.value[t].category_id==le){_e.value=t;break}if(ve.value[t].child_list)for(let e=0;e<ve.value[t].child_list.length;e++)if(ve.value[t].child_list[e].category_id==le){_e.value=t;break}}else le=e.data[0].category_id;se.value=!1})).catch((()=>{se.value=!1}))},_e=t(0),me=()=>{f({url:"/addon/shop/pages/goods/list",param:{goods_name:encodeURIComponent(ae.value)}})},be=t(),he=(e,t)=>{if("virtual"==e.goods_type&&"verify"==e.virtual_receive_type)return ce(e.goodsSku.goods_id);if("cart"!==X.cart.event)return ce(e.goodsSku.goods_id);if(!Y.value)return $().setLoginBack({url:"/addon/shop/pages/goods/category"}),!1;if(e.goodsSku.sku_spec_format)be.value.open(e.goodsSku.sku_id);else{if(!e.goodsSku.stock||parseInt(e.goodsSku.num||0)>parseInt(e.goodsSku.stock))return void I({title:"商品库存不足",icon:"none"});if(e.min_buy&&e.min_buy>parseInt(e.stock))return void I({title:"商品库存小于起购数量",icon:"none"});fe(e,t)}},ye=()=>{f({url:"/addon/shop/pages/goods/cart"})},ke=t(null);t(uni.getStorageSync("isBindMobile"));const we=()=>{if(!O.value)return void I({title:"还没有选择商品",icon:"none"});const e=[];Object.values(z.value).forEach((t=>{Object.keys(t).forEach((l=>{"totalNum"!=l&&"totalMoney"!=l&&e.push(t[l].id)}))})),0!=e.length&&uni.setStorage({key:"orderCreateData",data:{cart_ids:e},success(){f({url:"/addon/shop/pages/order/payment"})}})},Ce=e=>{let t="";return t=e.goodsSku.show_type,t},je=e=>{let t="0.00";return t=e.goodsSku.show_price,t};return(e,t)=>{const l=v,f=g,j=_,S=m,B=C,L=b(h("u--image"),H),U=V,$=b(h("loading-page"),P),A=b(h("tabbar"),D);return o(),r(j,{class:"min-h-screen bg-[var(--page-bg-color)] overflow-hidden"},{default:a((()=>[ve.value.length?(o(),r(j,{key:0,class:x(["mescroll-box bg-[var(--page-bg-color)]",{cart:s(X).cart.control&&"cart"===s(X).cart.event,detail:!(s(X).cart.control&&"cart"===s(X).cart.event)}])},{default:a((()=>[i(Z,{ref:"mescrollRef",down:{use:!1},onInit:s(Q),onUp:ue},{default:a((()=>[s(X).search.control?(o(),r(j,{key:0,class:"search-box z-10 bg-[#fff] fixed top-0 left-0 right-0 h-[100rpx] box-border"},{default:a((()=>[i(j,{class:"flex-1 search-input"},{default:a((()=>[i(l,{onClick:u(me,["stop"]),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"},null,8,["onClick"]),i(f,{class:"input",type:"text",modelValue:ae.value,"onUpdate:modelValue":t[0]||(t[0]=e=>ae.value=e),modelModifiers:{trim:!0},placeholder:s(X).search.title,onConfirm:me,placeholderClass:"text-[var(--text-color-light9)]"},null,8,["modelValue","placeholder"]),ae.value?(o(),r(l,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[1]||(t[1]=e=>ae.value="")})):d("v-if",!0)])),_:1})])),_:1})):d("v-if",!0),i(j,{class:x(["tabs-box z-2 fixed left-0 bg-[#fff] bottom-[50px] top-0",{"!top-[100rpx]":s(X).search.control,"pb-[98rpx]":s(X).cart.control&&"cart"===s(X).cart.event}])},{default:a((()=>[i(S,{"scroll-y":!0,class:"scroll-height"},{default:a((()=>[i(j,{class:"bg-[var(--temp-bg)]"},{default:a((()=>[(o(!0),c(p,null,n(ve.value,((e,t)=>(o(),r(j,{class:x(["tab-item",{"tab-item-active":t==_e.value,"rounded-br-[12rpx]":_e.value-1===t,"rounded-tr-[12rpx]":_e.value+1===t}]),key:t,onClick:l=>((e,t)=>{_e.value=e,le=t.category_id,re.value=[],G().resetUpScroll()})(t,e)},{default:a((()=>[i(j,{class:"text-box text-left leading-[1.3] break-words px-[16rpx]"},{default:a((()=>[y(k(e.category_name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["class"]),i(j,{class:x(["flex justify-center flex-wrap pl-[188rpx] pb-[20rpx]",{" pt-[120rpx]":s(X).search.control," pt-[20rpx]":!s(X).search.control}])},{default:a((()=>[(o(!0),c(p,null,n(re.value,((e,t)=>(o(),r(j,{key:e.goods_id,class:x(["box-border bg-white w-full flex mr-[20rpx] py-[24rpx] px-[20rpx] rounded-[var(--rounded-small)]",{"mt-[16rpx]":t}]),onClick:u((t=>ce(e.goods_id)),["stop"])},{default:a((()=>[i(j,{class:"w-[168rpx] h-[168rpx] flex items-center justify-center rounded-[var(--goods-rounded-small)] overflow-hidden"},{default:a((()=>[i(L,{width:"168rpx",height:"168rpx",radius:"var(--goods-rounded-small)",src:s(w)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:a((()=>[i(B,{class:"w-[168rpx] h-[168rpx]",src:s(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1024),i(j,{class:"flex flex-1 ml-[20rpx] flex-wrap flex-col"},{default:a((()=>[i(j,{class:"max-h-[80rpx] text-[26rpx] leading-[40rpx] multi-hidden"},{default:a((()=>[y(k(e.goods_name),1)])),_:2},1024),i(j,{class:"flex-1 flex items-end justify-between"},{default:a((()=>[i(j,{class:"text-[var(--price-text-color)] price-font -mb-[8rpx]"},{default:a((()=>[i(l,{class:"text-[24rpx] font-500"},{default:a((()=>[y("￥")])),_:1}),i(l,{class:"text-[40rpx] font-500"},{default:a((()=>[y(k(parseFloat(je(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),i(l,{class:"text-[24rpx] font-500"},{default:a((()=>[y("."+k(parseFloat(je(e)).toFixed(2).split(".")[1]),1)])),_:2},1024),"member_price"==Ce(e)?(o(),r(B,{key:0,class:"h-[24rpx] max-w-[46rpx] ml-[6rpx]",src:s(w)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):d("v-if",!0),"newcomer_price"==Ce(e)?(o(),r(B,{key:1,class:"h-[24rpx] max-w-[60rpx] ml-[6rpx]",src:s(w)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):d("v-if",!0),"discount_price"==Ce(e)?(o(),r(B,{key:2,class:"h-[24rpx] max-w-[80rpx] ml-[6rpx]",src:s(w)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):d("v-if",!0)])),_:2},1024),e.isMaxBuy?d("v-if",!0):(o(),c(p,{key:0},[("real"==e.goods_type||"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type)&&""===e.goodsSku.sku_spec_format&&s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id]&&s(X).cart.control&&"cart"===s(X).cart.event?(o(),r(j,{key:0,class:"flex items-center"},{default:a((()=>[i(j,{class:"relative w-[32rpx] h-[32rpx]"},{default:a((()=>[i(l,{class:"text-[32rpx] text-color nc-iconfont nc-icon-jianshaoV6xx absolute flex items-center justify-center -left-[14rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",onClick:u((t=>((e,t)=>{if(xe.value)return!1;xe.value=!0;let l=1;e.min_buy>0&&e.min_buy==t.num&&(l=e.min_buy),N.reduce({id:t.id,goods_id:t.goods_id,sku_id:t.sku_id,stock:t.stock,sale_price:t.sale_price,num:t.num},l,(()=>{xe.value=!1}))})(e,s(z)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["onClick"])])),_:2},1024),i(l,{class:"text-[#333] text-[24rpx] mx-[16rpx]"},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id].num),1)])),_:2},1024),i(j,{class:"relative w-[32rpx] h-[32rpx]"},{default:a((()=>[i(l,{class:"text-[32rpx] text-color iconfont iconjiahao2fill absolute flex items-center justify-center -left-[14rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",id:"itemCart"+t,onClick:u((l=>((e,t,l)=>{if(parseInt(t.num)>=parseInt(t.stock))return void I({title:"商品库存不足",icon:"none"});let o=t.num;if(e.min_buy>0&&e.min_buy>o&&(o=e.min_buy),e.is_limit&&e.max_buy){let t=0;if(1==e.limit_type)t=e.max_buy;else{let l=e.max_buy-(e.has_buy||0);t=l>0?l:0}e.goodsSku.stock,e.goodsSku.stock}if(e.is_limit&&o>=e.max_buy){let t=`该商品单次限购${e.max_buy}件`;return 1!=e.limit_type&&(t=`该商品每人限购${e.max_buy}件`),I({title:t,icon:"none"}),!1}let r=F(e);r.num=o,r.id=t.id,fe(r,l)})(e,s(z)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id],"itemCart"+t)),["stop"])},null,8,["id","onClick"])])),_:2},1024)])),_:2},1024)):"virtual"==e.goods_type&&"cart"!==s(X).cart.event||"real"==e.goods_type?(o(),c(p,{key:1},[s(X).cart.control&&"style-1"===s(X).cart.style?(o(),r(j,{key:0,class:"h-[44rpx] relative"},{default:a((()=>[i(j,{id:"itemCart"+t,class:"w-[102rpx] box-border text-center text-[#fff] primary-btn-bg h-[46rpx] text-[22rpx] leading-[46rpx] rounded-[100rpx]",onClick:u((l=>he(e,"itemCart"+t)),["stop"])},{default:a((()=>[y(k(s(X).cart.text),1)])),_:2},1032,["id","onClick"]),s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id].totalNum?(o(),r(j,{key:0,class:x(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)]  text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(z)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):d("v-if",!0)])),_:2},1024)):d("v-if",!0),s(X).cart.control&&"style-2"===s(X).cart.style?(o(),r(j,{key:1,class:"w-[50rpx] h-[50rpx] relative",onClick:u((l=>he(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(l,{id:"itemCart"+t,class:"text-color nc-iconfont nc-icon-tianjiaV6xx text-[44rpx]"},null,8,["id"]),s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id].totalNum?(o(),r(j,{key:0,class:x(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(z)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):d("v-if",!0)])),_:2},1032,["onClick"])):d("v-if",!0),s(X).cart.control&&"style-3"===s(X).cart.style?(o(),r(j,{key:2,class:"w-[50rpx] flex justify-center items-end h-[50rpx] relative",onClick:u((l=>he(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(l,{id:"itemCart"+t,class:"text-color nc-iconfont nc-icon-gouwucheV6xx6 !text-[34rpx]"},null,8,["id"]),s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id].totalNum?(o(),r(j,{key:0,class:x(["absolute right-[-10rpx] top-[-2rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(z)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):d("v-if",!0)])),_:2},1032,["onClick"])):d("v-if",!0),s(X).cart.control&&"style-4"===s(X).cart.style?(o(),r(j,{key:3,class:"w-[50rpx] h-[50rpx] justify-center flex items-end relative",onClick:u((l=>he(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(j,{id:"itemCart"+t,class:"flex items-center justify-center text-[#fff] bg-color h-[44rpx] w-[44rpx] rounded-[22rpx] text-center"},{default:a((()=>[i(l,{class:"nc-iconfont nc-icon-gouwucheV6xx6 !text-[26rpx]"})])),_:2},1032,["id"]),s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id].totalNum?(o(),r(j,{key:0,class:x(["absolute right-[-10rpx] top-[-10rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border border-[2rpx] border-solid border-[#fff]",s(z)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):d("v-if",!0)])),_:2},1032,["onClick"])):d("v-if",!0)],64)):d("v-if",!0)],64))])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128)),re.value.length||se.value||!ie.value?d("v-if",!0):(o(),r(K,{key:0,class:"part",option:{tip:"暂无商品"}}))])),_:1},8,["class"]),i(oe,{ref_key:"cartRef",ref:be},null,512)])),_:1},8,["onInit"]),s(X).cart.control&&"cart"===s(X).cart.event?(o(),r(j,{key:0,class:"bg-[#fff] z-10 flex justify-between items-center fixed left-0 right-0 bottom-[50px] box-solid px-[24rpx] py-[17rpx] mb-ios border-[0] border-t-[2rpx] border-solid border-[#f6f6f6]"},{default:a((()=>[i(j,{class:"flex items-center"},{default:a((()=>[i(j,{class:"w-[66rpx] h-[66rpx] mr-[27rpx] relative"},{default:a((()=>[i(j,{id:"animation-end",class:"w-[66rpx] h-[66rpx] rounded-[35rpx] bg-[var(--primary-color)] text-center leading-[70rpx]",onClick:u(ye,["stop"])},{default:a((()=>[i(l,{class:"nc-iconfont nc-icon-gouwucheV6mm1 text-[#fff] text-[32rpx]"})])),_:1},8,["onClick"]),s(O)?(o(),r(j,{key:0,class:x(["border-[1rpx] border-solid border-[#fff]",["absolute left-[40rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border",s(O)>9?"px-[10rpx]":""]])},{default:a((()=>[y(k(s(O)>99?"99+":s(O)),1)])),_:1},8,["class"])):d("v-if",!0)])),_:1}),i(l,{class:"text-[26rpx] font-500 text-[#333]"},{default:a((()=>[y("总计：")])),_:1}),i(j,{class:"text-[var(--price-text-color)] price-font font-bold flex items-baseline"},{default:a((()=>[i(l,{class:"text-[26rpx] mr-[6rpx]"},{default:a((()=>[y("￥")])),_:1}),i(l,{class:"text-[44rpx]"},{default:a((()=>[y(k(parseFloat(s(T))),1)])),_:1})])),_:1})])),_:1}),i(U,{class:x(["w-[180rpx] h-[70rpx] text-[26rpx] leading-[70rpx] font-500 m-0 rounded-full remove-border",{"primary-btn-bg !text-[#fff]":parseFloat(s(T))>0,"bg-[#F7F7F7] !text-[var(--text-color-light9)]":parseFloat(s(T))<=0}]),onClick:we},{default:a((()=>[y("去结算")])),_:1},8,["class"])])),_:1})):d("v-if",!0)])),_:1},8,["class"])):d("v-if",!0),M(i(j,{style:R(ne.value),class:"fixed z-999 flex items-center justify-center text-[#fff] bg-color h-[44rpx] w-[44rpx] rounded-[22rpx] text-center"},{default:a((()=>[i(l,{class:"nc-iconfont nc-icon-gouwucheV6xx6 !text-[30rpx]"})])),_:1},8,["style"]),[[E,ne.value]]),ve.value.length||se.value?d("v-if",!0):(o(),r(K,{key:1,option:{tip:"暂无商品分类"}})),i($,{loading:se.value},null,8,["loading"]),i(A),d(" 强制绑定手机号 "),i(te,{ref_key:"bindMobileRef",ref:ke},null,512)])),_:1})}}}),[["__scopeId","data-v-126d64c9"]]),ae=Q(e({__name:"category-template-two-two",props:{config:{type:Object,default:()=>({})},categoryId:{type:[String,Number],default:0}},setup(e){const S=e,N=ee();N.getList();const z=j((()=>N.cartList)),O=j((()=>N.totalNum)),T=j((()=>N.totalMoney)),W=B(),Y=j((()=>W.info)),{mescrollInit:Q,getMescroll:G}=q(U,L);let le=S.config,re=S.categoryId;const ae=t([]),se=t(""),ie=t(!0),ue=t(!1),de=t(!1);t([]).value=uni.getStorageSync("shopCart")||[];const ce=e=>{ue.value=!1,J({page:e.num,limit:e.size,goods_category:re}).then((t=>{let l=t.data.data;1==e.num&&(ae.value=[]),ae.value=ae.value.concat(l),ne(),ie.value=!1,e.endSuccess(l.length),ae.value.length||(ue.value=!0)})).catch((()=>{ie.value=!1,ue.value=!0,e.endErr()}))},ne=()=>{ae.value.forEach(((e,t)=>{e.isMaxBuy=!1;let l=-1;if(e.is_limit&&e.max_buy){let t=0;if(1==e.limit_type)t=e.max_buy;else{let l=e.max_buy-(e.has_buy||0);t=l>0?l:0}t>e.stock?l=e.stock:t<=e.stock&&(l=t)}0==l&&(e.isMaxBuy=!0)}))},pe=e=>{f({url:"/addon/shop/pages/goods/detail",param:{goods_id:e}})};l((()=>{me()}));const xe=t(""),fe=t(!1),ve=t(!1),ge=(e,t)=>{if(fe.value||ve.value)return!1;fe.value=!0,ve.value=!0;let l={goods_id:e.goodsSku.goods_id,sku_id:e.goodsSku.sku_id,sale_price:Fe(e),stock:e.goodsSku.stock};e.id&&(l.num=e.num,l.id=e.id);let o=1;o=e.min_buy>0&&!e.num?e.min_buy:1,N.increase(l,o,(()=>{ve.value=!1})),setTimeout((()=>{const e=window.document.getElementById("animation-end"),l=e.getBoundingClientRect().left,o=e.getBoundingClientRect().top,r=window.document.getElementById(t),a=r.getBoundingClientRect().left,s=r.getBoundingClientRect().top;xe.value=`top: ${s}px; left: ${a}px;`,setTimeout((()=>{xe.value=`top: ${o+e.offsetHeight/2-r.offsetHeight/3}px; left: ${l+e.offsetWidth/2-r.offsetHeight/3}px; transition: all 0.8s; transform: rotate(-720deg);`}),20),setTimeout((()=>{xe.value="",fe.value=!1}),1020)}),100)};t({allActive:-1,data:{category_name:"全部",category_id:""}});const _e=t([]),me=()=>{ie.value=!0,A().then((e=>{_e.value=e.data;for(let t=0;t<_e.value.length;t++)if(_e.value[t].child_list){let e={category_name:"全部",category_id:_e.value[t].category_id};_e.value[t].child_list.unshift(e)}if(re)for(let t=0;t<_e.value.length;t++){if(_e.value[t].category_id==re){be.value=t;break}if(_e.value[t].child_list)for(let e=0;e<_e.value[t].child_list.length;e++)if(_e.value[t].child_list[e].category_id==re){be.value=t,he.value=e;break}}else re=e.data[0].category_id;ie.value=!1})).catch((()=>{ie.value=!1}))},be=t(0),he=t(0),ye=(e,t)=>{he.value=e,re=t.category_id,de.value=!1,ae.value=[],G().resetUpScroll()},ke=()=>{f({url:"/addon/shop/pages/goods/list",param:{goods_name:encodeURIComponent(se.value)}})},we=t(),Ce=(e,t)=>{if("virtual"==e.goods_type&&"verify"==e.virtual_receive_type)return pe(e.goodsSku.goods_id);if("cart"!==le.cart.event)return pe(e.goods_id);if(!Y.value)return $().setLoginBack({url:"/addon/shop/pages/goods/category"}),!1;if(e.goodsSku.sku_spec_format)we.value.open(e.goodsSku.sku_id);else{if(!e.goodsSku.stock||parseInt(e.goodsSku.num||0)>parseInt(e.goodsSku.stock))return void I({title:"商品库存不足",icon:"none"});if(e.min_buy&&e.min_buy>parseInt(e.stock))return void I({title:"商品库存小于起购数量",icon:"none"});ge(e,t)}},je=()=>{f({url:"/addon/shop/pages/goods/cart"})},Se=t(null);t(uni.getStorageSync("isBindMobile"));const Ie=()=>{if(!O.value)return void I({title:"还没有选择商品",icon:"none"});const e=[];Object.values(z.value).forEach((t=>{Object.keys(t).forEach((l=>{"totalNum"!=l&&"totalMoney"!=l&&e.push(t[l].id)}))})),0!=e.length&&uni.setStorage({key:"orderCreateData",data:{cart_ids:e},success(){f({url:"/addon/shop/pages/order/payment"})}})},Ve=e=>{let t="";return t=e.goodsSku.show_type,t},Fe=e=>{let t="0.00";return t=e.goodsSku.show_price,t};return(e,t)=>{const l=v,f=g,j=_,S=m,B=b(h("u-popup"),X),L=C,U=b(h("u--image"),H),$=V,A=b(h("loading-page"),P),W=b(h("tabbar"),D);return o(),r(j,{class:"min-h-screen bg-[var(--page-bg-color)] overflow-hidden"},{default:a((()=>[_e.value.length?(o(),r(j,{key:0,class:x(["mescroll-box bg-[#f6f6f6]",{cart:s(le).cart.control&&"cart"===s(le).cart.event,detail:!(s(le).cart.control&&"cart"===s(le).cart.event)}])},{default:a((()=>[i(Z,{ref:"mescrollRef",down:{use:!1},onInit:s(Q),onUp:ce},{default:a((()=>{var e,v,g,_,m,b;return[s(le).search.control?(o(),r(j,{key:0,class:"box-border search-box z-10 bg-[#fff] fixed top-0 left-0 right-0 h-[96rpx]"},{default:a((()=>[i(j,{class:"flex-1 search-input"},{default:a((()=>[i(l,{onClick:u(ke,["stop"]),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"},null,8,["onClick"]),i(f,{class:"input",type:"text",modelValue:se.value,"onUpdate:modelValue":t[0]||(t[0]=e=>se.value=e),modelModifiers:{trim:!0},placeholder:s(le).search.title,placeholderClass:"text-[var(--text-color-light9)]",onConfirm:ke},null,8,["modelValue","placeholder"]),se.value?(o(),r(l,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[1]||(t[1]=e=>se.value="")})):d("v-if",!0)])),_:1})])),_:1})):d("v-if",!0),i(j,{class:x(["tabs-box z-2 fixed left-0 bg-[#fff] bottom-[50px] top-0",{"!top-[96rpx]":s(le).search.control,"pb-[98rpx]":s(le).cart.control&&"cart"===s(le).cart.event}])},{default:a((()=>[i(S,{"scroll-y":!0,class:"scroll-height"},{default:a((()=>[i(j,{class:"bg-[var(--temp-bg)]"},{default:a((()=>[(o(!0),c(p,null,n(_e.value,((e,t)=>(o(),r(j,{class:x(["tab-item",{"tab-item-active ":t==be.value,"rounded-br-[12rpx]":!(be.value-1!==t||_e.value[be.value].child_list&&_e.value[be.value].child_list.length),"rounded-tr-[12rpx]":!(be.value+1!==t||_e.value[be.value].child_list&&_e.value[be.value].child_list.length)}]),key:t,onClick:l=>((e,t)=>{be.value=e,t.child_list&&t.child_list.length?ye(0,t.child_list[0]):(re=t.category_id,ae.value=[],G().resetUpScroll())})(t,e)},{default:a((()=>[i(j,{class:"text-box leading-[1.3] break-words px-[24rpx]"},{default:a((()=>[y(k(e.category_name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["class"]),(null==(e=_e.value[be.value])?void 0:e.child_list)&&(null==(v=_e.value[be.value])?void 0:v.child_list.length)?(o(),r(j,{key:1,class:x(["flex items-center h-[98rpx] pl-[24rpx] pr-[48rpx] py-[20rpx] z-10 bg-white fixed left-[168rpx] right-0 box-border top-0",{"!top-[94rpx]":s(le).search.control}])},{default:a((()=>[de.value?(o(),c(p,{key:1},[i(j,{class:"flex-1 h-[48rpx] text-[28rpx] text-[var(--text-color-light9)] pr-[24rpx] leading-[48rpx]"},{default:a((()=>[y("全部分类")])),_:1}),i(l,{class:"absolute right-[24rpx] nc-iconfont nc-icon-shangV6xx-1 text-[#333] text-[30rpx]",onClick:t[3]||(t[3]=e=>de.value=!1)})],64)):(o(),c(p,{key:0},[i(S,{"scroll-x":!0,"scroll-with-animation":"","scroll-into-view":"id"+(he.value?he.value-1:0),class:"flex-1 h-[54rpx] scroll-Y box-border pr-[24rpx] bg-white"},{default:a((()=>[i(j,{class:"flex items-center h-[54rpx] box-border"},{default:a((()=>{var e;return[(o(!0),c(p,null,n(null==(e=_e.value[be.value])?void 0:e.child_list,((e,t)=>(o(),r(l,{class:x(["w-[150rpx] flex-shrink-0 px-[14rpx] h-[54rpx] truncate text-center !leading-[50rpx] !text-[24rpx] border-[2rpx] border-solid !rounded-[100rpx] box-border text-[#333] box-border",{"bg-[var(--primary-color-light)] font-500 text-[var(--primary-color)] border-[var(--primary-color)]":t===he.value,"border-[var(--temp-bg)]  bg-[var(--temp-bg)]":t!=he.value," ml-[24rpx]":0!=t}]),key:_e.value[be.value].category_id,id:"id"+t,onClick:l=>ye(t,e)},{default:a((()=>[y(k(e.category_name),1)])),_:2},1032,["class","id","onClick"])))),128))]})),_:1})])),_:1},8,["scroll-into-view"]),i(j,{class:"absolute right-[24rpx] nc-iconfont nc-icon-xiaV6xx text-[30rpx] w-[30rpx] h-[30rpx] text-center transform",onClick:t[2]||(t[2]=e=>de.value=!0)})],64))])),_:1},8,["class"])):d("v-if",!0),i(j,{class:x(["labelPopup",{active:s(le).search.control}])},{default:a((()=>[i(B,{show:de.value,mode:"top",zIndex:"1",onClose:t[5]||(t[5]=e=>de.value=!1),customStyle:{top:"192rpx",left:"168rpx"},overlayStyle:{top:"192rpx",left:"168rpx"}},{default:a((()=>[i(j,{class:"flex flex-wrap pt-[20rpx] pb-[24rpx] pr-[100rpx]",onTouchmove:t[4]||(t[4]=u((()=>{}),["prevent","stop"]))},{default:a((()=>{var e;return[(o(!0),c(p,null,n(null==(e=_e.value[be.value])?void 0:e.child_list,((e,t)=>(o(),r(l,{class:x(["px-[14rpx] flex-shrink-0 w-[160rpx] box-border ml-[20rpx] mb-[26rpx] h-[60rpx] text-center leading-[56rpx] text-[24rpx] border-[2rpx] border-solid !rounded-[100rpx] text-[#333] truncate",{"bg-[var(--primary-color-light)] font-500 text-[var(--primary-color)] border-[var(--primary-color)]":t===he.value,"border-[var(--temp-bg)]  bg-[var(--temp-bg)]":t!=he.value}]),key:_e.value[be.value].category_id,onClick:l=>ye(t,e)},{default:a((()=>[y(k(e.category_name),1)])),_:2},1032,["class","onClick"])))),128))]})),_:1})])),_:1},8,["show"])])),_:1},8,["class"]),i(j,{class:x(["flex justify-center flex-wrap pl-[168rpx] pt-[20rpx] pb-[20rpx]",{"!pt-[214rpx]":s(le).search.control&&(null==(g=_e.value[be.value])?void 0:g.child_list)&&(null==(_=_e.value[be.value])?void 0:_.child_list.length),"pt-[120rpx]":s(le).search.control&&(!_e.value[be.value].child_list||!_e.value[be.value].child_list.length),"pt-[118rpx]":(null==(m=_e.value[be.value])?void 0:m.child_list)&&(null==(b=_e.value[be.value])?void 0:b.child_list.length)&&!s(le).search.control}])},{default:a((()=>[(o(!0),c(p,null,n(ae.value,((e,t)=>(o(),r(j,{key:e.goods_id,class:x(["w-[536rpx] box-border bg-white w-full flex mx-[20rpx] py-[24rpx] px-[20rpx] rounded-[var(--rounded-small)]",{"mt-[20rpx]":t}]),onClick:u((t=>pe(e.goods_id)),["stop"])},{default:a((()=>[i(j,{class:"w-[168rpx] h-[168rpx] flex items-center justify-center mr-[20rpx] rounded-[var(--goods-rounded-small)] overflow-hidden"},{default:a((()=>[i(U,{width:"168rpx",height:"168rpx",radius:"var(--goods-rounded-small)",src:s(w)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:a((()=>[i(L,{class:"w-[168rpx] h-[168rpx]",src:s(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["radius","src"])])),_:2},1024),i(j,{class:"flex flex-1 flex-wrap flex-col"},{default:a((()=>[i(j,{class:"max-h-[80rpx] text-[26rpx] leading-[40rpx] multi-hidden"},{default:a((()=>[y(k(e.goods_name),1)])),_:2},1024),i(j,{class:"flex items-end justify-between flex-1"},{default:a((()=>[i(j,{class:"text-[var(--price-text-color)] price-font -mb-[8rpx]"},{default:a((()=>[i(l,{class:"text-[24rpx] font-500"},{default:a((()=>[y("￥")])),_:1}),i(l,{class:"text-[40rpx] font-500"},{default:a((()=>[y(k(parseFloat(Fe(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),i(l,{class:"text-[24rpx] font-500"},{default:a((()=>[y("."+k(parseFloat(Fe(e)).toFixed(2).split(".")[1]),1)])),_:2},1024),"member_price"==Ve(e)?(o(),r(L,{key:0,class:"h-[24rpx] max-w-[46rpx] ml-[6rpx]",src:s(w)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):d("v-if",!0),"newcomer_price"==Ve(e)?(o(),r(L,{key:1,class:"h-[24rpx] max-w-[60rpx] ml-[6rpx]",src:s(w)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):d("v-if",!0),"discount_price"==Ve(e)?(o(),r(L,{key:2,class:"h-[24rpx] max-w-[80rpx] ml-[6rpx]",src:s(w)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):d("v-if",!0)])),_:2},1024),e.isMaxBuy?d("v-if",!0):(o(),c(p,{key:0},[("real"==e.goods_type||"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type)&&""===e.goodsSku.sku_spec_format&&s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id]&&s(le).cart.control&&"cart"===s(le).cart.event?(o(),r(j,{key:0,class:"flex items-center"},{default:a((()=>[i(j,{class:"relative w-[32rpx] h-[32rpx]"},{default:a((()=>[i(l,{class:"text-[32rpx] text-color nc-iconfont nc-icon-jianshaoV6xx absolute flex items-center justify-center -left-[12rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",onClick:u((t=>((e,t)=>{if(ve.value)return!1;ve.value=!0;let l=1;e.min_buy>0&&e.min_buy==t.num&&(l=e.min_buy),N.reduce({id:t.id,goods_id:t.goods_id,sku_id:t.sku_id,stock:t.stock,sale_price:t.sale_price,num:t.num},l,(()=>{ve.value=!1}))})(e,s(z)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["onClick"])])),_:2},1024),i(l,{class:"text-[#333] text-[24rpx] mx-[16rpx]"},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id].num),1)])),_:2},1024),i(j,{class:"relative w-[32rpx] h-[32rpx]"},{default:a((()=>[i(l,{class:"text-[32rpx] text-color iconfont iconjiahao2fill absolute flex items-center justify-center -left-[14rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",id:"itemCart"+t,onClick:u((l=>((e,t,l)=>{if(parseInt(t.num)>=parseInt(t.stock))return void I({title:"商品库存不足",icon:"none"});let o=t.num;if(e.min_buy>0&&e.min_buy>t.num&&(o=e.min_buy),e.is_limit&&e.max_buy&&(1==e.limit_type?e.max_buy:(e.max_buy,e.has_buy)),e.is_limit&&o>=e.max_buy){let t=`该商品单次限购${e.max_buy}件`;return 1!=e.limit_type&&(t=`该商品每人限购${e.max_buy}件`),I({title:t,icon:"none"}),!1}let r=F(e);r.num=o,r.id=t.id,ge(r,l)})(e,s(z)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id],"itemCart"+t)),["stop"])},null,8,["id","onClick"])])),_:2},1024)])),_:2},1024)):"virtual"==e.goods_type&&"cart"!==s(le).cart.event||"real"==e.goods_type?(o(),c(p,{key:1},[s(le).cart.control&&"style-1"===s(le).cart.style?(o(),r(j,{key:0,class:"h-[44rpx] relative pl-[20rpx]"},{default:a((()=>[i(j,{id:"itemCart"+t,class:"w-[102rpx] box-border text-center text-[#fff] primary-btn-bg h-[46rpx] text-[22rpx] leading-[46rpx] rounded-[100rpx]",onClick:u((l=>Ce(e,"itemCart"+t)),["stop"])},{default:a((()=>[y(k(s(le).cart.text),1)])),_:2},1032,["id","onClick"]),s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id].totalNum?(o(),r(j,{key:0,class:x(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(z)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):d("v-if",!0)])),_:2},1024)):d("v-if",!0),s(le).cart.control&&"style-2"===s(le).cart.style?(o(),r(j,{key:1,class:"w-[50rpx] h-[50rpx] relative",onClick:u((l=>Ce(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(l,{id:"itemCart"+t,class:"text-color nc-iconfont nc-icon-tianjiaV6xx text-[44rpx]"},null,8,["id"]),s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id].totalNum?(o(),r(j,{key:0,class:x(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(z)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):d("v-if",!0)])),_:2},1032,["onClick"])):d("v-if",!0),s(le).cart.control&&"style-3"===s(le).cart.style?(o(),r(j,{key:2,class:"w-[50rpx] h-[50rpx] flex justify-center items-end relative",onClick:u((l=>Ce(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(l,{id:"itemCart"+t,class:"text-color nc-iconfont nc-icon-gouwucheV6xx6 !text-[34rpx]"},null,8,["id"]),s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id].totalNum?(o(),r(j,{key:0,class:x(["absolute right-[-10rpx] top-[-2rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(z)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):d("v-if",!0)])),_:2},1032,["onClick"])):d("v-if",!0),s(le).cart.control&&"style-4"===s(le).cart.style?(o(),r(j,{key:3,class:"w-[50rpx] h-[50rpx] flex items-end relative",onClick:u((l=>Ce(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(j,{id:"itemCart"+t,class:"flex items-center justify-center text-[#fff] bg-color h-[44rpx] w-[44rpx] rounded-[22rpx] text-center"},{default:a((()=>[i(l,{class:"nc-iconfont nc-icon-gouwucheV6xx6 !text-[26rpx]"})])),_:2},1032,["id"]),s(z)["goods_"+e.goods_id]&&s(z)["goods_"+e.goods_id].totalNum?(o(),r(j,{key:0,class:x(["absolute right-[-10rpx] top-[-10rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border border-[2rpx] border-solid border-[#fff]",s(z)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[y(k(s(z)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):d("v-if",!0)])),_:2},1032,["onClick"])):d("v-if",!0)],64)):d("v-if",!0)],64))])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128)),ae.value.length||ie.value||!ue.value?d("v-if",!0):(o(),r(K,{key:0,class:"part",option:{tip:"暂无商品"}}))])),_:1},8,["class"]),i(oe,{ref_key:"cartRef",ref:we},null,512)]})),_:1},8,["onInit"]),s(le).cart.control&&"cart"===s(le).cart.event?(o(),r(j,{key:0,class:"bg-[#fff] z-10 flex justify-between items-center fixed left-0 right-0 bottom-[50px] box-solid px-[24rpx] py-[17rpx] mb-ios border-[0] border-t-[2rpx] border-solid border-[#f6f6f6]"},{default:a((()=>[i(j,{class:"flex items-center"},{default:a((()=>[i(j,{class:"w-[66rpx] h-[66rpx] mr-[27rpx] relative"},{default:a((()=>[i(j,{id:"animation-end",class:"w-[66rpx] h-[66rpx] rounded-[35rpx] bg-[var(--primary-color)] text-center leading-[70rpx]",onClick:u(je,["stop"])},{default:a((()=>[i(l,{class:"nc-iconfont nc-icon-gouwucheV6mm1 text-[#fff] text-[32rpx]"})])),_:1},8,["onClick"]),s(O)?(o(),r(j,{key:0,class:x(["border-[1rpx] border-solid border-[#fff]",["absolute left-[40rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border",s(O)>9?"px-[10rpx]":""]])},{default:a((()=>[y(k(s(O)>99?"99+":s(O)),1)])),_:1},8,["class"])):d("v-if",!0)])),_:1}),i(l,{class:"text-[26rpx] text-[#333]"},{default:a((()=>[y("总计：")])),_:1}),i(j,{class:"text-[var(--price-text-color)] price-font font-bold flex items-baseline"},{default:a((()=>[i(l,{class:"text-[26rpx] mr-[6rpx]"},{default:a((()=>[y("￥")])),_:1}),i(l,{class:"text-[44rpx]"},{default:a((()=>[y(k(parseFloat(s(T))),1)])),_:1})])),_:1})])),_:1}),i($,{class:x(["w-[180rpx] h-[70rpx] text-[26rpx] leading-[70rpx] font-500 m-0 rounded-full remove-border",{"primary-btn-bg !text-[#fff]":parseFloat(s(T))>0,"bg-[#F7F7F7] !text-[var(--text-color-light9)]":parseFloat(s(T))<=0}]),onClick:Ie},{default:a((()=>[y("去结算")])),_:1},8,["class"])])),_:1})):d("v-if",!0)])),_:1},8,["class"])):d("v-if",!0),M(i(j,{style:R(xe.value),class:"fixed z-999 flex items-center justify-center text-[#fff] bg-color h-[44rpx] w-[44rpx] rounded-[22rpx] text-center"},{default:a((()=>[i(l,{class:"nc-iconfont nc-icon-gouwucheV6xx6 !text-[30rpx]"})])),_:1},8,["style"]),[[E,xe.value]]),_e.value.length||ie.value?d("v-if",!0):(o(),r(j,{key:1,class:"flex justify-center items-center w-[100%]"},{default:a((()=>[i(K,{option:{tip:"暂无商品分类"}})])),_:1})),i(A,{loading:ie.value},null,8,["loading"]),i(W),d(" 强制绑定手机号 "),i(te,{ref_key:"bindMobileRef",ref:Se},null,512)])),_:1})}}}),[["__scopeId","data-v-204429de"]]),se=Q(e({__name:"category",setup(e){const l=ee(),s=t({}),i=t(0);return z((e=>{i.value=e.category_id||0,Y().then((e=>{s.value=e.data,T({title:s.value.page_title})}))})),O((()=>{l.getList()})),(e,t)=>{const l=_;return o(),r(l,{style:R(e.themeColor())},{default:a((()=>[1===s.value.level&&"style-1"===s.value.template?(o(),r(re,{key:0,class:"category",categoryId:i.value,config:s.value},null,8,["categoryId","config"])):d("v-if",!0),2===s.value.level&&"style-1"===s.value.template?(o(),r(le,{key:1,categoryId:i.value,config:s.value},null,8,["categoryId","config"])):d("v-if",!0),2===s.value.level&&"style-2"===s.value.template?(o(),r(ae,{key:2,class:"category",categoryId:i.value,config:s.value},null,8,["categoryId","config"])):d("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-9d6cd3ec"]]);export{se as default};
