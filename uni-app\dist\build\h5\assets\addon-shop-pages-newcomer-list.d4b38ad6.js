import{d as e,r as t,aE as a,p as s,J as l,o as r,c as o,w as n,e as u,y as c,g as i,b as p,F as d,t as m,n as x,x as f,z as _,O as v,am as g,an as h,Q as y,k as b,S as k,i as w,j,au as F,R as C,A as T,$ as E,a as S,T as z}from"./index-3caf046d.js";import{a as I,b as M,_ as D}from"./newcomer.6993dfef.js";import{_ as N}from"./top-tabbar.f4fde406.js";import{_ as O}from"./u-popup.1b30ffa7.js";import{_ as P}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{M as R}from"./mescroll-body.36f14dc3.js";import{u as A}from"./useMescroll.26ccf5de.js";import{M as B}from"./mescroll-empty.d02c7bd6.js";import{t as H}from"./topTabbar.9217e319.js";import{_ as U}from"./_plugin-vue_export-helper.1b428a4d.js";import"./manifest.ed582bbb.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-safe-bottom.98e092c5.js";import"./u-loading-icon.255170b9.js";import"./mescroll-i18n.e7c22011.js";const J=U(e({__name:"list",setup(e){const{mescrollInit:U,downCallback:J,getMescroll:L}=A(h,g),Q=t([]),V=t(null),Y=t(!1),Z=t(!0),$=a();let q={};const G=H();G.setTopTabbarParam({title:"",topStatusBar:{textColor:"#fff"}});let K=G.setTopTabbarParam({title:"新人专享列表",topStatusBar:{textColor:"#333"}});s((()=>z(Number(q.height)+q.top+8)+30+"rpx;"));const W=t({}),X=e=>{W.value=e},ee=t(""),te=t(!1),ae=()=>{te.value=!1};l((async e=>{le()}));const se=t({}),le=()=>{I().then((e=>{se.value=e.data,Z.value=!1})).catch((()=>{Z.value=!1}))},re=t(0),oe=e=>{Y.value=!1;let t={page:e.num,limit:e.size};M(t).then((t=>{re.value=t.data.is_join;let a=t.data.data,s=(new Date).getTime();ee.value=1e3*Number(t.data.validity_time)-s,1===Number(e.num)&&(Q.value=[]),Q.value=Q.value.concat(a),e.endSuccess(a.length),Y.value=!0})).catch((()=>{Y.value=!0,e.endErr()}))};return(e,t)=>{const a=y,s=b,l=k,g=w(j("up-count-down"),D),h=w(j("top-tabbar"),N),z=F,I=C,M=w(j("u-popup"),O),A=w(j("loading-page"),P);return r(),o(s,{class:"min-h-[100vh] bg-[#f6f6f6] overflow-hidden",style:x(e.themeColor())},{default:n((()=>[Object.keys(se.value).length&&"active"==se.value.active_status?(r(),o(R,{key:0,ref_key:"mescrollRef",ref:V,onInit:u(U),down:{use:!1},onUp:oe},{default:n((()=>[Z.value?i("v-if",!0):(r(),o(s,{key:0,class:"marketing-head"},{default:n((()=>[se.value.banner_list&&se.value.banner_list.length?(r(),o(a,{key:0,class:"w-[100%] h-[434rpx] -mt-[130rpx]",src:u(T)(se.value.banner_list[0].imageUrl),mode:"aspectFill",onClick:t[0]||(t[0]=e=>u($).toRedirect(se.value.banner_list[0].toLink))},null,8,["src"])):(r(),c(d,{key:1},[i(" 占位作用 "),p(s,{class:"w-[100%] h-[434rpx] -mt-[130rpx]"})],2112)),se.value.active_desc?(r(),o(s,{key:2,class:"side-tab !top-[30rpx]",onClick:t[1]||(t[1]=e=>te.value=!0)},{default:n((()=>[p(l,{class:"iconfont iconxinrenV6xx icon"}),p(l,{class:"desc"},{default:n((()=>[m("活动规则")])),_:1})])),_:1})):i("v-if",!0),ee.value>0&&null!=ee.value?(r(),o(s,{key:3,class:"time newcomer-time",style:x({"background-image":"url("+u(T)("addon/shop/newcomer/time_bg.png")+")"})},{default:n((()=>[p(l,{class:"text-[26rpx]"},{default:n((()=>[m("距结束还有：")])),_:1}),p(g,{class:"text-[#fff] min-w-[150rpx] text-[28rpx]",time:ee.value,format:"DD:HH:mm:ss",onChange:X},{default:n((()=>[p(s,{class:"flex"},{default:n((()=>[W.value.days&&W.value.days>0?(r(),o(s,{key:0,class:"text-[24rpx] flex items-center"},{default:n((()=>[p(l,{class:"min-w-[30rpx] text-right"},{default:n((()=>[m(f(W.value.days),1)])),_:1}),p(l,{class:"text-[20rpx]"},{default:n((()=>[m("天")])),_:1})])),_:1})):i("v-if",!0),p(s,{class:"text-[24rpx] flex items-center"},{default:n((()=>[W.value.hours?(r(),o(l,{key:0,class:"min-w-[30rpx] text-center"},{default:n((()=>[m(f(W.value.hours>=10?W.value.hours:"0"+W.value.hours),1)])),_:1})):(r(),o(l,{key:1,class:"min-w-[30rpx] text-center"},{default:n((()=>[m("00")])),_:1})),p(l,{class:"text-[20rpx]"},{default:n((()=>[m("时")])),_:1})])),_:1}),p(s,{class:"text-[24rpx] flex items-center"},{default:n((()=>[W.value.minutes?(r(),o(l,{key:0,class:"min-w-[30rpx] text-center"},{default:n((()=>[m(f(W.value.minutes>=10?W.value.minutes:"0"+W.value.minutes),1)])),_:1})):(r(),o(l,{key:1,class:"min-w-[30rpx] text-center"},{default:n((()=>[m("00")])),_:1})),p(l,{class:"text-[20rpx]"},{default:n((()=>[m("分")])),_:1})])),_:1}),p(s,{class:"text-[24rpx] flex items-center"},{default:n((()=>[W.value.seconds?(r(),o(l,{key:0,class:"min-w-[30rpx] text-center"},{default:n((()=>[m(f(W.value.seconds<10?"0"+W.value.seconds:W.value.seconds),1)])),_:1})):(r(),o(l,{key:1,class:"min-w-[30rpx] text-center"},{default:n((()=>[m("00")])),_:1})),p(l,{class:"text-[20rpx]"},{default:n((()=>[m("秒")])),_:1})])),_:1})])),_:1})])),_:1},8,["time"])])),_:1},8,["style"])):i("v-if",!0)])),_:1})),Q.value.length?(r(),o(s,{key:1,class:"marketing-list p-[20rpx] relative -mt-[50rpx]"},{default:n((()=>[(r(!0),c(d,null,_(Q.value,((e,t)=>(r(),o(s,{class:E(["bg-[#fff] flex rounded-[var(--rounded-mid)] p-[20rpx]",{"mb-[20rpx]":Q.value.length-1!=t}]),key:t,onClick:t=>{S({url:"/addon/shop/pages/goods/detail",param:{sku_id:e.goodsSku.sku_id,type:"newcomer_discount"}})}},{default:n((()=>[p(s,{class:"w-[250rpx] h-[250rpx] flex items-center justify-center"},{default:n((()=>[e.goods_cover?(r(),o(a,{key:0,class:"w-[250rpx] h-[250rpx] rounded-[var(--rounded-mid)]",src:u(T)(e.goods_cover_thumb_mid),mode:"aspectFill",onError:t=>e.goods_cover_thumb_mid="static/resource/images/diy/shop_default.jpg"},null,8,["src","onError"])):(r(),o(a,{key:1,class:"w-[250rpx] h-[250rpx] rounded-[var(--rounded-mid)]",src:u(T)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"]))])),_:2},1024),p(s,{class:"flex flex-col flex-1 ml-[20rpx] pt-[4rpx]"},{default:n((()=>[p(s,{class:"text-[28rpx] multi-hidden leading-[1.3]"},{default:n((()=>[m(f(e.goods_name),1)])),_:2},1024),p(s,{class:"w-[400rpx] flex items-center justify-center"},{default:n((()=>[p(a,{class:"w-[400rpx] h-[106rpx] mt-[auto] mb-[10rpx]",src:u(T)("addon/shop/newcomer/subsidy.png"),mode:"aspectFit"},null,8,["src"])])),_:1}),p(s,{class:"flex items-center justify-center btn-wrap"},{default:n((()=>[p(s,{class:"flex items-center text-[#FFF3E0] relative z-4",style:x({background:"linear-gradient(to right, #FF8A04 0%, #ff1b1b 84%)"})},{default:n((()=>[p(l,{class:"text-[22rpx]"},{default:n((()=>[m("新人价")])),_:1}),p(l,{class:"text-[20rpx] ml-[6rpx] mr-[2rpx]"},{default:n((()=>[m("￥")])),_:1}),p(l,{class:"text-[36rpx] truncate max-w-[160rpx]"},{default:n((()=>[m(f(parseFloat(e.goodsSku.newcomer_price).toFixed(2)),1)])),_:2},1024)])),_:2},1032,["style"]),p(a,{class:"w-[26rpx] h-[54rpx]",src:u(T)("addon/shop/newcomer/btn_02.png"),mode:"heightFix"},null,8,["src"]),p(a,{class:"w-[84rpx] h-[54rpx] relative ml-[-5rpx] z-2",src:u(T)("addon/shop/newcomer/btn_03.png"),mode:"aspectFit"},null,8,["src"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})):!Q.value.length&&Y.value?(r(),o(B,{key:2,option:{tip:"暂无商品，请看看其他商品吧！",btnText:"去逛逛"},onEmptyclick:t[2]||(t[2]=e=>u(S)({url:"/addon/shop/pages/goods/list"}))})):i("v-if",!0)])),_:1},8,["onInit"])):i("v-if",!0),!Z.value&&Object.keys(se.value).length&&"active"!=se.value.active_status?(r(),c(d,{key:1},[p(h,{data:u(K),class:"top-header"},null,8,["data"]),p(B,{option:{tip:"活动未开启,请看看其他商品吧！",btnText:"去逛逛"},onEmptyclick:t[3]||(t[3]=e=>u(S)({url:"/addon/shop/pages/index"}))})],64)):i("v-if",!0),p(s,{onTouchmove:t[5]||(t[5]=v((()=>{}),["prevent","stop"]))},{default:n((()=>[p(M,{show:te.value,onClose:ae,mode:"center",round:"var(--rounded-big)"},{default:n((()=>[p(s,{class:"w-[570rpx] px-[32rpx] popup-common center"},{default:n((()=>[p(s,{class:"title"},{default:n((()=>[m("活动规则")])),_:1}),se.value.active_desc?(r(),o(z,{key:0,"scroll-y":!0,class:"px-[30rpx] box-border max-h-[260rpx]"},{default:n((()=>[(r(!0),c(d,null,_(se.value.active_desc.split("\n"),(e=>(r(),o(s,{class:"text-[28rpx] leading-[40rpx] mb-[20rpx]"},{default:n((()=>[m(f(e),1)])),_:2},1024)))),256))])),_:1})):i("v-if",!0),p(s,{class:"btn-wrap !pt-[40rpx]"},{default:n((()=>[p(I,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:t[4]||(t[4]=e=>te.value=!1)},{default:n((()=>[m("我知道了")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),p(A,{loading:Z.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-62aa7a58"]]);export{J as default};
