import{_ as e}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{d as l,I as a,r as t,N as s,L as r,p as u,c1 as n,c2 as o,b1 as x,o as i,c,w as p,b as f,e as v,n as d,g as _,y as g,z as m,F as b,t as h,i as y,j as w,k,Q as C,aN as j,au as F,S as T,A as z,$ as X,x as E,a as I,b4 as S,aM as B}from"./index-3caf046d.js";import{t as N}from"./topTabbar.9217e319.js";import{_ as $}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.255170b9.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */const A=$(l({__name:"level",setup(l){const $=a(),A=t(!1),L=t([]),M=t([]),O=t(0),P=t(0);N().setTopTabbarParam({title:"会员等级"}),t({title:"会员等级",topStatusBar:{style:"style-1",bgColor:"#fff",rollBgColor:"#333",textColor:"#333",rollTextColor:"#333"}}),s((()=>{r()&&(K(),J())}));const Q=u((()=>$.info)),q=e=>{let l=e,a=100;return L.value[l]&&L.value[l].growth&&(a=Q.value.growth/L.value[l].growth*100),a},D=t(""),G=t(""),H=t(""),J=()=>{A.value=!0,n().then((e=>{L.value=e.data||[];let l=!0;Q.value&&L.value&&L.value.length&&L.value.forEach(((e,a)=>{e.level_id==Q.value.member_level&&(l=!1,O.value=a,P.value=O.value,V(a))})),l&&V(0),L.value&&L.value.length>=5?(D.value="width:115rpx;",G.value="max-width:115rpx;",H.value="width:460rpx;transform: translateX(-235rpx);"):L.value&&4==L.value.length?(D.value="width:144rpx;",G.value="max-width:144rpx;",H.value="width:436rpx;transform: translateX(-218rpx);"):L.value&&3==L.value.length?(D.value="width:192rpx;",G.value="max-width:192rpx;",H.value="width:388rpx;transform: translateX(-194rpx);"):L.value&&2==L.value.length?(D.value="width:289rpx;",G.value="max-width:289rpx;",H.value="width:289rpx;transform: translateX(-144rpx);"):(G.value="max-width:578rpx;",D.value="width:100%;"),A.value=!1})).catch((()=>{A.value=!1}))},K=()=>{o().then((e=>{M.value=e.data}))},R=e=>{O.value=e.detail.current,P.value=O.value,V(e.detail.current)},U=t({}),V=e=>{let l=x(L.value[e]);if(l&&l.level_benefits&&(l.benefits_arr=[],Object.values(l.level_benefits).forEach(((e,a,t)=>{e.content&&l.benefits_arr.push(e.content)}))),l&&l.level_gifts){l.gifts_arr=[];for(let e in l.level_gifts)l.level_gifts[e].content&&(l.level_gifts[e].content.forEach(((l,a,t)=>{t[a].type=e})),l.gifts_arr=l.gifts_arr.concat(l.level_gifts[e].content))}U.value=l};return(l,a)=>{const t=y(w("loading-page"),e),s=k,r=C,u=S,n=B,o=j,x=F,N=T;return i(),c(s,{style:d(l.themeColor()),class:"bg-[var(--page-bg-color)] min-h-[100vh] overflow-hidden"},{default:p((()=>[f(t,{loading:A.value&&v(Q)},null,8,["loading"]),!A.value&&v(Q)&&L.value&&L.value.length?(i(),c(s,{key:0,class:"min-h-[100vh] overflow-hidden flex flex-col",style:d({backgroundColor:U.value.level_style.bg_color})},{default:p((()=>[f(s,null,{default:p((()=>[f(s,{class:"pt-[40rpx] mb-[40rpx]"},{default:p((()=>[_(" 轮播图 "),f(s,{class:"relative"},{default:p((()=>[f(o,{class:"swiper ns-indicator-dots relative",style:{height:"300rpx"},onChange:R,current:O.value,"previous-margin":"30rpx","next-margin":"30rpx"},{default:p((()=>[(i(!0),g(b,null,m(L.value,((e,l)=>(i(),c(n,{class:"swiper-item",key:e.id},{default:p((()=>[f(s,{class:"h-[300rpx] relative"},{default:p((()=>[v(Q).member_level==e.level_id&&O.value==l?(i(),c(s,{key:0,class:"text-[24rpx] absolute top-0 left-0 z-10 h-[66rpx] !bg-contain w-[150rpx] flex pt-[12rpx] pl-[16rpx] box-border",style:d({background:"url("+v(z)(U.value.level_tag)+") no-repeat",color:U.value.level_style.level_color})},{default:p((()=>[h("当前等级")])),_:1},8,["style"])):_("v-if",!0),f(s,{class:X(["absolute top-0 left-0 right-0 bottom-0 z-20 px-[30rpx] pt-[68rpx] box-border",{"px-[50rpx]":O.value!=l}])},{default:p((()=>[f(s,{class:"flex items-center leading-[50rpx] mb-[90rpx]"},{default:p((()=>[f(r,{class:"h-[32rpx] w-[34rpx] align-middle",src:v(z)(e.level_icon?e.level_icon:""),mode:"aspectFill"},null,8,["src"]),f(s,{class:"text-[36rpx] font-bold ml-[10rpx] max-w-[340rpx] truncate",style:d({color:U.value.level_style.level_color})},{default:p((()=>[h(E(e.level_name),1)])),_:2},1032,["style"])])),_:2},1024),f(s,{class:"flex items-baseline",style:d({color:U.value.level_style.level_color})},{default:p((()=>[f(s,{class:"text-[30rpx] font-500 leading-[38rpx]"},{default:p((()=>[h(E(v(Q).growth),1)])),_:1}),f(s,{class:"text-[24rpx] leading-[34rpx]"},{default:p((()=>[h("/"+E(L.value[l].growth)+"成长值",1)])),_:2},1024)])),_:2},1032,["style"]),f(s,{class:"flex justify-between items-center mt-[10rpx]"},{default:p((()=>[f(s,{class:"flex flex-col flex-1"},{default:p((()=>[f(s,null,{default:p((()=>[f(u,{percent:q(l),"border-radius":100,activeColor:U.value.level_style.level_color,backgroundColor:"#fff","stroke-width":"4"},null,8,["percent","activeColor"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"]),f(s,{class:"relatvie h-full w-full"},{default:p((()=>[f(r,{class:X(["h-full w-full",{"swiper-animation":O.value!=l}]),src:v(z)(e.level_bg),"show-menu-by-longpress":!0},null,8,["src","class"])])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["current"])])),_:1})])),_:1}),f(s,{class:"mb-[30rpx] relative"},{default:p((()=>[f(s,{class:"bg-[#fff] opacity-15 h-[2rpx] w-full absolute top-[15rpx]"}),f(s,{style:d(H.value),class:"bg-[#fff] opacity-60 h-[2rpx] absolute top-[15rpx] z-4 left-[50%]"},null,8,["style"]),f(s,{class:"mx-[86rpx]"},{default:p((()=>[f(x,{"scroll-x":!0,"scroll-with-animation":"","scroll-into-view":"id"+(P.value?P.value-1:0)},{default:p((()=>[f(s,{class:"flex flex-nowrap py-[10rpx]"},{default:p((()=>[(i(!0),g(b,null,m(L.value,((e,l)=>(i(),c(s,{key:e.id,style:d(D.value),class:"flex-shrink-0 flex flex-col items-center justify-center",onClick:e=>(e=>{P.value=e,O.value=e,V(e)})(l),id:"id"+l},{default:p((()=>[f(s,{class:X(["w-[14rpx] h-[14rpx] level-class",{"level-select":P.value==l}])},null,8,["class"]),f(s,{style:d(G.value),class:X(["text-[22rpx] text-[#aaa] mt-[16rpx] truncate",{"!text-[#fff]":P.value==l}])},{default:p((()=>[h(E(e.level_name),1)])),_:2},1032,["style","class"])])),_:2},1032,["style","onClick","id"])))),128))])),_:1})])),_:1},8,["scroll-into-view"])])),_:1})])),_:1}),U.value.benefits_arr&&U.value.benefits_arr.length?(i(),c(s,{key:0,class:"flex mx-[var(--sidebar-m)] pt-[30rpx] pb-[46rpx] items-center flex-col level_benefits",style:d({backgroundImage:"url("+v(z)(U.value.member_bg)+")"})},{default:p((()=>[f(s,{class:"flex items-center justify-center"},{default:p((()=>[f(N,{class:"text-[#fff] text-[30rpx] font-500 leading-[44rpx]"},{default:p((()=>[h("会员权益")])),_:1})])),_:1}),f(s,{class:"flex flex-wrap w-[690rpx] mt-[40rpx] justify-between"},{default:p((()=>[(i(!0),g(b,null,m(U.value.benefits_arr,((e,l)=>(i(),c(s,{class:"flex flex-col w-[25%] items-center",key:l},{default:p((()=>[f(r,{class:"h-[88rpx] w-[88rpx]",src:v(z)(e.icon),mode:"heightFix"},null,8,["src"]),f(N,{class:"text-[rgba(255,255,255,0.9)] mt-[16rpx] text-[24rpx] leading-[34rpx]"},{default:p((()=>[h(E(e.title),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1},8,["style"])):_("v-if",!0)])),_:1}),f(s,{class:"flex-1 rounded-t-[40rpx] px-[30rpx] pt-[var(--pad-top-m)] mt-[-10rpx] relative tab-bar",style:d({background:`linear-gradient( 180deg, ${U.value.level_style.gift} 0%, #FFFFFF 20%)`})},{default:p((()=>[_(" 升级礼包 "),U.value.gifts_arr&&U.value.gifts_arr.length?(i(),c(s,{key:0},{default:p((()=>[f(s,{class:"pb-[30rpx] flex items-center"},{default:p((()=>[f(N,{class:"text-[30rpx] text-[#333] font-500 leading-[44rpx]"},{default:p((()=>[h("升级礼包")])),_:1})])),_:1}),f(s,{class:"flex flex-wrap"},{default:p((()=>[(i(!0),g(b,null,m(U.value.gifts_arr,((e,l)=>(i(),c(s,{key:l,class:X(["mb-[20rpx]",{"mr-[21rpx]":(l+1)%3!=0}])},{default:p((()=>[f(s,{class:"relative box-border mb-[16rpx] w-[216rpx] h-[180rpx] !bg-contain",style:d({background:"url("+v(z)(e.background)+") no-repeat"})},null,8,["style"]),f(s,{class:"text-center font-500 text-[#333] text-[28rpx] truncate leading-[40rpx] max-w-[216rpx]"},{default:p((()=>[h(E(e.text),1)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1})])),_:1})):_("v-if",!0),_(" 升级技巧 "),M.value&&M.value.length?(i(),c(s,{key:1},{default:p((()=>[f(s,{class:"pt-[30rpx] pb-[30rpx] flex items-center"},{default:p((()=>[f(N,{class:"text-[30rpx] text-[#333] font-500 leading-[44rpx]"},{default:p((()=>[h("升级技巧")])),_:1})])),_:1}),f(s,{class:"pb-[30rpx]"},{default:p((()=>[(i(!0),g(b,null,m(M.value,((e,l)=>(i(),c(s,{class:"flex items-center mb-[34rpx]",key:l},{default:p((()=>[f(r,{class:"h-[100rpx] w-[100rpx] mr-[20rpx]",src:v(z)(e.icon),mode:"heightFix"},null,8,["src"]),f(s,{class:"flex flex-col"},{default:p((()=>[f(s,{class:"text-[28rpx] leading-[38rpx] mb-[8rpx]"},{default:p((()=>[h(E(e.title),1)])),_:2},1024),f(s,{class:"text-[24rpx] text-[var(--text-color-light9)] leading-[34rpx]"},{default:p((()=>[h(E(e.desc),1)])),_:2},1024)])),_:2},1024),f(N,{class:"skill-btn",onClick:l=>v(I)({url:e.button.wap_redirect,param:{},mode:"redirectTo"})},{default:p((()=>[h(E(e.button.text),1)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})):_("v-if",!0)])),_:1},8,["style"])])),_:1},8,["style"])):_("v-if",!0),A.value||L.value&&L.value.length?_("v-if",!0):(i(),c(s,{key:1,class:""},{default:p((()=>[f(s,{class:"empty-page"},{default:p((()=>[f(r,{class:"img",src:v(z)("static/resource/images/empty.png"),mode:"aspectFill"},null,8,["src"]),f(N,{class:"desc"},{default:p((()=>[h("暂无会员等级")])),_:1})])),_:1})])),_:1}))])),_:1},8,["style"])}}}),[["__scopeId","data-v-a2bdf418"]]);export{A as default};
