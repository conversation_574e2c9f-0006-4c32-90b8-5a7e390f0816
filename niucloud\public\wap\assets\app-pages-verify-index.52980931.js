import{d as e,r as a,N as t,L as r,o as l,c as s,w as c,b as p,e as x,t as n,f as o,v as i,$ as u,g as f,n as d,K as m,ak as v,a as _,M as g,S as h,k as y,Q as b,al as w,i as j,j as C,A as k}from"./index-3caf046d.js";import{_ as V}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{g as I,a as z}from"./verify.8c605f2d.js";import{_ as F}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.255170b9.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */const S=F(e({__name:"index",setup(e){const F=a("manualInput");F.value="manualInput";const S=a(!1),Q=a(""),X=a(!0),Z=a(!1);t((()=>{r()&&A()}));const A=()=>{I().then((e=>{e.data?(Z.value=!0,X.value=!1):(Z.value=!1,X.value=!1)}))},E=()=>{m()&&(v.init(),v.scanQRCode((e=>{if(e.resultStr){let a=e.resultStr;_({url:"/app/pages/verify/verify",param:{code:a}})}})))};let H=!1;const K=()=>/[\S]+/.test(Q.value)?!H&&(H=!0,void z({code:Q.value}).then((e=>{H=!1,_({url:"/app/pages/verify/verify",param:{code:Q.value}})})).catch((()=>{H=!1}))):(g({title:"请输入核销码",icon:"none"}),!1),L=()=>{S.value=!S.value},M=e=>{"sweepCode"!=e||m()?F.value=e:g({title:"H5端不支持扫码核销",icon:"none"})};return(e,a)=>{const t=h,r=y,m=b,v=w,g=j(C("loading-page"),V);return l(),s(r,{style:d(e.themeColor())},{default:c((()=>[!X.value&&Z.value?(l(),s(r,{key:0,class:"w-[100vw] min-h-[100vh] bg-[#f8f8f8]"},{default:c((()=>[p(r,{class:"w-full bg-[#fff] verify-box h-[760rpx]"},{default:c((()=>[p(r,{class:"text-[var(--primary-color)] fixed top-[40rpx] right-[30rpx] flex items-center",onClick:a[0]||(a[0]=e=>x(_)({url:"/app/pages/verify/record"}))},{default:c((()=>[p(t,{class:"nc-iconfont nc-icon-lishijiluV6xx !text-[28rpx] -mb-[2rpx]"}),p(t,{class:"text-[26rpx] ml-[8rpx]"},{default:c((()=>[n("核销记录")])),_:1})])),_:1}),o(p(r,{class:"flex flex-col items-center justify-center"},{default:c((()=>[p(r,{class:"sweep-code flex items-center justify-center",onClick:E},{default:c((()=>[p(m,{class:"w-[354rpx] h-[354rpx]",src:x(k)("static/resource/images/verify/saoma.png")},null,8,["src"])])),_:1}),p(r,{class:"mt-[40rpx] text-[30rpx]"},{default:c((()=>[n("点击扫描二维码")])),_:1}),p(r,{class:"mt-[20rpx] text-[var(--text-color-light9)] text-[26rpx] font-400 pb-[142rpx]"},{default:c((()=>[n("扫描二维码进行核销")])),_:1})])),_:1},512),[[i,"sweepCode"==F.value]]),o(p(r,null,{default:c((()=>[p(r,{class:"flex pt-[126rpx] items-center justify-center"},{default:c((()=>[p(r,{class:"flex justify-center items-center flex-col pr-[30rpx] min-w-[130rpx]"},{default:c((()=>[p(m,{class:"w-[100rpx] h-[100rpx]",src:x(k)("static/resource/images/verify/shuruhexiaoma.png")},null,8,["src"]),p(r,{class:"text-[26rpx] h-[36rpx] leading-[36rpx] mt-[14rpx]"},{default:c((()=>[n("验证核销码")])),_:1})])),_:1}),p(m,{class:"w-[74rpx] h-[12rpx] mb-[50rpx]",src:x(k)("static/resource/images/verify/youjiantou.png")},null,8,["src"]),p(r,{class:"flex justify-center items-center flex-col pl-[30rpx] min-w-[130rpx]"},{default:c((()=>[p(m,{class:"w-[100rpx] h-[100rpx]",src:x(k)("static/resource/images/verify/hexiao1.png")},null,8,["src"]),p(r,{class:"text-[26rpx] h-[36rpx] leading-[36rpx] mt-[14rpx]"},{default:c((()=>[n("核销")])),_:1})])),_:1})])),_:1}),p(r,{class:"mt-[50rpx]"},{default:c((()=>[p(r,{class:"h-[90rpx] border-[2rpx] border-solid border-[#eee] rounded-[16rpx] box-border p-[20rpx] mx-[60rpx] flex items-center"},{default:c((()=>[p(t,{class:"nc-iconfont nc-icon-saotiaoxingmaV6xx text-[44rpx] text-[#EF000C]"}),p(v,{type:"text",placeholder:"请输入核销码",class:"h-[90rpx] border-none text-start ml-[30rpx] text-[28rpx] flex-1","placeholder-class":"_placeholder",modelValue:Q.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.value=e),focus:S.value,ref:"input"},null,8,["modelValue","focus"])])),_:1}),p(r,{class:"h-[80rpx] primary-btn-bg min-w-[630rpx] text-[#fff] flex-center !text-[26rpx] save-btn rounded-[100rpx] h-[80rpx] font-500 mx-[60rpx] mt-[146rpx] relative z-1",onClick:K},{default:c((()=>[n("核销")])),_:1})])),_:1})])),_:1},512),[[i,"manualInput"==F.value]])])),_:1}),p(r,{class:"w-[630rpx] h-[100rpx] bg-[#fff] mx-[auto] mt-[220rpx] rounded-[90rpx] flex relative action-type-wrap"},{default:c((()=>[p(r,{class:u(["relative w-[51%] pr-[50rpx] box-border rounded-[50rpx] z-0 flex flex-col items-center justify-center",{xuanZhong1:"sweepCode"==F.value}]),onClick:a[2]||(a[2]=e=>M("sweepCode"))},{default:c((()=>[p(t,{class:"nc-iconfont nc-icon-saoyisaoV6xx !text-[40rpx]"}),p(r,{class:"text-[24rpx] leading-[1] mt-[10rpx]"},{default:c((()=>[n("扫码核销")])),_:1})])),_:1},8,["class"]),p(r,{class:"flex flex-col items-center flex-col w-[120rpx] h-[120rpx] bg-[#FF7354] rounded-[50%] absolute top-[-10rpx] left-[255rpx] heXiao text-white z-10 shrink-0"},{default:c((()=>[p(r,{class:"nc-iconfont nc-icon-saotiaoxingmaV6xx ns-gradient-otherpages-member-balance-balance-rechange !text-[44rpx] mt-[19rpx]"}),p(r,{class:"text-[24rpx] mt-[8rpx] leading-[34rpx] h-[34rpx]"},{default:c((()=>[n("核销台")])),_:1})])),_:1}),p(r,{class:u(["relative w-[51%] pl-[50rpx] box-border rounded-[50rpx] z-0 flex flex-col items-center justify-center",{xuanZhong:"manualInput"==F.value}]),onClick:a[3]||(a[3]=e=>M("manualInput"))},{default:c((()=>[p(t,{class:"iconfont iconVector-77 !text-[40rpx]"}),p(r,{class:"ml-[20rpx] text-[24rpx] leading-[1] mt-[10rpx]",onClick:L},{default:c((()=>[n("手动输入")])),_:1})])),_:1},8,["class"])])),_:1})])),_:1})):f("v-if",!0),X.value||Z.value?f("v-if",!0):(l(),s(r,{key:1,class:"w-[100vw] min-h-[100vh] bg-[#f8f8f8] overflow-hidden"},{default:c((()=>[p(r,{class:"empty-page"},{default:c((()=>[p(m,{class:"img",src:x(k)("static/resource/images/system/empty.png"),mode:"aspectFit"},null,8,["src"]),p(r,{class:"desc"},{default:c((()=>[n("非核销员无此权限")])),_:1})])),_:1})])),_:1})),p(g,{loading:X.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-745e2fdc"]]);export{S as default};
