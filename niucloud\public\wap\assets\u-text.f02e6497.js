import{ab as e,ac as t,ad as n,ae as s,bt as l,af as a,o as i,c as o,w as r,t as u,x as p,O as d,n as c,S as f,aU as y,bu as m,aV as h,bv as g,bi as x,a$ as S,i as k,j as b,$ as _,g as v,b as w,k as N,R as I}from"./index-3caf046d.js";import{_ as z}from"./u-icon.ba193921.js";import{_ as C}from"./_plugin-vue_export-helper.1b428a4d.js";const $=C({name:"u-link",mixins:[t,n,{props:{color:{type:String,default:()=>e.link.color},fontSize:{type:[String,Number],default:()=>e.link.fontSize},underLine:{type:Boolean,default:()=>e.link.underLine},href:{type:String,default:()=>e.link.href},mpTips:{type:String,default:()=>e.link.mpTips},lineColor:{type:String,default:()=>e.link.lineColor},text:{type:String,default:()=>e.link.text}}}],computed:{linkStyle(){return{color:this.color,fontSize:s(this.fontSize),lineHeight:s(l(this.fontSize)+2),textDecoration:this.underLine?"underline":"none"}}},emits:["click"],methods:{addStyle:a,openLink(){window.open(this.href),this.$emit("click")}}},[["render",function(e,t,n,s,l,a){const y=f;return i(),o(y,{class:"u-link",onClick:d(a.openLink,["stop"]),style:c([a.linkStyle,a.addStyle(e.customStyle)])},{default:r((()=>[u(p(e.text),1)])),_:1},8,["onClick","style"])}],["__scopeId","data-v-839b1acb"]]),L={props:{type:{type:String,default:()=>e.text.type},show:{type:Boolean,default:()=>e.text.show},text:{type:[String,Number],default:()=>e.text.text},prefixIcon:{type:String,default:()=>e.text.prefixIcon},suffixIcon:{type:String,default:()=>e.text.suffixIcon},mode:{type:String,default:()=>e.text.mode},href:{type:String,default:()=>e.text.href},format:{type:[String,Function],default:()=>e.text.format},call:{type:Boolean,default:()=>e.text.call},openType:{type:String,default:()=>e.text.openType},bold:{type:Boolean,default:()=>e.text.bold},block:{type:Boolean,default:()=>e.text.block},lines:{type:[String,Number],default:()=>e.text.lines},color:{type:String,default:()=>e.text.color},size:{type:[String,Number],default:()=>e.text.size},iconStyle:{type:[Object,String],default:()=>e.text.iconStyle},decoration:{tepe:String,default:()=>e.text.decoration},margin:{type:[Object,String,Number],default:()=>e.text.margin},lineHeight:{type:[String,Number],default:()=>e.text.lineHeight},align:{type:String,default:()=>e.text.align},wordWrap:{type:String,default:()=>e.text.wordWrap}}};const T=C({name:"u--text",mixins:[t,n,{computed:{value(){const{text:e,mode:t,format:n,href:s}=this;return"price"===t?y.func(n)?n(e):m(e,2):"date"===t?(!y.date(e)&&h(),y.func(n)?n(e):g(e,n||"yyyy-mm-dd")):"phone"===t?y.func(n)?n(e):"encrypt"===n?`${e.substr(0,3)}****${e.substr(7)}`:e:"name"===t?y.func(n)?n(e):"encrypt"===n?this.formatName(e):e:"link"===t?(!y.url(s)&&h(),e):e}},methods:{formatName(e){let t="";if(2===e.length)t=e.substr(0,1)+"*";else if(e.length>2){let n="";for(let t=0,s=e.length-2;t<s;t++)n+="*";t=e.substr(0,1)+n+e.substr(-1,1)}else t=e;return t}}},L],emits:["click"],computed:{valueStyle(){const e={textDecoration:this.decoration,fontWeight:this.bold?"bold":"normal",wordWrap:this.wordWrap,fontSize:s(this.size)};return!this.type&&(e.color=this.color),this.isNvue&&this.lines&&(e.lines=this.lines),this.lineHeight&&(e.lineHeight=s(this.lineHeight)),!this.isNvue&&this.block&&(e.display="block"),x(e,a(this.customStyle))},isNvue:()=>!1,isMp:()=>!1},data:()=>({}),methods:{addStyle:a,clickHandler(){this.call&&"phone"===this.mode&&S({phoneNumber:this.text}),this.$emit("click")}}},[["render",function(e,t,n,s,l,a){const d=f,y=k(b("u-icon"),z),m=N,h=k(b("u-link"),$),g=I;return e.show?(i(),o(m,{key:0,class:_(["u-text",[]]),style:c({margin:e.margin,justifyContent:"left"===e.align?"flex-start":"center"===e.align?"center":"flex-end"}),onClick:a.clickHandler},{default:r((()=>["price"===e.mode?(i(),o(d,{key:0,class:_(["u-text__price",e.type&&`u-text__value--${e.type}`]),style:c([a.valueStyle])},{default:r((()=>[u("￥")])),_:1},8,["class","style"])):v("v-if",!0),e.prefixIcon?(i(),o(m,{key:1,class:"u-text__prefix-icon"},{default:r((()=>[w(y,{name:e.prefixIcon,customStyle:a.addStyle(e.iconStyle)},null,8,["name","customStyle"])])),_:1})):v("v-if",!0),"link"===e.mode?(i(),o(h,{key:2,class:_(["u-text__value",[e.type&&`u-text__value--${e.type}`,e.lines&&`u-line-${e.lines}`]]),style:c({fontWeight:a.valueStyle.fontWeight,wordWrap:a.valueStyle.wordWrap,fontSize:a.valueStyle.fontSize}),text:e.value,href:e.href,underLine:""},null,8,["style","class","text","href"])):e.openType&&a.isMp?(i(),o(g,{key:3,class:"u-reset-button u-text__value",style:c([a.valueStyle]),"data-index":e.index,openType:e.openType,onGetuserinfo:e.onGetUserInfo,onContact:e.onContact,onGetphonenumber:e.onGetPhoneNumber,onError:e.onError,onLaunchapp:e.onLaunchApp,onOpensetting:e.onOpenSetting,lang:e.lang,"session-from":e.sessionFrom,"send-message-title":e.sendMessageTitle,"send-message-path":e.sendMessagePath,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"app-parameter":e.appParameter},{default:r((()=>[u(p(e.value),1)])),_:1},8,["style","data-index","openType","onGetuserinfo","onContact","onGetphonenumber","onError","onLaunchapp","onOpensetting","lang","session-from","send-message-title","send-message-path","send-message-img","show-message-card","app-parameter"])):(i(),o(d,{key:4,class:_(["u-text__value",[e.type&&`u-text__value--${e.type}`,e.lines&&`u-line-${e.lines}`]]),style:c([a.valueStyle])},{default:r((()=>[u(p(e.value),1)])),_:1},8,["style","class"])),e.suffixIcon?(i(),o(m,{key:5,class:"u-text__suffix-icon"},{default:r((()=>[w(y,{name:e.suffixIcon,customStyle:a.addStyle(e.iconStyle)},null,8,["name","customStyle"])])),_:1})):v("v-if",!0)])),_:1},8,["style","onClick"])):v("v-if",!0)}],["__scopeId","data-v-a3f99f4d"]]);export{T as _};
