import{ab as e,ac as t,ad as a,af as l,bi as r,ae as s,bt as o,aR as i,bj as d,i as u,j as n,o as p,c,w as _,b as x,a7 as m,y as v,F as f,z as y,$ as g,n as b,t as h,x as k,S as w,k as j,au as C,d as S,r as F,s as T,p as V,O as B,e as $,g as O,f as R,v as D,R as L,at as z,b1 as I,A as E,q as P,_ as N,P as A,M as U,Q as W,al as K,I as q,m as G,N as H,a_ as M,D as Z,as as Q,a as J,bK as X}from"./index-3caf046d.js";import{_ as Y}from"./u--image.eb573bce.js";import{_ as ee,a as te,b as ae,n as le,c as re}from"./index.75b1e6c8.js";import{_ as se}from"./u-tabbar.38f37e13.js";import{_ as oe}from"./pay.e8ba1ab9.js";import{f as ie,h as de,i as ue}from"./order.5c5c6bee.js";import{a as ne}from"./u-tabbar-item.31141540.js";import{_ as pe}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as ce}from"./u-popup.1b30ffa7.js";import{n as _e}from"./ns-goods-manjian.50c99c6a.js";import{_ as xe}from"./u-number-box.8a6aafb5.js";import{_ as me}from"./u-parse.406d0731.js";import{_ as ve}from"./u-swiper.1a811b26.js";import{t as fe}from"./topTabbar.9217e319.js";import{d as ye}from"./index.23d98e09.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-loading-icon.255170b9.js";import"./u-empty.ce10a891.js";import"./u-form.49dbb57f.js";import"./u-line.69c0c00f.js";import"./u-input.2d8dc7a4.js";import"./u-safe-bottom.98e092c5.js";import"./pay.1a29db5c.js";import"./useDiyForm.26962b36.js";import"./diy_form.9eef685a.js";import"./index.9de114a1.js";import"./top-tabbar.f4fde406.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.abe3938e.js";import"./u-checkbox-group.0328273c.js";import"./u-datetime-picker.a5259774.js";import"./u-upload.83871903.js";import"./u-radio-group.63482a1c.js";import"./u-avatar.30e31e9c.js";import"./u-text.f02e6497.js";import"./tabbar.2c31519d.js";import"./index.32583a71.js";import"./goods.6a81cb49.js";import"./useGoods.9c8f1c51.js";import"./coupon.2f3f2d3d.js";import"./point.0698952c.js";import"./rank.7a4c9318.js";import"./bind-mobile.25318c0e.js";import"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import"./u-modal.8624728a.js";import"./voucher.db23b7c0.js";import"./quote.9b84c391.js";import"./newcomer.6993dfef.js";const ge=pe({name:"u-tabs",mixins:[t,a,{props:{duration:{type:Number,default:()=>e.tabs.duration},list:{type:Array,default:()=>e.tabs.list},lineColor:{type:String,default:()=>e.tabs.lineColor},activeStyle:{type:[String,Object],default:()=>e.tabs.activeStyle},inactiveStyle:{type:[String,Object],default:()=>e.tabs.inactiveStyle},lineWidth:{type:[String,Number],default:()=>e.tabs.lineWidth},lineHeight:{type:[String,Number],default:()=>e.tabs.lineHeight},lineBgSize:{type:String,default:()=>e.tabs.lineBgSize},itemStyle:{type:[String,Object],default:()=>e.tabs.itemStyle},scrollable:{type:Boolean,default:()=>e.tabs.scrollable},current:{type:[Number,String],default:()=>e.tabs.current},keyName:{type:String,default:()=>e.tabs.keyName}}}],data:()=>({firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}),watch:{current:{immediate:!0,handler(e,t){e!==this.innerCurrent&&(this.innerCurrent=e,this.$nextTick((()=>{this.resize()})))}},list(){this.$nextTick((()=>{this.resize()}))}},computed:{textStyle(){return e=>{const t={},a=e===this.innerCurrent?l(this.activeStyle):l(this.inactiveStyle);return this.list[e].disabled&&(t.color="#c8c9cc"),r(a,t)}},propsBadge:()=>e.badge},async mounted(){this.init()},emits:["click","change"],methods:{addStyle:l,addUnit:s,setLineLeft(){const e=this.list[this.innerCurrent];if(!e)return;let t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0);const a=o(this.lineWidth);this.lineOffsetLeft=t+(e.rect.width-a)/2,this.firstTime&&setTimeout((()=>{this.firstTime=!1}),10)},animation(e,t=0){},clickHandler(e,t){this.$emit("click",{...e,index:t},t),e.disabled||(this.innerCurrent=t,this.resize(),this.$emit("change",{...e,index:t},t))},init(){i().then((()=>{this.resize()}))},setScrollLeft(){const e=this.list[this.innerCurrent],t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0),a=d().windowWidth;let l=t-(this.tabsRect.width-e.rect.width)/2-(a-this.tabsRect.right)/2+this.tabsRect.left/2;l=Math.min(l,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,l)},resize(){0!==this.list.length&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then((([e,t=[]])=>{this.tabsRect=e,this.scrollViewWidth=0,t.map(((e,t)=>{this.scrollViewWidth+=e.width,this.list[t].rect=e})),this.setLineLeft(),this.setScrollLeft()}))},getTabsRect(){return new Promise((e=>{this.queryRect("u-tabs__wrapper__scroll-view").then((t=>e(t)))}))},getAllItemRect(){return new Promise((e=>{const t=this.list.map(((e,t)=>this.queryRect(`u-tabs__wrapper__nav__item-${t}`,!0)));Promise.all(t).then((t=>e(t)))}))},queryRect(e,t){return new Promise((t=>{this.$uGetRect(`.${e}`).then((e=>{t(e)}))}))}}},[["render",function(e,t,a,l,r,s){const o=w,i=u(n("u-badge"),ne),d=j,S=C;return p(),c(d,{class:"u-tabs"},{default:_((()=>[x(d,{class:"u-tabs__wrapper"},{default:_((()=>[m(e.$slots,"left",{},void 0,!0),x(d,{class:"u-tabs__wrapper__scroll-view-wrapper"},{default:_((()=>[x(S,{"scroll-x":e.scrollable,"scroll-left":r.scrollLeft,"scroll-with-animation":"",class:"u-tabs__wrapper__scroll-view","show-scrollbar":!1,ref:"u-tabs__wrapper__scroll-view"},{default:_((()=>[x(d,{class:"u-tabs__wrapper__nav",ref:"u-tabs__wrapper__nav"},{default:_((()=>[(p(!0),v(f,null,y(e.list,((t,a)=>(p(),c(d,{class:g(["u-tabs__wrapper__nav__item",[`u-tabs__wrapper__nav__item-${a}`,t.disabled&&"u-tabs__wrapper__nav__item--disabled"]]),key:a,onClick:e=>s.clickHandler(t,a),ref_for:!0,ref:`u-tabs__wrapper__nav__item-${a}`,style:b([s.addStyle(e.itemStyle),{flex:e.scrollable?"":1}])},{default:_((()=>[x(o,{class:g([[t.disabled&&"u-tabs__wrapper__nav__item__text--disabled"],"u-tabs__wrapper__nav__item__text"]),style:b([s.textStyle(a)])},{default:_((()=>[h(k(t[e.keyName]),1)])),_:2},1032,["class","style"]),x(i,{show:!(!t.badge||!(t.badge.show||t.badge.isDot||t.badge.value)),isDot:t.badge&&t.badge.isDot||s.propsBadge.isDot,value:t.badge&&t.badge.value||s.propsBadge.value,max:t.badge&&t.badge.max||s.propsBadge.max,type:t.badge&&t.badge.type||s.propsBadge.type,showZero:t.badge&&t.badge.showZero||s.propsBadge.showZero,bgColor:t.badge&&t.badge.bgColor||s.propsBadge.bgColor,color:t.badge&&t.badge.color||s.propsBadge.color,shape:t.badge&&t.badge.shape||s.propsBadge.shape,numberType:t.badge&&t.badge.numberType||s.propsBadge.numberType,inverted:t.badge&&t.badge.inverted||s.propsBadge.inverted,customStyle:"margin-left: 4px;"},null,8,["show","isDot","value","max","type","showZero","bgColor","color","shape","numberType","inverted"])])),_:2},1032,["onClick","style","class"])))),128)),x(d,{class:"u-tabs__wrapper__nav__line",ref:"u-tabs__wrapper__nav__line",style:b([{width:s.addUnit(e.lineWidth),transform:`translate(${r.lineOffsetLeft}px)`,transitionDuration:`${r.firstTime?0:e.duration}ms`,height:s.addUnit(e.lineHeight),background:e.lineColor,backgroundSize:e.lineBgSize}])},null,8,["style"])])),_:1},512)])),_:1},8,["scroll-x","scroll-left"])])),_:1}),m(e.$slots,"right",{},void 0,!0)])),_:3})])),_:3})}],["__scopeId","data-v-caa7e19d"]]),be=S({__name:"select-coupon",props:{orderKey:{type:String,default:""}},emits:["confirm"],setup(e,{expose:t,emit:a}){const l=e,r=F(0),s=F([]),o=F([]),i=F(!1),d=F(null);T((()=>l.orderKey),(()=>{l.orderKey&&!s.value.length&&ie({order_key:l.orderKey}).then((({data:e})=>{const t=[],l=[];e.length&&e.forEach((e=>{e.is_normal?t.push(e):l.push(e)})),o.value=l,s.value=t,t.length&&(d.value=t[0],a("confirm",d.value))})).catch()}),{immediate:!0});const m=V((()=>[{name:`可用优惠券(${s.value.length})`,key:"normal"},{name:`不可用优惠券(${o.value.length})`,key:"disabled"}])),b=e=>{r.value=e.index},S=()=>{a("confirm",d.value),i.value=!1};return t({open:e=>{if(i.value=!0,e)for(let t=0;t<s.value.length;t++)if(s.value[t].id==e){d.value=s.value[t];break}},couponList:s}),(e,t)=>{const a=j,l=u(n("u-tabs"),ge),F=w,T=C,V=L,z=u(n("u-popup"),ce);return p(),c(z,{show:i.value,onClose:t[1]||(t[1]=e=>i.value=!1),mode:"bottom",round:10,closeable:!0},{default:_((()=>[x(a,{onTouchmove:t[0]||(t[0]=B((()=>{}),["prevent","stop"])),class:"popup-common"},{default:_((()=>[x(a,{class:"title"},{default:_((()=>[h("请选择优惠券")])),_:1}),e.type?O("v-if",!0):(p(),c(a,{key:0,class:"-mt-[20rpx]"},{default:_((()=>[x(l,{list:$(m),onClick:b,current:r.value,itemStyle:"width:50%;height:88rpx;box-sizing: border-box; font-size: 28rpx;",activeStyle:{color:"var(--primary-color)"},lineColor:"var(--primary-color)"},null,8,["list","current","activeStyle"])])),_:1})),x(T,{"scroll-y":"true",class:"h-[50vh] pt-[10rpx]"},{default:_((()=>[R(x(a,{class:"px-[var(--popup-sidebar-m)] pb-[30rpx] pt-0 text-sm"},{default:_((()=>[(p(!0),v(f,null,y(s.value,(e=>(p(),c(a,{class:g(["mt-[var(--top-m)] px-[var(--pad-sidebar-m)] py-[var(--pad-top-m)] border-1 border-[#eee] border-solid rounded-[20rpx]",{"!border-[var(--primary-color)] bg-[var(--primary-color-light2)]":d.value&&d.value.id==e.id}]),onClick:t=>{return a=e,void(d.value?d.value=d.value.id!=a.id?a:null:d.value=a);var a}},{default:_((()=>[x(a,{class:g(["flex border-0 !border-b border-[#eee] border-dashed pb-[20rpx]",{"!border-[var(--primary-color)]":d.value&&d.value.id==e.id}])},{default:_((()=>[x(a,{class:"flex-1 w-0"},{default:_((()=>[x(a,{class:"text-[30rpx] mb-[20rpx] font-500"},{default:_((()=>[h(k(e.title),1)])),_:2},1024),e.min_condition_money>0?(p(),c(a,{key:0,class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:_((()=>[h("满"+k(e.min_condition_money)+"可用",1)])),_:2},1024)):(p(),c(a,{key:1,class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:_((()=>[h("无门槛券")])),_:1}))])),_:2},1024),x(a,{class:"text-[36rpx] price-font"},{default:_((()=>[x(F,{class:"text-xs mr-[2rpx]"},{default:_((()=>[h("￥")])),_:1}),h(" "+k(e.price),1)])),_:2},1024)])),_:2},1032,["class"]),x(a,{class:"pt-[20rpx] text-[24rpx] text-[var(--text-color-light6)]"},{default:_((()=>[h(k(e.create_time)+" ~ "+k(e.expire_time)+"有效",1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),256))])),_:1},512),[[D,0==r.value]]),R(x(a,{class:"px-[var(--popup-sidebar-m)] pb-[30rpx] pt-0 text-sm"},{default:_((()=>[(p(!0),v(f,null,y(o.value,(e=>(p(),c(a,{class:"mt-[var(--top-m)] px-[var(--pad-sidebar-m)] py-[var(--pad-top-m)] border-1 !border-[#eee] border-solid rounded-[var(--rounded-mid)] bg-[var(--temp-bg)]"},{default:_((()=>[x(a,{class:"flex border-0 !border-b !border-[#ddd] border-dashed pb-[20rpx]"},{default:_((()=>[x(a,{class:"flex-1 w-0"},{default:_((()=>[x(a,{class:"text-[30rpx] mb-[20rpx] font-500"},{default:_((()=>[h(k(e.title),1)])),_:2},1024),e.min_condition_money>0?(p(),c(a,{key:0,class:"text-[24rpx] text-[var(--text-color-light9)]"},{default:_((()=>[h("满"+k(e.min_condition_money)+"可用",1)])),_:2},1024)):(p(),c(a,{key:1,class:"text-[24rpx] text-[var(--text-color-light9)]"},{default:_((()=>[h("无门槛券")])),_:1}))])),_:2},1024),x(a,{class:"text-[36rpx] price-font"},{default:_((()=>[x(F,{class:"text-xs mr-[2rpx]"},{default:_((()=>[h("￥")])),_:1}),h(" "+k(e.price),1)])),_:2},1024)])),_:2},1024),x(a,{class:"pt-[20rpx] text-[24rpx] text-[var(--text-color-light9)]"},{default:_((()=>[h(k(e.create_time)+" ~ "+k(e.expire_time)+"期间有效",1)])),_:2},1024),x(a,{class:"text-[24rpx] pt-[10rpx] flex text-[var(--text-color-light9)]"},{default:_((()=>[h("不可用原因："+k(e.error),1)])),_:2},1024)])),_:2},1024)))),256))])),_:1},512),[[D,1==r.value]])])),_:1}),x(a,{class:"btn-wrap"},{default:_((()=>[x(V,{class:"primary-btn-bg btn",onClick:S},{default:_((()=>[h("确认")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])}}});const he=S({__name:"goods-detail",setup(e,{expose:t}){const a=F(!1),l=F(!1),r=F(),s=F(),o=e=>{var t;(t=e,z.get(`shop_impulse_buy/detail/${t.impulse_buy_goods_id}`,t)).then((({data:e})=>{r.value=I(e),r.value.goods.goods_image=r.value.goods.goods_image_thumb_big,r.value.goods.goods_image.forEach(((e,t)=>{r.value.goods.goods_image[t]=E(e)})),l.value=!1}))},i=()=>{s.value&&s.value.num,a.value=!1},d=F(!1),m=e=>{let t=e.detail.scrollTop;d.value=t>375};return t({open:e=>{r.value="",a.value=!0,s.value=e,o({impulse_buy_goods_id:e.impulse_buy_goods_id})}}),(e,t)=>{const l=j,o=u(n("u-swiper"),ve),v=w,f=u(n("u-parse"),me),y=C,b=L,S=u(n("u-popup"),ce);return p(),c(S,{show:a.value,onClose:t[2]||(t[2]=e=>a.value=!1),mode:"bottom",round:10},{default:_((()=>[r.value?(p(),c(l,{key:0,onTouchmove:t[1]||(t[1]=B((()=>{}),["prevent","stop"])),class:"popup-common bg-[var(--page-bg-color)]"},{default:_((()=>[x(l,{class:g(["absolute top-[30rpx] right-[20rpx] z-10 p-[10rpx] bg-[rgba(255,255,255,.4)] rounded-full border-[2rpx] border-solid border-transparent box-border nc-iconfont nc-icon-guanbiV6xx2 font-bold text-[#303133] text-[36rpx]",{"border-[#d8d8d8]":d.value}]),onClick:t[0]||(t[0]=e=>a.value=!1)},null,8,["class"]),x(y,{"scroll-y":"true",class:"h-[80vh]",onScroll:m},{default:_((()=>[x(l,{class:"w-full transition-transform duration-300 ease-linear transform translate-x-0"},{default:_((()=>[x(l,{class:"swiper-box"},{default:_((()=>[x(o,{list:r.value.goods.goods_image,indicator:r.value.goods.goods_image.length,indicatorStyle:{bottom:"70rpx"},autoplay:!0,height:"100vw",radius:"0"},null,8,["list","indicator"])])),_:1})])),_:1}),x(l,{class:"bg-[var(--page-bg-color)] rounded-tr-[40rpx] rounded-tl-[40rpx] overflow-hidden -mt-[34rpx] relative"},{default:_((()=>[x(l,{class:"flex flex-col px-[30rpx] pt-[30rpx]"},{default:_((()=>[x(l,{class:"text-[var(--price-text-color)] flex items-baseline mb-[12rpx]"},{default:_((()=>[x(l,{class:"inline-block goods-price-time"},{default:_((()=>[x(v,{class:"price-font text-[32rpx]"},{default:_((()=>[h("￥")])),_:1}),x(v,{class:"price-font text-[48rpx]"},{default:_((()=>[h(k(parseFloat(r.value.impulse_buy_price).toFixed(2).split(".")[0]),1)])),_:1}),x(v,{class:"price-font text-[32rpx] mr-[10rpx]"},{default:_((()=>[h("."+k(parseFloat(r.value.impulse_buy_price).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),O(' <text class="text-[26rpx] text-[var(--text-color-light9)] line-through price-font" v-if="goodsDetail.market_price && parseFloat(goodsDetail.market_price)">\n                            ￥{{ goodsDetail.market_price }}\n                            </text> ')])),_:1}),x(l,{class:"text-[#333] font-medium text-[30rpx] multi-hidden leading-[40rpx]"},{default:_((()=>[h(k(r.value.goods.goods_name),1)])),_:1}),x(l,{class:"flex justify-between items-start mt-[24rpx]"},{default:_((()=>[r.value.sku&&r.value.sku.sku_name?(p(),c(l,{key:0,class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:_((()=>[x(v,{class:"whitespace-nowrap mr-[4rpx]"},{default:_((()=>[h("已选:")])),_:1}),x(v,null,{default:_((()=>[h(k(r.value.sku.sku_name),1)])),_:1})])),_:1})):O("v-if",!0),x(l,{class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:_((()=>[x(v,{class:"whitespace-nowrap mr-[4rpx]"},{default:_((()=>[h("库存:")])),_:1}),x(v,null,{default:_((()=>[h(k(r.value.sku.stock),1)])),_:1}),x(v,null,{default:_((()=>[h(k(r.value.goods.unit),1)])),_:1})])),_:1})])),_:1})])),_:1}),x(l,{class:"mt-[40rpx] sidebar-margin card-template px-[var(--pad-sidebar-m)]"},{default:_((()=>[x(l,{class:"title !py-[0] !text-left !text-[30rpx]"},{default:_((()=>[h("商品详情")])),_:1}),x(l,{class:"u-content"},{default:_((()=>[x(f,{content:r.value.goods.goods_desc,tagStyle:{img:"vertical-align: top;",p:"overflow: hidden;word-break:break-word;"}},null,8,["content"])])),_:1})])),_:1})])),_:1})])),_:1}),x(l,{class:"btn-wrap"},{default:_((()=>[x(b,{class:g(["primary-btn-bg btn",{"opacity-80":s.value&&s.value.num}]),onClick:i},{default:_((()=>[h("确定")])),_:1},8,["class"])])),_:1})])),_:1})):O("v-if",!0)])),_:1},8,["show"])}}}),ke=pe(S({__name:"ns-impulse-buy",props:["data","orderKey","deliveryType","calculateLoading"],emits:["confirm"],setup(e,{expose:t,emit:a}){const l=e,r=F(!1),s=F({}),o=F(!0),i=P({is_change:0,order_key:"",delivery_type:"",buy_sku_id:[]}),d=F(),m=F([]);z.get("shop_impulse_buy/config").then((e=>{s.value=e.data}));const b=(e={})=>{(function(e){return z.get("shop_impulse_buy/list",e)})(Object.assign(i,e)).then((e=>{m.value=e.data.list,o.value=e.data.is_show_change,m.value&&m.value.forEach((e=>{e.num=0}))}))},S=e=>{const t={impulse_buy_goods_id:e.impulse_buy_goods_id,num:e.num};d.value.open(t)};N((()=>{i.order_key=l.orderKey,i.delivery_type=l.deliveryType,b()}));const T=e=>({min:1,max:e.sku.stock||1}),V=e=>{e&&(!e||0!=Object.keys(e).length)&&e.goods_id&&e.sku_id&&(e.num>parseInt(e.stock)?U({title:A("stockNotEnough"),icon:"none"}):uni.$u.debounce((t=>{const l={sku_id:e.sku_id,num:e.num,impulse_buy_goods_id:e.impulse_buy_goods_id};a("confirm",l)}),500))},R=()=>{b({is_change:1}),a("confirm","")},D=e=>{let t="0.00";return t=Object.keys(e).length&&Object.keys(e.goods).length&&e.goods.member_discount&&e.member_price!=e.sku.price&&e.sku.member_price?e.sku.member_price:e.sku.price,t};return t({}),(e,t)=>{const a=w,i=W,b=j,F=u(n("u--image"),Y),z=L,I=K,P=u(n("u-number-box"),xe),N=u(n("u-parse"),me),q=C,G=u(n("u-popup"),ce);return m.value&&m.value.length?(p(),c(b,{key:0},{default:_((()=>[x(b,{class:"container rounded-[var(--rounded-big)] px-[24rpx] py-[30rpx] mb-[20rpx]"},{default:_((()=>[x(b,{class:"flex items-center justify-between"},{default:_((()=>[x(b,{class:"flex items-center"},{default:_((()=>["text"==s.value.type?(p(),c(a,{key:0,class:g(["head-title text-[28rpx] font-bold pr-[10rpx]",{"column-size !pr-[20rpx]":s.value.sub_title}])},{default:_((()=>[h(k(s.value.title),1)])),_:1},8,["class"])):O("v-if",!0),"image"==s.value.type?(p(),c(i,{key:1,class:g(["head-title h-[28rpx] w-[auto] pr-[10rpx]",{"column-size !pr-[20rpx]":s.value.sub_title}]),src:$(E)(s.value.title_image),mode:"heightFix"},null,8,["class","src"])):O("v-if",!0),s.value.sub_title?(p(),c(a,{key:2,class:"text-[24rpx] text-[#FF5900]"},{default:_((()=>[h(k(s.value.sub_title),1)])),_:1})):O("v-if",!0),s.value.rule?(p(),c(a,{key:3,class:"iconfont icon24gl-questionCircle text-[#666] ml-[4rpx] !text-[28rpx]",onClick:t[0]||(t[0]=e=>r.value=!0)})):O("v-if",!0)])),_:1}),o.value?(p(),c(b,{key:0,class:"flex items-center",onClick:R},{default:_((()=>[x(a,{class:"text-[24rpx] text-[#666]"},{default:_((()=>[h(k($(A)("changeIt")),1)])),_:1}),x(a,{class:"iconfont iconshuaxin1 ml-[4rpx] text-[#666] !text-[28rpx]"})])),_:1})):O("v-if",!0)])),_:1}),x(b,{class:"space-y-4 mt-[30rpx]"},{default:_((()=>[(p(!0),v(f,null,y(m.value,((e,r)=>(p(),c(b,{key:r,class:"rounded-[20rpx] flex"},{default:_((()=>[x(F,{radius:"var(--goods-rounded-big)",width:"180rpx",height:"180rpx",src:$(E)(e.goods.goods_cover),model:"aspectFill",onClick:t=>S(e)},{error:_((()=>[x(i,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:$(E)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src","onClick"]),x(b,{class:"flex flex-1 w-0 flex-col justify-between ml-[20rpx] py-[6rpx]"},{default:_((()=>[x(b,{class:"line-normal"},{default:_((()=>[x(b,{class:"truncate text-[#303133] text-[28rpx] mb-[6rpx] leading-[32rpx]",onClick:t=>S(e)},{default:_((()=>[h(k(e.goods.goods_name),1)])),_:2},1032,["onClick"]),x(b,{class:"truncate text-[#666] text-[24rpx] mb-[4rpx] leading-[32rpx]"},{default:_((()=>[h(k(e.sku.sku_name),1)])),_:2},1024),Number(e.limit_buy)>0?(p(),c(b,{key:0,class:"text-[#DA7E2D] break-words text-[24rpx] leading-[32rpx]"},{default:_((()=>[h(k(`${$(A)("limitPlaceholderOne")}${e.limit_buy}${$(A)("limitPlaceholderTwo")}￥${D(e)}${$(A)("limitPlaceholderThree")}`),1)])),_:2},1024)):O("v-if",!0)])),_:2},1024),x(b,{class:"flex items-center justify-between mt-2"},{default:_((()=>[x(b,{class:"text-[var(--price-text-color)] flex items-baseline price-font"},{default:_((()=>[x(a,{class:"text-[24rpx] font-500 mr-[4rpx]"},{default:_((()=>[h("￥")])),_:1}),x(a,{class:"text-[40rpx] font-500"},{default:_((()=>[h(k(parseFloat(e.impulse_buy_price).toFixed(2).split(".")[0]),1)])),_:2},1024),x(a,{class:"text-[24rpx] font-500"},{default:_((()=>[h("."+k(parseFloat(e.impulse_buy_price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),x(b,{class:"h-[50rpx] flex items-center"},{default:_((()=>[e.num?(p(),c(P,{key:1,modelValue:e.num,"onUpdate:modelValue":t=>e.num=t,min:0,max:T(e).max,integer:"",step:1,"input-width":"68rpx",disabled:l.calculateLoading,"input-height":"52rpx","button-size":"52rpx",disabledInput:"",onChange:e=>((e,t)=>{const a=m.value[t];V(a)})(0,r)},{minus:_((()=>[x(b,{class:"relative w-[26rpx] h-[26rpx]",onClick:t=>{var a;1==(a=e).num&&(a.num=0,V(a))}},{default:_((()=>[x(a,{class:g([{"text-[#303133]":e.num!==T(e).min,"text-[var(--text-color-light9)]":l.calculateLoading},"text-[24rpx] absolute flex items-center justify-center -left-[20rpx] -bottom-[20rpx] -right-[20rpx] -top-[20rpx] font-500 nc-iconfont nc-icon-jianV6xx"])},null,8,["class"])])),_:2},1032,["onClick"])])),input:_((()=>[x(I,{class:"text-[#303133] text-[28rpx] mx-[14rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onBlur:e=>((e,t)=>{setTimeout((()=>{const e=m.value[t];e.num=parseInt(e.num),e.num?e.num<=T(e).min&&(e.num=T(e).min):e.num=0,e.num>=T(e).max&&(e.num=T(e).max),V(e)}),0)})(0,r),onClick:t[1]||(t[1]=B((()=>{}),["stop"])),modelValue:e.num,"onUpdate:modelValue":t=>e.num=t,disabled:l.calculateLoading},null,8,["onBlur","modelValue","onUpdate:modelValue","disabled"])])),plus:_((()=>[x(b,{class:"relative w-[26rpx] h-[26rpx]",onClick:t=>{var a;(a=e).num>=a.sku.stock&&U({title:A("stockNotEnough"),icon:"none"})}},{default:_((()=>[x(a,{class:g([{"text-[var(--text-color-light9)]":e.num===T(e).max||l.calculateLoading," text-[#303133]":e.num!==T(e).max},"text-[24rpx] absolute flex items-center justify-center -left-[20rpx] -bottom-[20rpx] -right-[20rpx] -top-[20rpx] font-500 nc-iconfont nc-icon-jiahaoV6xx"])},null,8,["class"])])),_:2},1032,["onClick"])])),_:2},1032,["modelValue","onUpdate:modelValue","max","disabled","onChange"])):(p(),c(z,{key:0,onClick:t=>{return(a=e).num=T(a).min,void V(a);var a},class:g(["!rounded-[30rpx] !bg-transparent !text-[var(--primary-color)] text-[22rpx] border-[2rpx] border-solid border-[var(--primary-color)] min-w-[130rpx] h-[50rpx] flex items-center",{"opacity-80":l.calculateLoading}]),disabled:l.calculateLoading,"hover-class":"none"},{default:_((()=>[h(k($(A)("buyWithOneClick")),1)])),_:2},1032,["onClick","disabled","class"]))])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),x(b,{onTouchmove:t[4]||(t[4]=B((()=>{}),["prevent","stop"]))},{default:_((()=>[x(G,{show:r.value,onClose:t[3]||(t[3]=e=>r.value=!1),mode:"center",round:"var(--rounded-big)",safeAreaInsetBottom:!1},{default:_((()=>[x(b,{class:"w-[570rpx] px-[32rpx] popup-common center"},{default:_((()=>[x(b,{class:"title"},{default:_((()=>[h(k($(A)("activityInstructions")),1)])),_:1}),x(q,{"scroll-y":!0,class:"px-[10rpx] box-border h-[350rpx]"},{default:_((()=>[x(N,{content:s.value.rule,tagStyle:{img:"vertical-align: top;",p:"overflow: hidden;word-break:break-word;",span:"text-wrap-mode:wrap !important;"}},null,8,["content"])])),_:1}),x(b,{class:"btn-wrap !pt-[40rpx]"},{default:_((()=>[x(z,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:t[2]||(t[2]=e=>r.value=!1)},{default:_((()=>[h(k($(A)("know")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),O(" 商品详情 "),x(he,{ref_key:"goodsDetailRef",ref:d},null,512)])),_:1})])),_:1})):O("v-if",!0)}}}),[["__scopeId","data-v-43785ce2"]]),we=pe(S({__name:"payment",setup(e){const t=q(),a=V((()=>t.info));fe().setTopTabbarParam({title:"待付款订单"});const l=G(),r=F(),s=F({order_key:"",member_remark:"",discount:{},invoice:{},delivery:{delivery_type:"",buyer_ask_delivery_time:"",taker_name:"",taker_mobile:""},extend_data:{},form_data:{}}),o=F(null),i=F(null),d=F(),m=F(),C=F(),S=F(),R=F(),D=F(!1),z=F(0),I=F([]),P=F(!1);uni.getStorageSync("orderCreateData")&&Object.assign(s.value,uni.getStorageSync("orderCreateData"));const N=F(null),A=F(null),ie=F(0),ne=F({}),pe=F(null),ce=()=>{pe.value?pe.value.show=!0:U({title:"请选择自提点",icon:"none"})},xe=e=>{s.value.delivery.buyer_ask_delivery_time=e},me=e=>{},ve=F(null),ge=e=>{ve.value=e};H((()=>{}));const he=uni.getStorageSync("selectAddressCallback");he&&(s.value.order_key="",s.value.delivery.delivery_type=he.delivery,s.value.delivery.take_address_id=he.address_id,uni.removeStorage({key:"selectAddressCallback"}));const we=async(e,t)=>{await Q(),m.value&&(s.value.delivery.delivery_type!=e&&s.value&&delete s.value.impulse_buy_goods,"store"==e&&0==ie.value&&(ie.value++,m.value.getData((e=>{var t;e.length&&(s.value.delivery.take_store_id=(null==(t=e[0])?void 0:t.store_id)??0,je())}))),s.value.delivery.delivery_type!=e&&(z.value=t,s.value.order_key="",s.value.delivery.delivery_type=e,s.value.delivery.take_address_id=0,je()))},je=(e={})=>{const t=Object.assign({},s.value,e);P.value=!0,de(t).then((({data:e})=>{i.value=Z(e),P.value=!1,i.value.goods=[],i.value.goods_data&&Object.values(i.value.goods_data).length&&Object.values(i.value.goods_data).forEach(((e,t)=>{i.value.goods.push(e)})),"store"==s.value.delivery.delivery_type?(s.value.delivery.taker_name=a.value.nickname,s.value.delivery.taker_mobile=a.value.mobile):i.value.delivery&&i.value.delivery.take_address&&(s.value.delivery.taker_name=i.value.delivery.take_address.name,s.value.delivery.taker_mobile=i.value.delivery.take_address.mobile),s.value.order_key=e.order_key,i.value.delivery.delivery_type_list&&(I.value=Z(Object.values(i.value.delivery.delivery_type_list))),i.value.discount&&i.value.discount.manjian&&(i.value.manjian=i.value.discount.manjian),i.value.delivery.take_store&&(ne.value={time_interval:i.value.delivery.take_store.time_interval,time_week:i.value.delivery.take_store.time_week,trade_time_json:i.value.delivery.take_store.trade_time_json}),he&&(z.value=I.value.findIndex((e=>e.key===i.value.delivery.delivery_type))),!s.value.delivery.delivery_type&&e.delivery.delivery_type&&(s.value.delivery.delivery_type=e.delivery.delivery_type),Q((()=>{setTimeout((()=>{I.value&&Object.keys(I.value).length&&"store"==I.value[0].key&&m.value&&m.value.getData((e=>{e.length&&(s.value.delivery.take_store_id=e[0]&&e[0].store_id?e[0].store_id:0)}))}),500)}))})).catch((()=>{P.value=!1}))};je(),T((()=>I.value.length),((e,t)=>{I.value.length&&uni.getStorageSync("distributionType")&&(I.value.forEach(((e,t)=>{e.name==uni.getStorageSync("distributionType")&&(z.value=t,we(e.key,t))})),uni.removeStorage({key:"distributionType"}))}));let Ce=0;const Se=(e={})=>{if(e&&Object.keys(e).length){let t=Z(e);s.value.impulse_buy_goods=s.value.impulse_buy_goods&&s.value.impulse_buy_goods.length?s.value.impulse_buy_goods:[],s.value.impulse_buy_goods.forEach(((a,l,r)=>{t.impulse_buy_goods_id==a.impulse_buy_goods_id&&(a.num=e.num,a.num||r.splice(l,1),t="")})),t&&s.value.impulse_buy_goods.push(t)}else s.value.impulse_buy_goods&&delete s.value.impulse_buy_goods;je({is_need_recalculate:1})},Fe=()=>{if(Te()&&!D.value){if(A.value){let e=!0;for(let t=0;t<A.value.length;t++)if(!A.value[t].verify()){e=!1;break}if(!e)return}N.value&&!N.value.verify()||(D.value=!0,s.value.form_data.order={},s.value.form_data.goods={},N.value&&(s.value.form_data.form_id=i.value.form_id,s.value.form_data.order=N.value.getData()),A.value&&i.value.goods.forEach((e=>{if(e.goods.form_id)for(let t=0;t<A.value.length;t++){let a=A.value[t].getData();a.relate_id==e.sku_id&&e.goods.form_id==a.form_id&&(s.value.form_data.goods[e.sku_id]=a)}})),ue(s.value).then((({data:e})=>{var t;if(Ce=e.order_id,N.value&&N.value.clearStorage(),A.value)for(let a=0;a<A.value.length;a++)A.value[a].clearStorage();s.value.form_data={},0==i.value.basic.order_money?J({url:"/addon/shop/pages/order/detail",param:{order_id:Ce},mode:"redirectTo"}):null==(t=C.value)||t.open(e.trade_type,e.order_id,`/addon/shop/pages/order/detail?order_id=${e.order_id}`)})).catch((()=>{s.value.form_data={},D.value=!1})))}},Te=()=>{const e=s.value;if(i.value.basic.has_goods_types.includes("real")){if(["express","local_delivery"].includes(e.delivery.delivery_type)&&!i.value.delivery.take_address)return U({title:"请选择收货地址",icon:"none"}),!1;if("store"==e.delivery.delivery_type&&!e.delivery.take_store_id)return U({title:"请选择自提点",icon:"none"}),!1}if("store"==e.delivery.delivery_type){if(!e.delivery.taker_name)return U({title:"请输入姓名",icon:"none"}),!1;if(!e.delivery.taker_mobile)return U({title:"请输入手机号",icon:"none"}),!1;if(!/^1[3-9]\d{9}$/.test(e.delivery.taker_mobile))return U({title:"请输入正确的手机号",icon:"none"}),!1;if(!e.delivery.buyer_ask_delivery_time)return U({title:"请选择自提时间",icon:"none"}),!1}return!0},Ve=()=>{J({url:"/addon/shop/pages/order/detail",param:{order_id:Ce},mode:"redirectTo"})},Be=()=>{let e={};e.delivery=s.value.delivery.delivery_type,e.type="local_delivery"==s.value.delivery.delivery_type?"location_address":"address",e.id=i.value.delivery.take_address.id,S.value.open(e)},$e=V((()=>{var e;return(null==(e=d.value)?void 0:e.couponList)||[]})),Oe=e=>{s.value.discount.coupon_id=e?e.id:0,je()},Re=e=>{s.value.delivery.take_store_id=e&&e.store_id?e.store_id:0,e&&(ne.value={time_interval:e.time_interval,time_week:e.time_week,trade_time_json:e.trade_time_json}),je()},De=e=>{s.value.invoice=e},Le=e=>{s.value.order_key="",s.value.delivery.delivery_type=e.delivery,s.value.delivery.take_address_id=e.address_id,je()};return(e,t)=>{const a=j,F=W,T=w,V=K,D=u(n("u--image"),Y),U=u(n("u-alert"),re),q=L,G=u(n("u-tabbar"),se),H=u(n("pay"),oe);return p(),c(a,{style:b(e.themeColor()),class:"payment-wrap"},{default:_((()=>[i.value?(p(),c(a,{key:0,class:"payment-body min-h-[100vh]"},{default:_((()=>[x(a,{class:"pt-[30rpx] sidebar-margin payment-bottom"},{default:_((()=>[O(" 配送方式 "),i.value.basic.has_goods_types.includes("real")&&I.value.length?(p(),c(a,{key:0,class:"mb-[var(--top-m)] rounded-[var(--rounded-big)] bg-white",style:b({backgroundImage:`url(${$(E)("addon/shop/payment/head_bg.png")})`,backgroundSize:"100%",backgroundRepeat:"no-repeat",backgroundPosition:"bottom"})},{default:_((()=>[I.value.length>1?(p(),c(a,{key:0,class:"rounded-tl-[var(--rounded-big)] rounded-tr-[var(--rounded-big)] head-tab flex items-center w-full bg-[var(--shop-payment-header-tab-color)]"},{default:_((()=>[(p(!0),v(f,null,y(I.value,((e,t)=>(p(),c(a,{key:t,class:g(["head-tab-item flex-1 relative",{active:t===z.value}])},{default:_((()=>[x(a,{class:"h-[74rpx] relative z-10 text-center leading-[74rpx] text-[28rpx]",onClick:a=>we(e.key,t)},{default:_((()=>[h(k(e.name),1)])),_:2},1032,["onClick"]),t===z.value&&3==I.value.length?(p(),c(F,{key:0,class:"tab-image absolute bottom-[-2rpx] h-[94rpx] w-[240rpx]",src:$(E)(`addon/shop/payment/tab_${t}.png`),mode:"aspectFit"},null,8,["src"])):t===z.value&&2==I.value.length?(p(),c(F,{key:1,class:"tab-img absolute bottom-[-2rpx] h-[95rpx] w-[354rpx]",src:$(E)(`addon/shop/payment/tabstyle_${t}.png`),mode:"aspectFit"},null,8,["src"])):O("v-if",!0)])),_:2},1032,["class"])))),128))])),_:1})):O("v-if",!0),x(a,{class:"min-h-[140rpx] flex items-center px-[30rpx]"},{default:_((()=>[O(" 收货地址 "),["express","local_delivery"].includes(s.value.delivery.delivery_type)?(p(),c(a,{key:0,class:"w-full",onClick:Be},{default:_((()=>[e.$u.test.isEmpty(i.value.delivery.take_address)?(p(),c(a,{key:1,class:"flex items-center"},{default:_((()=>[x(F,{class:"w-[26rpx] h-[30rpx] mr-[10rpx]",src:$(E)("addon/shop/payment/position_02.png"),mode:"aspectFit"},null,8,["src"]),x(T,{class:"text-[28rpx]"},{default:_((()=>[h("添加收货地址")])),_:1}),x(T,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)] ml-auto"})])),_:1})):(p(),c(a,{key:0,class:"pt-[20rpx] pb-[30rpx] flex items-center"},{default:_((()=>[x(F,{class:"w-[60rpx] h-[60rpx] mr-[20rpx] flex-shrink-0",src:$(E)("addon/shop/payment/position_01.png"),mode:"aspectFit"},null,8,["src"]),x(a,{class:"flex flex-col overflow-hidden"},{default:_((()=>[x(T,{class:"text-[26rpx] text-[var(--text-color-light9)] mt-[16rpx] truncate max-w-[536rpx]"},{default:_((()=>[h(k(i.value.delivery.take_address.full_address.split(i.value.delivery.take_address.address)[0]),1)])),_:1}),x(T,{class:"font-500 text-[30rpx] mt-[14rpx] text-[#333] truncate max-w-[536rpx]"},{default:_((()=>[h(k(i.value.delivery.take_address.address),1)])),_:1}),x(a,{class:"flex items-center text-[26rpx] text-[var(--text-color-light6)] mt-[16rpx]"},{default:_((()=>[x(T,{class:"mr-[16rpx]"},{default:_((()=>[h(k(i.value.delivery.take_address.name),1)])),_:1}),x(T,null,{default:_((()=>[h(k($(X)(i.value.delivery.take_address.mobile)),1)])),_:1})])),_:1})])),_:1}),x(T,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})):O("v-if",!0),O(" 自提点 "),"store"==s.value.delivery.delivery_type?(p(),c(a,{key:1,class:"flex items-center w-full",onClick:t[0]||(t[0]=e=>(m.value&&(s.value.delivery.take_store_id||m.value.getData((e=>{e.length&&(s.value.delivery.take_store_id=e[0]&&e[0].store_id?e[0].store_id:0)}))),void m.value.open()))},{default:_((()=>[e.$u.test.isEmpty(i.value.delivery.take_store)?(p(),c(a,{key:1,class:"flex items-center w-full"},{default:_((()=>[x(F,{class:"w-[26rpx] h-[30rpx] mr-[10rpx]",src:$(E)("addon/shop/payment/position_02.png"),mode:"aspectFit"},null,8,["src"]),x(T,{class:"text-[28rpx]"},{default:_((()=>[h("请选择自提点")])),_:1}),x(T,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):(p(),c(a,{key:0,class:"pt-[40rpx] pb-[30rpx] w-full flex items-center"},{default:_((()=>[x(a,{class:"flex flex-col"},{default:_((()=>[x(a,{class:"text-[30rpx] font-500 text-[#303133] mb-[20rpx]"},{default:_((()=>[h(k(i.value.delivery.take_store.store_name),1)])),_:1}),x(a,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[20rpx] leading-[1.4] flex"},{default:_((()=>[x(T,{class:"flex-shrink-0"},{default:_((()=>[h("门店地址：")])),_:1}),x(T,{class:"max-w-[490rpx]"},{default:_((()=>[h(k(i.value.delivery.take_store.full_address),1)])),_:1})])),_:1}),x(a,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[20rpx]"},{default:_((()=>[x(T,null,{default:_((()=>[h("联系电话：")])),_:1}),x(T,null,{default:_((()=>[h(k(i.value.delivery.take_store.store_mobile),1)])),_:1})])),_:1}),x(a,{class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:_((()=>[x(T,null,{default:_((()=>[h("营业时间：")])),_:1}),x(T,null,{default:_((()=>[h(k(i.value.delivery.take_store.trade_time),1)])),_:1})])),_:1})])),_:1}),x(T,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})):O("v-if",!0)])),_:1}),"store"==s.value.delivery.delivery_type?(p(),c(a,{key:1},{default:_((()=>[O(" 姓名 "),x(a,{class:"px-[20rpx] py-[14rpx]"},{default:_((()=>[x(a,{class:"flex justify-between items-center"},{default:_((()=>[x(a,{class:"text-color text-[28rpx]",onClick:ce},{default:_((()=>[h("姓名")])),_:1}),x(V,{class:"text-right",maxlength:"20","placeholder-style":"color:#B1B3B5",placeholder:"请输入",modelValue:s.value.delivery.taker_name,"onUpdate:modelValue":t[1]||(t[1]=e=>s.value.delivery.taker_name=e)},null,8,["modelValue"])])),_:1})])),_:1}),O(" 预留手机 "),x(a,{class:"px-[20rpx] py-[14rpx]"},{default:_((()=>[x(a,{class:"flex justify-between items-center"},{default:_((()=>[x(a,{class:"text-color text-[28rpx]"},{default:_((()=>[h("预留手机")])),_:1}),x(V,{class:"text-right",maxlength:"11","placeholder-style":"color:#B1B3B5",placeholder:"请输入",modelValue:s.value.delivery.taker_mobile,"onUpdate:modelValue":t[2]||(t[2]=e=>s.value.delivery.taker_mobile=e)},null,8,["modelValue"])])),_:1})])),_:1}),O(" 提货时间 "),x(a,{class:"flex justify-between items-center px-[20rpx] pt-[14rpx] pb-[24rpx] px-[20rpx]"},{default:_((()=>[x(a,{class:"text-color text-[28rpx]"},{default:_((()=>[h("提货时间")])),_:1}),x(a,{class:"flex",onClick:ce},{default:_((()=>[x(a,{class:g(["text-[28rpx] ml-2 text-right",{"text-[#63676D]":!s.value.delivery.buyer_ask_delivery_time}])},{default:_((()=>[h(k(s.value.delivery.buyer_ask_delivery_time?ve.value:"选择提货时间"),1)])),_:1},8,["class"]),x(T,{class:"text-[26rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1})])),_:1})):O("v-if",!0)])),_:1},8,["style"])):O("v-if",!0),i.value.basic.has_goods_types.includes("real")&&!I.value.length?(p(),c(a,{key:1,class:"mb-[var(--top-m)] card-template h-[100rpx] flex items-center"},{default:_((()=>[M("p",{class:"text-[28rpx] text-[var(--primary-color)]"},"商家尚未配置配送方式")])),_:1})):O("v-if",!0),x(a,{class:"mb-[var(--top-m)] card-template p-[0] pb-[var(--pad-top-m)]"},{default:_((()=>[x(a,{class:"pt-[var(--pad-top-m)] pb-[14rpx]"},{default:_((()=>[(p(!0),v(f,null,y(i.value.goods,((e,t)=>(p(),v(f,{key:t},[1!=e.is_impulse_buy?(p(),c(a,{key:0,class:g(["px-[var(--pad-sidebar-m)]",{"mb-[20rpx]":t+1!=i.value.goods.length}])},{default:_((()=>[x(a,{class:"flex"},{default:_((()=>[x(D,{radius:"var(--goods-rounded-big)",width:"180rpx",height:"180rpx",src:$(E)(e.sku_image),model:"aspectFill"},{error:_((()=>[x(F,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:$(E)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),x(a,{class:"flex flex-1 w-0 flex-col justify-between ml-[20rpx] py-[6rpx]"},{default:_((()=>[x(a,{class:"line-normal"},{default:_((()=>[x(a,{class:"truncate text-[#303133] text-[28rpx] leading-[32rpx]"},{default:_((()=>[h(k(e.goods.goods_name),1)])),_:2},1024),e.sku_name?(p(),c(a,{key:0,class:"mt-[14rpx] flex"},{default:_((()=>[x(T,{class:"truncate text-[24rpx] text-[var(--text-color-light9)] leading-[28rpx]"},{default:_((()=>[h(k(e.sku_name),1)])),_:2},1024)])),_:2},1024)):O("v-if",!0)])),_:2},1024),e.manjian_info&&Object.keys(e.manjian_info).length?(p(),c(a,{key:0,class:"flex items-center mt-[10rpx] mb-[auto]",onClick:B((t=>(e=>{let t={};t.condition_type=Z(e).condition_type,t.rule_json=[Z(e).rule],t.name=Z(e).manjian_name,o.value.open(t)})(e.manjian_info)),["stop"])},{default:_((()=>[x(a,{class:"bg-[var(--primary-color-light)] text-[var(--primary-color)] rounded-[6rpx] text-[20rpx] flex items-center justify-center w-[88rpx] h-[36rpx] mr-[6rpx]"},{default:_((()=>[h("满减送")])),_:1}),x(T,{class:"text-[22rpx] text-[#999]"},{default:_((()=>[h(k(e.manjian_info.manjian_name),1)])),_:2},1024)])),_:2},1032,["onClick"])):O("v-if",!0),e.not_support_delivery?(p(),c(a,{key:1,class:g(["mb-auto",{"mt-[6rpx]":!e.sku_name}])},{default:_((()=>[x(U,{type:"error",description:"该商品不支持当前所选配送方式",class:"leading-[30rpx] !inline-block",fontSize:"11"})])),_:2},1032,["class"])):O("v-if",!0),x(a,{class:"flex justify-between items-baseline"},{default:_((()=>[x(a,{class:"text-[var(--price-text-color)] flex items-baseline price-font"},{default:_((()=>[x(T,{class:"text-[24rpx] font-500 mr-[4rpx]"},{default:_((()=>[h("￥")])),_:1}),x(T,{class:"text-[40rpx] font-500"},{default:_((()=>[h(k(parseFloat(e.price).toFixed(2).split(".")[0]),1)])),_:2},1024),x(T,{class:"text-[24rpx] font-500"},{default:_((()=>[h("."+k(parseFloat(e.price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),x(a,{class:"font-400 text-[28rpx] text-[#303133]"},{default:_((()=>[x(T,null,{default:_((()=>[h("x")])),_:1}),x(T,null,{default:_((()=>[h(k(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),e.is_newcomer&&e.newcomer_price!=e.price&&e.num>1?(p(),c(a,{key:0,class:g(["flex items-center mt-[8rpx]",{"pb-[40rpx]":t+1!=Object.keys(i.value.goods_data).length}])},{default:_((()=>[x(F,{class:"h-[24rpx] w-[56rpx]",src:$(E)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"]),x(a,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:_((()=>[h("第1"+k(e.goods.unit)+"，￥"+k(parseFloat(e.newcomer_price).toFixed(2))+"/"+k(e.goods.unit)+"；第"+k(e.num>2?"2~"+e.num:"2")+k(e.goods.unit)+"，￥"+k(parseFloat(e.price).toFixed(2))+"/"+k(e.goods.unit),1)])),_:2},1024)])),_:2},1032,["class"])):O("v-if",!0),e.goods.form_id?(p(),c(a,{key:1,class:"card-template !p-[0]"},{default:_((()=>[x(ye,{ref_for:!0,ref_key:"diyFormGoodsRef",ref:A,form_id:e.goods.form_id,relate_id:e.sku_id,storage_name:"diyFormStorageByGoodsDetail_"+e.sku_id,form_border:"none"},null,8,["form_id","relate_id","storage_name"])])),_:2},1024)):O("v-if",!0)])),_:2},1032,["class"])):O("v-if",!0)],64)))),128)),O(" 赠品 "),i.value.gift_goods&&Object.keys(i.value.gift_goods).length?(p(),c(a,{key:0,class:"pt-[20rpx] mb-[10rpx] bg-[#f9f9f9] mt-[24rpx] mx-[var(--pad-sidebar-m)] rounded-[30rpx]"},{default:_((()=>[(p(!0),v(f,null,y(i.value.gift_goods,((e,t,l)=>(p(),c(a,{key:l,class:"flex px-[var(--pad-sidebar-m)] pb-[20rpx]"},{default:_((()=>[x(D,{radius:"var(--goods-rounded-big)",width:"120rpx",height:"120rpx",src:$(E)(e.sku_image),model:"aspectFill"},{error:_((()=>[x(F,{class:"w-[120rpx] h-[120rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:$(E)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),x(a,{class:"ml-[16rpx] py-[8rpx] flex flex-1 flex-col justify-between"},{default:_((()=>[x(a,{class:"flex items-center"},{default:_((()=>[x(a,{class:"bg-[var(--primary-color-light)] whitespace-nowrap text-[var(--primary-color)] rounded-[6rpx] text-[22rpx] flex items-center justify-center w-[64rpx] h-[34rpx] mr-[6rpx]"},{default:_((()=>[h("赠品")])),_:1}),x(a,{class:"text-[26rpx] max-w-[400rpx] truncate leading-[40rpx] text-[#333]"},{default:_((()=>[h(k(e.goods.goods_name),1)])),_:2},1024)])),_:2},1024),x(a,{class:"flex items-center"},{default:_((()=>[e.sku_name?(p(),c(a,{key:0,class:"text-[22rpx] text-[var(--text-color-light9)] truncate max-w-[400rpx] leading-[28rpx]"},{default:_((()=>[h(k(e.sku_name),1)])),_:2},1024)):O("v-if",!0),x(a,{class:"ml-[auto] font-400 text-[26rpx] text-[#303133]"},{default:_((()=>[x(T,null,{default:_((()=>[h("x")])),_:1}),x(T,null,{default:_((()=>[h(k(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):O("v-if",!0)])),_:1}),O(" 买家留言 "),x(a,{class:"bg-white flex items-center leading-[30rpx] px-[var(--pad-sidebar-m)]"},{default:_((()=>[x(a,{class:"text-[28rpx] w-[150rpx] text-[#303133]"},{default:_((()=>[h("买家留言")])),_:1}),x(a,{class:"flex-1 text-[#303133]"},{default:_((()=>[x(V,{type:"text",modelValue:s.value.member_remark,"onUpdate:modelValue":t[3]||(t[3]=e=>s.value.member_remark=e),class:"text-right text-[#333] text-[28rpx]",maxlength:"50",placeholder:"请输入留言信息给卖家","placeholder-class":"text-[var(--text-color-light9)] text-[28rpx]"},null,8,["modelValue"])])),_:1})])),_:1}),O(" 发票 "),R.value&&R.value.invoiceOpen?(p(),c(a,{key:0,class:"flex items-center text-[#303133] leading-[30rpx] mt-[30rpx] px-[var(--pad-sidebar-m)]",onClick:t[4]||(t[4]=e=>R.value.open())},{default:_((()=>[x(a,{class:"text-[28rpx] w-[150rpx] text-[#303133]"},{default:_((()=>[h("发票信息")])),_:1}),x(a,{class:"flex-1 w-0 text-right truncate"},{default:_((()=>[x(T,{class:"text-[28rpx] text-[#333]"},{default:_((()=>[h(k(s.value.invoice.header_name||"不需要发票"),1)])),_:1})])),_:1}),x(T,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):O("v-if",!0)])),_:1}),d.value&&$($e).length?(p(),c(a,{key:2,class:"mb-[var(--top-m)] card-template"},{default:_((()=>[O(" 优惠券 "),$($e).length?(p(),c(a,{key:0,class:"flex items-center h-[40rpx] leading-[40rpx]",onClick:t[5]||(t[5]=e=>d.value.open(s.value.discount.coupon_id))},{default:_((()=>[x(a,{class:"text-[28rpx] w-[150rpx] text-[#303133] flex-shrink-0"},{default:_((()=>[h("优惠券")])),_:1}),x(a,{class:"flex-1 flex justify-end truncate"},{default:_((()=>[i.value.discount&&i.value.discount.coupon?(p(),c(T,{key:0,class:"text-[var(--primary-color)] text-[28rpx] truncate"},{default:_((()=>[h(k(i.value.discount.coupon.title),1)])),_:1})):(p(),c(T,{key:1,class:"text-[28rpx] text-gray-subtitle"},{default:_((()=>[h("请选择优惠券")])),_:1}))])),_:1}),x(T,{class:"nc-iconfont nc-icon-youV6xx -mb-[2rpx] text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):O("v-if",!0)])),_:1})):O("v-if",!0),i.value.form_id?(p(),c(a,{key:3,class:"card-template py-[10rpx] mb-[var(--top-m)]"},{default:_((()=>[x(ye,{ref_key:"diyFormRef",ref:N,form_id:i.value.form_id,storage_name:"diyFormStorageByOrderPayment"},null,8,["form_id"])])),_:1})):O("v-if",!0),O(" 顺手买 "),$(l).siteAddons.includes("shop_impulse_buy")&&(!s.value.extend_data||s.value.extend_data&&!s.value.extend_data.activity_type||"discount"==s.value.extend_data.activity_type)?(p(),c($(ke),{key:z.value,ref_key:"impulseBuyRef",ref:r,data:i.value.goods,"order-key":i.value.order_key,"delivery-type":s.value.delivery.delivery_type,"calculate-loading":P.value,onConfirm:Se},null,8,["data","order-key","delivery-type","calculate-loading"])):O("v-if",!0),x(a,{class:"card-template"},{default:_((()=>[x(a,{class:"title"},{default:_((()=>[h("价格明细")])),_:1}),x(a,{class:"card-template-item"},{default:_((()=>[x(a,{class:"text-[28rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:_((()=>[h("商品金额")])),_:1}),x(a,{class:"flex-1 w-0 text-right price-font text-[#333] text-[32rpx]"},{default:_((()=>[h("￥"+k(parseFloat(i.value.basic.goods_money).toFixed(2)),1)])),_:1})])),_:1}),parseFloat(i.value.basic.delivery_money)?(p(),c(a,{key:0,class:"card-template-item"},{default:_((()=>[x(a,{class:"text-[28rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:_((()=>[h("配送费用")])),_:1}),x(a,{class:"flex-1 w-0 text-right price-font text-[#333] text-[32rpx]"},{default:_((()=>[h("￥"+k(parseFloat(i.value.basic.delivery_money).toFixed(2)),1)])),_:1})])),_:1})):O("v-if",!0),parseFloat(i.value.basic.coupon_money)?(p(),c(a,{key:1,class:"card-template-item"},{default:_((()=>[x(a,{class:"text-[28rpx] w-[170rpx] leading-[30rpx] text-[#303133]"},{default:_((()=>[h("优惠券优惠")])),_:1}),x(a,{class:"flex-1 w-0 text-right text-[var(--price-text-color)] text-[32rpx] price-font leading-[1]"},{default:_((()=>[h("-￥"+k(parseFloat(i.value.basic.coupon_money).toFixed(2)),1)])),_:1})])),_:1})):O("v-if",!0),parseFloat(i.value.basic.manjian_discount_money)?(p(),c(a,{key:2,class:"card-template-item"},{default:_((()=>[x(a,{class:"text-[28rpx] w-[170rpx] leading-[30rpx] text-[#303133]"},{default:_((()=>[h("满减优惠")])),_:1}),x(a,{class:"flex-1 w-0 text-right text-[var(--price-text-color)] text-[32rpx] price-font leading-[1]"},{default:_((()=>[h("-￥"+k(parseFloat(i.value.basic.manjian_discount_money).toFixed(2)),1)])),_:1})])),_:1})):O("v-if",!0)])),_:1})])),_:1}),x(G,{fixed:!0,placeholder:!0,safeAreaInsetBottom:!0,zIndex:"10"},{default:_((()=>[x(a,{class:"flex-1 flex items-center justify-between pl-[30rpx] pr-[20rpx]"},{default:_((()=>[x(a,{class:"flex items-baseline"},{default:_((()=>[x(T,{class:"text-[26rpx] text-[#333] leading-[32rpx]"},{default:_((()=>[h("合计：")])),_:1}),x(a,{class:"inline-block"},{default:_((()=>[x(T,{class:"text-[26rpx] font-500 text-[var(--price-text-color)] price-font leading-[30rpx]"},{default:_((()=>[h("￥")])),_:1}),x(T,{class:"text-[44rpx] font-500 text-[var(--price-text-color)] price-font leading-[46rpx]"},{default:_((()=>[h(k(parseFloat(i.value.basic.order_money).toFixed(2).split(".")[0]),1)])),_:1}),x(T,{class:"text-[26rpx] font-500 text-[var(--price-text-color)] price-font leading-[46rpx]"},{default:_((()=>[h("."+k(parseFloat(i.value.basic.order_money).toFixed(2).split(".")[1]),1)])),_:1})])),_:1})])),_:1}),x(q,{class:g(["w-[196rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] m-0 rounded-full primary-btn-bg remove-border",{"opacity-80":P.value}]),"hover-class":"none",disabled:P.value,onClick:Fe},{default:_((()=>[h("提交订单")])),_:1},8,["disabled","class"])])),_:1})])),_:1}),O(" 选择优惠券 "),x($(be),{"order-key":s.value.order_key,ref_key:"couponRef",ref:d,onConfirm:Oe},null,8,["order-key"])])),_:1})):O("v-if",!0),O(" 选择自提点 "),i.value&&i.value.basic&&i.value.basic.has_goods_types&&i.value.basic.has_goods_types.includes("real")?(p(),c($(ee),{key:1,ref_key:"storeRef",ref:m,onConfirm:Re},null,512)):O("v-if",!0),O(" 发票 "),x($(te),{ref_key:"invoiceRef",ref:R,onConfirm:De},null,512),O(" 地址 "),x($(ae),{ref_key:"addressRef",ref:S,onConfirm:Le,back:"/addon/shop/pages/order/payment"},null,512),O(" 满减 "),x(_e,{ref_key:"manjianShowRef",ref:o},null,512),x(H,{ref_key:"payRef",ref:C,onClose:Ve},null,512),Object.keys(ne.value).length?(p(),c($(le),{key:2,ref_key:"selectTime",ref:pe,rules:ne.value,isQuantum:!0,onChange:xe,onGetStamp:me,onGetDate:ge},null,8,["rules"])):O("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-84e033e7"]]);export{we as default};
