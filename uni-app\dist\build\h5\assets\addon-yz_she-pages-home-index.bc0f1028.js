import{_ as t}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{d as e,u as o,r as s,o as r,c as a,w as i,b as p,e as u,f as m,v as n,g as _,n as l,i as d,j,k as g}from"./index-3caf046d.js";import{u as c}from"./useDiy.39dfdf8b.js";import{d as f}from"./index.9de114a1.js";import{_ as v}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.255170b9.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-popup.1b30ffa7.js";import"./u-safe-bottom.98e092c5.js";import"./top-tabbar.f4fde406.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.abe3938e.js";import"./u-checkbox-group.0328273c.js";import"./u-datetime-picker.a5259774.js";import"./u-input.2d8dc7a4.js";import"./u-upload.83871903.js";import"./u-radio-group.63482a1c.js";import"./diy_form.9eef685a.js";import"./u-avatar.30e31e9c.js";import"./u-text.f02e6497.js";import"./u-parse.406d0731.js";import"./tabbar.2c31519d.js";import"./u-tabbar-item.31141540.js";import"./u-tabbar.38f37e13.js";import"./index.32583a71.js";import"./u--image.eb573bce.js";import"./u-image.04cba9a2.js";/* empty css                                                                */import"./goods.6a81cb49.js";import"./useGoods.9c8f1c51.js";import"./coupon.2f3f2d3d.js";import"./point.0698952c.js";import"./rank.7a4c9318.js";import"./bind-mobile.25318c0e.js";import"./u-form.49dbb57f.js";import"./u-line.69c0c00f.js";import"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import"./u-modal.8624728a.js";import"./voucher.db23b7c0.js";import"./quote.9b84c391.js";import"./newcomer.6993dfef.js";import"./order.5c5c6bee.js";const y=v(e({__name:"index",setup(e){const{setShare:v}=o(),y=c({name:"DIY_YZ_SHE_INDEX"}),b=s(null);return s(null),y.onLoad(),y.onShow((t=>{var e;let o=t.share?JSON.parse(t.share):null;v(o),null==(e=b.value)||e.refresh()})),y.onHide(),y.onUnload(),y.onPageScroll(),(e,o)=>{const s=d(j("loading-page"),t),c=g;return r(),a(c,{style:l(e.themeColor())},{default:i((()=>[p(s,{loading:u(y).getLoading()},null,8,["loading"]),m(p(c,null,{default:i((()=>[_(" 自定义模板渲染 "),p(c,{class:"diy-template-wrap bg-index",style:l(u(y).pageStyle())},{default:i((()=>[p(f,{ref_key:"diyGroupRef",ref:b,data:u(y).data},null,8,["data"])])),_:1},8,["style"])])),_:1},512),[[n,!u(y).getLoading()]])])),_:1},8,["style"])}}}),[["__scopeId","data-v-f36cf2a7"]]);export{y as default};
