import{d as a,I as e,p as l,r as s,_ as t,o as c,c as o,w as d,g as n,b as r,y as u,a_ as i,t as _,z as f,F as v,f as m,v as g,O as h,e as p,M as y,U as b,be as k,k as C,al as x,S as w,$ as z,x as I,A as $,ax as S,Q as A}from"./index-3caf046d.js";import{s as F,g as M,a as j,b as B,c as V}from"./brand.3bc41d81.js";import{_ as E}from"./_plugin-vue_export-helper.1b428a4d.js";const L=E(a({__name:"index",setup(a){const E=e(),L=l((()=>E.info)),P=s(""),U=s("A"),D=s(!1),O=s(1),Q=s(!1),G=s(!1),H=s([]),J=s([]),K=s([]),N=s(["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","其他"]),R=s([]),T=s([]),W=l((()=>{const a={};return R.value.forEach((e=>{const l=e.letter;a[l]||(a[l]=[]),a[l].push(e)})),N.value.map((e=>({letter:e,brands:a[e]||[]}))).filter((a=>a.brands.length>0))})),X=l((()=>{const a=new Set;return R.value.forEach((e=>{a.add(e.letter)})),N.value.filter((e=>a.has(e)))})),Y=()=>{P.value.trim()?(G.value=!0,q()):(G.value=!1,ea())},Z=()=>{if(!P.value.trim())return G.value=!1,void ea();G.value=!0,q()},q=async()=>{try{D.value=!0;const a=await F(P.value,{category_id:O.value});1===a.code&&(R.value=a.data||[],y({title:`找到${a.data.length}个品牌`,icon:"success"}))}catch(a){console.error("搜索失败:",a),y({title:"搜索失败，请重试",icon:"none"})}finally{D.value=!1}};l((()=>{const a=H.value.find((a=>a.id===O.value));return a?a.name:"请选择分类"}));const aa=a=>{console.log("选择品牌:",a),uni.setStorageSync("selectedBrandInfo",{id:a.id,name:a.name,logo:a.logo,hot_name:a.hot_name,category_id:a.category_id}),k({url:`/addon/yz_she/pages/evaluate/index?brandId=${a.id}`})},ea=()=>{T.value.length>0&&(R.value=[...T.value]),G.value=!1},la=()=>{Q.value=!0},sa=()=>{Q.value=!1},ta=()=>{if(console.log("批量下单"),!L.value)return console.log("用户未登录，跳转到登录页面"),b().setLoginBack({url:"/addon/yz_she/pages/brand/index"}),!1;const a=O.value;console.log("跳转批量回收页面，分类ID:",a),k({url:`/addon/yz_she/pages/evaluate/batch?category_id=${a}`})},ca=async()=>{try{const a=await j({category_id:O.value});1===a.code&&(J.value=a.data||[])}catch(a){console.error("加载热门品牌失败:",a)}},oa=async()=>{try{const a=await B({category_id:O.value});1===a.code&&(R.value=a.data||[],T.value=[...a.data||[]])}catch(a){console.error("加载全部品牌失败:",a)}},da=async()=>{try{const a={category_id:O.value,is_hot:1,status:1,limit:4},e=await V(a);if(1===e.code){const a=e.data.data||[];K.value=a.map((a=>({id:a.id,name:a.name,image:a.image,price:Math.max(a.price_new||0,a.price_used||0,a.price_damaged||0)})))}}catch(a){console.error("加载热门回收商品失败:",a)}};return t((async()=>{await(async()=>{try{const a=await M();1===a.code&&(H.value=a.data||[],H.value.length>0&&(O.value=H.value[0].id))}catch(a){console.error("加载分类失败:",a)}})(),await Promise.all([ca(),oa(),da()])})),(a,e)=>{const l=C,s=x,t=w,y=A;return c(),o(l,{class:"brand-page"},{default:d((()=>[n(" 顶部搜索栏 "),r(l,{class:"search-section"},{default:d((()=>[r(l,{class:"search-container"},{default:d((()=>[r(l,{class:"search-box"},{default:d((()=>[r(l,{class:"search-icon"},{default:d((()=>[(c(),u("svg",{viewBox:"0 0 1024 1024",width:"20",height:"20"},[i("path",{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z",fill:"currentColor"})]))])),_:1}),r(s,{class:"search-input",type:"text",placeholder:"输入品牌名搜索",modelValue:P.value,"onUpdate:modelValue":e[0]||(e[0]=a=>P.value=a),onInput:Y,onConfirm:Z},null,8,["modelValue"])])),_:1}),r(l,{class:"search-button",onClick:Z},{default:d((()=>[_("搜索")])),_:1})])),_:1})])),_:1}),n(" 分类选择区域 "),r(l,{class:"category-section"},{default:d((()=>[r(l,{class:"category-tabs"},{default:d((()=>[(c(!0),u(v,null,f(H.value,(a=>(c(),o(l,{class:z(["category-tab",{active:O.value===a.id}]),key:a.id,onClick:e=>(async a=>{O.value=a.id,console.log("选择分类:",a),await Promise.all([ca(),oa(),da()])})(a)},{default:d((()=>[r(t,{class:"tab-title"},{default:d((()=>[_(I(a.name),1)])),_:2},1024),O.value===a.id?(c(),o(l,{key:0,class:"tab-underline"})):n("v-if",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),n(" 品牌快捷入口区 "),m(r(l,{class:"hot-brands-section"},{default:d((()=>[r(l,{class:"section-header"},{default:d((()=>[r(t,{class:"section-title"},{default:d((()=>[_("热门品牌")])),_:1})])),_:1}),r(l,{class:"brands-grid"},{default:d((()=>[(c(!0),u(v,null,f(J.value,(a=>(c(),o(l,{class:"brand-card",key:a.id,onClick:e=>aa(a)},{default:d((()=>[r(l,{class:"brand-logo-container"},{default:d((()=>[a.logo?(c(),o(y,{key:0,src:p($)(a.logo),class:"brand-logo",mode:"aspectFill"},null,8,["src"])):(c(),o(l,{key:1,class:"brand-logo-placeholder"},{default:d((()=>[r(t,{class:"brand-initial"},{default:d((()=>[_(I((a.hot_name||a.name).charAt(0)),1)])),_:2},1024)])),_:2},1024))])),_:2},1024),r(t,{class:"brand-name"},{default:d((()=>[_(I(a.hot_name||a.name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1},512),[[g,!G.value]]),n(" 热门回收展示区 "),m(r(l,{class:"hot-recycle-section"},{default:d((()=>[r(l,{class:"section-header"},{default:d((()=>[r(t,{class:"section-title"},{default:d((()=>[_("热门回收")])),_:1})])),_:1}),r(l,{class:"recycle-grid"},{default:d((()=>[(c(!0),u(v,null,f(K.value.slice(0,4),(a=>(c(),o(l,{class:"recycle-item",key:a.id,onClick:e=>(a=>{console.log("查看回收商品:",a),k({url:`/addon/yz_she/pages/product/detail?id=${a.id}`})})(a)},{default:d((()=>[r(l,{class:"item-image"},{default:d((()=>[a.image?(c(),o(y,{key:0,src:p($)(a.image),class:"product-image",mode:"aspectFill"},null,8,["src"])):(c(),o(l,{key:1,class:"image-placeholder"}))])),_:2},1024),r(l,{class:"item-info"},{default:d((()=>[r(t,{class:"item-name"},{default:d((()=>{return[_(I((e=a.name,l=15,e?e.length<=l?e:e.substring(0,l)+"...":"")),1)];var e,l})),_:2},1024),r(t,{class:"item-price"},{default:d((()=>[r(t,{class:"price-label"},{default:d((()=>[_("最高")])),_:1}),r(t,{class:"price-value"},{default:d((()=>[_(" ¥"+I(a.price),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1},512),[[g,!G.value]]),n(" 辅助功能区 "),m(r(l,{class:"helper-section"},{default:d((()=>[r(l,{class:"helper-content"},{default:d((()=>[r(l,{class:"help-info",onClick:la},{default:d((()=>[r(t,{class:"help-text"},{default:d((()=>[_("线上估价太麻烦？")])),_:1}),r(l,{class:"help-badge"},{default:d((()=>[r(t,{class:"help-icon"},{default:d((()=>[_("?")])),_:1})])),_:1})])),_:1}),r(l,{class:"batch-btn",onClick:ta},{default:d((()=>[r(t,{class:"batch-text"},{default:d((()=>[_("批量下单")])),_:1})])),_:1})])),_:1})])),_:1},512),[[g,!G.value]]),n(" 自定义帮助弹窗 "),Q.value?(c(),o(l,{key:0,class:"custom-modal",onClick:sa},{default:d((()=>[r(l,{class:"modal-content",onClick:e[1]||(e[1]=h((()=>{}),["stop"]))},{default:d((()=>[r(l,{class:"modal-header"},{default:d((()=>[r(t,{class:"modal-title"},{default:d((()=>[_("批量下单")])),_:1})])),_:1}),r(l,{class:"modal-body"},{default:d((()=>[r(t,{class:"modal-text"},{default:d((()=>[_("您可批量创建回收订单进行上门取件回收，无需逐个添加商品，仅需填写数量。鉴定仓库收到货后会为您进行商品清点并且拍照进行报价。")])),_:1})])),_:1}),r(l,{class:"modal-footer"},{default:d((()=>[r(l,{class:"modal-btn",onClick:sa},{default:d((()=>[r(t,{class:"modal-btn-text"},{default:d((()=>[_("了解了")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):n("v-if",!0),n(" 全部品牌列表区 "),r(l,{class:"all-brands-section"},{default:d((()=>[r(l,{class:"section-header"},{default:d((()=>[r(t,{class:"section-title"},{default:d((()=>[_("全部品牌")])),_:1})])),_:1}),n(" 字母索引 "),r(l,{class:"alphabet-sidebar"},{default:d((()=>[(c(!0),u(v,null,f(p(X),(a=>(c(),o(l,{class:z(["alphabet-letter",{active:U.value===a}]),key:a,onClick:e=>(a=>{U.value=a,S({selector:`#letter-${a}`,duration:300})})(a)},{default:d((()=>[_(I(a),1)])),_:2},1032,["class","onClick"])))),128))])),_:1}),n(" 品牌列表 "),r(l,{class:"brands-container"},{default:d((()=>[(c(!0),u(v,null,f(p(W),(a=>(c(),o(l,{key:a.letter,id:`letter-${a.letter}`,class:"brand-group"},{default:d((()=>[r(l,{class:"group-title"},{default:d((()=>[_(I(a.letter),1)])),_:2},1024),(c(!0),u(v,null,f(a.brands,(a=>(c(),o(l,{class:"brand-row",key:a.id,onClick:e=>aa(a)},{default:d((()=>[r(l,{class:"brand-avatar"},{default:d((()=>[a.logo?(c(),o(y,{key:0,src:p($)(a.logo),class:"avatar-image",mode:"aspectFill"},null,8,["src"])):(c(),o(t,{key:1,class:"avatar-text"},{default:d((()=>[_(I(a.name.charAt(0)),1)])),_:2},1024))])),_:2},1024),r(t,{class:"brand-label"},{default:d((()=>[_(I(a.name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:2},1032,["id"])))),128))])),_:1})])),_:1})])),_:1})}}}),[["__scopeId","data-v-6987d5f1"]]);export{L as default};
