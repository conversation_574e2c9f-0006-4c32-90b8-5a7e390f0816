import{at as e,d as a,r as l,p as o,_ as t,a2 as s,M as c,o as d,c as n,w as u,g as r,b as i,e as m,t as v,x as g,y as p,F as f,z as _,$ as y,br as h,bN as b,aC as I,bO as k,be as x,Q as C,S as N,k as w,i as F,j as L,aD as D,A as P,n as S,O as U,aB as j}from"./index-3caf046d.js";import{_ as z}from"./u-icon.ba193921.js";import{c as R}from"./quote.9b84c391.js";import{_ as E}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const T=E(a({__name:"photo",setup(a){const E=l({from:"",brandId:"",brandName:"",brandLogo:"",productId:"",productName:"",productImage:"",categoryId:""}),T=l({id:"",name:"请选择品牌",image:"",status:"未选择品牌"}),O=l({id:"",name:"",image:"",category_id:""}),V=l([]),$=l([]),q=l({}),A=l([]),B=l(!1),M=l(new Set),Q=l(!1),G=l(""),H=o((()=>V.value.every((e=>q.value[e.id])))),J=()=>{A.value.length>=6?c({title:"最多只能上传6张瑕疵照片",icon:"none"}):Q.value?c({title:"正在上传中，请稍候",icon:"none"}):h({count:1,sizeType:["compressed"],sourceType:["camera","album"],success:e=>{if(e.tempFilePaths&&e.tempFilePaths.length>0){const a=e.tempFilePaths[0];Q.value=!0,b({title:"上传中...",mask:!0}),I({filePath:a,name:"file"}).then((e=>{if(1!==e.code)throw new Error(e.msg||"上传失败");A.value.push(e.data.url),c({title:"上传成功",icon:"success"})})).catch((e=>{console.error("上传瑕疵照片失败:",e),c({title:"上传失败，请重试",icon:"none"})})).finally((()=>{Q.value=!1,k()}))}},fail:e=>{console.error("选择瑕疵照片失败:",e),c({title:"选择图片失败",icon:"none"})}})},K=async()=>{var e,a,l,o,t;if(H.value){if(!B.value){B.value=!0;try{const s=$.value.filter((e=>e.selected)),d={category_id:parseInt(E.value.categoryId),brand_id:(null==(e=T.value)?void 0:e.id)||0,product_id:(null==(a=T.value)?void 0:a.product_id)||0,product_name:(null==(l=T.value)?void 0:l.name)||"",product_code:(null==(o=T.value)?void 0:o.code)||"",product_image:(null==(t=T.value)?void 0:t.image)||"",photos:q.value,defect_photos:A.value,accessories:s,note:G.value};console.log("提交评估数据:",d);const n=await R(d);1===n.code?(c({title:"评估提交成功",icon:"success"}),setTimeout((()=>{x({url:`/addon/yz_she/pages/order/success/quote-success?id=${n.data.order_id}`})}),1500)):c({title:n.msg||"提交失败",icon:"none"})}catch(s){console.error("提交评估失败:",s),c({title:"提交失败，请重试",icon:"none"})}finally{B.value=!1}}}else c({title:"请先上传所有必需的照片",icon:"none"})},W=async a=>{if(a)try{B.value=!0;const l=await function(a){return e.get("yz_she/category/config",{category_id:a})}(parseInt(a));if(l.data){V.value=l.data.photos||[];const e={};V.value.forEach((a=>{e[a.id]=""})),q.value=e,$.value=(l.data.accessories||[]).map((e=>({...e,selected:!1}))),console.log("分类配置加载成功:",l.data)}}catch(l){console.error("加载分类配置失败:",l),c({title:"配置加载失败",icon:"none"})}finally{B.value=!1}else console.warn("分类ID为空，无法加载配置")};t((()=>{const e=s(),a=e[e.length-1].options||{};console.log("拍照估价页面URL参数:",a);const l=uni.getStorageSync("photoEvaluateData");console.log("缓存的跳转数据:",l),l&&l.from?(E.value={from:l.from||"",brandId:l.brandId||"",brandName:l.brandName||"",brandLogo:l.brandLogo||"",productId:l.productId||"",productName:l.productName||"",productImage:l.productImage||"",categoryId:l.categoryId||""},console.log("使用缓存数据:",E.value),uni.removeStorageSync("photoEvaluateData")):(E.value={from:a.from||"",brandId:a.brandId||"",brandName:decodeURIComponent(a.brandName||""),brandLogo:decodeURIComponent(a.brandLogo||""),productId:a.productId||"",productName:decodeURIComponent(a.productName||""),productImage:decodeURIComponent(a.productImage||""),categoryId:a.categoryId||""},console.log("使用URL参数:",E.value)),"index"===E.value.from?E.value.brandId?X():(console.warn("从index跳转但缺少brandId参数"),c({title:"品牌信息获取失败",icon:"none"})):"detail"===E.value.from?E.value.productId?Y():(console.warn("从detail跳转但缺少productId参数"),c({title:"商品信息获取失败",icon:"none"})):(console.log("未识别的跳转来源或参数缺失"),c({title:"页面参数异常",icon:"none"})),!E.value.from&&E.value.categoryId&&(console.log("直接加载分类配置:",E.value.categoryId),W(E.value.categoryId))}));const X=()=>{console.log("设置品牌信息（来自index）:",E.value),T.value={id:E.value.brandId||"",name:E.value.brandName||"未知品牌",image:E.value.brandLogo||"",status:"已选择品牌"},console.log("品牌信息设置完成:",T.value),E.value.categoryId?(console.log("从缓存获取到分类ID:",E.value.categoryId),W(E.value.categoryId.toString())):console.warn("从index跳转但缺少分类ID")},Y=()=>{console.log("设置商品信息（来自detail）:",E.value),O.value={id:E.value.productId||"",name:E.value.productName||"未知商品",image:E.value.productImage||"",category_id:E.value.categoryId||""},T.value={id:E.value.brandId||"",name:E.value.brandName||"未知品牌",image:E.value.brandLogo||"",status:"已选择商品"},console.log("商品信息设置完成:",O.value),console.log("品牌信息设置完成:",T.value),E.value.categoryId?(console.log("从detail缓存获取到分类ID:",E.value.categoryId),W(E.value.categoryId.toString())):console.warn("从detail跳转但缺少分类ID")};return(e,a)=>{const l=C,o=N,t=w,s=F(L("u-icon"),z),x=D;return d(),n(t,{class:"photo-evaluate-page"},{default:u((()=>[r(" 品牌信息区域 "),i(t,{class:"brand-section"},{default:u((()=>[i(t,{class:"brand-info"},{default:u((()=>[i(t,{class:"brand-logo"},{default:u((()=>[T.value.image?(d(),n(l,{key:0,src:m(P)(T.value.image),class:"logo-image",mode:"aspectFit"},null,8,["src"])):(d(),n(t,{key:1,class:"brand-placeholder"},{default:u((()=>[i(o,{class:"placeholder-text"},{default:u((()=>[v("暂无图片")])),_:1})])),_:1}))])),_:1}),i(t,{class:"brand-details"},{default:u((()=>[i(o,{class:"brand-name"},{default:u((()=>[v(g(T.value.name),1)])),_:1}),i(o,{class:"brand-status"},{default:u((()=>[v(g(T.value.status),1)])),_:1}),O.value.name?(d(),n(o,{key:0,class:"product-name"},{default:u((()=>[v(g(O.value.name),1)])),_:1})):r("v-if",!0)])),_:1})])),_:1}),i(t,{class:"brand-bg"},{default:u((()=>[i(t,{class:"bg-circle"})])),_:1})])),_:1}),r(" 实物照片区域 "),i(t,{class:"photo-section"},{default:u((()=>[i(t,{class:"section-header"},{default:u((()=>[i(o,{class:"section-title"},{default:u((()=>[v("实物照片")])),_:1}),i(o,{class:"section-desc"},{default:u((()=>[v("请按照要求上传清晰照片")])),_:1})])),_:1}),r(" 加载状态 "),B.value?(d(),n(t,{key:0,class:"loading-state"},{default:u((()=>[i(o,{class:"loading-text"},{default:u((()=>[v("正在加载配置...")])),_:1})])),_:1})):(d(),p(f,{key:1},[r(" 照片上传网格 "),i(t,{class:"photo-grid"},{default:u((()=>[(d(!0),p(f,null,_(V.value,((e,a)=>(d(),n(t,{class:"photo-item",key:e.id,onClick:a=>{return l=e.id,void(M.value.has(l)?c({title:"正在上传中，请稍候",icon:"none"}):h({count:1,sizeType:["compressed"],sourceType:["camera","album"],success:e=>{if(e.tempFilePaths&&e.tempFilePaths.length>0){const a=e.tempFilePaths[0];M.value.add(l),b({title:"上传中...",mask:!0}),I({filePath:a,name:"file"}).then((e=>{if(1!==e.code)throw new Error(e.msg||"上传失败");q.value[l]=e.data.url,c({title:"上传成功",icon:"success"})})).catch((e=>{console.error("上传图片失败:",e),c({title:"上传失败，请重试",icon:"none"})})).finally((()=>{M.value.delete(l),k()}))}},fail:e=>{console.error("选择图片失败:",e),c({title:"选择图片失败",icon:"none"})}}));var l}},{default:u((()=>[i(t,{class:y(["photo-placeholder",{uploaded:q.value[e.id],uploading:M.value.has(e.id)}]),style:S({backgroundImage:e.background_image?`url(${m(P)(e.background_image)})`:""})},{default:u((()=>[q.value[e.id]?(d(),n(l,{key:0,src:m(P)(q.value[e.id]),class:"photo-image",mode:"aspectFill"},null,8,["src"])):M.value.has(e.id)?(d(),n(t,{key:1,class:"uploading-content"},{default:u((()=>[i(t,{class:"uploading-spinner"}),i(o,{class:"uploading-text"},{default:u((()=>[v("上传中...")])),_:1})])),_:1})):(d(),n(t,{key:2,class:"placeholder-content"},{default:u((()=>[r(" 移除拍照图标，只保留背景 ")])),_:1}))])),_:2},1032,["class","style"]),i(o,{class:"photo-label"},{default:u((()=>[v(g(e.photo_name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})],2112))])),_:1}),r(" 明显瑕疵区域 "),i(t,{class:"defect-section"},{default:u((()=>[i(t,{class:"section-header"},{default:u((()=>[i(o,{class:"section-title"},{default:u((()=>[v("明显瑕疵(最多6张)")])),_:1})])),_:1}),i(t,{class:"defect-photos"},{default:u((()=>[i(t,{class:"defect-item",onClick:J},{default:u((()=>[i(t,{class:y(["defect-placeholder",{uploading:Q.value}])},{default:u((()=>[Q.value?(d(),n(t,{key:0,class:"uploading-content"},{default:u((()=>[i(t,{class:"uploading-spinner"}),i(o,{class:"uploading-text"},{default:u((()=>[v("上传中...")])),_:1})])),_:1})):(d(),n(t,{key:1,class:"placeholder-content"},{default:u((()=>[r(" 移除加号图标，只保留背景 ")])),_:1}))])),_:1},8,["class"]),i(o,{class:"defect-label"},{default:u((()=>[v(g(Q.value?"上传中":"新增"),1)])),_:1})])),_:1}),(d(!0),p(f,null,_(A.value,((e,a)=>(d(),n(t,{class:"defect-item",key:a,onClick:e=>(e=>{const a=A.value.map((e=>P(e)));j({urls:a,current:e})})(a)},{default:u((()=>[i(t,{class:"defect-placeholder uploaded"},{default:u((()=>[i(l,{src:m(P)(e),class:"defect-image",mode:"aspectFill"},null,8,["src"]),i(t,{class:"delete-btn",onClick:U((e=>(e=>{A.value.splice(e,1)})(a)),["stop"])},{default:u((()=>[i(s,{name:"close",color:"#fff",size:"16"})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1}),r(" 其他配件区域 "),i(t,{class:"accessories-section"},{default:u((()=>[i(t,{class:"section-header"},{default:u((()=>[i(o,{class:"section-title"},{default:u((()=>[v("其他")])),_:1}),i(o,{class:"section-desc"},{default:u((()=>[v("请配置配件获得更高价")])),_:1})])),_:1}),r(" 加载状态 "),B.value?(d(),n(t,{key:0,class:"loading-state"},{default:u((()=>[i(o,{class:"loading-text"},{default:u((()=>[v("正在加载配置...")])),_:1})])),_:1})):$.value.length>0?(d(),p(f,{key:1},[r(" 配件网格 "),i(t,{class:"accessories-grid"},{default:u((()=>[(d(!0),p(f,null,_($.value,(e=>(d(),n(t,{class:y(["accessory-item",{selected:e.selected}]),key:e.id,onClick:a=>(e=>{e.selected=!e.selected})(e)},{default:u((()=>[i(o,{class:"accessory-name"},{default:u((()=>[v(g(e.accessory_name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})],2112)):(d(),p(f,{key:2},[r(" 空状态 "),i(t,{class:"empty-state"},{default:u((()=>[i(o,{class:"empty-text"},{default:u((()=>[v("暂无配件配置")])),_:1})])),_:1})],2112))])),_:1}),r(" 备注区域 "),i(t,{class:"note-section"},{default:u((()=>[i(t,{class:"section-header"},{default:u((()=>[i(o,{class:"section-title"},{default:u((()=>[v("备注")])),_:1})])),_:1}),i(x,{class:"note-textarea",modelValue:G.value,"onUpdate:modelValue":a[0]||(a[0]=e=>G.value=e),placeholder:"您可以填写其他明显瑕疵备注、使用情况、是否有包装等信息，帮助我们更好的估价。",maxlength:"200"},null,8,["modelValue"])])),_:1}),r(" 底部提交按钮 "),i(t,{class:"bottom-action"},{default:u((()=>[i(t,{class:y(["submit-button",{disabled:!m(H)}]),onClick:K},{default:u((()=>[i(o,{class:"submit-text"},{default:u((()=>[v("免费评估")])),_:1})])),_:1},8,["class"])])),_:1})])),_:1})}}}),[["__scopeId","data-v-58906bba"]]);export{T as default};
