import{d as e,r as a,p as s,J as l,M as t,N as c,o as d,c as u,w as o,g as i,b as n,t as r,x as _,e as f,y as v,z as p,F as m,$ as k,a_ as g,O as b,be as y,bU as x,a3 as h,S as C,k as w,i as $,j as I,al as S,A as z,Q as j}from"./index-3caf046d.js";import{_ as A}from"./u-icon.ba193921.js";import{h as D,i as U}from"./quote.9b84c391.js";import{e as q}from"./recycle_order.a252d983.js";import{_ as O}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const E=O(e({__name:"quote-order",setup(e){const O=a({}),E=a([]),F=a("pickup"),M=a(null),V=a("尽快上门"),B=a(""),J=a(""),N=a(!1),P=a(!1),L=a(0),R=a(-1),T=a(!1),H=a([]),Q=a([]),G=s((()=>{var e;return R.value>=0&&!(null==(e=Q.value[R.value])?void 0:e.disabled)})),K=["顺丰速运","圆通速递","中通快递","申通快递","韵达速递","百世快递","德邦快递","京东快递","邮政EMS","极兔速递"],W=s((()=>E.value.reduce(((e,a)=>e+parseFloat(a.quote_price||0)),0).toFixed(2)));l((e=>{if(console.log("批量发货页面参数:",e),O.value=e||{},e.orderIds){const a=JSON.parse(decodeURIComponent(e.orderIds));X(a)}ee()}));const X=async e=>{try{console.log("开始加载订单列表，订单IDs:",e);const a=await D(e);if(console.log("API响应:",a),1!==a.code)throw new Error(a.msg||"获取订单详情失败");E.value=a.data,console.log("订单列表加载成功:",E.value)}catch(a){console.error("加载订单列表失败:",a),t({title:"加载失败",icon:"none"})}},Y=e=>{F.value=e},Z=()=>{const e=`/addon/yz_she/pages/order/detail/quote-order?orderIds=${encodeURIComponent(JSON.stringify(E.value.map((e=>e.id))))}`;uni.setStorage({key:"selectAddressCallback",data:{back:e,isBatchOrder:!0}}),y({url:"/addon/yz_she/pages/address/index"})},ee=()=>{const e=new Date,a=[];for(let s=0;s<3;s++){const l=new Date(e);l.setDate(e.getDate()+s);let t="";0===s?t="今天":1===s?t="明天":2===s&&(t="后天"),a.push({date:l,label:t,value:`${l.getMonth()+1}月${l.getDate()}日`})}H.value=a,ae(0)},ae=e=>{H.value[e];const a=new Date,s=[{label:"尽快上门",value:"尽快上门",isUrgent:!0},{label:"09:00-12:00",value:"09:00-12:00",desc:"上午"},{label:"14:00-18:00",value:"14:00-18:00",desc:"下午"},{label:"19:00-21:00",value:"19:00-21:00",desc:"晚上"}];if(0===e){const e=a.getHours();s.forEach((a=>{("09:00-12:00"===a.value&&e>=12||"14:00-18:00"===a.value&&e>=18||"19:00-21:00"===a.value&&e>=21)&&(a.disabled=!0)}))}Q.value=s},se=()=>{M.value?(P.value=!0,ee()):t({title:"请先选择地址",icon:"none"})},le=()=>{P.value=!1,L.value=0,R.value=-1},te=()=>{if(!G.value)return;const e=H.value[L.value],a=Q.value[R.value];a.isUrgent?V.value="尽快上门":V.value=`${e.value} ${a.value}`,le(),t({title:`已选择${V.value}`,icon:"success"})},ce=()=>{N.value=!0},de=()=>{N.value=!1},ue=()=>{x({data:"四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递",success:()=>{t({title:"地址已复制",icon:"success"})}})},oe=async()=>{var e;if(!T.value)try{if("pickup"===F.value){if(!M.value)return void t({title:"请选择取件地址",icon:"none"})}else if("self"===F.value){if(!B.value)return void t({title:"请选择快递公司",icon:"none"});if(!J.value.trim())return void t({title:"请输入快递单号",icon:"none"})}T.value=!0;const a={order_ids:E.value.map((e=>e.id)),delivery_type:"pickup"===F.value?1:2,pickup_address_id:(null==(e=M.value)?void 0:e.id)||null,pickup_time:V.value,express_company:B.value,express_number:J.value};console.log("批量发货数据:",a);const s=await U(a);if(console.log("批量发货API响应:",s),1!==s.code)throw new Error(s.msg||"批量发货失败");const l={quote_order_ids:a.order_ids,delivery_type:a.delivery_type,pickup_address_id:a.pickup_address_id,pickup_time:a.pickup_time,express_company:a.express_company,express_number:a.express_number,admin_note:a.ship_note};console.log("批量创建回收订单数据:",l);const c=await q(l);if(console.log("批量创建回收订单API响应:",c),T.value=!1,1===c.code){const e=s.data,a=c.data,l=`批量发货成功！已发货：${e.success_count}个订单，已创建：${a.success_count}个回收订单`;t({title:l,icon:"success",duration:3e3}),setTimeout((()=>{h()}),3e3)}else t({title:`发货成功，但回收订单创建失败：${c.msg}`,icon:"none",duration:3e3}),setTimeout((()=>{h()}),3e3)}catch(a){T.value=!1,console.error("批量发货失败:",a),t({title:"发货失败，请重试",icon:"none"})}};return c((()=>{const e=uni.getStorageSync("selectAddressCallback");e&&e.address_id&&e.isBatchOrder&&(e.address_info?(M.value=e.address_info,t({title:"地址选择成功",icon:"success"})):M.value=null,uni.removeStorage({key:"selectAddressCallback"}))})),(e,a)=>{const s=C,l=w,c=j,y=$(I("u-icon"),A),x=S;return d(),u(l,{class:"batch-order-page"},{default:o((()=>[i(" 主要内容 "),n(l,{class:"main-content"},{default:o((()=>[i(" 批量订单概览 "),n(l,{class:"batch-overview"},{default:o((()=>[n(l,{class:"overview-header"},{default:o((()=>[n(s,{class:"overview-title"},{default:o((()=>[r("批量发货订单")])),_:1}),n(s,{class:"overview-count"},{default:o((()=>[r("共"+_(E.value.length)+"个订单",1)])),_:1})])),_:1}),n(l,{class:"overview-stats"},{default:o((()=>[n(l,{class:"stat-item"},{default:o((()=>[n(s,{class:"stat-label"},{default:o((()=>[r("总金额")])),_:1}),n(s,{class:"stat-value"},{default:o((()=>[r("¥"+_(f(W)),1)])),_:1})])),_:1}),n(l,{class:"stat-divider"}),n(l,{class:"stat-item"},{default:o((()=>[n(s,{class:"stat-label"},{default:o((()=>[r("预计收益")])),_:1}),n(s,{class:"stat-value"},{default:o((()=>[r("¥"+_(f(W)),1)])),_:1})])),_:1})])),_:1})])),_:1}),i(" 订单列表 "),n(l,{class:"order-list-section"},{default:o((()=>[n(l,{class:"section-header"},{default:o((()=>[n(s,{class:"section-title"},{default:o((()=>[r("发货订单列表")])),_:1})])),_:1}),n(l,{class:"order-list"},{default:o((()=>[(d(!0),v(m,null,p(E.value,((e,a)=>(d(),u(l,{key:e.id,class:"order-item"},{default:o((()=>[n(l,{class:"order-content"},{default:o((()=>[n(c,{class:"product-image",src:f(z)(e.product_image),mode:"aspectFill"},null,8,["src"]),n(l,{class:"product-info"},{default:o((()=>[n(s,{class:"product-name"},{default:o((()=>[r(_(e.product_name),1)])),_:2},1024),n(s,{class:"order-no"},{default:o((()=>[r("订单号："+_(e.order_no),1)])),_:2},1024),n(l,{class:"price-info"},{default:o((()=>[n(s,{class:"price-label"},{default:o((()=>[r("预估价格")])),_:1}),n(s,{class:"price-value"},{default:o((()=>[r("¥"+_(e.quote_price),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1}),i(" 配送方式选择 "),n(l,{class:"shipping-section"},{default:o((()=>[i(" 配送方式 "),n(l,{class:"delivery-section"},{default:o((()=>[n(l,{class:"delivery-options"},{default:o((()=>[n(l,{class:k(["delivery-option",{active:"pickup"===F.value}]),onClick:a[0]||(a[0]=e=>Y("pickup"))},{default:o((()=>[n(l,{class:"option-content"},{default:o((()=>[n(s,{class:"option-name"},{default:o((()=>[r("快递上门")])),_:1}),n(l,{class:"option-tag"},{default:o((()=>[n(s,{class:"tag-text"},{default:o((()=>[r("免费")])),_:1})])),_:1})])),_:1})])),_:1},8,["class"]),n(l,{class:k(["delivery-option",{active:"self"===F.value}]),onClick:a[1]||(a[1]=e=>Y("self"))},{default:o((()=>[n(s,{class:"option-name"},{default:o((()=>[r("自行寄出")])),_:1})])),_:1},8,["class"])])),_:1}),i(" 快递上门内容 "),"pickup"===F.value?(d(),u(l,{key:0,class:"pickup-content"},{default:o((()=>[i(" 地址选择 "),n(l,{class:"address-item",onClick:Z},{default:o((()=>[n(l,{class:"address-icon"},{default:o((()=>[n(y,{name:"map",color:"#333",size:"18"})])),_:1}),n(l,{class:"address-content"},{default:o((()=>[M.value?(d(),u(l,{key:1,class:"selected-address"},{default:o((()=>[n(s,{class:"address-name"},{default:o((()=>[r(_(M.value.name)+" "+_(M.value.mobile),1)])),_:1}),n(s,{class:"address-detail"},{default:o((()=>[r(_(M.value.full_address),1)])),_:1})])),_:1})):(d(),u(s,{key:0,class:"address-text"},{default:o((()=>[r("请选择取件地址")])),_:1}))])),_:1}),n(l,{class:"divider-line"}),n(s,{class:"address-action"},{default:o((()=>[r("地址簿")])),_:1})])),_:1}),i(" 预约时间 "),n(l,{class:k(["time-item",{disabled:!M.value}]),onClick:se},{default:o((()=>[n(l,{class:"time-icon"},{default:o((()=>[(d(),v("svg",{viewBox:"0 0 1024 1024",width:"32",height:"32"},[g("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m176.5 585.7l-28.6 39c-4.4 6.1-11.9 9.3-19.4 9.3-4.7 0-9.5-1.4-13.6-4.3L512 609.7l-114.9 84c-9.9 7.2-23.8 5.1-31-4.9l-28.6-39c-7.2-9.9-5.1-23.8 4.9-31L457 515.3V264c0-11 9-20 20-20h70c11 0 20 9 20 20v251.3l114.6 103.5c9.9 7.1 12 21 4.9 30.9z",fill:M.value?"#333":"#ccc"},null,8,["fill"])]))])),_:1}),n(s,{class:"time-text"},{default:o((()=>[r("期望上门时间")])),_:1}),n(s,{class:k(["time-action",{disabled:!M.value}])},{default:o((()=>[r(_(M.value?V.value||"尽快上门":"请先选择地址")+" > ",1)])),_:1},8,["class"])])),_:1},8,["class"])])),_:1})):i("v-if",!0),i(" 自行寄出内容 "),"self"===F.value?(d(),u(l,{key:1,class:"self-content"},{default:o((()=>[i(" 收货地址 "),n(l,{class:"address-item"},{default:o((()=>[n(l,{class:"address-icon orange-bg"},{default:o((()=>[n(s,{class:"address-text-icon"},{default:o((()=>[r("收")])),_:1})])),_:1}),n(l,{class:"address-info"},{default:o((()=>[n(s,{class:"address-name"},{default:o((()=>[r("放心星仓库 13060000687")])),_:1}),n(s,{class:"address-detail"},{default:o((()=>[r("四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递")])),_:1})])),_:1}),n(s,{class:"copy-btn",onClick:ue},{default:o((()=>[r("复制")])),_:1})])),_:1}),i(" 快递公司 "),n(l,{class:"express-item",onClick:ce},{default:o((()=>[n(l,{class:"express-icon"},{default:o((()=>[n(y,{name:"car",color:"#333",size:"32"})])),_:1}),n(s,{class:"express-text"},{default:o((()=>[r("快递公司")])),_:1}),n(s,{class:"express-action"},{default:o((()=>[r(_(B.value||"请选择快递公司")+" >",1)])),_:1})])),_:1}),i(" 快递单号 "),n(l,{class:"tracking-item"},{default:o((()=>[n(l,{class:"tracking-icon"},{default:o((()=>[n(y,{name:"order",color:"#333",size:"32"})])),_:1}),n(s,{class:"tracking-text"},{default:o((()=>[r("快递单号")])),_:1}),n(x,{class:"tracking-input",modelValue:J.value,"onUpdate:modelValue":a[2]||(a[2]=e=>J.value=e),placeholder:"请输入快递单号",maxlength:"30"},null,8,["modelValue"])])),_:1})])),_:1})):i("v-if",!0)])),_:1})])),_:1})])),_:1}),i(" 底部操作按钮 "),n(l,{class:"bottom-actions"},{default:o((()=>[n(l,{class:k(["batch-submit-button",{submitting:T.value}]),onClick:oe},{default:o((()=>[n(l,{class:"button-content"},{default:o((()=>[T.value?(d(),u(l,{key:1,class:"loading-content"},{default:o((()=>[n(l,{class:"loading-spinner"}),n(s,{class:"loading-text"},{default:o((()=>[r("提交中...")])),_:1})])),_:1})):(d(),u(l,{key:0,class:"normal-content"},{default:o((()=>[n(s,{class:"button-text"},{default:o((()=>[r("确认批量发货")])),_:1})])),_:1}))])),_:1})])),_:1},8,["class"])])),_:1}),i(" 快递公司选择弹窗 "),N.value?(d(),u(l,{key:0,class:"express-modal",onClick:de},{default:o((()=>[n(l,{class:"modal-content",onClick:a[3]||(a[3]=b((()=>{}),["stop"]))},{default:o((()=>[n(l,{class:"modal-header"},{default:o((()=>[n(s,{class:"modal-title"},{default:o((()=>[r("选择快递公司")])),_:1}),n(l,{class:"close-btn",onClick:de},{default:o((()=>[r("×")])),_:1})])),_:1}),n(l,{class:"express-list"},{default:o((()=>[(d(),v(m,null,p(K,(e=>n(l,{class:"express-option",key:e,onClick:a=>(e=>{B.value=e,N.value=!1,t({title:`已选择${e}`,icon:"success"})})(e)},{default:o((()=>[n(s,{class:"express-name"},{default:o((()=>[r(_(e),1)])),_:2},1024),B.value===e?(d(),u(l,{key:0,class:"express-check"},{default:o((()=>[r("✓")])),_:1})):i("v-if",!0)])),_:2},1032,["onClick"]))),64))])),_:1})])),_:1})])),_:1})):i("v-if",!0),i(" 时间选择弹窗 "),P.value?(d(),u(l,{key:1,class:"time-modal",onClick:le},{default:o((()=>[n(l,{class:"time-modal-content",onClick:a[4]||(a[4]=b((()=>{}),["stop"]))},{default:o((()=>[n(l,{class:"time-header"},{default:o((()=>[n(s,{class:"time-title"},{default:o((()=>[r("选择上门时间")])),_:1}),n(l,{class:"close-btn",onClick:le},{default:o((()=>[r("×")])),_:1})])),_:1}),i(" 时间选择内容 "),n(l,{class:"time-picker-container"},{default:o((()=>[i(" 左侧日期列表 "),n(l,{class:"date-list"},{default:o((()=>[(d(!0),v(m,null,p(H.value,((e,a)=>(d(),u(l,{class:k(["date-item",{active:L.value===a}]),key:a,onClick:e=>(e=>{L.value=e,R.value=-1,ae(e)})(a)},{default:o((()=>[n(s,{class:"date-text"},{default:o((()=>[r(_(e.label),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),i(" 右侧时间段列表 "),n(l,{class:"time-list"},{default:o((()=>[(d(!0),v(m,null,p(Q.value,((e,a)=>(d(),u(l,{class:k(["time-item",{active:R.value===a,disabled:e.disabled,urgent:e.isUrgent}]),key:a,onClick:e=>(e=>{var a;(null==(a=Q.value[e])?void 0:a.disabled)||(R.value=e)})(a)},{default:o((()=>[n(s,{class:"time-text"},{default:o((()=>[r(_(e.label),1)])),_:2},1024),e.desc?(d(),u(s,{key:0,class:"time-desc"},{default:o((()=>[r(_(e.desc),1)])),_:2},1024)):i("v-if",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),i(" 确认按钮 "),n(l,{class:"time-confirm"},{default:o((()=>[n(l,{class:k(["confirm-btn",{disabled:!f(G)}]),onClick:te},{default:o((()=>[n(s,{class:"confirm-text"},{default:o((()=>[r("确认")])),_:1})])),_:1},8,["class"])])),_:1})])),_:1})])),_:1})):i("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-413af878"]]);export{E as default};
