import{d as e,r as t,p as l,s as o,o as r,c as a,w as s,g as i,$ as d,n,y as u,F as p,z as c,e as x,b as g,a7 as y,k as m,aE as f,q as h,_ as v,as as _,av as b,aw as w,i as k,j as S,au as R,A as C,t as B,x as F,a as G,Q as j,S as W}from"./index-3caf046d.js";import{_ as T}from"./u--image.eb573bce.js";import{_ as E}from"./_plugin-vue_export-helper.1b428a4d.js";import{a as $}from"./goods.6a81cb49.js";import{u as A}from"./useGoods.9c8f1c51.js";const H=E(e({__name:"x-skeleton",props:{type:{type:String,default:"",required:!0},loading:{type:Boolean,default:!0},animate:{type:Boolean,default:!0},animateTime:{type:Number,default:1.8},fadeOut:{type:Boolean,default:!0},fadeOutTime:{type:Number,default:.5},bgColor:{type:String,default:""},highlightBgColor:{type:String,default:""},config:{type:Object,default:()=>({})}},setup(e){const f=e,h=t(f.config||{}),v=t(f.loading),_=t(!1);t(0);const b=l((()=>{var e;return new Array(Number((null==(e=h.value)?void 0:e.gridRows)||[]))})),w=l((()=>{var e;return new Array(Number((null==(e=h.value)?void 0:e.gridColumns)||[]))})),k=l((()=>{var e;if(!(null==(e=h.value)?void 0:e.textShow))return[];/%$/.test(h.value.textHeight)&&console.error("x-skeleton: textHeight参数不支持百分比单位");const t=[];for(let l=0;l<h.value.textRows;l++){const{gridRows:e,textWidth:o,textHeight:r}=h.value;let a={},s=R(o)?o[l]||(l===e-1?"70%":"100%"):l===e-1?"70%":o,i=R(r)?r[l]||"30rpx":r;/%$/.test(s)?a.width=s:a.width=C(s),a.height=C(i),t.push(a)}return t})),S=l((()=>["animateTime","fadeOutTime","bgColor","highlightBgColor"].map((e=>e.indexOf("Time")>-1?`--${e}:${f[e]}s`:`--${e}:${f[e]}`)).join(";")));o((()=>f.loading),(e=>{e?v.value=!0:f.fadeOut?(_.value=!0,setTimeout((()=>{v.value=!1,_.value=!1}),1e3*f.fadeOutTime)):v.value=!1}),{immediate:!0}),o((()=>f.type),(e=>{var t;h.value="banner"===e?(t=f.config,{padding:"20rpx",gridRows:1,gridColumns:1,gridRowsGap:"40rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"center",headShow:!0,headWidth:"100%",headHeight:"300rpx",headBorderRadius:"20rpx",textShow:!1,textRows:3,textRowsGap:"20rpx",textWidth:"100%",textHeight:"30rpx",textBorderRadius:"6rpx",...t}):"info"===e?function(e){return{padding:"20rpx",gridRows:1,gridColumns:1,gridRowsGap:"50rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"flex-start",headShow:!0,headWidth:"100rpx",headHeight:"100rpx",headBorderRadius:"50%",textShow:!0,textRows:4,textRowsGap:"30rpx",textWidth:["50%","100%","100%","80%"],textHeight:["40rpx","24rpx","24rpx","24rpx"],textBorderRadius:"6rpx",...e}}(f.config):"text"===e?function(e){return{padding:"20rpx",gridRows:1,gridColumns:1,gridRowsGap:"50rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"flex-start",headShow:!1,headWidth:"100rpx",headHeight:"100rpx",headBorderRadius:"50%",textShow:!0,textRows:4,textRowsGap:"30rpx",textWidth:["50%","100%","100%","80%"],textHeight:"30rpx",textBorderRadius:"6rpx",...e}}(f.config):"menu"===e?function(e){return{padding:"20rpx",gridRows:2,gridColumns:5,gridRowsGap:"40rpx",gridColumnsGap:"40rpx",itemDirection:"column",itemGap:"16rpx",itemAlign:"center",headShow:!0,headWidth:"100rpx",headHeight:"100rpx",headBorderRadius:"50%",textShow:!0,textRows:1,textRowsGap:"0rpx",textWidth:"100%",textHeight:"24rpx",textBorderRadius:"6rpx",...e}}(f.config):"list"===e?function(e){return{padding:"20rpx",gridRows:2,gridColumns:1,gridRowsGap:"50rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"flex-start",headShow:!0,headWidth:"200rpx",headHeight:"200rpx",headBorderRadius:"16rpx",textShow:!0,textRows:4,textRowsGap:"30rpx",textWidth:["50%","100%","100%","80%"],textHeight:["38rpx","24rpx","24rpx","24rpx"],textBorderRadius:"6rpx",...e}}(f.config):"waterfall"===e?function(e){return{padding:"20rpx",gridRows:2,gridColumns:2,gridRowsGap:"40rpx",gridColumnsGap:"24rpx",itemDirection:"column",itemGap:"16rpx",itemAlign:"center",headShow:!0,headWidth:"100%",headHeight:"400rpx",headBorderRadius:"12rpx",textShow:!0,textRows:3,textRowsGap:"12rpx",textWidth:["40%","85%","60%"],textHeight:["30rpx","20rpx","20rpx"],textBorderRadius:"6rpx",...e}}(f.config):f.config||{}}),{immediate:!0});const R=e=>"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e),C=(e="auto",t="px")=>(e=String(e),/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)?`${e}${t}`:e);return(t,l)=>{const o=m;return r(),a(o,{class:"x-skeleton",style:n(x(S))},{default:s((()=>[i(" 骨架屏 "),v.value?(r(),a(o,{key:0,class:d(["x-skeleton__wrapper",[_.value&&"fade-out"]]),style:n({padding:h.value.padding})},{default:s((()=>[(r(!0),u(p,null,c(x(b),((t,l)=>(r(),a(o,{key:l,class:"x-skeleton__wrapper__rows",style:n({marginBottom:l<x(b).length-1?h.value.gridRowsGap:0})},{default:s((()=>[(r(!0),u(p,null,c(x(w),((t,l)=>(r(),a(o,{key:l,class:"x-skeleton__wrapper__columns",style:n({flexDirection:h.value.itemDirection,alignItems:h.value.itemAlign,marginRight:l<x(w).length-1?h.value.gridColumnsGap:0})},{default:s((()=>[h.value.headShow?(r(),a(o,{key:0,class:d(["x-skeleton__wrapper__head",[e.animate&&"animate"]]),style:n({width:h.value.headWidth,height:h.value.headHeight,borderRadius:h.value.headBorderRadius,marginRight:"row"==h.value.itemDirection&&h.value.textShow?h.value.itemGap:0,marginBottom:"column"==h.value.itemDirection&&h.value.textShow?h.value.itemGap:0})},null,8,["class","style"])):i("v-if",!0),h.value.textShow?(r(),a(o,{key:1,class:"x-skeleton__wrapper__text"},{default:s((()=>[(r(!0),u(p,null,c(x(k),((t,l)=>(r(),a(o,{key:l,class:d(["x-skeleton__wrapper__text__row",[e.animate&&"animate"]]),style:n({width:t.width,height:t.height,borderRadius:h.value.textBorderRadius,marginBottom:l<x(k).length-1?h.value.textRowsGap:0})},null,8,["class","style"])))),128))])),_:1})):i("v-if",!0)])),_:2},1032,["style"])))),128))])),_:2},1032,["style"])))),128))])),_:1},8,["class","style"])):(r(),u(p,{key:1},[i(" 插槽 "),g(o,null,{default:s((()=>[y(t.$slots,"default",{},void 0,!0)])),_:3})],2112))])),_:3},8,["style"])}}}),[["__scopeId","data-v-507980c7"]]),N=E(e({__name:"index",props:["component","index","value"],emits:["loadingFn"],setup(e,{emit:y}){const E=e,N=A(),D=f(),P=h({type:"",loading:"decorate"!=D.mode,config:{}}),O=t([]),I=l((()=>E.value?E.value:"decorate"==D.mode?D.value[E.index]:E.component)),U=l((()=>{var e="";return e+="position:relative;",I.value.componentStartBgColor&&I.value.componentEndBgColor?e+=`background:linear-gradient(${I.value.componentGradientAngle},${I.value.componentStartBgColor},${I.value.componentEndBgColor});`:e+="background-color:"+(I.value.componentStartBgColor||I.value.componentEndBgColor)+";",I.value.componentBgUrl&&(e+=`background-image:url('${C(I.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),I.value.topRounded&&(e+="border-top-left-radius:"+2*I.value.topRounded+"rpx;"),I.value.topRounded&&(e+="border-top-right-radius:"+2*I.value.topRounded+"rpx;"),I.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*I.value.bottomRounded+"rpx;"),I.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*I.value.bottomRounded+"rpx;"),e})),z=l((()=>{var e={val:"",style:""};return I.value.imgElementRounded&&(e.val=2*I.value.imgElementRounded+"rpx",e.style+="border-radius:"+2*I.value.imgElementRounded+"rpx;"),e})),V=l((()=>{var e="";return I.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${I.value.componentBgAlpha/10});`,e+=`height:${Y.value}px;`,I.value.topRounded&&(e+="border-top-left-radius:"+2*I.value.topRounded+"rpx;"),I.value.topRounded&&(e+="border-top-right-radius:"+2*I.value.topRounded+"rpx;"),I.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*I.value.bottomRounded+"rpx;"),I.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*I.value.bottomRounded+"rpx;")),e})),q=l((()=>{var e="";return I.value.elementBgColor&&(e+="background-color:"+I.value.elementBgColor+";"),I.value.topElementRounded&&(e+="border-top-left-radius:"+2*I.value.topElementRounded+"rpx;"),I.value.topElementRounded&&(e+="border-top-right-radius:"+2*I.value.topElementRounded+"rpx;"),I.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*I.value.bottomElementRounded+"rpx;"),I.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*I.value.bottomElementRounded+"rpx;"),"style-2"==I.value.style&&(I.value.margin&&I.value.margin.both?e+="width: calc((100vw - "+4*I.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );"),e})),L=l((()=>{var e="";return"button"==I.value.btnStyle.style&&I.value.btnStyle.aroundRadius&&(e+="border-radius:"+2*I.value.btnStyle.aroundRadius+"rpx;"),I.value.btnStyle.startBgColor&&I.value.btnStyle.endBgColor?e+=`background:linear-gradient(${I.value.btnStyle.startBgColor},${I.value.btnStyle.endBgColor});`:e+="background-color:"+(I.value.btnStyle.startBgColor||I.value.btnStyle.endBgColor)+";",I.value.btnStyle.textColor&&(e+="color:"+I.value.btnStyle.textColor+";"),"button"==I.value.btnStyle.style&&I.value.btnStyle.fontWeight&&(e+="font-weight: bold;"),e})),Q=l((()=>{var e="";return I.value.margin&&I.value.margin.both?e+="calc((100vw - "+4*I.value.margin.both+"rpx - 20rpx) / 2)":e+="calc((100vw - 20rpx) / 2 )",e})),J=l((()=>{var e="";return e+="padding:0 20rpx;",I.value.margin&&I.value.margin.both?e+="width: calc(100vw - "+(4*I.value.margin.both+40)+"rpx);":e+="box-sizing: border-box; width: 100vw;",e})),K=t(""),M=()=>{K.value="margin-right:14rpx;"},X=w(),Y=t(0);v((()=>{Z(),"decorate"==D.mode?o((()=>I.value),((e,t)=>{e&&"GoodsList"==e.componentName&&_((()=>{b().in(X).select(".diy-shop-goods-list").boundingClientRect((e=>{e&&(Y.value=e.height)})).exec(),"style-3"==I.value.style&&M()}))})):o((()=>I.value),((e,t)=>{Z()}),{deep:!0})}));const Z=()=>{if("decorate"==D.mode){let e={goods_cover_thumb_mid:"",goods_name:"商品名称",sale_num:"100",unit:"件",goodsSku:{show_price:100}};O.value.push(e),O.value.push(e),_((()=>{"style-3"==I.value.style&&M()}))}else"style-1"==I.value.style?(P.type="list",P.type="list",P.config={textRows:2}):"style-2"==I.value.style?(P.type="waterfall",P.config={headHeight:"320rpx",gridRows:1,textRows:2,textWidth:["100%","80%"]}):"style-3"==I.value.style&&(P.type="waterfall",P.config={gridRows:1,gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]}),(()=>{let e={num:"all"==I.value.source||"category"==I.value.source?I.value.num:"",goods_ids:"custom"==I.value.source?I.value.goods_ids:"",goods_category:"category"==I.value.source?I.value.goods_category:"",order:I.value.sortWay};$(e).then((e=>{O.value=e.data,P.loading=!1,y("loadingFn",e.data),I.value.componentBgUrl&&setTimeout((()=>{b().in(X).select(".diy-shop-goods-list").boundingClientRect((e=>{e&&(Y.value=e.height)})).exec()}),1e3),_((()=>{setTimeout((()=>{"style-3"==I.value.style&&M()}),500)}))}))})()},ee=e=>{G({url:"/addon/shop/pages/goods/detail",param:{goods_id:e.goods_id}})};return(e,t)=>{const l=m,o=j,y=k(S("u--image"),T),f=W,h=R,v=k(S("x-skeleton"),H);return r(),a(v,{type:P.type,loading:P.loading,config:P.config},{default:s((()=>[g(l,{style:n(x(U)),class:"overflow-hidden"},{default:s((()=>[g(l,{style:n(x(V))},null,8,["style"]),g(l,{class:d({"diy-shop-goods-list relative flex flex-wrap justify-between":"style-2"!=x(I).style,"biserial-goods-list":"style-2"==x(I).style})},{default:s((()=>["style-1"==x(I).style?(r(!0),u(p,{key:0},c(O.value,((e,t)=>(r(),a(l,{class:d(["bg-white w-full flex p-[20rpx] overflow-hidden",{"mt-[20rpx]":t>0}]),style:n(x(q)),key:e.goods_id,onClick:t=>ee(e)},{default:s((()=>[g(y,{radius:x(z).val,width:"200rpx",height:"200rpx",src:x(C)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:s((()=>[g(o,{class:"w-[200rpx] h-[200rpx] overflow-hidden",style:n(x(z).style),src:x(C)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"]),g(l,{class:"flex-1 flex flex-col ml-[20rpx] py-[6rpx] relative"},{default:s((()=>[x(I).goodsNameStyle.control?(r(),a(l,{key:0,class:"text-[28rpx] leading-[40rpx] text-[#303133] multi-hidden mb-[10rpx]",style:n({color:x(I).goodsNameStyle.color,fontWeight:x(I).goodsNameStyle.fontWeight})},{default:s((()=>[e.goods_brand?(r(),a(l,{key:0,class:"brand-tag",style:n(x(N).baseTagStyle(e.goods_brand))},{default:s((()=>[B(F(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),B(" "+F(e.goods_name),1)])),_:2},1032,["style"])):i("v-if",!0),e.goods_label_name&&e.goods_label_name.length&&x(I).labelStyle.control?(r(),a(l,{key:1,class:"flex flex-wrap mb-[10rpx]"},{default:s((()=>[(r(!0),u(p,null,c(e.goods_label_name,((e,t)=>(r(),u(p,null,["icon"==e.style_type&&e.icon?(r(),a(o,{key:0,class:"img-tag",src:x(C)(e.icon),mode:"heightFix",onError:t=>x(N).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),a(l,{key:1,class:"base-tag",style:n(x(N).baseTagStyle(e))},{default:s((()=>[B(F(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),g(l,{class:"mt-auto flex justify-between items-center"},{default:s((()=>[g(l,{class:"flex flex-col"},{default:s((()=>[x(I).priceStyle.control?(r(),a(l,{key:0,class:"flex items-baseline leading-[1]"},{default:s((()=>[g(l,{class:"font-bold text-[var(--price-text-color)] price-font block truncate max-w-[350rpx]",style:n({color:x(I).priceStyle.color})},{default:s((()=>[g(f,{class:"text-[24rpx] font-500 mr-[4rpx]"},{default:s((()=>[B("￥")])),_:1}),g(f,{class:"text-[40rpx] font-500"},{default:s((()=>[B(F(parseFloat(x(N).goodsPrice(e)).toFixed(2)),1)])),_:2},1024)])),_:2},1032,["style"]),"member_price"==x(N).priceType(e)?(r(),a(o,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:x(C)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==x(N).priceType(e)?(r(),a(o,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:x(C)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==x(N).priceType(e)?(r(),a(o,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:x(C)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0)])),_:2},1024)):i("v-if",!0),x(I).saleStyle.control?(r(),a(f,{key:1,class:"mt-[8rpx] text-[22rpx] text-[var(--text-color-light9)]",style:n({color:x(I).saleStyle.color})},{default:s((()=>[B(" 已售"+F(e.sale_num)+F(e.unit||"件"),1)])),_:2},1032,["style"])):i("v-if",!0)])),_:2},1024),x(I).btnStyle.control?(r(),a(l,{key:0,class:"absolute right-[0] bottom-[0]"},{default:s((()=>["button"==x(I).btnStyle.style?(r(),a(l,{key:0,style:n(x(L)),class:"px-[18rpx] min-w-[100rpx] box-border h-[48rpx] flex items-center justify-center"},{default:s((()=>[g(f,{class:"text-[20rpx]"},{default:s((()=>[B(F(x(I).btnStyle.text),1)])),_:1})])),_:1},8,["style"])):(r(),a(l,{key:1,style:n(x(L)),class:"w-[50rpx] h-[50rpx] rounded-[50%] flex items-center justify-center"},{default:s((()=>[g(f,{class:d(["nc-iconfont","text-[30rpx]",x(I).btnStyle.style])},null,8,["class"])])),_:1},8,["style"]))])),_:1})):i("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128)):i("v-if",!0),"style-2"==x(I).style?(r(),u(p,{key:1},[g(l,null,{default:s((()=>[(r(!0),u(p,null,c(O.value,((e,t)=>(r(),u(p,null,[t%2==0?(r(),a(l,{key:0,class:d(["flex flex-col bg-[#fff] box-border overflow-hidden",{"mt-[24rpx]":t>1}]),style:n(x(q)),onClick:t=>ee(e)},{default:s((()=>[g(y,{radius:x(z).val,width:x(Q),height:x(Q),src:x(C)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:s((()=>[g(o,{style:n({width:x(Q),height:x(Q),"border-radius":x(z).val}),src:x(C)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","width","height","src"]),g(l,{class:"relative min-h-[44rpx] px-[16rpx] flex-1 pt-[16rpx] pb-[20rpx] flex flex-col justify-between"},{default:s((()=>[x(I).goodsNameStyle.control?(r(),a(l,{key:0,class:"text-[#303133] leading-[40rpx] text-[28rpx] multi-hidden",style:n({color:x(I).goodsNameStyle.color,fontWeight:x(I).goodsNameStyle.fontWeight})},{default:s((()=>[e.goods_brand?(r(),a(l,{key:0,class:"brand-tag",style:n(x(N).baseTagStyle(e.goods_brand))},{default:s((()=>[B(F(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),B(" "+F(e.goods_name),1)])),_:2},1032,["style"])):i("v-if",!0),e.goods_label_name&&e.goods_label_name.length&&x(I).labelStyle.control?(r(),a(l,{key:1,class:"flex flex-wrap"},{default:s((()=>[(r(!0),u(p,null,c(e.goods_label_name,((e,t)=>(r(),u(p,null,["icon"==e.style_type&&e.icon?(r(),a(o,{key:0,class:"img-tag",src:x(C)(e.icon),mode:"heightFix",onError:t=>x(N).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),a(l,{key:1,class:"base-tag",style:n(x(N).baseTagStyle(e))},{default:s((()=>[B(F(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),g(l,{class:"flex justify-between flex-wrap items-center mt-[20rpx]"},{default:s((()=>[g(l,{class:"flex flex-col"},{default:s((()=>[x(I).priceStyle.control?(r(),a(l,{key:0,class:"flex items-baseline leading-[1]"},{default:s((()=>[g(l,{class:"text-[var(--price-text-color)] price-font block truncate max-w-[270rpx]",style:n({color:x(I).priceStyle.color})},{default:s((()=>[g(f,{class:"text-[24rpx] font-400"},{default:s((()=>[B("￥")])),_:1}),g(f,{class:"text-[40rpx] font-500"},{default:s((()=>[B(F(parseFloat(x(N).goodsPrice(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),g(f,{class:"text-[24rpx] font-500"},{default:s((()=>[B("."+F(parseFloat(x(N).goodsPrice(e)).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1032,["style"]),"member_price"==x(N).priceType(e)?(r(),a(o,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:x(C)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==x(N).priceType(e)?(r(),a(o,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:x(C)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==x(N).priceType(e)?(r(),a(o,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:x(C)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0)])),_:2},1024)):i("v-if",!0),x(I).saleStyle.control?(r(),a(f,{key:1,class:"text-[22rpx] mt-[8rpx] text-[var(--text-color-light9)]",style:n({color:x(I).saleStyle.color})},{default:s((()=>[B(" 已售"+F(e.sale_num)+F(e.unit||"件"),1)])),_:2},1032,["style"])):i("v-if",!0)])),_:2},1024),x(I).btnStyle.control?(r(),a(l,{key:0,class:"absolute right-[16rpx] bottom-[16rpx]"},{default:s((()=>["button"==x(I).btnStyle.style?(r(),a(l,{key:0,style:n(x(L)),class:"px-[18rpx] h-[48rpx] flex items-center justify-center"},{default:s((()=>[g(f,{class:"text-[20rpx]"},{default:s((()=>[B(F(x(I).btnStyle.text),1)])),_:1})])),_:1},8,["style"])):(r(),a(l,{key:1,style:n(x(L)),class:"w-[46rpx] h-[46rpx] rounded-[50%] flex items-center justify-center"},{default:s((()=>[g(f,{class:d(["nc-iconfont","text-[30rpx]",x(I).btnStyle.style])},null,8,["class"])])),_:1},8,["style"]))])),_:1})):i("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])):i("v-if",!0)],64)))),256))])),_:1}),g(l,null,{default:s((()=>[(r(!0),u(p,null,c(O.value,((e,t)=>(r(),u(p,null,[t%2==1?(r(),a(l,{key:0,class:d(["flex flex-col bg-[#fff] box-border overflow-hidden",{"mt-[24rpx]":t>1}]),style:n(x(q)),onClick:t=>ee(e)},{default:s((()=>[g(y,{width:x(Q),height:x(Q),radius:x(z).val,src:x(C)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:s((()=>[g(o,{style:n({width:x(Q),height:x(Q),"border-radius":x(z).val}),src:x(C)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["width","height","radius","src"]),g(l,{class:"relative min-h-[44rpx] px-[16rpx] flex-1 pt-[16rpx] pb-[20rpx] flex flex-col justify-between"},{default:s((()=>[x(I).goodsNameStyle.control?(r(),a(l,{key:0,class:"text-[#303133] leading-[40rpx] text-[28rpx] multi-hidden",style:n({color:x(I).goodsNameStyle.color,fontWeight:x(I).goodsNameStyle.fontWeight})},{default:s((()=>[e.goods_brand?(r(),a(l,{key:0,class:"brand-tag",style:n(x(N).baseTagStyle(e.goods_brand))},{default:s((()=>[B(F(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),B(" "+F(e.goods_name),1)])),_:2},1032,["style"])):i("v-if",!0),e.goods_label_name&&e.goods_label_name.length&&x(I).labelStyle.control?(r(),a(l,{key:1,class:"flex flex-wrap"},{default:s((()=>[(r(!0),u(p,null,c(e.goods_label_name,((e,t)=>(r(),u(p,null,["icon"==e.style_type&&e.icon?(r(),a(o,{key:0,class:"img-tag",src:x(C)(e.icon),mode:"heightFix",onError:t=>x(N).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),a(l,{key:1,class:"base-tag",style:n(x(N).baseTagStyle(e))},{default:s((()=>[B(F(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),g(l,{class:"flex justify-between flex-wrap items-center mt-[20rpx]"},{default:s((()=>[g(l,{class:"flex flex-col"},{default:s((()=>[x(I).priceStyle.control?(r(),a(l,{key:0,class:"flex items-baseline leading-[1]"},{default:s((()=>[g(l,{class:"text-[var(--price-text-color)] price-font block truncate max-w-[270rpx]",style:n({color:x(I).priceStyle.color})},{default:s((()=>[g(f,{class:"text-[24rpx] font-400"},{default:s((()=>[B("￥")])),_:1}),g(f,{class:"text-[40rpx] font-500"},{default:s((()=>[B(F(parseFloat(x(N).goodsPrice(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),g(f,{class:"text-[24rpx] font-500"},{default:s((()=>[B("."+F(parseFloat(x(N).goodsPrice(e)).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1032,["style"]),"member_price"==x(N).priceType(e)?(r(),a(o,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:x(C)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==x(N).priceType(e)?(r(),a(o,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:x(C)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==x(N).priceType(e)?(r(),a(o,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:x(C)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0)])),_:2},1024)):i("v-if",!0),x(I).saleStyle.control?(r(),a(f,{key:1,class:"text-[22rpx] mt-[8rpx] text-[var(--text-color-light9)]",style:n({color:x(I).saleStyle.color})},{default:s((()=>[B(" 已售"+F(e.sale_num)+F(e.unit||"件"),1)])),_:2},1032,["style"])):i("v-if",!0)])),_:2},1024),x(I).btnStyle.control?(r(),a(l,{key:0,class:"absolute right-[16rpx] bottom-[16rpx]"},{default:s((()=>["button"==x(I).btnStyle.style?(r(),a(l,{key:0,style:n(x(L)),class:"px-[18rpx] h-[48rpx] flex items-center justify-center"},{default:s((()=>[g(f,{class:"text-[20rpx]"},{default:s((()=>[B(F(x(I).btnStyle.text),1)])),_:1})])),_:1},8,["style"])):(r(),a(l,{key:1,style:n(x(L)),class:"w-[46rpx] h-[46rpx] rounded-[50%] flex items-center justify-center"},{default:s((()=>[g(f,{class:d(["nc-iconfont","text-[30rpx]",x(I).btnStyle.style])},null,8,["class"])])),_:1},8,["style"]))])),_:1})):i("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])):i("v-if",!0)],64)))),256))])),_:1})],64)):i("v-if",!0),"style-3"==x(I).style?(r(),u(p,{key:2},[O.value.length?(r(),a(l,{key:0,style:n(x(J))},{default:s((()=>[g(h,{id:"warpStyle3-"+x(I).id,class:"whitespace-nowrap min-h-[290rpx]","scroll-x":!0},{default:s((()=>[(r(!0),u(p,null,c(O.value,((e,t)=>(r(),a(l,{id:"item"+t+x(I).id,class:d(["w-[214rpx] mb-[6rpx] inline-block bg-[#fff] box-border overflow-hidden",{"!mr-[0rpx]":t==O.value.length-1}]),style:n(x(q)+K.value),key:e.goods_id,onClick:t=>ee(e)},{default:s((()=>[g(y,{width:"214rpx",height:"160rpx",radius:x(z).val,src:x(C)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:s((()=>[g(o,{class:"w-[214rpx] h-[160rpx]",style:n(x(z).style),src:x(C)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"]),g(l,{class:"relative min-h-[40rpx] px-[10rpx] pt-[16rpx] pb-[10rpx]"},{default:s((()=>[x(I).goodsNameStyle.control?(r(),a(l,{key:0,class:"text-[26rpx] text-[#303133] truncate",style:n({color:x(I).goodsNameStyle.color,fontWeight:x(I).goodsNameStyle.fontWeight})},{default:s((()=>[B(F(e.goods_name),1)])),_:2},1032,["style"])):i("v-if",!0),x(I).priceStyle.control?(r(),a(l,{key:1,class:"text-[var(--price-text-color)] pt-[16rpx] pb-[6rpx] font-bold price-font block truncate max-w-[160rpx] leading-[1] overflow-hidden",style:n({color:x(I).priceStyle.color})},{default:s((()=>[g(f,{class:"text-[20rpx] font-400 mr-[2rpx]"},{default:s((()=>[B("￥")])),_:1}),g(f,{class:"text-[36rpx] font-500"},{default:s((()=>[B(F(parseFloat(x(N).goodsPrice(e)).toFixed(2)),1)])),_:2},1024)])),_:2},1032,["style"])):i("v-if",!0),x(I).btnStyle.control?(r(),a(l,{key:2,class:"absolute right-[10rpx] bottom-[12rpx]"},{default:s((()=>["button"!=x(I).btnStyle.style?(r(),a(l,{key:0,style:n(x(L)),class:"w-[40rpx] h-[40rpx] rounded-[50%] flex items-center justify-center"},{default:s((()=>[g(f,{class:d([[x(I).btnStyle.style],"nc-iconfont text-[28rpx]"])},null,8,["class"])])),_:1},8,["style"])):i("v-if",!0)])),_:1})):i("v-if",!0)])),_:2},1024)])),_:2},1032,["id","class","style","onClick"])))),128))])),_:1},8,["id"])])),_:1},8,["style"])):i("v-if",!0)],64)):i("v-if",!0)])),_:1},8,["class"])])),_:1},8,["style"])])),_:1},8,["type","loading","config"])}}}),[["__scopeId","data-v-a8ea12ff"]]);export{H as _,N as d};
