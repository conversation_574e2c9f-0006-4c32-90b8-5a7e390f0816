import{d as e,r as a,J as s,A as l,M as t,o as c,c as o,w as u,g as r,b as d,t as i,x as n,y as _,z as p,F as f,be as m,Q as v,i as g,j as h,k,S as y,au as q,aB as b}from"./index-3caf046d.js";import{_ as x}from"./u-icon.ba193921.js";import{d as w}from"./quote.9b84c391.js";import{_ as j}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const z=j(e({__name:"quote-success",setup(e){const j=a({}),z=a({name:"",code:"",image:"",price:"0.00"}),F=a({orderNo:"",status:"",createTime:""}),N=a({}),T=a([]);s((e=>{console.log("页面参数:",e),j.value=e,e.id?C(e.id):D()}));const C=async e=>{var a;try{const s=await w(parseInt(e));if(1!==s.code)throw new Error(s.msg||"获取估价详情失败");{const e=s.data;N.value=e,z.value={name:e.product_name||(null==(a=e.brand)?void 0:a.name)||"估价商品",code:e.product_code||"",image:e.product_image?l(e.product_image):"",price:e.quote_price?e.quote_price.toFixed(2):null},F.value={orderNo:e.order_no||"",status:e.status_text||"",createTime:e.create_time||""},T.value=(e.photos||[]).map((e=>({...e,photo_url:l(e.photo_url)})))}}catch(s){console.error("加载估价详情失败:",s),t({title:"加载失败",icon:"none"}),D()}},D=()=>{z.value={name:"估价商品",code:"",image:"",price:"0.00"},F.value={orderNo:"QT"+Date.now(),status:"待估价",createTime:(new Date).toLocaleString()}},I=()=>{N.value.id,m({url:"/addon/yz_she/pages/order/quote-list"})};return(e,a)=>{const s=v,l=g(h("u-icon"),x),t=k,m=y,w=q;return c(),o(t,{class:"quote-success-page"},{default:u((()=>[r(" 商品信息卡片 "),d(t,{class:"product-card"},{default:u((()=>[d(t,{class:"product-info"},{default:u((()=>[z.value.image?(c(),o(s,{key:0,src:z.value.image,class:"product-image",mode:"aspectFit"},null,8,["src"])):(c(),o(t,{key:1,class:"product-image no-image"},{default:u((()=>[d(l,{name:"image",color:"#ccc",size:"40"})])),_:1})),d(t,{class:"product-details"},{default:u((()=>[d(m,{class:"product-name"},{default:u((()=>[i(n(z.value.name),1)])),_:1}),z.value.code?(c(),o(m,{key:0,class:"product-code"},{default:u((()=>[i(n(z.value.code),1)])),_:1})):r("v-if",!0)])),_:1})])),_:1}),d(t,{class:"price-section"},{default:u((()=>[d(t,{class:"price-info"},{default:u((()=>[d(m,{class:"price-label"},{default:u((()=>[i("预估价格")])),_:1}),d(t,{class:"price-amount"},{default:u((()=>[z.value.price&&"0.00"!==z.value.price?(c(),o(m,{key:0,class:"currency"},{default:u((()=>[i("¥")])),_:1})):r("v-if",!0),d(m,{class:"price-value"},{default:u((()=>[i(n(z.value.price&&"0.00"!==z.value.price?z.value.price:"估价中"),1)])),_:1})])),_:1})])),_:1}),d(t,{class:"quote-note"},{default:u((()=>[d(m,{class:"note-text"},{default:u((()=>[i("*最终价格以实物质检为准")])),_:1})])),_:1})])),_:1}),r(" 用户上传的图片 "),T.value.length>0?(c(),o(t,{key:0,class:"upload-photos"},{default:u((()=>[d(m,{class:"photos-title"},{default:u((()=>[i("上传图片")])),_:1}),d(w,{class:"photos-scroll","scroll-x":"true","show-scrollbar":"false"},{default:u((()=>[d(t,{class:"photos-list"},{default:u((()=>[(c(!0),_(f,null,p(T.value,(e=>(c(),o(t,{class:"photo-item",key:e.id},{default:u((()=>[d(s,{src:e.photo_url,class:"photo-image",mode:"aspectFill",onClick:a=>(e=>{const a=T.value.map((e=>e.photo_url)),s=a.indexOf(e);b({urls:a,current:s})})(e.photo_url)},null,8,["src","onClick"]),d(m,{class:"photo-label"},{default:u((()=>[i(n(e.photo_name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):r("v-if",!0)])),_:1}),r(" 成功状态区域 "),d(t,{class:"success-section"},{default:u((()=>[d(t,{class:"success-icon"},{default:u((()=>[d(t,{class:"quote-bg"},{default:u((()=>[d(t,{class:"quote-body"},{default:u((()=>[d(t,{class:"quote-lines"},{default:u((()=>[d(t,{class:"line"}),d(t,{class:"line"}),d(t,{class:"line"})])),_:1})])),_:1}),d(t,{class:"quote-clip"})])),_:1}),d(t,{class:"check-mark"},{default:u((()=>[d(l,{name:"checkmark",color:"#fff",size:"16"})])),_:1})])),_:1}),d(m,{class:"success-title"},{default:u((()=>[i("估价提交成功")])),_:1}),d(m,{class:"order-number"},{default:u((()=>[i("估价单号: "+n(F.value.orderNo),1)])),_:1}),d(m,{class:"success-desc"},{default:u((()=>[i("我们将在24小时内为您提供专业估价")])),_:1}),r(" 查看估价单按钮 "),d(t,{class:"view-quote-button",onClick:I},{default:u((()=>[d(m,{class:"button-text"},{default:u((()=>[i("查看估价单")])),_:1})])),_:1})])),_:1})])),_:1})}}}),[["__scopeId","data-v-44d00d63"]]);export{z as default};
