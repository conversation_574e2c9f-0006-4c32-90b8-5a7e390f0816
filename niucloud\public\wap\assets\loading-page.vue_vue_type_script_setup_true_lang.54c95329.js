import{ab as a,ac as o,ad as e,ae as i,i as l,j as t,o as n,c as d,w as g,b as s,n as r,a7 as p,t as u,x as c,Q as f,k as _,S as m,d as S}from"./index-3caf046d.js";import{_ as y}from"./u-loading-icon.255170b9.js";import{_ as b}from"./u-transition.4c8b523a.js";import{_ as z}from"./_plugin-vue_export-helper.1b428a4d.js";const x=z({name:"u-loading-page",mixins:[o,e,{props:{loadingText:{type:[String,Number],default:()=>a.loadingPage.loadingText},image:{type:String,default:()=>a.loadingPage.image},loadingMode:{type:String,default:()=>a.loadingPage.loadingMode},loading:{type:Boolean,default:()=>a.loadingPage.loading},bgColor:{type:String,default:()=>a.loadingPage.bgColor},color:{type:String,default:()=>a.loadingPage.color},fontSize:{type:[String,Number],default:()=>a.loadingPage.fontSize},iconSize:{type:[String,Number],default:()=>a.loadingPage.fontSize},loadingColor:{type:String,default:()=>a.loadingPage.loadingColor}}}],data:()=>({}),methods:{addUnit:i}},[["render",function(a,o,e,i,S,z){const x=f,h=l(t("u-loading-icon"),y),w=_,C=m,P=l(t("u-transition"),b);return n(),d(P,{show:a.loading,"custom-style":{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:a.bgColor,display:"flex"}},{default:g((()=>[s(w,{class:"u-loading-page"},{default:g((()=>[s(w,{class:"u-loading-page__warpper"},{default:g((()=>[s(w,{class:"u-loading-page__warpper__loading-icon"},{default:g((()=>[a.image?(n(),d(x,{key:0,src:a.image,class:"u-loading-page__warpper__loading-icon__img",mode:"widthFit",style:r({width:z.addUnit(a.iconSize),height:z.addUnit(a.iconSize)})},null,8,["src","style"])):(n(),d(h,{key:1,mode:a.loadingMode,size:z.addUnit(a.iconSize),color:a.loadingColor},null,8,["mode","size","color"]))])),_:1}),p(a.$slots,"default",{},(()=>[s(C,{class:"u-loading-page__warpper__text",style:r({fontSize:z.addUnit(a.fontSize),color:a.color})},{default:g((()=>[u(c(a.loadingText),1)])),_:1},8,["style"])]),!0)])),_:3})])),_:3})])),_:3},8,["show","custom-style"])}],["__scopeId","data-v-306503b4"]]),h=S({__name:"loading-page",props:{loading:{type:Boolean,default:!1},iconSize:{type:String||Number,default:30},bgColor:{type:String,default:"rgb(248,248,248)"}},setup(a){const o=a;return(a,e)=>{const i=l(t("u-loading-page"),x),r=_;return n(),d(r,null,{default:g((()=>[s(i,{"bg-color":"props.bgColor","icon-size":"props.iconSize",loading:o.loading,loadingText:""},null,8,["loading"])])),_:1})}}});export{h as _};
