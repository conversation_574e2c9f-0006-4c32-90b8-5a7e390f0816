import{aG as e,aH as t,aI as l,aJ as o,aK as a,aL as r,d as n,aE as s,p as i,A as d,r as u,_ as c,s as p,as as f,av as m,i as x,j as v,o as b,c as g,w as y,b as h,n as _,e as k,t as w,x as C,g as S,y as R,F as B,z,$ as F,aw as T,k as E,S as D,Q as $,au as I,m as M,a9 as A,q as j,O as N,aF as W,W as U,aM as Y,aN as P,an as V,P as H,al as L,aD as O,ab as q,ac as G,ad as X,aO as Z,af as Q,aP as K,a7 as J,R as ee,ae as te,aQ as le,aR as oe,aS as ae,aT as re,aU as ne,aV as se,aW as ie,a8 as de,aX as ue,aY as ce,aZ as pe,aC as fe,a_ as me,ao as xe,ar as ve,aA as be,aB as ge,M as ye,a$ as he,b0 as _e,b1 as ke,a as we,b2 as Ce,I as Se,B as Re,C as Be,K as ze,L as Fe,b3 as Te,ah as Ee,U as De,b4 as $e,b5 as Ie,b6 as Me,b7 as Ae,f as je,v as Ne,N as We,aq as Ue}from"./index-3caf046d.js";import{_ as Ye}from"./u-icon.ba193921.js";import{_ as Pe}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as Ve}from"./u-popup.1b30ffa7.js";import{u as He,_ as Le}from"./top-tabbar.f4fde406.js";import{_ as Oe}from"./area-select.vue_vue_type_script_setup_true_lang.abe3938e.js";import{_ as qe,a as Ge}from"./u-checkbox-group.0328273c.js";import{_ as Xe}from"./u-loading-icon.255170b9.js";import{d as Ze,_ as Qe}from"./u-datetime-picker.a5259774.js";import{_ as Ke}from"./u-upload.83871903.js";import{_ as Je,a as et}from"./u-radio-group.63482a1c.js";import{a as tt}from"./diy_form.9eef685a.js";import{_ as lt}from"./u-avatar.30e31e9c.js";import{_ as ot}from"./u-parse.406d0731.js";import{_ as at}from"./tabbar.2c31519d.js";import{_ as rt,d as nt}from"./index.32583a71.js";import{d as st,b as it,e as dt}from"./coupon.2f3f2d3d.js";import{g as ut,a as ct}from"./goods.6a81cb49.js";import{_ as pt}from"./u--image.eb573bce.js";import{g as ft,a as mt}from"./point.0698952c.js";import{g as xt}from"./rank.7a4c9318.js";import{u as vt}from"./useGoods.9c8f1c51.js";import{b as bt}from"./bind-mobile.25318c0e.js";import{g as gt}from"./voucher.db23b7c0.js";import{g as yt}from"./quote.9b84c391.js";import{g as ht,_ as _t}from"./newcomer.6993dfef.js";import{a as kt}from"./order.5c5c6bee.js";var wt="[object Symbol]";var Ct=/\s/;var St=/^\s+/;function Rt(e){return e?e.slice(0,function(e){for(var t=e.length;t--&&Ct.test(e.charAt(t)););return t}(e)+1).replace(St,""):e}var Bt=NaN,zt=/^[-+]0x[0-9a-f]+$/i,Ft=/^0b[01]+$/i,Tt=/^0o[0-7]+$/i,Et=parseInt;function Dt(o){if("number"==typeof o)return o;if(function(l){return"symbol"==typeof l||e(l)&&t(l)==wt}(o))return Bt;if(l(o)){var a="function"==typeof o.valueOf?o.valueOf():o;o=l(a)?a+"":a}if("string"!=typeof o)return 0===o?o:+o;o=Rt(o);var r=Ft.test(o);return r||Tt.test(o)?Et(o.slice(2),r?2:8):zt.test(o)?Bt:+o}var $t=1/0,It=17976931348623157e292;function Mt(e){return e?(e=Dt(e))===$t||e===-$t?(e<0?-1:1)*It:e==e?e:0:0===e?e:0}var At=Math.ceil,jt=Math.max;var Nt,Wt=function(e,t,n){return n&&"number"!=typeof n&&function(e,t,n){if(!l(n))return!1;var s=typeof t;return!!("number"==s?o(n)&&a(t,n.length):"string"==s&&t in n)&&r(n[t],e)}(e,t,n)&&(t=n=void 0),e=Mt(e),void 0===t?(t=e,e=0):t=Mt(t),function(e,t,l,o){for(var a=-1,r=jt(At((t-e)/(l||1)),0),n=Array(r);r--;)n[o?r:++a]=e,e+=l;return n}(e,t,n=void 0===n?e<t?1:-1:Mt(n),Nt)};const Ut=Wt,Yt=Pe(n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=i((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${W.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e})),n=u(""),M=u(""),A=e=>{var t="";return e.listFrame.startColor&&e.listFrame.endColor?t+=`background:linear-gradient(${e.listFrame.startColor},${e.listFrame.endColor});`:t+=`background:${e.listFrame.startColor||e.listFrame.endColor};`,o.value.topElementRounded&&(t+="border-top-left-radius:"+2*o.value.topElementRounded+"rpx;"),o.value.topElementRounded&&(t+="border-top-right-radius:"+2*o.value.topElementRounded+"rpx;"),o.value.bottomElementRounded&&(t+="border-bottom-left-radius:"+2*o.value.bottomElementRounded+"rpx;"),o.value.bottomElementRounded&&(t+="border-bottom-right-radius:"+2*o.value.bottomElementRounded+"rpx;"),t+="overflow: hidden;"},j=e=>{var t="";return t+=`background:linear-gradient(90deg,${e.startColor},${e.endColor});`};c((()=>{U(),"decorate"==l.mode?p((()=>o.value),((e,t)=>{e&&"ActiveCube"==e.componentName&&U()})):p((()=>o.value),((e,t)=>{U()}))}));const N=T(),W=u(0),U=()=>{f((()=>{m().in(N).select(".diy-active-cube").boundingClientRect((e=>{W.value=e.height})).exec(),"style-3"==o.value.blockStyle.value&&(n.value="margin-right:14rpx;"),"style-4"==o.value.blockStyle.value&&(M.value="margin-right:14rpx;")}))};return(e,t)=>{const s=E,i=D,u=$,c=x(v("u-icon"),Ye),p=I;return b(),g(s,{style:_(k(a))},{default:y((()=>[h(s,{style:_(k(r))},null,8,["style"]),h(s,{class:"diy-active-cube relative"},{default:y((()=>[h(s,{class:"active-cube-wrap pt-[28rpx] px-[20rpx] pb-[24rpx]"},{default:y((()=>["style-1"==k(o).titleStyle.value?(b(),g(s,{key:0,class:"flex items-center"},{default:y((()=>[h(s,{class:"mr-[10rpx] font-500 text-[30rpx]",style:_({color:k(o).titleColor}),onClick:t[0]||(t[0]=e=>k(l).toRedirect(k(o).textLink))},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"]),k(o).subTitle.text?(b(),g(s,{key:0,onClick:t[1]||(t[1]=e=>k(l).toRedirect(k(o).subTitle.link)),class:"text-center text-[22rpx] rounded-[40rpx] rounded-tl-[10rpx] py-[6rpx] px-[14rpx]",style:_({color:k(o).subTitle.textColor,background:"linear-gradient(90deg, "+k(o).subTitle.startColor+", "+k(o).subTitle.endColor+")"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(o).titleStyle.value?(b(),g(s,{key:1,class:"flex items-center"},{default:y((()=>[h(s,{class:"mr-[10rpx] font-500 text-[30rpx]",style:_({color:k(o).titleColor}),onClick:t[2]||(t[2]=e=>k(l).toRedirect(k(o).textLink))},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"]),k(o).subTitle.text?(b(),g(s,{key:0,onClick:t[3]||(t[3]=e=>k(l).toRedirect(k(o).subTitle.link)),class:"text-center text-[22rpx] rounded-[6rpx] py-[6rpx] px-[14rpx]",style:_({color:k(o).subTitle.textColor,background:"linear-gradient(90deg, "+k(o).subTitle.startColor+", "+k(o).subTitle.endColor+")"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),"style-3"==k(o).titleStyle.value?(b(),g(s,{key:2,class:"flex items-center"},{default:y((()=>[h(s,{class:"mr-[10rpx] font-500 text-[30rpx]",onClick:t[4]||(t[4]=e=>k(l).toRedirect(k(o).textLink)),style:_({color:k(o).titleColor})},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"]),h(s,{class:"relative h-[36rpx]",onClick:t[5]||(t[5]=e=>k(l).toRedirect(k(o).subTitle.link))},{default:y((()=>[k(o).subTitle.text?(b(),g(s,{key:0,class:"flex items-center text-[22rpx] leading-0 min-w-[60rpx] h-[34rpx] pl-[10rpx] pr-[34rpx]",style:_({color:k(o).subTitle.textColor,"background-image":"url("+k(d)("static/resource/images/diy/active_cube/bg_2.png")+")","background-size":"100% 100%","background-repeat":"no-repeat"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"])):S("v-if",!0),S(' \t<image class="absolute left-0 top-0 bottom-0 !w-[16rpx] !h-[36rpx]" :src="img(\'static/resource/images/diy/active_cube/block_style2_1.png\')" mode="scaleToFill"/>\n                                    <image class="absolute right-0 top-0 bottom-0 !w-[28rpx] !h-[36rpx]" :src="img(\'static/resource/images/diy/active_cube/block_style2_2.png\')" mode="scaleToFill"/> ')])),_:1})])),_:1})):S("v-if",!0),"style-4"==k(o).titleStyle.value?(b(),g(s,{key:3,class:"flex items-center justify-between"},{default:y((()=>[h(s,{class:"font-500 text-[30rpx]",onClick:t[6]||(t[6]=e=>k(l).toRedirect(k(o).textLink)),style:_({color:k(o).titleColor})},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"]),k(o).subTitle.text?(b(),g(s,{key:0,onClick:t[7]||(t[7]=e=>k(l).toRedirect(k(o).subTitle.link)),class:"text-[22rpx] rounded-[40rpx] pl-[16rpx] pr-[8rpx] h-[42rpx] flex-center",style:_({color:k(o).subTitle.textColor,background:"linear-gradient(90deg, "+k(o).subTitle.startColor+", "+k(o).subTitle.endColor+")"})},{default:y((()=>[h(i,null,{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1}),h(i,{class:"nc-iconfont nc-icon-youV6xx !text-[26rpx]"})])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),"style-5"==k(o).titleStyle.value?(b(),g(s,{key:4,class:"flex items-center"},{default:y((()=>[k(o).textImg?(b(),g(s,{key:0,class:"h-[32rpx] flex items-center",onClick:t[8]||(t[8]=e=>k(l).toRedirect(k(o).textLink))},{default:y((()=>[h(u,{class:"h-[100%] w-[auto]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])])),_:1})):S("v-if",!0),k(o).subTitle.text&&k(o).textImg?(b(),g(s,{key:1,class:"mx-[16rpx] w-[2rpx] h-[24rpx]",style:_({background:k(o).subTitle.textColor})},null,8,["style"])):S("v-if",!0),k(o).subTitle.text?(b(),g(s,{key:2,onClick:t[9]||(t[9]=e=>k(l).toRedirect(k(o).subTitle.link)),class:"text-center text-[22rpx] py-[6rpx]",style:_({color:k(o).subTitle.textColor,background:"linear-gradient(90deg, "+k(o).subTitle.startColor+", "+k(o).subTitle.endColor+")"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),h(s,{class:"bd flex flex-wrap justify-between"},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,(e=>(b(),R(B,{key:e.id},["style-1"==k(o).blockStyle.value?(b(),g(s,{key:0,onClick:t=>k(l).toRedirect(e.link),class:"item flex justify-between px-[20rpx] py-[30rpx] bg-white mt-[20rpx]",style:_(A(e))},{default:y((()=>[h(s,{class:"flex-1 flex items-baseline flex-col"},{default:y((()=>[h(s,{class:"text-[28rpx] pb-[10rpx] text-[#333]",style:_({fontWeight:k(o).blockStyle.fontWeight})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"]),h(s,{class:"text-[22rpx] text-[#999] pb-[30rpx]"},{default:y((()=>[w(C(e.subTitle.text),1)])),_:2},1024),e.moreTitle.text?(b(),g(s,{key:0,class:"link relative text-[22rpx] leading-[40rpx] flex items-center text-white rounded-r-[20rpx] h-[40rpx] pl-[26rpx] pr-[10rpx]",style:_(j(e.moreTitle))},{default:y((()=>[h(i,{class:"mr-[8rpx]"},{default:y((()=>[w(C(e.moreTitle.text),1)])),_:2},1024),h(i,{class:"iconfont iconjiantou-you-cuxiantiao-fill !text-[20rpx] text-[#fff]"}),h(u,{class:"absolute left-0 top-0 bottom-0 !w-[28rpx]",src:k(d)("static/resource/images/diy/active_cube/block_style1_1.png"),mode:"scaleToFill"},null,8,["src"])])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1024),e.imageUrl?(b(),g(s,{key:0,class:"img-box ml-[10rpx] w-[130rpx]"},{default:y((()=>[h(u,{src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["src"])])),_:2},1024)):(b(),g(s,{key:1,class:"img-box ml-[10rpx] flex items-center justify-center w-[130rpx] bg-[#f3f4f6]"},{default:y((()=>[h(c,{name:"photo",color:"#999",size:"50"})])),_:1}))])),_:2},1032,["onClick","style"])):S("v-if",!0),"style-2"==k(o).blockStyle.value?(b(),g(s,{key:1,onClick:t=>k(l).toRedirect(e.link),class:"item h-[150rpx] flex justify-between p-[20rpx] bg-white mt-[20rpx]",style:_(A(e))},{default:y((()=>[h(s,{class:"flex-1 flex items-baseline flex-col"},{default:y((()=>[h(s,{class:"text-[26rpx] mt-[10rpx] pb-[16rpx]",style:_({fontWeight:k(o).blockStyle.fontWeight})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"]),h(s,{class:"text-[22rpx] text-gray-500 pb-[26rpx]"},{default:y((()=>[w(C(e.subTitle.text),1)])),_:2},1024),e.moreTitle.text?(b(),g(s,{key:0,class:"link relative text-[22rpx] leading-[40rpx] flex items-center text-white rounded-[20rpx] h-[40rpx] pl-[20rpx] pr-[10rpx]",style:_(j(e.moreTitle))},{default:y((()=>[h(i,{class:F(["mr-[8rpx]",{italic:"italics"==k(o).blockStyle.btnText}])},{default:y((()=>[w(C(e.moreTitle.text),1)])),_:2},1032,["class"]),h(i,{class:"iconfont iconjiantou-you-cuxiantiao-fill !text-[20rpx] text-[#fff]"})])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1024),e.imageUrl?(b(),g(s,{key:0,class:"img-box ml-[10rpx] w-[130rpx]"},{default:y((()=>[h(u,{src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["src"])])),_:2},1024)):(b(),g(s,{key:1,class:"img-box ml-[10rpx] flex items-center justify-center w-[130rpx] bg-[#f3f4f6]"},{default:y((()=>[h(c,{name:"photo",color:"#999",size:"50"})])),_:1}))])),_:2},1032,["onClick","style"])):S("v-if",!0)],64)))),128))])),_:1}),"style-3"==k(o).blockStyle.value?(b(),g(p,{key:5,"scroll-x":!0,class:"whitespace-nowrap",id:"warpStyle3-"+k(o).id},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,((e,t)=>(b(),g(s,{key:e.id,class:"inline-flex"},{default:y((()=>[h(s,{id:"item"+t+k(o).id,onClick:t=>k(l).toRedirect(e.link),class:F(["flex flex-col items-center justify-between p-[10rpx] bg-white mt-[20rpx] w-[157rpx] h-[200rpx] box-border",{"!mr-[0rpx]":t+1===k(o).list.length}]),style:_(n.value+A(e))},{default:y((()=>[e.imageUrl?(b(),g(s,{key:0,class:"w-[141rpx] h-[141rpx] rounded-[var(--rounded-small)] overflow-hidden"},{default:y((()=>[h(u,{class:"w-[141rpx] h-[141rpx]",src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["src"])])),_:2},1024)):(b(),g(s,{key:1,class:"w-[141rpx] h-[141rpx] relative flex-shrink-0"},{default:y((()=>[h(s,{class:"absolute left-0 top-0 flex items-center justify-center w-[141rpx] h-[141rpx] bg-[#f3f4f6]"},{default:y((()=>[h(c,{name:"photo",color:"#999",size:"50"})])),_:1})])),_:1})),h(s,{class:"mt-[10rpx] mb-[2rpx] text-[26rpx]",style:_({color:e.title.textColor,fontWeight:k(o).blockStyle.fontWeight})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"])])),_:2},1032,["id","onClick","style","class"])])),_:2},1024)))),128))])),_:1},8,["id"])):S("v-if",!0),"style-4"==k(o).blockStyle.value?(b(),g(p,{key:6,"scroll-x":"true",class:"whitespace-nowrap",id:"warpStyle4-"+k(o).id},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,((e,t)=>(b(),g(s,{key:e.id,class:"inline-flex"},{default:y((()=>[h(s,{id:"item"+t+k(o).id,onClick:t=>k(l).toRedirect(e.link),class:F(["flex flex-col items-center justify-between p-[4rpx] bg-[#F93D02] mt-[20rpx] box-border",{"!mr-[0rpx]":t+1===k(o).list.length}]),style:_(A(e)+M.value)},{default:y((()=>[h(s,{class:"w-[149rpx] h-[149rpx] box-border px-[18rpx] pt-[16rpx] pb-[6rpx] bg-[#fff] flex flex-col items-center rounded-[var(--rounded-small)]"},{default:y((()=>[e.imageUrl?(b(),g(s,{key:0,class:"w-[112rpx] h-[102rpx]"},{default:y((()=>[h(u,{class:"w-[112rpx] h-[102rpx]",src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["src"])])),_:2},1024)):(b(),g(s,{key:1,class:"w-[112rpx] h-[102rpx] relative flex-shrink-0"},{default:y((()=>[h(s,{class:"absolute left-0 top-0 flex items-center justify-center w-[112rpx] h-[102rpx] bg-[#f3f4f6]"},{default:y((()=>[h(c,{name:"photo",color:"#999",size:"50"})])),_:1})])),_:1})),h(s,{class:"relative -mt-[10rpx] text-[20rpx] bg-[#F3DAC5] text-[#ED6E00] rounded-[16rpx] px-[12rpx] h-[34rpx] flex-center",style:_({color:e.subTitle.textColor,background:"linear-gradient(to right,"+e.subTitle.startColor+","+e.subTitle.endColor+")"})},{default:y((()=>[w(C(e.subTitle.text),1)])),_:2},1032,["style"])])),_:2},1024),h(s,{class:"mt-[12rpx] mb-[12rpx] text-[26rpx] text-[#fff]",style:_({fontWeight:"bold"==k(o).blockStyle.fontWeight?k(o).blockStyle.fontWeight:"500"})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"])])),_:2},1032,["id","onClick","class","style"])])),_:2},1024)))),128))])),_:1},8,["id"])):S("v-if",!0)])),_:1})])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-0ce90cab"]]),Pt=Pe(n({__name:"index",props:["component","index","global","scrollBool"],setup(e){const t=e,l=M(),o=A(),a=T(),r=s(),n=i((()=>"decorate"==r.mode?r.value[t.index]:t.component));let V=!1;n.value&&"style-2"==n.value.search.style&&"decorate"!=r.mode&&(V=!0);const H=He(V);H.onLoad(),H.init();const L=i((()=>{var e="";return n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e})),O=u(""),q=u(!1),G=i((()=>{var e="";if("style-3"==n.value.swiper.swiperStyle&&(e+="position: absolute;z-index: 99;left: 0;right: 0;"),t.global.topStatusBar.isShow&&"style-4"==t.global.topStatusBar.style&&(e+="top:"+r.topTabarHeight+"px;"),"style-3"==n.value.swiper.swiperStyle&&("ios"===o.platform?e+="top: 55px;":e+="top: 44.5px;"),"decorate"==r.mode)return e;if("fixed"==n.value.positionWay&&(null!=t.scrollBool&&-1!=t.scrollBool&&(e+="position: fixed;z-index: 99;top: 0;left: 0;right: 0;"),1!=t.scrollBool&&2!=t.scrollBool||t.global.topStatusBar.isShow&&"style-4"==t.global.topStatusBar.style&&(e+="top:"+r.topTabarHeight+"px;"),q.value=!1,1==t.scrollBool)){let t=(n.value.fixedBgColor||"").split(","),l=n.value.fixedBgColor?parseInt(t[t.length-1]):0;n.value.fixedBgColor&&0!=l?(q.value=!1,e+="background-color:"+n.value.fixedBgColor+";"):q.value=!0}return e})),X=()=>{let e="";return"style-3"==n.value.swiper.swiperStyle&&(e="ios"===o.platform?"margin-top: -55px;":"margin-top: -44.5px;"),e},Z=e=>{let l="";return e?(l=n.value.tab.selectColor,"fixed"==n.value.positionWay&&1==t.scrollBool&&(l=n.value.tab.fixedSelectColor)):(l=n.value.tab.noColor,"fixed"==n.value.positionWay&&1==t.scrollBool&&(l=n.value.tab.fixedNoColor)),l},Q=i((()=>{let e=!0;for(let t=0;t<n.value.search.hotWord.list.length;t++){if(n.value.search.hotWord.list[t].text){e=!1;break}}return e})),K=i((()=>{var e="";let l=t.global.pageStartBgColor?t.global.pageStartBgColor:"rgba(255,255,255,1)";if(l.indexOf("(")>-1){let t=l.split("(")[1].split(")")[0].split(",");1==n.value.bgGradient&&(e+=`background: linear-gradient(rgba(${t[0]}, ${t[1]}, ${t[2]}, 0) 65%, rgba(${t[0]}, ${t[1]}, ${t[2]}, 0.6) 70%, rgba(${t[0]}, ${t[1]}, ${t[2]}, 0.85) 80%, rgba(${t[0]}, ${t[1]}, ${t[2]}, 0.95) 90%,  rgb(${t[0]}, ${t[1]}, ${t[2]}, 1) 100%);`)}else e+=`background: (${l});`;return e})),J=i((()=>"style-2"==n.value.swiper.swiperStyle||"style-3"==n.value.swiper.swiperStyle)),ee=i((()=>2*n.value.swiper.imageHeight+"rpx")),te=u(0),le=e=>{te.value=e.detail.current},oe=i((()=>{var e="";return n.value.swiper.topRounded&&(e+="border-top-left-radius:"+2*n.value.swiper.topRounded+"rpx;"),n.value.swiper.topRounded&&(e+="border-top-right-radius:"+2*n.value.swiper.topRounded+"rpx;"),n.value.swiper.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.swiper.bottomRounded+"rpx;"),n.value.swiper.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.swiper.bottomRounded+"rpx;"),e})),ae=i((()=>{var e="";return n.value.search.subTitle.textColor&&(e+="color:"+n.value.search.subTitle.textColor+";"),n.value.search.subTitle.startColor&&n.value.search.subTitle.endColor?e+=`background:linear-gradient(${n.value.search.subTitle.startColor}, ${n.value.search.subTitle.endColor});`:e+="background-color:"+(n.value.search.subTitle.startColor||n.value.search.subTitle.endColor)+";",e})),re=u(-1),ne=u(""),se=(e,t)=>{if("decorate"==r.mode)return!1;ne.value=e.source,re.value=t,"home"==e.source?r.topFixedStatus="home":"diy_page"==e.source&&(r.topFixedStatus="diy",fe(e.diy_id))},ie=u(!1);let de={};const ue=u("");c((()=>{ce(),"decorate"==r.mode&&p((()=>n.value),((e,t)=>{e&&"CarouselSearch"==e.componentName&&ce()})),me=i((()=>{var e,t,l;return(null==(l=null==(t=null==(e=n.value)?void 0:e.swiper)?void 0:t.list)?void 0:l.length)>1}))}));const ce=()=>{f((()=>{setTimeout((()=>{"style-3"!=n.value.swiper.swiperStyle?m().in(a).select(".fixed-wrap").boundingClientRect((e=>{O.value=(e.height||0)+"px"})).exec():O.value=""}))})),H.refresh(),se({source:"home"},-1),n.value.swiper.list.forEach((e=>{""==e.imageUrl&&(e.imgWidth=690,e.imgHeight=330)}))},pe=j({pageMode:"diy",title:"",global:{},value:[]}),fe=e=>{if(!e)return pe.pageMode="diy",pe.title="",pe.global={},void(pe.value=[]);W({id:e}).then((e=>{if(e.data.value){let t=e.data;pe.pageMode=t.mode,pe.title=t.title;let l=JSON.parse(t.value);pe.global=l.global,pe.global.topStatusBar.isShow=!1,pe.global.bottomTabBarSwitch=!1,pe.value=l.value,pe.value.forEach(((e,t)=>{e.pageStyle="",e.pageStartBgColor&&(e.pageStartBgColor&&e.pageEndBgColor?e.pageStyle+=`background:linear-gradient(${e.pageGradientAngle},${e.pageStartBgColor},${e.pageEndBgColor});`:e.pageStyle+="background-color:"+e.pageStartBgColor+";"),e.margin&&(e.margin.top>0&&(e.pageStyle+="padding-top:"+2*e.margin.top+"rpx;"),e.pageStyle+="padding-bottom:"+2*e.margin.bottom+"rpx;",e.pageStyle+="padding-right:"+2*e.margin.both+"rpx;",e.pageStyle+="padding-left:"+2*e.margin.both+"rpx;")})),U({title:pe.title})}}))};let me=u(!0);me.value=!0;let xe=uni.getStorageSync("componentsScrollValGroup");if(xe&&"object"==typeof xe)xe.CarouselSearch=20,uni.setStorageSync("componentsScrollValGroup",xe);else{let e={CarouselSearch:20};uni.setStorageSync("componentsScrollValGroup",e)}return(e,o)=>{const a=$,s=E,i=D,u=Y,c=P,p=I,f=x(v("u-popup"),Ve);return b(),g(s,{style:_(k(L)),class:"goods-carousel-search-wrap"},{default:y((()=>[h(s,{class:"relative pb-[20rpx]"},{default:y((()=>[h(s,{class:F(["bg-img",{"!-bottom-[200rpx]":1==k(n).bgGradient}])},{default:y((()=>[k(n).swiper.control&&k(n).swiper.list&&k(n).swiper.list[te.value].imageUrl?(b(),g(a,{key:0,src:k(d)(k(n).swiper.list[te.value].imageUrl),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"])):(b(),g(s,{key:1,class:"w-full h-full bg-[#fff]"})),h(s,{class:"bg-img-box",style:_(k(K))},null,8,["style"])])),_:1},8,["class"]),h(s,{class:"fixed-wrap",style:_(k(G))},{default:y((()=>["style-1"==k(n).search.style?(b(),g(s,{key:0,class:"diy-search-wrap relative z-10",onClick:o[1]||(o[1]=e=>k(r).toRedirect(k(n).search.link)),style:_(ue.value)},{default:y((()=>[k(n).search.logo?(b(),g(s,{key:0,class:"img-wrap"},{default:y((()=>[h(a,{src:k(d)(k(n).search.logo),mode:"aspectFit"},null,8,["src"])])),_:1})):S("v-if",!0),h(s,{class:"search-content",style:_({backgroundColor:k(n).search.bgColor}),onClick:o[0]||(o[0]=N((e=>k(r).toRedirect(k(n).search.link)),["stop"]))},{default:y((()=>[h(i,{class:"input-content text-[#fff] text-[24rpx] leading-[68rpx]",style:_({color:k(n).search.color})},{default:y((()=>[w(C(k(Q)?k(n).search.text:""),1)])),_:1},8,["style"]),h(i,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 w-[80rpx] h-[52rpx] flex items-center justify-center rounded-[50rpx] text-[28rpx] text-[#fff]",style:_({backgroundColor:k(n).search.btnBgColor,color:k(n).search.btnColor})},null,8,["style"]),k(Q)?S("v-if",!0):(b(),g(c,{key:0,class:"swiper-wrap",interval:1e3*k(n).search.hotWord.interval,autoplay:"true",vertical:"true",circular:"true"},{default:y((()=>[(b(!0),R(B,null,z(k(n).search.hotWord.list,(e=>(b(),g(u,{class:"swiper-item",key:e.id},{default:y((()=>[h(s,{class:"leading-[64rpx] text-[24rpx]",style:_({color:k(n).search.color})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["style"])])),_:2},1024)))),128))])),_:1},8,["interval"]))])),_:1},8,["style"])])),_:1},8,["style"])):S("v-if",!0),"style-2"==k(n).search.style?(b(),g(s,{key:1,class:"diy-search-wrap style-2 relative z-10",onClick:o[5]||(o[5]=e=>k(r).toRedirect(k(n).search.link))},{default:y((()=>[h(s,{class:"flex items-center",style:_(ue.value)},{default:y((()=>[k(n).search.logo?(b(),g(s,{key:0,class:"img-wrap"},{default:y((()=>[h(a,{src:k(d)(k(n).search.logo),mode:"aspectFit"},null,8,["src"])])),_:1})):S("v-if",!0),k(n).search.subTitle.text?(b(),g(s,{key:1,style:_(k(ae)),class:"max-w-[360rpx] text-[24rpx] h-[38rpx] rounded-r-[20rpx] rounded-t-[20rpx] rounded-bl-[2rpx]"},{default:y((()=>[h(s,{class:"truncate leading-[38rpx] h-[38rpx] px-[12rpx]"},{default:y((()=>[w(C(k(n).search.subTitle.text),1)])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"]),h(s,{class:"flex items-center w-full mt-[16rpx]"},{default:y((()=>[k(l).diyAddressInfo?(b(),g(s,{key:0,onClick:o[2]||(o[2]=N((e=>k(H).reposition()),["stop"])),style:_({color:k(n).search.positionColor}),class:"mr-[30rpx]"},{default:y((()=>[h(s,{class:"flex items-baseline font-500"},{default:y((()=>[h(i,{class:"text-[24rpx] mr-[2rpx]"},{default:y((()=>[w(C(k(l).diyAddressInfo.city),1)])),_:1}),h(i,{class:"iconfont iconxiaV6xx !text-[24rpx]"})])),_:1}),k(l).diyAddressInfo.community?(b(),g(s,{key:0,class:"text-[18rpx] mt-[10rpx] truncate max-w-[160rpx]"},{default:y((()=>[w(C(k(l).diyAddressInfo.community),1)])),_:1})):S("v-if",!0)])),_:1},8,["style"])):(b(),g(s,{key:1,onClick:o[3]||(o[3]=N((e=>k(H).reposition()),["stop"])),class:"text-[24rpx] mr-[30rpx] truncate max-w-[160rpx]",style:_({color:k(n).search.positionColor})},{default:y((()=>[w(C(k(l).defaultPositionAddress),1)])),_:1},8,["style"])),h(s,{class:"search-content",style:_({backgroundColor:k(n).search.bgColor}),onClick:o[4]||(o[4]=N((e=>k(r).toRedirect(k(n).search.link)),["stop"]))},{default:y((()=>[h(i,{class:"input-content text-[#fff] text-[24rpx] leading-[68rpx]",style:_({color:k(n).search.color})},{default:y((()=>[w(C(k(Q)?k(n).search.text:""),1)])),_:1},8,["style"]),h(i,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 w-[80rpx] h-[52rpx] flex items-center justify-center rounded-[50rpx] text-[28rpx] text-[#fff]",style:_({backgroundColor:k(n).search.btnBgColor,color:k(n).search.btnColor})},null,8,["style"]),k(Q)?S("v-if",!0):(b(),g(c,{key:0,class:"swiper-wrap",interval:1e3*k(n).search.hotWord.interval,autoplay:"true",vertical:"true",circular:"true"},{default:y((()=>[(b(!0),R(B,null,z(k(n).search.hotWord.list,(e=>(b(),g(u,{class:"swiper-item",key:e.id},{default:y((()=>[h(s,{class:"leading-[64rpx] text-[24rpx]",style:_({color:k(n).search.color})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["style"])])),_:2},1024)))),128))])),_:1},8,["interval"]))])),_:1},8,["style"])])),_:1})])),_:1})):S("v-if",!0),k(n).tab.control?(b(),g(s,{key:2,class:"tab-list-wrap relative z-10"},{default:y((()=>[h(p,{"scroll-x":"true",class:"scroll-wrap","scroll-into-view":"a"+re.value},{default:y((()=>[h(s,{onClick:o[6]||(o[6]=e=>se({source:"home"},-1)),class:F(["scroll-item",[{active:-1==re.value}]])},{default:y((()=>[h(s,{class:"name",style:_({color:Z(-1==re.value)})},{default:y((()=>[w("首页")])),_:1},8,["style"]),S(' <view class="line" :style="{\'background-color\': getTabColor(currTabIndex == -1)}" v-if="currTabIndex == -1"></view> ')])),_:1},8,["class"]),(b(!0),R(B,null,z(k(n).tab.list,((e,t)=>(b(),g(s,{class:F(["scroll-item",[{active:t==re.value}]]),onClick:l=>se(e,t),id:"a"+t,key:t},{default:y((()=>[h(s,{class:"name",style:_({color:Z(t==re.value)})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["style"]),S(' <view class="line" :style="{\'background-color\': getTabColor(index == currTabIndex)}" v-if="index == currTabIndex"></view> ')])),_:2},1032,["class","onClick","id"])))),128))])),_:1},8,["scroll-into-view"]),k(n).tab.list.length?(b(),g(s,{key:0,class:"absolute tab-btn iconfont icona-yingyongliebiaoV6xx-32",onClick:o[7]||(o[7]=e=>ie.value=!0)})):S("v-if",!0)])),_:1})):S("v-if",!0),q.value?(b(),g(s,{key:3,class:"bg-img"},{default:y((()=>[k(n).swiper.control&&k(n).swiper.list&&k(n).swiper.list[te.value].imageUrl?(b(),g(a,{key:0,src:k(d)(k(n).swiper.list[te.value].imageUrl),mode:"widthFix",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"])):(b(),g(s,{key:1,class:"w-full h-full bg-[#fff]"}))])),_:1})):S("v-if",!0)])),_:1},8,["style"]),S(" 解决fixed定位后导航栏塌陷的问题 "),"decorate"!=k(r).mode?(b(),R(B,{key:0},["fixed"==k(n).positionWay&&null!=t.scrollBool&&-1!=t.scrollBool?(b(),g(s,{key:0,class:"u-navbar-placeholder",style:_({width:"100%",paddingTop:O.value})},null,8,["style"])):S("v-if",!0)],64)):S("v-if",!0),S(" 轮播图 "),h(s,{class:F(["relative",{"mx-[20rpx]":k(J)&&"style-3"!=k(n).swiper.swiperStyle,"swiper-style-3":"style-3"==k(n).swiper.swiperStyle}]),style:_(X())},{default:y((()=>[k(n).swiper.control?(b(),g(c,{key:0,class:F(["swiper",{"swiper-left":"left"==k(n).swiper.indicatorAlign,"swiper-right":"right"==k(n).swiper.indicatorAlign,"ns-indicator-dots":"style-2"==k(n).swiper.indicatorStyle,"ns-indicator-dots-three":"style-3"==k(n).swiper.indicatorStyle}]),style:_({height:k(ee)}),autoplay:"true",circular:"true",onChange:le,"previous-margin":k(J)?0:"26rpx","next-margin":k(J)?0:"26rpx",interval:1e3*k(n).swiper.interval,"indicator-dots":k(me),"indicator-color":k(n).swiper.indicatorColor,"indicator-active-color":k(n).swiper.indicatorActiveColor},{default:y((()=>[(b(!0),R(B,null,z(k(n).swiper.list,((e,t)=>(b(),g(u,{class:"swiper-item",key:e.id,style:_(k(oe))},{default:y((()=>[h(s,{onClick:t=>k(r).toRedirect(e.link)},{default:y((()=>[h(s,{class:"item",style:_({height:k(ee)})},{default:y((()=>[e.imageUrl?(b(),g(a,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",style:_(k(oe)),class:F(["w-full h-full",{"swiper-animation":te.value!=t&&"style-3"!=k(n).swiper.indicatorStyle}]),"show-menu-by-longpress":!0},null,8,["src","style","class"])):(b(),g(a,{key:1,src:k(d)("static/resource/images/diy/figure.png"),style:_(k(oe)),mode:"scaleToFill",class:F(["w-full h-full",{"swiper-animation":te.value!=t&&"style-3"!=k(n).swiper.indicatorStyle}]),"show-menu-by-longpress":!0},null,8,["src","style","class"]))])),_:2},1032,["style"])])),_:2},1032,["onClick"])])),_:2},1032,["style"])))),128))])),_:1},8,["style","class","previous-margin","next-margin","interval","indicator-dots","indicator-color","indicator-active-color"])):S("v-if",!0)])),_:1},8,["class","style"]),S(" 分类展开 "),h(f,{safeAreaInsetTop:!0,show:ie.value,mode:"top",onClose:o[9]||(o[9]=e=>ie.value=!1)},{default:y((()=>[h(s,{class:"text-sm px-[30rpx] pt-3",style:_({"padding-top":k(de).top+"px"})},{default:y((()=>[w("全部分类")])),_:1},8,["style"]),h(s,{class:"flex flex-wrap pl-[30rpx] pt-[30rpx]"},{default:y((()=>[h(s,{onClick:o[8]||(o[8]=e=>se({source:"home"},-1)),class:F(["px-[26rpx] border-[2rpx] border-solid border-transparent h-[60rpx] mr-[30rpx] mb-[30rpx] flex items-center justify-center bg-[#F4F4F4] rounded-[8rpx] text-xs",{"tab-select-popup":-1==re.value}])},{default:y((()=>[w("首页")])),_:1},8,["class"]),(b(!0),R(B,null,z(k(n).tab.list,((e,t)=>(b(),g(i,{onClick:l=>se(e,t),key:t,class:F(["px-[26rpx] border-[2rpx] border-solid border-transparent h-[60rpx] mr-[30rpx] mb-[30rpx] flex items-center justify-center bg-[#F4F4F4] rounded-[8rpx] text-xs",{"tab-select-popup":t==re.value}])},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["onClick","class"])))),128))])),_:1})])),_:1},8,["show"])])),_:1}),S(" 展示微页面数据 "),"diy_page"==ne.value?(b(),g(s,{key:0,class:"child-diy-template-wrap bg-index"},{default:y((()=>[h(Ka,{data:pe},null,8,["data"])])),_:1})):S("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-f21c8f11"]]),Vt=Pe(n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=i((()=>{var e="";return e+="width:"+2*o.value.imageSize+"rpx;",e+="height:"+2*o.value.imageSize+"rpx;",e+="border-radius:"+2*o.value.aroundRadius+"rpx;"})),n=i((()=>{let e="";return o.value.offset&&("lowerRight"==o.value.bottomPosition||"lowerLeft"==o.value.bottomPosition?e+="translateY("+2*-o.value.offset+"rpx)":"upperRight"!=o.value.bottomPosition&&"upperLeft"!=o.value.bottomPosition||(e+="translateY("+2*o.value.offset+"rpx)")),o.value.lateralOffset&&("upperLeft"==o.value.bottomPosition||"lowerLeft"==o.value.bottomPosition?e+=" translateX("+2*o.value.lateralOffset+"rpx)":"upperRight"!=o.value.bottomPosition&&"lowerRight"!=o.value.bottomPosition||(e+=" translateX("+2*-o.value.lateralOffset+"rpx)")),e=`transform: ${e};`,e})),c=u(!0),p=u(null);return i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e+="transition: right .25s;box-shadow:0px 32rpx 96rpx 32rpx rgba(0, 0, 0, .08), 0px 24rpx 64px rgba(0, 0, 0, .12), 0px 16rpx 32rpx -16rpx rgba(0, 0, 0, .16);",e+=c.value?"transition-delay: 0.25s;":"right:-"+(2*o.value.imageSize+24)+"rpx !important;"})),i((()=>{var e="transition: right .25s;background: rgba(0, 0, 0, 0.5);";return e+=c.value?"":"right:-32rpx !important;transition-delay: 0.25s;"})),V((()=>{"style-2"===o.value.style&&(p&&clearTimeout(p.value),c.value=!1,p.value=setTimeout((()=>{c.value=!0,clearTimeout(p.value)}),200))})),(e,t)=>{const s=$,i=E;return b(),g(i,{class:F(["float-btn fixed z-1000",[k(o).style,k(o).bottomPosition,"decorate"==k(l).mode?"float-btn-border":""]]),style:_(k(n))},{default:y((()=>["style-1"===k(o).style?(b(),g(i,{key:0,class:"flex flex-col items-center p-[24rpx]",style:_(k(a))},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,((e,t)=>(b(),g(i,{key:t,onClick:t=>k(l).toRedirect(e.link),class:F({"flex items-center justify-center":!0,"mb-[20rpx]":k(o).list.length!=t+1}),style:_(k(r))},{default:y((()=>[e&&e.imageUrl?(b(),g(s,{key:0,style:_(k(r)),src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["style","src"])):(b(),g(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFit",style:_(k(r))},null,8,["src","style"]))])),_:2},1032,["onClick","class","style"])))),128))])),_:1},8,["style"])):S("v-if",!0),S(' <view v-if="diyComponent.style===\'style-2\'" class="relative w-[3rpx] h-[3rpx]">\n          <view class="py-[14rpx] overflow-hidden absolute right-[25rpx] top-[1rpx] transform -translate-y-1/2" :style="styleTwoWarpCss">\n            <swiper :style="{\'width\':diyComponent.imageSize * 2+24+\'rpx\',\'height\':diyComponent.imageSize * 2+44+\'rpx !important\',}" circular>\n              <swiper-item v-for="(item,index) in diyComponent.list" :key="index">\n                <view @click="diyStore.toRedirect(item.link)" class="px-[12rpx] flex flex-col items-center justify-center">\n                  <image v-if="item && item.imageUrl" :style="floatBtnItemCss" :src="img(item.imageUrl)" mode="aspectFit" />\n                  <image v-else :src="img(\'static/resource/images/diy/figure.png\')" mode="aspectFit" :style="floatBtnItemCss"/>\n                  <view class="text-[24rpx] text-[303133] text-center mt-[20rpx]">{{ item.link.title }}</view>\n                </view>\n              </swiper-item>\n            </swiper>\n          </view>\n          <view class="w-[60rpx] h-[60rpx] absolute right-[-64rpx] top-[1rpx] transform -translate-y-1/2 rounded-[30rpx] flex items-center" :style="styleTwoSphere">\n            <text class="!text-[60rpx] iconfont iconxiaolian-1 text-[var(--primary-color)] font-400  transform rotate-90 translate-x-[-13rpx]"></text>\n          </view>\n        </view> ')])),_:1},8,["class","style"])}}}),[["__scopeId","data-v-7dd4888c"]]),Ht=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(),r=u(null),n=u(!1),f=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),m=i((()=>l.global)),T=u({id:0,name:"",mobile:"",province_id:0,city_id:0,district_id:0,lat:"",lng:"",address:"",address_name:"",full_address:"",is_default:0,area:""}),$=i((()=>{let e="请选择";return"province/city/district/address"==f.value.addressFormat?e+="省/市/区/街道":"province/city/district/street"==f.value.addressFormat?e+="省/市/区/街道(镇)":"province/city/district"==f.value.addressFormat?e+="省/市/区(县)":"province/city"==f.value.addressFormat?e+="省/市":e+="省份",e})),I=i((()=>{var e="";return e+="position:relative;",f.value.componentStartBgColor&&(f.value.componentStartBgColor&&f.value.componentEndBgColor?e+=`background:linear-gradient(${f.value.componentGradientAngle},${f.value.componentStartBgColor},${f.value.componentEndBgColor});`:e+="background-color:"+f.value.componentStartBgColor+";"),f.value.componentBgUrl&&(e+=`background-image:url('${d(f.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),f.value.topRounded&&(e+="border-top-left-radius:"+2*f.value.topRounded+"rpx;"),f.value.topRounded&&(e+="border-top-right-radius:"+2*f.value.topRounded+"rpx;"),f.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*f.value.bottomRounded+"rpx;"),f.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*f.value.bottomRounded+"rpx;"),e})),M=e=>{!n.value||T.value.province_id!=e.province.id&&T.value.city_id==e.city.id&&T.value.district_id==e.district.id||(T.value.lat="",T.value.lng=""),T.value.province_id=e.province.id||0,T.value.city_id=e.city.id||0,T.value.district_id=e.district.id||0,T.value.area=`${e.province.name||""}${e.city.name||""}${e.district.name||""}`,f.value.field.value=T.value.area,n.value=!1};c((()=>{A(),"decorate"==o.mode&&p((()=>f.value),((e,t)=>{e&&"FormAddress"==e.componentName&&A()}))}));const A=()=>{},j=()=>{n.value=!0,a.value.open()},N=()=>{let e=[];if(f.value.autofill){let t={title:"已自动填充"};e.push(t)}if(f.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},W=()=>{f.value.field.value=""},U=i((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return f.value.field.required&&""==f.value.field.value&&"decorate"!=o.mode&&(e.code=!1,e.message="111"),r.value=e,e},reset:W}),(e,t)=>{const l=D,n=E,s=L,i=O,d=x(v("area-select"),Oe);return b(),R(B,null,[k(f).viewFormDetail?(b(),g(n,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(m).completeLayout?(b(),g(n,{key:0,class:"base-layout-one"},{default:y((()=>[h(n,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(f).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(f).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(m).completeLayout?(b(),g(n,{key:1,class:"base-layout-two"},{default:y((()=>[h(n,{class:"detail-two-content"},{default:y((()=>[h(n,null,{default:y((()=>[w(C(k(f).field.name),1)])),_:1}),h(n,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(f).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(n,{key:1,style:_(k(I)),class:"form-item-frame"},{default:y((()=>["style-1"==k(m).completeLayout?(b(),g(n,{key:0,class:"base-layout-one"},{default:y((()=>[h(n,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(f).textColor,"font-size":2*k(f).fontSize+"rpx","font-weight":k(f).fontWeight})},{default:y((()=>[w(C(k(f).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(f).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(f).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(f).field.remark.text?(b(),g(n,{key:0,class:"layout-one-remark",style:_({color:k(f).field.remark.color,fontSize:2*k(f).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(f).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),r.value&&!r.value.code?(b(),g(n,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),h(n,{class:"flex layout-one-content justify-between items-center"},{default:y((()=>[h(s,{type:"text",class:"flex-1",placeholder:k($),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(f).fontSize+"rpx"},style:_({color:k(f).textColor,"font-size":2*k(f).fontSize+"rpx"}),modelValue:k(f).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(f).field.value=e),disabled:k(U),onClick:j},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),k(f).field.value?(b(),g(n,{key:0,class:"text-[var(--primary-color)]",onClick:W},{default:y((()=>[w(" 清除 ")])),_:1})):S("v-if",!0)])),_:1}),"province/city/district/address"==k(f).addressFormat?(b(),g(i,{key:2,type:"textarea",class:"layout-one-content mt-2 w-full",placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(f).fontSize+"rpx"},placeholder:"详细地址(如小区门牌号)",disabled:k(U)},null,8,["placeholder-style","disabled"])):S("v-if",!0),N().length?(b(),g(n,{key:3,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(N(),((t,l)=>(b(),g(n,{key:l,onClick:l=>e.eventFn(t.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(m).completeLayout?(b(),g(n,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(f).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(n,{class:F(["layout-two-wrap",{"no-border":!k(m).borderControl}])},{default:y((()=>[h(n,{class:F(["layout-two-label",{"justify-start":"left"==k(m).completeAlign,"justify-end":"right"==k(m).completeAlign}])},{default:y((()=>[k(f).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(f).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(f).textColor,"font-size":2*k(f).fontSize+"rpx","font-weight":k(f).fontWeight})},{default:y((()=>[w(C(k(f).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{type:"text",class:"layout-two-content no-flex",placeholder:k($),placeholderClass:"layout-two-input-placeholder",onClick:j,"placeholder-style":{"font-size":2*k(f).fontSize+"rpx"},style:_({color:k(f).textColor,"font-size":2*k(f).fontSize+"rpx"}),modelValue:k(f).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(f).field.value=e),disabled:k(U)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),"province/city/district/address"==k(f).addressFormat?(b(),g(i,{key:1,type:"textarea",class:"layout-one-content p-2 mt-2 w-full",placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(f).fontSize+"rpx"},placeholder:"详细地址(如小区门牌号)",disabled:k(U)},null,8,["placeholder-style","disabled"])):S("v-if",!0),r.value&&!r.value.code?(b(),g(n,{key:2,class:"layout-two-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),k(f).field.remark.text?(b(),g(n,{key:3,class:"layout-two-remark",style:_({color:k(f).field.remark.color,fontSize:2*k(f).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(f).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),N().length?(b(),g(n,{key:4,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(N(),((t,l)=>(b(),g(n,{key:l,onClick:l=>e.eventFn(t.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(b(),g(n,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"])),h(d,{ref_key:"areaRef",ref:a,onComplete:M,"area-id":T.value.district_id},null,8,["area-id"])],64)}}}),[["__scopeId","data-v-7ae9807f"]]),Lt=Pe(n({__name:"index",props:["data"],setup(e,{expose:t}){const l=e,o=i((()=>l.data||"已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息")),a=u(!1),r=()=>{a.value=!1};return t({open:()=>{a.value=!0}}),(e,t)=>{const l=E,n=D,s=x(v("u-popup"),Ve);return b(),g(l,{onTouchmove:t[0]||(t[0]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(s,{show:a.value,onClose:r,zIndex:"500",mode:"center",round:8},{default:y((()=>[h(l,{class:"flex flex-col items-center w-[640rpx] pt-[50rpx]"},{default:y((()=>[h(l,{class:"text-[32rpx] font-bold"},{default:y((()=>[w(C(k(H)("diyForm.prompt")),1)])),_:1}),h(l,{class:"text-center px-[40rpx] py-[30rpx] leading-[1.5] min-h-[90rpx]"},{default:y((()=>[w(C(k(o)),1)])),_:1}),h(l,{class:"flex items-center justify-center border-solid border-[0] border-t-[2rpx] border-[#e6e6e6] w-[100%] h-[90rpx] text-[28rpx]"},{default:y((()=>[h(n,{onClick:r},{default:y((()=>[w(C(k(H)("diyForm.know")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-4bdc5551"]]),Ot=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u([]),r=u(!1),n=u([]),f=u(null),m=u(null),T=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),$=i((()=>l.global)),M=i((()=>`${T.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),A=()=>{let e=[];if(T.value.autofill){let t={title:"已自动填充"};e.push(t)}if(T.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},j=e=>{"privacy"==e&&m.value.open()},N=i((()=>{var e="";return e+="position:relative;",T.value.componentStartBgColor&&T.value.componentEndBgColor?e+=`background:linear-gradient(${T.value.componentGradientAngle},${T.value.componentStartBgColor},${T.value.componentEndBgColor});`:T.value.componentStartBgColor?e+="background-color:"+T.value.componentStartBgColor+";":T.value.componentEndBgColor&&(e+="background-color:"+T.value.componentEndBgColor+";"),T.value.componentBgUrl&&(e+=`background-image:url('${d(T.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),T.value.topRounded&&(e+="border-top-left-radius:"+2*T.value.topRounded+"rpx;"),T.value.topRounded&&(e+="border-top-right-radius:"+2*T.value.topRounded+"rpx;"),T.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*T.value.bottomRounded+"rpx;"),T.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*T.value.bottomRounded+"rpx;"),e}));c((()=>{W(),"decorate"==o.mode&&p((()=>T.value),((e,t)=>{e&&"FormCheckbox"==e.componentName&&W()})),"style-3"==T.value.style&&(n.value=T.value.field.value.map((e=>e.id))),T.value.field.value.length>0&&(a.value=T.value.field.value.map((e=>e.id)))}));const W=()=>{},U=i((()=>{let e="";return e+=`请选择${T.value.field.name}`,e})),Y=u([]),P=()=>{r.value=!1,n.value=Y.value.map((e=>e.id))},V=()=>{r.value=!1;const e=T.value.options.filter((e=>n.value.includes(e.id)));T.value.field.value=e.map((e=>({id:e.id,text:e.text})))},L=i((()=>"decorate"===o.mode)),O=()=>{L.value||(Y.value=[...T.value.field.value],r.value=!0)},q=e=>{const t=T.value.options.filter((t=>e.includes(t.id)));T.value.field.value=t.map((e=>({id:e.id,text:e.text})))},G=e=>{if(L.value)return;const t=e.id,l=a.value.indexOf(t);l>-1?a.value.splice(l,1):a.value.push(t);const o=T.value.options.filter((e=>a.value.includes(e.id)));T.value.field.value=o.map((e=>({id:e.id,text:e.text})))};return t({verify:()=>{const e={code:!0,message:""};return T.value.field.required&&""==T.value.field.value.length&&"decorate"!=o.mode&&(e.code=!1,e.message=U.value),f.value=e,e},reset:()=>{n.value=[],a.value=[],T.value.field.value=[]}}),(e,t)=>{const l=E,s=D,i=x(v("u-checkbox"),qe),d=x(v("u-checkbox-group"),Ge),u=I,c=x(v("u-popup"),Ve);return k(T).viewFormDetail?(b(),g(l,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k($).completeLayout?(b(),g(l,{key:0,class:"base-layout-one"},{default:y((()=>[h(l,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(T).field.name),1)])),_:1}),h(l,{class:"flex detail-one-content-value"},{default:y((()=>[(b(!0),R(B,null,z(k(T).field.value,((e,t)=>(b(),g(l,{key:t},{default:y((()=>[w(C(e.text)+" ",1),t!==k(T).field.value.length-1?(b(),g(s,{key:0},{default:y((()=>[w("、")])),_:1})):S("v-if",!0)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k($).completeLayout?(b(),g(l,{key:1,class:"base-layout-two"},{default:y((()=>[h(l,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(T).field.name),1)])),_:1}),h(l,{class:"detail-two-content-value flex w-[80%] justify-end"},{default:y((()=>[(b(!0),R(B,null,z(k(T).field.value,((e,t)=>(b(),g(l,{key:t},{default:y((()=>[w(C(e.text)+" ",1),t!==k(T).field.value.length-1?(b(),g(s,{key:0},{default:y((()=>[w("、")])),_:1})):S("v-if",!0)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(l,{key:1,style:_(k(N)),class:"form-item-frame"},{default:y((()=>["style-1"==k($).completeLayout?(b(),g(l,{key:0,class:"base-layout-one"},{default:y((()=>[h(l,{class:"layout-one-label"},{default:y((()=>[h(s,{class:"name",style:_({color:k(T).textColor,"font-size":2*k(T).fontSize+"rpx","font-weight":k(T).fontWeight})},{default:y((()=>[w(C(k(T).field.name),1)])),_:1},8,["style"]),h(s,{class:"required"},{default:y((()=>[w(C(k(T).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(T).isHidden?(b(),g(s,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(T).field.remark.text?(b(),g(l,{key:0,class:"layout-one-remark",style:_({color:k(T).field.remark.color,fontSize:2*k(T).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(T).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),f.value&&!f.value.code?(b(),g(l,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(f.value.message),1)])),_:1})):S("v-if",!0),"style-1"==k(T).style?(b(),g(l,{key:2,class:"layout-one-content"},{default:y((()=>[h(d,{modelValue:a.value,"onUpdate:modelValue":t[0]||(t[0]=e=>a.value=e),onChange:q,iconPlacement:"left"},{default:y((()=>[(b(!0),R(B,null,z(k(T).options,((e,t)=>(b(),g(l,{key:t,class:"mr-[40rpx]"},{default:y((()=>[h(i,{activeColor:"var(--primary-color)",labelSize:2*k(T).fontSize+"rpx",labelColor:k(T).textColor,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1024)))),128))])),_:1},8,["modelValue"])])),_:1})):S("v-if",!0),"style-2"==k(T).style?(b(),g(d,{key:3,modelValue:a.value,"onUpdate:modelValue":t[1]||(t[1]=e=>a.value=e),onChange:q,iconPlacement:"left",placement:"column"},{default:y((()=>[(b(!0),R(B,null,z(k(T).options,((e,t)=>(b(),g(l,{key:t,onClick:t=>G(e),class:F(["layout-one-content mb-[16rpx]",{"!mb-[0]":k(T).options.length-1==t}])},{default:y((()=>[h(i,{class:"!m-[0]",activeColor:"var(--primary-color)",labelSize:2*k(T).fontSize+"rpx",labelColor:k(T).textColor,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick","class"])))),128))])),_:1},8,["modelValue"])):S("v-if",!0),"style-3"==k(T).style?(b(),g(l,{key:4,onClick:O,class:"layout-one-content justify-between"},{default:y((()=>[k(T).field.value&&k(T).field.value.length?(b(),g(l,{key:0},{default:y((()=>[(b(!0),R(B,null,z(k(T).field.value,((e,t)=>(b(),g(s,{class:"mr-[10rpx] text-[28rpx]",style:_({color:k(T).textColor,"font-size":2*k(T).fontSize+"rpx"})},{default:y((()=>[w(C(e.text),1),t!==k(T).field.value.length-1?(b(),g(s,{key:0},{default:y((()=>[w(",")])),_:1})):S("v-if",!0)])),_:2},1032,["style"])))),256))])),_:1})):(b(),g(s,{key:1,class:"text-[28rpx] text-[#999]",style:_({"font-size":2*k(T).fontSize+"rpx"})},{default:y((()=>[w(C(k(U)),1)])),_:1},8,["style"])),h(s,{class:F(["nc-iconfont nc-icon-xiaV6xx pull-down-arrow text-[#666]",{selected:r.value}]),style:_({"font-size":2*k(T).fontSize+2+"rpx !important"})},null,8,["class","style"])])),_:1})):S("v-if",!0),A().length?(b(),g(l,{key:5,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(A(),((e,t)=>(b(),g(l,{key:t,onClick:t=>j(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k($).completeLayout?(b(),g(l,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(T).isHidden?(b(),g(s,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(l,{class:F(["layout-two-wrap",{"!pb-[20rpx]":("style-2"==k(T).style||"style-3"==k(T).style)&&k($).borderControl,"no-border":!k($).borderControl}])},{default:y((()=>[h(l,{class:F(["layout-two-label",{"justify-start":"left"==k($).completeAlign,"justify-end":"right"==k($).completeAlign}])},{default:y((()=>[k(T).field.required?(b(),g(s,{key:0,class:"required"},{default:y((()=>[w(C(k(T).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(s,{class:"name",style:_({color:k(T).textColor,"font-size":2*k(T).fontSize+"rpx","font-weight":k(T).fontWeight})},{default:y((()=>[w(C(k(T).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),"style-1"==k(T).style?(b(),g(l,{key:0,class:"layout-two-content"},{default:y((()=>[h(d,{modelValue:a.value,"onUpdate:modelValue":t[2]||(t[2]=e=>a.value=e),onChange:q,iconPlacement:"left",class:"justify-end"},{default:y((()=>[(b(!0),R(B,null,z(k(T).options,((e,t)=>(b(),g(l,{class:"ml-[30rpx]"},{default:y((()=>[(b(),g(i,{activeColor:"var(--primary-color)",labelSize:2*k(T).fontSize+"rpx",labelColor:k(T).textColor,key:t,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"]))])),_:2},1024)))),256))])),_:1},8,["modelValue"])])),_:1})):S("v-if",!0),"style-2"==k(T).style?(b(),g(l,{key:1,class:"layout-two-content"},{default:y((()=>[h(l,{class:"justify-end w-full"},{default:y((()=>[h(d,{modelValue:a.value,"onUpdate:modelValue":t[3]||(t[3]=e=>a.value=e),placement:"column",onChange:q,iconPlacement:"left"},{default:y((()=>[(b(!0),R(B,null,z(k(T).options,((e,t)=>(b(),g(l,{key:t,onClick:t=>G(e),class:F(["border-solid border-[2rpx] border-[#e6e6e6] rounded-[10rpx] flex items-center h-[80rpx] mb-[16rpx] px-[16rpx] box-border",{"mb-[0]":k(T).options.length==t+1}])},{default:y((()=>[h(i,{activeColor:"var(--primary-color)",labelSize:2*k(T).fontSize+"rpx",labelColor:k(T).textColor,class:"!m-[0]",label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick","class"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})):S("v-if",!0),"style-3"==k(T).style?(b(),g(l,{key:2,class:"layout-two-content"},{default:y((()=>[h(l,{onClick:O,class:"px-[16rpx] box-border h-[80rpx] flex items-center justify-between border-solid border-[2rpx] border-[#e6e6e6] rounded-[10rpx] w-[100%]"},{default:y((()=>[k(T).field.value&&k(T).field.value.length?(b(),g(l,{key:0},{default:y((()=>[(b(!0),R(B,null,z(k(T).field.value,((e,t)=>(b(),g(s,{class:"mr-[10rpx] text-[28rpx]",style:_({color:k(T).textColor,"font-size":2*k(T).fontSize+"rpx"})},{default:y((()=>[w(C(e.text)+" ",1),t!==k(T).field.value.length-1?(b(),g(s,{key:0},{default:y((()=>[w(",")])),_:1})):S("v-if",!0)])),_:2},1032,["style"])))),256))])),_:1})):(b(),g(s,{key:1,class:"text-[28rpx] text-[#999]",style:_({"font-size":2*k(T).fontSize+"rpx"})},{default:y((()=>[w(C(k(U)),1)])),_:1},8,["style"])),h(s,{class:F(["nc-iconfont nc-icon-xiaV6xx pull-down-arrow text-[#666]",{selected:r.value}]),style:_({"font-size":2*k(T).fontSize+2+"rpx !important"})},null,8,["class","style"])])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["class"]),f.value&&!f.value.code?(b(),g(l,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(f.value.message),1)])),_:1})):S("v-if",!0),k(T).field.remark.text?(b(),g(l,{key:2,class:"layout-two-remark",style:_({color:k(T).field.remark.color,fontSize:2*k(T).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(T).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),A().length?(b(),g(l,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(A(),((e,t)=>(b(),g(l,{key:t,onClick:t=>j(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),S(" 样式三，下拉弹窗 "),h(c,{show:r.value,mode:"bottom",onClose:t[5]||(t[5]=e=>r.value=!1)},{default:y((()=>[h(l,{class:"p-[15rpx]"},{default:y((()=>[h(u,{"scroll-y":"true",class:"max-h-[450rpx] px-[14rpx] box-border"},{default:y((()=>[h(d,{modelValue:n.value,"onUpdate:modelValue":t[4]||(t[4]=e=>n.value=e),placement:"column",iconPlacement:"right"},{default:y((()=>[(b(!0),R(B,null,z(k(T).options,((e,t)=>(b(),g(l,{key:t,class:"border-solid border-[0] border-b-[2rpx] border-[#e6e6e6] py-[20rpx]"},{default:y((()=>[h(i,{activeColor:"var(--primary-color)",labelSize:"30rpx",labelColor:"#333",style:{width:"100%"},label:e.text,name:e.id},null,8,["label","name"])])),_:2},1024)))),128))])),_:1},8,["modelValue"])])),_:1}),h(l,{class:"flex items-center pt-[20rpx]"},{default:y((()=>[h(l,{onClick:P,class:"flex-1 flex justify-center h-[70rpx] leading-[70rpx] text-[#333] bg-[#eee] text-[26rpx] border-[0] font-500 rounded-[10rpx] mr-[20rpx]"},{default:y((()=>[w(C(k(H)("cancel")),1)])),_:1}),h(l,{onClick:V,class:"flex-1 flex justify-center bg-[var(--primary-color)] h-[70rpx] leading-[70rpx] text-[#fff] text-[26rpx] border-[0] rounded-[10rpx]"},{default:y((()=>[w(C(k(H)("confirm")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"]),"decorate"==k(o).mode?(b(),g(l,{key:2,class:"form-item-mask"})):S("v-if",!0),S(" 隐私弹窗 "),h(Lt,{ref_key:"formPrivacyRef",ref:m,data:k(M)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-3ff66877"]]);const qt=Pe({name:"u-button",mixins:[G,X,{props:{hairline:{type:Boolean,default:()=>q.button.hairline},type:{type:String,default:()=>q.button.type},size:{type:String,default:()=>q.button.size},shape:{type:String,default:()=>q.button.shape},plain:{type:Boolean,default:()=>q.button.plain},disabled:{type:Boolean,default:()=>q.button.disabled},loading:{type:Boolean,default:()=>q.button.loading},loadingText:{type:[String,Number],default:()=>q.button.loadingText},loadingMode:{type:String,default:()=>q.button.loadingMode},loadingSize:{type:[String,Number],default:()=>q.button.loadingSize},openType:{type:String,default:()=>q.button.openType},formType:{type:String,default:()=>q.button.formType},appParameter:{type:String,default:()=>q.button.appParameter},hoverStopPropagation:{type:Boolean,default:()=>q.button.hoverStopPropagation},lang:{type:String,default:()=>q.button.lang},sessionFrom:{type:String,default:()=>q.button.sessionFrom},sendMessageTitle:{type:String,default:()=>q.button.sendMessageTitle},sendMessagePath:{type:String,default:()=>q.button.sendMessagePath},sendMessageImg:{type:String,default:()=>q.button.sendMessageImg},showMessageCard:{type:Boolean,default:()=>q.button.showMessageCard},dataName:{type:String,default:()=>q.button.dataName},throttleTime:{type:[String,Number],default:()=>q.button.throttleTime},hoverStartTime:{type:[String,Number],default:()=>q.button.hoverStartTime},hoverStayTime:{type:[String,Number],default:()=>q.button.hoverStayTime},text:{type:[String,Number],default:()=>q.button.text},icon:{type:String,default:()=>q.button.icon},iconColor:{type:String,default:()=>q.button.icon},color:{type:String,default:()=>q.button.color}}}],data:()=>({}),computed:{bemClass(){return this.color?this.bem("button",["shape","size"],["disabled","plain","hairline"]):this.bem("button",["type","shape","size"],["disabled","plain","hairline"])},loadingColor(){return this.plain?this.color?this.color:Z[`u-${this.type}`]:"info"===this.type?"#c9c9c9":"rgb(200, 200, 200)"},iconColorCom(){return this.iconColor?this.iconColor:this.plain?this.color?this.color:this.type:"info"===this.type?"#000000":"#ffffff"},baseColor(){let e={};return this.color&&(e.color=this.plain?this.color:"white",this.plain||(e["background-color"]=this.color),-1!==this.color.indexOf("gradient")?(e.borderTopWidth=0,e.borderRightWidth=0,e.borderBottomWidth=0,e.borderLeftWidth=0,this.plain||(e.backgroundImage=this.color)):(e.borderColor=this.color,e.borderWidth="1px",e.borderStyle="solid")),e},nvueTextStyle(){let e={};return"info"===this.type&&(e.color="#323233"),this.color&&(e.color=this.plain?this.color:"white"),e.fontSize=this.textSize+"px",e},textSize(){let e=14,{size:t}=this;return"large"===t&&(e=16),"normal"===t&&(e=14),"small"===t&&(e=12),"mini"===t&&(e=10),e}},emits:["click","getphonenumber","getuserinfo","error","opensetting","launchapp","agreeprivacyauthorization"],methods:{addStyle:Q,clickHandler(){this.disabled||this.loading||K((()=>{this.$emit("click")}),this.throttleTime)},getphonenumber(e){this.$emit("getphonenumber",e)},getuserinfo(e){this.$emit("getuserinfo",e)},error(e){this.$emit("error",e)},opensetting(e){this.$emit("opensetting",e)},launchapp(e){this.$emit("launchapp",e)},agreeprivacyauthorization(e){this.$emit("agreeprivacyauthorization",e)}}},[["render",function(e,t,l,o,a,r){const n=x(v("u-loading-icon"),Xe),s=D,i=x(v("u-icon"),Ye),d=ee;return b(),g(d,{"hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":e.sendMessagePath,lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,onGetphonenumber:r.getphonenumber,onGetuserinfo:r.getuserinfo,onError:r.error,onOpensetting:r.opensetting,onLaunchapp:r.launchapp,onAgreeprivacyauthorization:r.agreeprivacyauthorization,"hover-class":e.disabled||e.loading?"":"u-button--active",class:F(["u-button u-reset-button",r.bemClass]),style:_([r.baseColor,r.addStyle(e.customStyle)]),onClick:r.clickHandler},{default:y((()=>[e.loading?(b(),R(B,{key:0},[h(n,{mode:e.loadingMode,size:1.15*e.loadingSize,color:r.loadingColor},null,8,["mode","size","color"]),h(s,{class:"u-button__loading-text",style:_([{fontSize:r.textSize+"px"}])},{default:y((()=>[w(C(e.loadingText||e.text),1)])),_:1},8,["style"])],64)):(b(),R(B,{key:1},[e.icon?(b(),g(i,{key:0,name:e.icon,color:r.iconColorCom,size:1.35*r.textSize,customStyle:{marginRight:"2px"}},null,8,["name","color","size"])):S("v-if",!0),J(e.$slots,"default",{},(()=>[h(s,{class:"u-button__text",style:_([{fontSize:r.textSize+"px"}])},{default:y((()=>[w(C(e.text),1)])),_:1},8,["style"])]),!0)],64))])),_:3},8,["hover-start-time","hover-stay-time","form-type","open-type","app-parameter","hover-stop-propagation","send-message-title","send-message-path","lang","data-name","session-from","send-message-img","show-message-card","onGetphonenumber","onGetuserinfo","onError","onOpensetting","onLaunchapp","onAgreeprivacyauthorization","hover-class","style","onClick","class"])}],["__scopeId","data-v-db65a0af"]]);const Gt=Pe({name:"u-calendar-header",mixins:[G,X],props:{title:{type:String,default:""},subtitle:{type:String,default:""},showTitle:{type:Boolean,default:!0},showSubtitle:{type:Boolean,default:!0}},data:()=>({}),methods:{name(){}}},[["render",function(e,t,l,o,a,r){const n=D,s=E;return b(),g(s,{class:"u-calendar-header u-border-bottom"},{default:y((()=>[l.showTitle?(b(),g(n,{key:0,class:"u-calendar-header__title"},{default:y((()=>[w(C(l.title),1)])),_:1})):S("v-if",!0),l.showSubtitle?(b(),g(n,{key:1,class:"u-calendar-header__subtitle"},{default:y((()=>[w(C(l.subtitle),1)])),_:1})):S("v-if",!0),h(s,{class:"u-calendar-header__weekdays"},{default:y((()=>[h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("一")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("二")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("三")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("四")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("五")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("六")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("日")])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-583c0179"]]),Xt={name:"u-calendar-month",mixins:[G,X],props:{showMark:{type:Boolean,default:!0},color:{type:String,default:"#3c9cff"},months:{type:Array,default:()=>[]},mode:{type:String,default:"single"},rowHeight:{type:[String,Number],default:58},maxCount:{type:[String,Number],default:1/0},startText:{type:String,default:"开始"},endText:{type:String,default:"结束"},defaultDate:{type:[Array,String,Date],default:null},minDate:{type:[String,Number],default:0},maxDate:{type:[String,Number],default:0},maxMonth:{type:[String,Number],default:2},readonly:{type:Boolean,default:()=>q.calendar.readonly},maxRange:{type:[Number,String],default:1/0},rangePrompt:{type:String,default:""},showRangePrompt:{type:Boolean,default:!0},allowSameDay:{type:Boolean,default:!1}},data:()=>({width:0,item:{},selected:[]}),watch:{selectedChange:{immediate:!0,handler(e){this.setDefaultDate()}}},computed:{selectedChange(){return[this.minDate,this.maxDate,this.defaultDate]},dayStyle(e,t,l){return(e,t,l)=>{const o={};let a=l.week;const r=Number(parseFloat(this.width/7).toFixed(3).slice(0,-1));return o.height=te(this.rowHeight),0===t&&(a=(0===a?7:a)-1,o.marginLeft=te(a*r)),"range"===this.mode&&(o.paddingLeft=0,o.paddingRight=0,o.paddingBottom=0,o.paddingTop=0),o}},daySelectStyle(){return(e,t,l)=>{let o=Ze(l.date).format("YYYY-MM-DD"),a={};if(this.selected.some((e=>this.dateSame(e,o)))&&(a.backgroundColor=this.color),"single"===this.mode)o===this.selected[0]&&(a.borderTopLeftRadius="3px",a.borderBottomLeftRadius="3px",a.borderTopRightRadius="3px",a.borderBottomRightRadius="3px");else if("range"===this.mode)if(this.selected.length>=2){const e=this.selected.length-1;this.dateSame(o,this.selected[0])&&(a.borderTopLeftRadius="3px",a.borderBottomLeftRadius="3px"),this.dateSame(o,this.selected[e])&&(a.borderTopRightRadius="3px",a.borderBottomRightRadius="3px"),Ze(o).isAfter(Ze(this.selected[0]))&&Ze(o).isBefore(Ze(this.selected[e]))&&(a.backgroundColor=le(this.color,"#ffffff",100)[90],a.opacity=.7)}else 1===this.selected.length&&(a.borderTopLeftRadius="3px",a.borderBottomLeftRadius="3px");else this.selected.some((e=>this.dateSame(e,o)))&&(a.borderTopLeftRadius="3px",a.borderBottomLeftRadius="3px",a.borderTopRightRadius="3px",a.borderBottomRightRadius="3px");return a}},textStyle(){return e=>{const t=Ze(e.date).format("YYYY-MM-DD"),l={};if(this.selected.some((e=>this.dateSame(e,t)))&&(l.color="#ffffff"),"range"===this.mode){const e=this.selected.length-1;Ze(t).isAfter(Ze(this.selected[0]))&&Ze(t).isBefore(Ze(this.selected[e]))&&(l.color=this.color)}return l}},getBottomInfo(){return(e,t,l)=>{const o=Ze(l.date).format("YYYY-MM-DD"),a=l.bottomInfo;if("range"===this.mode&&this.selected.length>0){if(1===this.selected.length)return this.dateSame(o,this.selected[0])?this.startText:a;{const e=this.selected.length-1;return this.dateSame(o,this.selected[0])&&this.dateSame(o,this.selected[1])&&1===e?`${this.startText}/${this.endText}`:this.dateSame(o,this.selected[0])?this.startText:this.dateSame(o,this.selected[e])?this.endText:a}}return a}}},mounted(){this.init()},methods:{init(){this.$emit("monthSelected",this.selected),this.$nextTick((()=>{oe(10).then((()=>{this.getWrapperWidth(),this.getMonthRect()}))}))},dateSame:(e,t)=>Ze(e).isSame(Ze(t)),getWrapperWidth(){this.$uGetRect(".u-calendar-month-wrapper").then((e=>{this.width=e.width}))},getMonthRect(){const e=this.months.map(((e,t)=>this.getMonthRectByPromise(`u-calendar-month-${t}`)));Promise.all(e).then((e=>{let t=1;const l=[];for(let o=0;o<this.months.length;o++)l[o]=t,t+=e[o].height;this.$emit("updateMonthTop",l)}))},getMonthRectByPromise(e){return new Promise((t=>{this.$uGetRect(`.${e}`).then((e=>{t(e)}))}))},clickHandler(e,t,l){if(this.readonly)return;this.item=l;const o=Ze(l.date).format("YYYY-MM-DD");if(l.disabled)return;let a=ae(this.selected);if("single"===this.mode)a=[o];else if("multiple"===this.mode)if(a.some((e=>this.dateSame(e,o)))){const e=a.findIndex((e=>e===o));a.splice(e,1)}else a.length<this.maxCount&&a.push(o);else if(0===a.length||a.length>=2)a=[o];else if(1===a.length){const e=a[0];if(Ze(o).isBefore(e))a=[o];else if(Ze(o).isAfter(e)){if(Ze(Ze(o).subtract(this.maxRange,"day")).isAfter(Ze(a[0]))&&this.showRangePrompt)return void(this.rangePrompt?re(this.rangePrompt):re(`选择天数不能超过 ${this.maxRange} 天`));a.push(o);const e=a[0],t=a[1],l=[];let r=0;do{l.push(Ze(e).add(r,"day").format("YYYY-MM-DD")),r++}while(Ze(e).add(r,"day").isBefore(Ze(t)));l.push(t),a=l}else{if(a[0]===o&&!this.allowSameDay)return;a.push(o)}}this.setSelected(a)},setDefaultDate(){if(!this.defaultDate){const e=[Ze().format("YYYY-MM-DD")];return this.setSelected(e,!1)}let e=[];const t=this.minDate||Ze().format("YYYY-MM-DD"),l=this.maxDate||Ze(t).add(this.maxMonth-1,"month").format("YYYY-MM-DD");if("single"===this.mode)e=ne.array(this.defaultDate)?[this.defaultDate[0]]:[Ze(this.defaultDate).format("YYYY-MM-DD")];else{if(!ne.array(this.defaultDate))return;e=this.defaultDate}e=e.filter((e=>Ze(e).isAfter(Ze(t).subtract(1,"day"))&&Ze(e).isBefore(Ze(l).add(1,"day")))),this.setSelected(e,!1)},setSelected(e,t=!0){this.selected=e,t&&this.$emit("monthSelected",this.selected,"tap")}}};const Zt={props:{title:{type:String,default:()=>q.calendar.title},showTitle:{type:Boolean,default:()=>q.calendar.showTitle},showSubtitle:{type:Boolean,default:()=>q.calendar.showSubtitle},mode:{type:String,default:()=>q.calendar.mode},startText:{type:String,default:()=>q.calendar.startText},endText:{type:String,default:()=>q.calendar.endText},customList:{type:Array,default:()=>q.calendar.customList},color:{type:String,default:()=>q.calendar.color},minDate:{type:[String,Number],default:()=>q.calendar.minDate},maxDate:{type:[String,Number],default:()=>q.calendar.maxDate},defaultDate:{type:[Array,String,Date,null],default:()=>q.calendar.defaultDate},maxCount:{type:[String,Number],default:()=>q.calendar.maxCount},rowHeight:{type:[String,Number],default:()=>q.calendar.rowHeight},formatter:{type:[Function,null],default:()=>q.calendar.formatter},showLunar:{type:Boolean,default:()=>q.calendar.showLunar},showMark:{type:Boolean,default:()=>q.calendar.showMark},confirmText:{type:String,default:()=>q.calendar.confirmText},confirmDisabledText:{type:String,default:()=>q.calendar.confirmDisabledText},show:{type:Boolean,default:()=>q.calendar.show},closeOnClickOverlay:{type:Boolean,default:()=>q.calendar.closeOnClickOverlay},readonly:{type:Boolean,default:()=>q.calendar.readonly},showConfirm:{type:Boolean,default:()=>q.calendar.showConfirm},maxRange:{type:[Number,String],default:()=>q.calendar.maxRange},rangePrompt:{type:String,default:()=>q.calendar.rangePrompt},showRangePrompt:{type:Boolean,default:()=>q.calendar.showRangePrompt},allowSameDay:{type:Boolean,default:()=>q.calendar.allowSameDay},round:{type:[Boolean,String,Number],default:()=>q.calendar.round},monthNum:{type:[Number,String],default:3}}};var Qt={lunarInfo:[19416,19168,42352,21717,53856,55632,91476,22176,39632,21970,19168,42422,42192,53840,119381,46400,54944,44450,38320,84343,18800,42160,46261,27216,27968,109396,11104,38256,21234,18800,25958,54432,59984,28309,23248,11104,100067,37600,116951,51536,54432,120998,46416,22176,107956,9680,37584,53938,43344,46423,27808,46416,86869,19872,42416,83315,21168,43432,59728,27296,44710,43856,19296,43748,42352,21088,62051,55632,23383,22176,38608,19925,19152,42192,54484,53840,54616,46400,46752,103846,38320,18864,43380,42160,45690,27216,27968,44870,43872,38256,19189,18800,25776,29859,59984,27480,23232,43872,38613,37600,51552,55636,54432,55888,30034,22176,43959,9680,37584,51893,43344,46240,47780,44368,21977,19360,42416,86390,21168,43312,31060,27296,44368,23378,19296,42726,42208,53856,60005,54576,23200,30371,38608,19195,19152,42192,118966,53840,54560,56645,46496,22224,21938,18864,42359,42160,43600,111189,27936,44448,84835,37744,18936,18800,25776,92326,59984,27424,108228,43744,41696,53987,51552,54615,54432,55888,23893,22176,42704,21972,21200,43448,43344,46240,46758,44368,21920,43940,42416,21168,45683,26928,29495,27296,44368,84821,19296,42352,21732,53600,59752,54560,55968,92838,22224,19168,43476,41680,53584,62034,54560],solarMonth:[31,28,31,30,31,30,31,31,30,31,30,31],Gan:["甲","乙","丙","丁","戊","己","庚","辛","壬","癸"],Zhi:["子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥"],Animals:["鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪"],solarTerm:["小寒","大寒","立春","雨水","惊蛰","春分","清明","谷雨","立夏","小满","芒种","夏至","小暑","大暑","立秋","处暑","白露","秋分","寒露","霜降","立冬","小雪","大雪","冬至"],sTermInfo:["9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","9778397bd19801ec9210c965cc920e","97b6b97bd19801ec95f8c965cc920f","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd197c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bcf97c3598082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd19801ec9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bd07f1487f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b97bd197c36c9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b70c9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","977837f0e37f149b0723b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0723b06bd","7f07e7f0e37f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f595b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e37f14998083b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14898082b0723b02d5","7f07e7f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66aa89801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e26665b66a449801e9808297c35","665f67f0e37f1489801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722"],nStr1:["日","一","二","三","四","五","六","七","八","九","十"],nStr2:["初","十","廿","卅"],nStr3:["正","二","三","四","五","六","七","八","九","十","冬","腊"],lYearDays:function(e){var t,l=348;for(t=32768;t>8;t>>=1)l+=this.lunarInfo[e-1900]&t?1:0;return l+this.leapDays(e)},leapMonth:function(e){return 15&this.lunarInfo[e-1900]},leapDays:function(e){return this.leapMonth(e)?65536&this.lunarInfo[e-1900]?30:29:0},monthDays:function(e,t){return t>12||t<1?-1:this.lunarInfo[e-1900]&65536>>t?30:29},solarDays:function(e,t){if(t>12||t<1)return-1;var l=t-1;return 1==l?e%4==0&&e%100!=0||e%400==0?29:28:this.solarMonth[l]},toGanZhiYear:function(e){var t=(e-3)%10,l=(e-3)%12;return 0==t&&(t=10),0==l&&(l=12),this.Gan[t-1]+this.Zhi[l-1]},toAstro:function(e,t){return"魔羯水瓶双鱼白羊金牛双子巨蟹狮子处女天秤天蝎射手魔羯".substr(2*e-(t<[20,19,21,21,21,22,23,23,23,23,22,22][e-1]?2:0),2)+"座"},toGanZhi:function(e){return this.Gan[e%10]+this.Zhi[e%12]},getTerm:function(e,t){if(e<1900||e>2100)return-1;if(t<1||t>24)return-1;var l=this.sTermInfo[e-1900],o=[parseInt("0x"+l.substr(0,5)).toString(),parseInt("0x"+l.substr(5,5)).toString(),parseInt("0x"+l.substr(10,5)).toString(),parseInt("0x"+l.substr(15,5)).toString(),parseInt("0x"+l.substr(20,5)).toString(),parseInt("0x"+l.substr(25,5)).toString()],a=[o[0].substr(0,1),o[0].substr(1,2),o[0].substr(3,1),o[0].substr(4,2),o[1].substr(0,1),o[1].substr(1,2),o[1].substr(3,1),o[1].substr(4,2),o[2].substr(0,1),o[2].substr(1,2),o[2].substr(3,1),o[2].substr(4,2),o[3].substr(0,1),o[3].substr(1,2),o[3].substr(3,1),o[3].substr(4,2),o[4].substr(0,1),o[4].substr(1,2),o[4].substr(3,1),o[4].substr(4,2),o[5].substr(0,1),o[5].substr(1,2),o[5].substr(3,1),o[5].substr(4,2)];return parseInt(a[t-1])},toChinaMonth:function(e){if(e>12||e<1)return-1;var t=this.nStr3[e-1];return t+="月"},toChinaDay:function(e){var t;switch(e){case 10:t="初十";break;case 20:t="二十";break;case 30:t="三十";break;default:t=this.nStr2[Math.floor(e/10)],t+=this.nStr1[e%10]}return t},getAnimal:function(e){return this.Animals[(e-4)%12]},solar2lunar:function(e,t,l){if(e<1900||e>2100)return-1;if(1900==e&&1==t&&l<31)return-1;if(e)o=new Date(e,parseInt(t)-1,l);else var o=new Date;var a,r=0,n=(e=o.getFullYear(),t=o.getMonth()+1,l=o.getDate(),(Date.UTC(o.getFullYear(),o.getMonth(),o.getDate())-Date.UTC(1900,0,31))/864e5);for(a=1900;a<2101&&n>0;a++)n-=r=this.lYearDays(a);n<0&&(n+=r,a--);var s=new Date,i=!1;s.getFullYear()==e&&s.getMonth()+1==t&&s.getDate()==l&&(i=!0);var d=o.getDay(),u=this.nStr1[d];0==d&&(d=7);var c=a,p=this.leapMonth(a),f=!1;for(a=1;a<13&&n>0;a++)p>0&&a==p+1&&0==f?(--a,f=!0,r=this.leapDays(c)):r=this.monthDays(c,a),1==f&&a==p+1&&(f=!1),n-=r;0==n&&p>0&&a==p+1&&(f?f=!1:(f=!0,--a)),n<0&&(n+=r,--a);var m=a,x=n+1,v=t-1,b=this.toGanZhiYear(c),g=this.getTerm(e,2*t-1),y=this.getTerm(e,2*t),h=this.toGanZhi(12*(e-1900)+t+11);l>=g&&(h=this.toGanZhi(12*(e-1900)+t+12));var _=!1,k=null;g==l&&(_=!0,k=this.solarTerm[2*t-2]),y==l&&(_=!0,k=this.solarTerm[2*t-1]);var w=Date.UTC(e,v,1,0,0,0,0)/864e5+25567+10,C=this.toGanZhi(w+l-1),S=this.toAstro(t,l);return{lYear:c,lMonth:m,lDay:x,Animal:this.getAnimal(c),IMonthCn:(f?"闰":"")+this.toChinaMonth(m),IDayCn:this.toChinaDay(x),cYear:e,cMonth:t,cDay:l,gzYear:b,gzMonth:h,gzDay:C,isToday:i,isLeap:f,nWeek:d,ncWeek:"星期"+u,isTerm:_,Term:k,astro:S}},lunar2solar:function(e,t,l,o){o=!!o;var a=this.leapMonth(e);if(this.leapDays(e),o&&a!=t)return-1;if(2100==e&&12==t&&l>1||1900==e&&1==t&&l<31)return-1;var r=this.monthDays(e,t),n=r;if(o&&(n=this.leapDays(e,t)),e<1900||e>2100||l>n)return-1;for(var s=0,i=1900;i<e;i++)s+=this.lYearDays(i);var d=0,u=!1;for(i=1;i<t;i++)d=this.leapMonth(e),u||d<=i&&d>0&&(s+=this.leapDays(e),u=!0),s+=this.monthDays(e,i);o&&(s+=r);var c=Date.UTC(1900,1,30,0,0,0),p=new Date(864e5*(s+l-31)+c),f=p.getUTCFullYear(),m=p.getUTCMonth()+1,x=p.getUTCDate();return this.solar2lunar(f,m,x)}};const Kt=Pe({name:"u-calendar",mixins:[G,X,Zt],components:{uHeader:Gt,uMonth:Pe(Xt,[["render",function(e,t,l,o,a,r){const n=D,s=E;return b(),g(s,{class:"u-calendar-month-wrapper",ref:"u-calendar-month-wrapper"},{default:y((()=>[(b(!0),R(B,null,z(l.months,((e,t)=>(b(),g(s,{key:t,class:F([`u-calendar-month-${t}`]),ref_for:!0,ref:`u-calendar-month-${t}`,id:`month-${t}`},{default:y((()=>[0!==t?(b(),g(n,{key:0,class:"u-calendar-month__title"},{default:y((()=>[w(C(e.year)+"年"+C(e.month)+"月",1)])),_:2},1024)):S("v-if",!0),h(s,{class:"u-calendar-month__days"},{default:y((()=>[l.showMark?(b(),g(s,{key:0,class:"u-calendar-month__days__month-mark-wrapper"},{default:y((()=>[h(n,{class:"u-calendar-month__days__month-mark-wrapper__text"},{default:y((()=>[w(C(e.month),1)])),_:2},1024)])),_:2},1024)):S("v-if",!0),(b(!0),R(B,null,z(e.date,((e,l)=>(b(),g(s,{class:F(["u-calendar-month__days__day",[e.selected&&"u-calendar-month__days__day__select--selected"]]),key:l,style:_([r.dayStyle(t,l,e)]),onClick:o=>r.clickHandler(t,l,e)},{default:y((()=>[h(s,{class:"u-calendar-month__days__day__select",style:_([r.daySelectStyle(t,l,e)])},{default:y((()=>[h(n,{class:F(["u-calendar-month__days__day__select__info",[e.disabled&&"u-calendar-month__days__day__select__info--disabled"]]),style:_([r.textStyle(e)])},{default:y((()=>[w(C(e.day),1)])),_:2},1032,["class","style"]),r.getBottomInfo(t,l,e)?(b(),g(n,{key:0,class:F(["u-calendar-month__days__day__select__buttom-info",[e.disabled&&"u-calendar-month__days__day__select__buttom-info--disabled"]]),style:_([r.textStyle(e)])},{default:y((()=>[w(C(r.getBottomInfo(t,l,e)),1)])),_:2},1032,["class","style"])):S("v-if",!0),e.dot?(b(),g(n,{key:1,class:"u-calendar-month__days__day__select__dot"})):S("v-if",!0)])),_:2},1032,["style"])])),_:2},1032,["style","onClick","class"])))),128))])),_:2},1024)])),_:2},1032,["class","id"])))),128))])),_:1},512)}],["__scopeId","data-v-0ae350d7"]])},data:()=>({months:[],monthIndex:0,listHeight:0,selected:[],scrollIntoView:"",scrollIntoViewScroll:"",scrollTop:0,innerFormatter:e=>e}),watch:{scrollIntoView:{immediate:!0,handler(e){}},selectedChange:{immediate:!0,handler(e){this.setMonth()}},show:{immediate:!0,handler(e){e?this.setMonth():this.scrollIntoView=""}}},computed:{innerMaxDate(){return ne.number(this.maxDate)?Number(this.maxDate):this.maxDate},innerMinDate(){return ne.number(this.minDate)?Number(this.minDate):this.minDate},selectedChange(){return[this.innerMinDate,this.innerMaxDate,this.defaultDate]},subtitle(){return this.months.length?`${this.months[this.monthIndex].year}年${this.months[this.monthIndex].month}月`:""},buttonDisabled(){return"range"===this.mode&&this.selected.length<=1}},mounted(){this.start=Date.now(),this.init()},emits:["confirm","close"],methods:{addUnit:te,setFormatter(e){this.innerFormatter=e},monthSelected(e,t="init"){if(this.selected=e,!this.showConfirm&&("multiple"===this.mode||"single"===this.mode||"range"===this.mode&&this.selected.length>=2)){if("init"===t)return;"tap"===t&&this.$emit("confirm",this.selected)}},init(){if(this.innerMaxDate&&this.innerMinDate&&new Date(this.innerMaxDate).getTime()<new Date(this.innerMinDate).getTime())return se();this.listHeight=5*this.rowHeight+30,this.setMonth()},close(){this.$emit("close")},confirm(){this.buttonDisabled||this.$emit("confirm",this.selected)},getMonths(e,t){const l=Ze(e).year(),o=Ze(e).month()+1;return 12*(Ze(t).year()-l)+(Ze(t).month()+1-o)+1},setMonth(){const e=this.innerMinDate||Ze().valueOf(),t=this.innerMaxDate||Ze(e).add(this.monthNum-1,"month").valueOf(),l=ie(1,this.monthNum,this.getMonths(e,t));this.months=[];for(let o=0;o<l;o++)this.months.push({date:new Array(Ze(e).add(o,"month").daysInMonth()).fill(1).map(((l,a)=>{let r=a+1;const n=Ze(e).add(o,"month").date(r).day(),s=Ze(e).add(o,"month").date(r).format("YYYY-MM-DD");let i="";if(this.showLunar){i=Qt.solar2lunar(Ze(s).year(),Ze(s).month()+1,Ze(s).date()).IDayCn}let d={day:r,week:n,disabled:Ze(s).isBefore(Ze(e).format("YYYY-MM-DD"))||Ze(s).isAfter(Ze(t).format("YYYY-MM-DD")),date:new Date(s),bottomInfo:i,dot:!1,month:Ze(e).add(o,"month").month()+1};return(this.formatter||this.innerFormatter)(d)})),month:Ze(e).add(o,"month").month()+1,year:Ze(e).add(o,"month").year()})},scrollIntoDefaultMonth(e){const t=this.months.findIndex((({year:t,month:l})=>`${t}-${l=ue(l)}`===e));-1!==t&&this.$nextTick((()=>{this.scrollIntoView=`month-${t}`,this.scrollIntoViewScroll=this.scrollIntoView}))},onScroll(e){const t=Math.max(0,e.detail.scrollTop);for(let l=0;l<this.months.length;l++)t>=(this.months[l].top||this.listHeight)&&(this.monthIndex=l,this.scrollIntoViewScroll=`month-${l}`)},updateMonthTop(e=[]){if(e.map(((e,t)=>{this.months[t].top=e})),!this.defaultDate){const e=Ze().format("YYYY-MM");return void this.scrollIntoDefaultMonth(e)}let t=Ze().format("YYYY-MM");t=ne.array(this.defaultDate)?Ze(this.defaultDate[0]).format("YYYY-MM"):Ze(this.defaultDate).format("YYYY-MM"),this.scrollIntoDefaultMonth(t)}}},[["render",function(e,t,l,o,a,r){const n=de("uHeader"),s=de("uMonth"),i=I,d=x(v("u-button"),qt),u=E,c=x(v("u-popup"),Ve);return b(),g(c,{show:e.show,mode:"bottom",closeable:"",onClose:r.close,round:e.round,closeOnClickOverlay:e.closeOnClickOverlay},{default:y((()=>[h(u,{class:"u-calendar"},{default:y((()=>[h(n,{title:e.title,subtitle:r.subtitle,showSubtitle:e.showSubtitle,showTitle:e.showTitle},null,8,["title","subtitle","showSubtitle","showTitle"]),h(i,{style:_({height:r.addUnit(a.listHeight)}),"scroll-y":"",onScroll:r.onScroll,"scroll-top":a.scrollTop,scrollIntoView:a.scrollIntoView},{default:y((()=>[h(s,{color:e.color,rowHeight:e.rowHeight,showMark:e.showMark,months:a.months,mode:e.mode,maxCount:e.maxCount,startText:e.startText,endText:e.endText,defaultDate:e.defaultDate,minDate:r.innerMinDate,maxDate:r.innerMaxDate,maxMonth:e.monthNum,readonly:e.readonly,maxRange:e.maxRange,rangePrompt:e.rangePrompt,showRangePrompt:e.showRangePrompt,allowSameDay:e.allowSameDay,ref:"month",onMonthSelected:r.monthSelected,onUpdateMonthTop:r.updateMonthTop},null,8,["color","rowHeight","showMark","months","mode","maxCount","startText","endText","defaultDate","minDate","maxDate","maxMonth","readonly","maxRange","rangePrompt","showRangePrompt","allowSameDay","onMonthSelected","onUpdateMonthTop"])])),_:1},8,["style","onScroll","scroll-top","scrollIntoView"]),e.showConfirm?J(e.$slots,"footer",{key:0},(()=>[h(u,{class:"u-calendar__confirm"},{default:y((()=>[h(d,{shape:"circle",text:r.buttonDisabled?e.confirmDisabledText:e.confirmText,color:e.color,onClick:r.confirm,disabled:r.buttonDisabled},null,8,["text","color","onClick","disabled"])])),_:1})]),!0):S("v-if",!0)])),_:3})])),_:3},8,["show","onClose","round","closeOnClickOverlay"])}],["__scopeId","data-v-43e34f6c"]]),Jt=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e;let o=new Date;o.setFullYear(o.getFullYear()+1);const a=u(o.getTime());let r=new Date;const n=u(r.getTime()),f=s(),m=u(!1),T=u(!1),$=u(null),I=i((()=>"decorate"==f.mode?f.value[l.index]:l.component)),M=i((()=>l.global)),A=()=>{let e=[];if(I.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},j=i((()=>{var e="";if(I.value.field.value.date){let t=I.value.field.value.timestamp;e=L(t),I.value.field.value.date=e,I.value.field.value.timestamp=ce(e)}else if(I.value.defaultControl){if("current"==I.value.dateWay)e=L();else if("diy"==I.value.dateWay){let t=I.value.field.default.timestamp||"";e=L(t)}I.value.field.value.date=e,I.value.field.value.timestamp=ce(e)}else e=I.value.placeholder;return e})),N=i((()=>{var e="";return e+="position:relative;",I.value.componentStartBgColor&&I.value.componentEndBgColor?e+=`background:linear-gradient(${I.value.componentGradientAngle},${I.value.componentStartBgColor},${I.value.componentEndBgColor});`:I.value.componentStartBgColor?e+="background-color:"+I.value.componentStartBgColor+";":I.value.componentEndBgColor&&(e+="background-color:"+I.value.componentEndBgColor+";"),I.value.componentBgUrl&&(e+=`background-image:url('${d(I.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),I.value.topRounded&&(e+="border-top-left-radius:"+2*I.value.topRounded+"rpx;"),I.value.topRounded&&(e+="border-top-right-radius:"+2*I.value.topRounded+"rpx;"),I.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*I.value.bottomRounded+"rpx;"),I.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*I.value.bottomRounded+"rpx;"),e}));c((()=>{W(),"decorate"==f.mode&&p((()=>I.value),((e,t)=>{e&&"FormDate"==e.componentName&&W()}))}));const W=()=>{},U=()=>{"decorate"!==f.mode&&("YYYY-MM-DD HH:mm"==I.value.dateFormat?T.value=!0:m.value=!0)},Y=e=>{I.value.field.value.date=e[0],I.value.field.value.timestamp=ce(e[0]),m.value=!1},P=e=>{I.value.field.value.date=e.value,I.value.field.value.timestamp=ce(e.value),T.value=!1},V=e=>(pe(Date.parse(e.date)/1e3,"year_month_day"),e),L=(e="",t=I.value.dateFormat)=>{let l=e?new Date(1e3*e):new Date,o=l.getFullYear(),a=String(l.getMonth()+1).padStart(2,"0"),r=String(l.getDate()).padStart(2,"0");const n=String(l.getHours()).padStart(2,"0"),s=String(l.getMinutes()).padStart(2,"0");let i="";return"YYYY年M月D日"==t?i=`${o}年${a}月${r}日`:"YYYY-MM-DD"==t?i=`${o}-${a}-${r}`:"YYYY/MM/DD"==t?i=`${o}/${a}/${r}`:"YYYY-MM-DD HH:mm"==t&&(i=`${o}-${a}-${r} ${n}:${s}`),i};return t({verify:()=>{const e={code:!0,message:""};return!I.value.field.required||I.value.field.value&&I.value.field.value.timestamp||(e.code=!1,e.message=`请选择${I.value.placeholder}`),$.value=e,e},reset:()=>{I.value.field.value.date="",I.value.field.value.timestamp=""}}),(e,t)=>{const l=D,o=E,r=x(v("u-calendar"),Kt),s=x(v("u-datetime-picker"),Qe);return k(I).viewFormDetail?(b(),g(o,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(M).completeLayout?(b(),g(o,{key:0,class:"base-layout-one"},{default:y((()=>[h(o,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(I).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(I).field.value.date),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(M).completeLayout?(b(),g(o,{key:1,class:"base-layout-two"},{default:y((()=>[h(o,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(I).field.name),1)])),_:1}),h(o,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(I).field.value.date),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(o,{key:1,style:_(k(N)),class:"form-item-frame"},{default:y((()=>["style-1"==k(M).completeLayout?(b(),g(o,{key:0,class:"base-layout-one"},{default:y((()=>[h(o,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k(I).textColor,"font-size":2*k(I).fontSize+"rpx","font-weight":k(I).fontWeight})},{default:y((()=>[w(C(k(I).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(I).field.required?"*":""),1)])),_:1}),"decorate"==k(f).mode&&k(I).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(I).field.remark.text?(b(),g(o,{key:0,class:"layout-one-remark",style:_({color:k(I).field.remark.color,fontSize:2*k(I).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(I).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),$.value&&!$.value.code?(b(),g(o,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C($.value.message),1)])),_:1})):S("v-if",!0),h(o,{class:"layout-one-content",onClick:U},{default:y((()=>[h(o,{class:"nc-iconfont nc-icon-a-riliV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(o,{class:F(["flex-1 text-overflow-ellipsis flex",{"!text-[#999]":!k(I).field.value.date&&!k(I).defaultControl}]),style:_({color:k(I).textColor,"font-size":2*k(I).fontSize+"rpx"})},{default:y((()=>[w(C(k(j)),1)])),_:1},8,["class","style"])])),_:1}),A().length?(b(),g(o,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(A(),((e,t)=>(b(),g(o,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(M).completeLayout?(b(),g(o,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(f).mode&&k(I).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(o,{class:F(["layout-two-wrap",{"no-border":!k(M).borderControl}])},{default:y((()=>[h(o,{class:F(["layout-two-label",{"justify-start":"left"==k(M).completeAlign,"justify-end":"right"==k(M).completeAlign}])},{default:y((()=>[k(I).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(I).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(I).textColor,"font-size":2*k(I).fontSize+"rpx","font-weight":k(I).fontWeight})},{default:y((()=>[w(C(k(I).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(o,{class:"layout-two-content",onClick:U},{default:y((()=>[h(o,{class:F(["flex-1 text-overflow-ellipsis flex justify-end",{"!text-[#999]":!k(I).field.value.date&&!k(I).defaultControl}]),style:_({color:k(I).textColor,"font-size":2*k(I).fontSize+"rpx"})},{default:y((()=>[w(C(k(j)),1)])),_:1},8,["class","style"]),h(l,{class:"nc-iconfont !text-[#666] !text-[36rpx] nc-icon-youV6xx -mr-[8rpx]"})])),_:1})])),_:1},8,["class"]),$.value&&!$.value.code?(b(),g(o,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C($.value.message),1)])),_:1})):S("v-if",!0),k(I).field.remark.text?(b(),g(o,{key:2,class:"layout-two-remark",style:_({color:k(I).field.remark.color,fontSize:2*k(I).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(I).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),A().length?(b(),g(o,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(A(),((e,t)=>(b(),g(o,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),h(o,{class:"calendar-wrap"},{default:y((()=>[h(r,{show:m.value,mode:"single",onConfirm:Y,onClose:t[0]||(t[0]=e=>m.value=!1),closeOnClickOverlay:"true",formatter:V,confirmDisabledText:"禁止选择",color:"var(--primary-color)",ref:"calendar",maxDate:a.value},null,8,["show","maxDate"]),h(s,{show:T.value,modelValue:k(I).field.value.date,"onUpdate:modelValue":t[1]||(t[1]=e=>k(I).field.value.date=e),mode:"datetime",onCancel:t[2]||(t[2]=e=>T.value=!1),closeOnClickOverlay:"true",onConfirm:P,onClose:t[3]||(t[3]=e=>T.value=!1),minDate:n.value},null,8,["show","modelValue","minDate"])])),_:1}),S(" 遮罩层，装修使用 "),"decorate"==k(f).mode?(b(),g(o,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-d7e15728"]]),el=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(!1),r=u(null),n=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),f=i((()=>l.global)),m=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},T=i((()=>{let e="",t="";if(n.value.field.value.start.date){let l=n.value.field.value.start.timestamp;e=W(l),t=e}else if(n.value.start.defaultControl){if("current"==n.value.start.dateWay)e=W();else if("diy"==n.value.start.dateWay){let t=n.value.field.default.start.timestamp?n.value.field.default.start.timestamp:"";e=W(t)}t=e}else e=n.value.start.placeholder,t="";return n.value.field.value.start.date=t,n.value.field.value.start.timestamp=t?ce(t):0,e})),$=i((()=>{let e="",t="";if(n.value.field.value.end.date){let l=n.value.field.value.end.timestamp;e=W(l),t=e,n.value.field.value.end.date=t,n.value.field.value.end.timestamp=ce(t)}else n.value.end.defaultControl?("current"==n.value.end.dateWay?e=W():"diy"==n.value.end.dateWay&&(e=W(n.value.field.default.end.timestamp)),t=e,n.value.field.value.end.date=t,n.value.field.value.end.timestamp=ce(t)):e=n.value.end.placeholder;return e})),I=i((()=>{let e=[];return e[0]=W(ce(T.value),"YYYY-MM-DD"),e[1]=W(ce($.value),"YYYY-MM-DD"),e})),M=i((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{A(),"decorate"==o.mode&&p((()=>n.value),((e,t)=>{e&&"FormDateScope"==e.componentName&&A()}))}));const A=()=>{},j=()=>{"decorate"!==o.mode&&(a.value=!0)},N=e=>{n.value.field.value.start.date=e[0],n.value.field.value.start.timestamp=ce(e[0]),n.value.field.value.end.date=e[e.length-1],n.value.field.value.end.timestamp=ce(e[e.length-1]),a.value=!1},W=(e="",t=n.value.dateFormat)=>{let l=e?new Date(1e3*e):new Date,o=l.getFullYear(),a=String(l.getMonth()+1).padStart(2,"0"),r=String(l.getDate()).padStart(2,"0"),s="";return"YYYY年M月D日"==t?s=`${o}年${a}月${r}日`:"YYYY-MM-DD"==t?s=`${o}-${a}-${r}`:"YYYY/MM/DD"==t&&(s=`${o}/${a}/${r}`),s};return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&(""!=n.value.field.value.start.date&&n.value.field.value.start.timestamp?""!=n.value.field.value.end.date&&n.value.field.value.end.timestamp||(e.code=!1,e.message=`请选择${n.value.end.placeholder}`):(e.code=!1,e.message=`请选择${n.value.start.placeholder}`)),r.value=e,e},reset:()=>{n.value.field.value.start.date="",n.value.field.value.start.timestamp=0,n.value.field.value.end.date="",n.value.field.value.end.timestamp=0}}),(e,t)=>{const l=D,s=E,i=x(v("u-calendar"),Kt);return k(n).viewFormDetail?(b(),g(s,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[k(n).field.value.start.date||k(n).field.value.end.date?(b(),g(s,{key:0,class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(s,{class:"detail-one-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(n).field.value.start.date),1)])),_:1}),k(n).field.value.start.date&&k(n).field.value.end.date?(b(),g(l,{key:0},{default:y((()=>[w("-")])),_:1})):S("v-if",!0),h(l,null,{default:y((()=>[w(C(k(n).field.value.end.date),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>[k(n).field.value.start.date||k(n).field.value.end.date?(b(),g(s,{key:0,class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(s,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(n).field.value.start.date),1)])),_:1}),k(n).field.value.start.date&&k(n).field.value.end.date?(b(),g(l,{key:0},{default:y((()=>[w("-")])),_:1})):S("v-if",!0),h(l,null,{default:y((()=>[w(C(k(n).field.value.end.date),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0)])),_:1})):(b(),g(s,{key:1,style:_(k(M)),class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),r.value&&!r.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),h(s,{class:"flex items-center"},{default:y((()=>[h(s,{class:"layout-one-content flex-1",onClick:j},{default:y((()=>[h(s,{class:"nc-iconfont nc-icon-a-riliV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(s,{class:F(["flex-1 text-overflow-ellipsis",{"!text-[#999]":!k(n).field.value.start.timestamp&&!k(n).defaultControl}]),style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w(C(k(T)),1)])),_:1},8,["class","style"])])),_:1}),h(s,{class:"mx-[10rpx]",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w("-")])),_:1},8,["style"]),h(s,{class:"layout-one-content flex-1",onClick:j},{default:y((()=>[h(s,{class:"nc-iconfont nc-icon-a-riliV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(s,{class:F(["flex-1 text-overflow-ellipsis",{"!text-[#999]":!k(n).field.value.end.timestamp&&!k(n).defaultControl}]),style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w(C(k($)),1)])),_:1},8,["class","style"])])),_:1})])),_:1}),m().length?(b(),g(s,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(m(),((e,t)=>(b(),g(s,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(s,{class:F(["layout-two-wrap",{"no-border":!k(f).borderControl}])},{default:y((()=>[h(s,{class:F(["layout-two-label",{"justify-start":"left"==k(f).completeAlign,"justify-end":"right"==k(f).completeAlign}])},{default:y((()=>[k(n).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{class:"layout-two-content",onClick:j},{default:y((()=>[h(s,{class:F(["text-overflow-ellipsis flex justify-center",{"!text-[#999]":!k(n).field.value.start.timestamp&&!k(n).defaultControl}]),style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w(C(k(T)),1)])),_:1},8,["class","style"]),h(s,{class:"mx-[10rpx]",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w("-")])),_:1},8,["style"]),h(s,{class:F(["text-overflow-ellipsis flex justify-center",{"!text-[#999]":!k(n).field.value.end.timestamp&&!k(n).defaultControl}]),style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w(C(k($)),1)])),_:1},8,["class","style"]),h(l,{class:"nc-iconfont !text-[#666] !text-[36rpx] nc-icon-youV6xx -mr-[8rpx]"})])),_:1})])),_:1},8,["class"]),r.value&&!r.value.code?(b(),g(s,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(b(),g(s,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),m().length?(b(),g(s,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(m(),((e,t)=>(b(),g(s,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),h(s,{class:"calendar-wrap"},{default:y((()=>[h(i,{show:a.value,mode:"range",onConfirm:N,onClose:t[0]||(t[0]=e=>a.value=!1),closeOnClickOverlay:"true",defaultDate:k(I),startText:"开始",endText:"结束",confirmDisabledText:"禁止选择",color:"var(--primary-color)",ref:"calendar",monthNum:"2"},null,8,["show","defaultDate"])])),_:1}),S(" 遮罩层，装修使用 "),"decorate"==k(o).mode?(b(),g(s,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-72a8e52f"]]),tl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(null),r=u(null),n=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),f=i((()=>l.global)),m=i((()=>{let e="";return e+=n.value.placeholder,e})),x=i((()=>`${n.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),v=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}if(n.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},T=e=>{"privacy"==e&&r.value.open()},$=i((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{I(),"decorate"==o.mode&&p((()=>n.value),((e,t)=>{e&&"FormEmail"==e.componentName&&I()}))}));const I=()=>{("decorate"==o.mode||""==n.value.field.value&&n.value.field.default)&&(n.value.field.value=n.value.field.default)},M=i((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=o.mode?(e.code=!1,e.message=`${m.value}`):/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(n.value.field.value)||"decorate"==o.mode||(e.code=!1,e.message="邮箱格式不正确，请重新输入"),a.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,s=E,i=L;return k(n).viewFormDetail?(b(),g(s,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>[h(s,{class:"detail-two-content"},{default:y((()=>[h(s,null,{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(s,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(s,{key:1,style:_(k($)),class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(i,{type:"email",class:"layout-one-content",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),v().length?(b(),g(s,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(v(),((e,t)=>(b(),g(s,{key:t,onClick:t=>T(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(s,{class:F(["layout-two-wrap",{"no-border":!k(f).borderControl}])},{default:y((()=>[h(s,{class:F(["layout-two-label",{"justify-start":"left"==k(f).completeAlign,"justify-end":"right"==k(f).completeAlign}])},{default:y((()=>[k(n).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{type:"email",class:"layout-two-content no-flex",placeholder:k(m),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(b(),g(s,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),v().length?(b(),g(s,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(v(),((e,t)=>(b(),g(s,{key:t,onClick:t=>T(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(b(),g(s,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Lt,{ref_key:"formPrivacyRef",ref:r,data:k(x)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-daee6c6a"]]),ll=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=u(null),a=s(),r=i((()=>"decorate"==a.mode?a.value[l.index]:l.component)),n=i((()=>l.global)),f=i((()=>{var e="";return e+="position:relative;",r.value.componentStartBgColor&&(r.value.componentStartBgColor&&r.value.componentEndBgColor?e+=`background:linear-gradient(${r.value.componentGradientAngle},${r.value.componentStartBgColor},${r.value.componentEndBgColor});`:e+="background-color:"+r.value.componentStartBgColor+";"),r.value.componentBgUrl&&(e+=`background-image:url('${d(r.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;"),e}));c((()=>{T(),"decorate"==a.mode&&p((()=>r.value),((e,t)=>{e&&"FormFile"==e.componentName&&T()}))}));const m=()=>{uni.chooseMessageFile({count:9,type:"all",success:e=>{console.log("选择的微信聊天文件:",e.tempFiles),e.tempFiles.forEach((e=>{fe({filePath:e.path,name:"file"}).then((e=>{r.value.field.value.length<Number(r.value.limit)&&r.value.field.value.push(e.data.url)})).catch((()=>{console.error("上传失败")}))}))},fail:e=>{console.error("选择文件失败",e)}})},F=e=>{e.file.forEach((e=>{fe({filePath:e.url,name:"file"}).then((e=>{r.value.field.value.length<Number(r.value.limit)&&r.value.field.value.push(e.data.url)})).catch((()=>{console.error("上传失败")}))}))},T=()=>{};return t({verify:()=>({code:!0,message:""}),reset:()=>{}}),(e,t)=>{const l=D,s=E,i=$,u=x(v("u-upload"),Ke);return b(),g(s,{style:_(k(f)),class:"form-item-frame"},{default:y((()=>["style-1"==k(n).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(r).textColor,"font-size":2*k(r).fontSize+"rpx","font-weight":k(r).fontWeight})},{default:y((()=>[w(C(k(r).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(r).field.required?"*":""),1)])),_:1}),"decorate"==k(a).mode&&k(r).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(r).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(r).field.remark.color,fontSize:2*k(r).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(r).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),o.value&&!o.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(o.value.message),1)])),_:1})):S("v-if",!0),h(s,{class:"flex flex-wrap"},{default:y((()=>[(b(!0),R(B,null,z(k(r).field.value,((e,t)=>(b(),g(s,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx] layout-one-content",key:t},{default:y((()=>[h(i,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(s,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>{return l=t,void r.value.field.value.splice(l.index,1);var l}},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),h(s,{class:"flex items-center flex-1"},{default:y((()=>[h(s,{class:"layout-one-content !p-[0] flex-1 !items-stretch !h-[100rpx]"},{default:y((()=>[h(s,{class:"flex items-center h-[100%] w-[100%] pl-[30rpx] box-border",onClick:m},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-weixinV6mm"}),h(l,{class:"text-[28rpx] ml-[10rpx]"},{default:y((()=>[w("选择微信聊天文件")])),_:1})])),_:1})])),_:1}),h(s,{class:"layout-one-content !p-[0] ml-[20rpx] !items-stretch flex-1 !h-[100rpx]"},{default:y((()=>[h(u,{accept:"file ",onAfterRead:F,multiple:"",maxCount:9},{default:y((()=>[h(s,{class:"flex items-center h-[100%] w-[100%] pl-[30rpx] box-border"},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-tupiandaohangpc"}),h(l,{class:"text-[28rpx] ml-[10rpx]"},{default:y((()=>[w("选择本地文件")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):S("v-if",!0),"decorate"==k(a).mode?(b(),g(s,{key:1,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-3511697c"]]),ol=Pe(n({__name:"index",props:["data"],setup(e,{expose:t}){const l=e,o=i((()=>l.data||"已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息")),a=u(!1),r=()=>{a.value=!1};return t({open:()=>{a.value=!0}}),(e,t)=>{const l=E,n=ee,s=x(v("u-popup"),Ve);return b(),g(l,{onTouchmove:t[1]||(t[1]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(s,{show:a.value,onClose:r,zIndex:"500",mode:"center",round:8},{default:y((()=>[h(l,{class:"w-[570rpx] popup-common center"},{default:y((()=>[h(l,{class:"text-center my-5"},{default:y((()=>[w(C(k(H)("diyForm.tips")),1),me("br"),w(C(k(o)),1)])),_:1}),h(l,{class:"flex justify-between"},{default:y((()=>[h(n,{class:"w-[50%] h-[100rpx] rounded-[0rpx] leading-[100rpx] !bg-[transform] border-solid border-[0] border-t-[2rpx] border-[#e6e6e6] !text-[#333]",onClick:t[0]||(t[0]=e=>k(xe)(k(o)))},{default:y((()=>[w(C(k(H)("diyForm.copy")),1)])),_:1}),h(n,{class:"w-[50%] h-[100rpx] rounded-[0rpx] border-solid border-[0] border-t-[2rpx] border-l-[2rpx] bo border-[#e6e6e6] leading-[100rpx] !bg-[transform] !text-[var(--primary-color)]",onClick:r},{default:y((()=>[w(C(k(H)("diyForm.know")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-bb1ac654"]]),al=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(null),r=u(null),n=u(null),m=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),x=i((()=>l.global)),v=i((()=>{const e=String(m.value.field.value);return m.value.field.privacyProtection?e.replace(/(\d{3})\d*(\d{4})/,"$1****$2"):e})),T=i((()=>{let e="";return e+=m.value.placeholder,e})),$=i((()=>`${m.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),I=()=>{let e=[];if(m.value.autofill){let t={title:"已自动填充"};e.push(t)}if(m.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},M=e=>{"privacy"==e&&r.value.open()},A=()=>{f((()=>{n.value?n.value.open():console.warn("formDetailPrivacyRef is not defined")}))},j=i((()=>{var e="";return e+="position:relative;",m.value.componentStartBgColor&&m.value.componentEndBgColor?e+=`background:linear-gradient(${m.value.componentGradientAngle},${m.value.componentStartBgColor},${m.value.componentEndBgColor});`:m.value.componentStartBgColor?e+="background-color:"+m.value.componentStartBgColor+";":m.value.componentEndBgColor&&(e+="background-color:"+m.value.componentEndBgColor+";"),m.value.componentBgUrl&&(e+=`background-image:url('${d(m.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),m.value.topRounded&&(e+="border-top-left-radius:"+2*m.value.topRounded+"rpx;"),m.value.topRounded&&(e+="border-top-right-radius:"+2*m.value.topRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*m.value.bottomRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*m.value.bottomRounded+"rpx;"),e}));c((()=>{N(),"decorate"==o.mode&&p((()=>m.value),((e,t)=>{e&&"FormIdentity"==e.componentName&&N()}))}));const N=()=>{("decorate"==o.mode||""==m.value.field.value&&m.value.field.default)&&(m.value.field.value=m.value.field.default)},W=i((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return m.value.field.required&&""==m.value.field.value&&"decorate"!=o.mode?(e.code=!1,e.message=`${T.value}`):/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}[0-9Xx]$/.test(m.value.field.value)||"decorate"==o.mode||(e.code=!1,e.message="身份证格式不正确，请重新输入"),a.value=e,e},reset:()=>{m.value.field.value=""}}),(e,t)=>{const l=D,s=E,i=L;return k(m).viewFormDetail?(b(),g(s,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(x).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(s,{class:"detail-one-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(v)),1)])),_:1}),k(m).field.privacyProtection?(b(),g(l,{key:0,class:"ml-[20rpx] text-[var(--primary-color)]",onClick:A},{default:y((()=>[w(C(k(H)("diyForm.view")),1)])),_:1})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(x).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>[h(s,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(s,{class:"detail-one-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(v)),1)])),_:1}),k(m).field.privacyProtection?(b(),g(l,{key:0,class:"ml-[20rpx] text-[var(--primary-color)]",onClick:A},{default:y((()=>[w(C(k(H)("diyForm.view")),1)])),_:1})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0),h(ol,{ref_key:"formDetailPrivacyRef",ref:n,data:k(m).field.value},null,8,["data"])])),_:1})):(b(),g(s,{key:1,style:_(k(j)),class:"form-item-frame"},{default:y((()=>["style-1"==k(x).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(m).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(m).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(i,{type:"idcard",class:"layout-one-content",placeholder:k(T),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(m).fontSize+"rpx"},style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),modelValue:k(m).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(m).field.value=e),disabled:k(W),maxlength:"18"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),I().length?(b(),g(s,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(I(),((e,t)=>(b(),g(s,{key:t,onClick:t=>M(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(x).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(m).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(s,{class:F(["layout-two-wrap",{"no-border":!k(x).borderControl}])},{default:y((()=>[h(s,{class:F(["layout-two-label",{"justify-start":"left"==k(x).completeAlign,"justify-end":"right"==k(x).completeAlign}])},{default:y((()=>[k(m).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{type:"idcard",class:"layout-two-content no-flex",placeholder:k(T),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(m).fontSize+"rpx"},style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),modelValue:k(m).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(m).field.value=e),disabled:k(W),maxlength:"18"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(m).field.remark.text?(b(),g(s,{key:2,class:"layout-two-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),I().length?(b(),g(s,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(I(),((e,t)=>(b(),g(s,{key:t,onClick:t=>M(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(b(),g(s,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Lt,{ref_key:"formPrivacyRef",ref:r,data:k($)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-3d76d97c"]]),rl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(null),r=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),n=i((()=>l.global)),f=i((()=>{let e=[];return r.value&&r.value.uploadMode&&r.value.uploadMode.forEach(((t,l)=>{"take_pictures"==t&&-1==r.value.uploadMode.indexOf("camera")?e.push("camera"):"select_from_album"==t&&-1==r.value.uploadMode.indexOf("album")&&e.push("album")})),e})),m=()=>{let e=[];if(r.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},T=(e,t)=>{let l=[];r.value.field.value&&(l=Object.values(r.value.field.value).map((e=>d(e)))),ge({current:t,urls:l,indicator:"number",loop:!0})};ve((()=>{try{be()}catch(e){}}));const I=i((()=>{var e="";return e+="position:relative;",r.value.componentStartBgColor&&(r.value.componentStartBgColor&&r.value.componentEndBgColor?e+=`background:linear-gradient(${r.value.componentGradientAngle},${r.value.componentStartBgColor},${r.value.componentEndBgColor});`:e+="background-color:"+r.value.componentStartBgColor+";"),r.value.componentBgUrl&&(e+=`background-image:url('${d(r.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;"),e}));c((()=>{N(),"decorate"==o.mode&&p((()=>r.value),((e,t)=>{e&&"FormImage"==e.componentName&&N()}))})),i((()=>"decorate"===o.mode)),i((()=>r.value.field.value.map((e=>({url:d(e)})))));const M=e=>{uni.setStorageSync("sku_form_refresh",!0),e.file.forEach((e=>{A(e)}))},A=e=>{if(r.value.field.value.length>Number(r.value.limit))return ye({title:`最多允许上传${r.value.limit}张图片`,icon:"none"}),!1;fe({filePath:e.url,name:"file"}).then((e=>{r.value.field.value.length<Number(r.value.limit)&&r.value.field.value.push(e.data.url)})).catch((()=>{}))},j=e=>{r.value.field.value.splice(e.index,1)},N=()=>{};return t({verify:()=>{const e={code:!0,message:""};return r.value.field.required&&(!r.value.field.value||r.value.field.value&&!r.value.field.value.length)?(e.code=!1,e.message="请上传图片"):r.value.field.value&&r.value.field.value.length>Number(r.value.limit)&&(e.code=!1,e.message="图片上传数量已超出限制数量"),a.value=e,e},reset:()=>{r.value.field.value=[]}}),(e,t)=>{const l=D,s=$,i=E,u=x(v("u-upload"),Ke);return k(r).viewFormDetail?(b(),g(i,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(n).completeLayout?(b(),g(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(r).field.name),1)])),_:1}),h(i,{class:"flex flex-wrap detail-one-content-value pt-[6rpx]"},{default:y((()=>[(b(!0),R(B,null,z(k(r).field.value,((e,t)=>(b(),g(i,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx]",key:t},{default:y((()=>[h(s,{class:"w-[100%] h-[100%]",src:k(d)(e),onClick:e=>T(0,t),mode:"aspectFill"},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(n).completeLayout?(b(),g(i,{key:1,class:"base-layout-two"},{default:y((()=>[h(i,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(r).field.name),1)])),_:1}),h(i,{class:"flex flex-wrap w-[80%] justify-end"},{default:y((()=>[(b(!0),R(B,null,z(k(r).field.value,((e,t)=>(b(),g(i,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx]",key:t},{default:y((()=>[h(s,{class:"w-[100%] h-[100%]",src:k(d)(e),onClick:e=>T(0,t),mode:"aspectFill"},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(i,{key:1,style:_(k(I)),class:"form-item-frame"},{default:y((()=>["style-1"==k(n).completeLayout?(b(),g(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(r).textColor,"font-size":2*k(r).fontSize+"rpx","font-weight":k(r).fontWeight})},{default:y((()=>[w(C(k(r).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(r).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(r).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(r).field.remark.text?(b(),g(i,{key:0,class:"layout-one-remark",style:_({color:k(r).field.remark.color,fontSize:2*k(r).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(r).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(b(),g(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(i,{class:"flex flex-wrap"},{default:y((()=>[(b(!0),R(B,null,z(k(r).field.value,((e,t)=>(b(),g(i,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx] layout-one-content",key:t},{default:y((()=>[h(s,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(i,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>j(t)},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),k(r).uploadMode.length>1&&k(r).field.value.length<=0?(b(),g(i,{key:0,class:"flex items-center flex-1"},{default:y((()=>[h(i,{class:"layout-one-content !p-[0] flex-1 !items-stretch !h-[100rpx]"},{default:y((()=>[h(u,{accept:"image",onAfterRead:M,multiple:"",maxCount:9,capture:"camera"},{default:y((()=>[h(i,{class:"flex items-center h-[100%] w-[100%] pl-[30rpx] box-border"},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-xiangjiV6xx"}),h(l,{class:"text-[28rpx] ml-[10rpx]"},{default:y((()=>[w("拍照上传")])),_:1})])),_:1})])),_:1})])),_:1}),h(i,{class:"layout-one-content !p-[0] ml-[20rpx] !items-stretch flex-1 !h-[100rpx]"},{default:y((()=>[h(u,{accept:"image",onAfterRead:M,multiple:"",maxCount:9,capture:"album"},{default:y((()=>[h(i,{class:"flex items-center h-[100%] w-[100%] pl-[30rpx] box-border"},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-tupiandaohangpc"}),h(l,{class:"text-[28rpx] ml-[10rpx]"},{default:y((()=>[w("从相册中选择")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):k(r).field.value.length<Number(k(r).limit)?(b(),g(i,{key:1,class:"layout-one-content h-[180rpx] w-[180rpx] !px-[0]"},{default:y((()=>[1==k(r).uploadMode.length?(b(),g(u,{key:0,accept:"image",onAfterRead:M,multiple:"",capture:k(f),maxCount:Number(k(r).limit)},{default:y((()=>[h(i,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:F(["nc-iconfont !text-[36rpx] mb-[16rpx]",{"nc-icon-xiangjiV6xx":k(r).uploadMode.indexOf("take_pictures")>-1,"nc-icon-tupiandaohangpc":-1==k(r).uploadMode.indexOf("take_pictures")}])},null,8,["class"]),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w(C(k(r).uploadMode.indexOf("take_pictures")>-1?"拍照上传":"从相册选择"),1)])),_:1})])),_:1})])),_:1},8,["capture","maxCount"])):(b(),g(u,{key:1,accept:"image",onAfterRead:M,multiple:"",capture:k(f),maxCount:Number(k(r).limit)},{default:y((()=>[h(i,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[40rpx] mb-[16rpx] nc-icon-jiahaoV6xx"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w(C(k(H)("diyForm.uploadTips")),1)])),_:1})])),_:1})])),_:1},8,["capture","maxCount"]))])),_:1})):S("v-if",!0)])),_:1}),m().length?(b(),g(i,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(m(),((e,t)=>(b(),g(i,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(n).completeLayout?(b(),g(i,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(r).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(i,{class:F(["layout-two-wrap",{"no-border":!k(n).borderControl}])},{default:y((()=>[h(i,{class:F(["layout-two-label",{"justify-start":"left"==k(n).completeAlign,"justify-end":"right"==k(n).completeAlign}])},{default:y((()=>[k(r).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(r).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(r).textColor,"font-size":2*k(r).fontSize+"rpx","font-weight":k(r).fontWeight})},{default:y((()=>[w(C(k(r).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{class:"layout-two-content flex-wrap"},{default:y((()=>[(b(!0),R(B,null,z(k(r).field.value,((e,t)=>(b(),g(i,{class:"relative border-box w-[180rpx] !h-[180rpx] ml-[16rpx] mb-[16rpx] border-box border-[2rpx] border-solid border-[#e6e6e6] rounded-[10rpx] flex items-center",key:t},{default:y((()=>[h(s,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(i,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>j(t)},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),h(i,{class:"items-start border-box border-[2rpx] ml-[16rpx] mb-[16rpx] border-solid border-[#e6e6e6] rounded-[10rpx]"},{default:y((()=>[k(r).field.value.length<Number(k(r).limit)?(b(),g(u,{key:0,accept:"image",onAfterRead:M,multiple:"",capture:k(f),maxCount:Number(k(r).limit)},{default:y((()=>[h(i,{class:"flex flex-col items-center justify-center min-w-[180rpx] min-h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[40rpx] mb-[16rpx] nc-icon-jiahaoV6xx"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w(C(k(H)("diyForm.uploadTips")),1)])),_:1})])),_:1})])),_:1},8,["capture","maxCount"])):S("v-if",!0)])),_:1})])),_:1})])),_:1},8,["class"]),a.value&&!a.value.code?(b(),g(i,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(r).field.remark.text?(b(),g(i,{key:2,class:"layout-two-remark",style:_({color:k(r).field.remark.color,fontSize:2*k(r).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(r).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),m().length?(b(),g(i,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(m(),((e,t)=>(b(),g(i,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(b(),g(i,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-fc7c2a10"]]),nl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(null),r=u(null),n=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),f=i((()=>l.global)),m=i((()=>{let e="";return e+=n.value.placeholder,e})),x=i((()=>`${n.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),v=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}if(n.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},T=e=>{"privacy"==e&&r.value.open()},$=i((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{I(),"decorate"==o.mode&&p((()=>n.value),((e,t)=>{e&&"FormInput"==e.componentName&&I()}))}));const I=()=>{("decorate"==o.mode||""==n.value.field.value&&n.value.field.default)&&(n.value.field.value=n.value.field.default)},M=i((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=o.mode&&(e.code=!1,e.message=`${m.value}`),a.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,s=E,i=L;return k(n).viewFormDetail?(b(),g(s,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>[h(s,{class:"detail-two-content"},{default:y((()=>[h(s,null,{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(s,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(s,{key:1,style:_(k($)),class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(i,{type:"text",class:"layout-one-content",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),v().length?(b(),g(s,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(v(),((e,t)=>(b(),g(s,{key:t,onClick:t=>T(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(s,{class:F(["layout-two-wrap",{"no-border":!k(f).borderControl}])},{default:y((()=>[h(s,{class:F(["layout-two-label",{"justify-start":"left"==k(f).completeAlign,"justify-end":"right"==k(f).completeAlign}])},{default:y((()=>[k(n).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{type:"text",class:"layout-two-content no-flex",placeholder:k(m),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(b(),g(s,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),v().length?(b(),g(s,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(v(),((e,t)=>(b(),g(s,{key:t,onClick:t=>T(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(b(),g(s,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Lt,{ref_key:"formPrivacyRef",ref:r,data:k(x)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-d4cb0a54"]]),sl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=u(null),a=M();A();const r=s(),n=i((()=>"decorate"==r.mode?r.value[l.index]:l.component)),f=i((()=>l.global));let m=!1;n.value&&"decorate"!=r.mode&&(m=!0);const x=He(m);x.onLoad(),x.init();const v=i((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{R(),"decorate"==r.mode&&p((()=>n.value),((e,t)=>{e&&"FormLocation"==e.componentName&&R()}))}));const R=()=>{};return t({verify:()=>({code:!0,message:""}),reset:()=>{}}),(e,t)=>{const l=D,s=E;return b(),g(s,{style:_(k(v)),class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(r).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(e.t("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),o.value&&!o.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(o.value.message),1)])),_:1})):S("v-if",!0),"authorized_wechat_location"==k(n).mode?(b(),g(s,{key:2},{default:y((()=>[k(a).diyAddressInfo?(b(),g(s,{key:0,onClick:t[0]||(t[0]=N((e=>k(x).reposition()),["stop"])),class:"layout-one-content"},{default:y((()=>[S(' <view class="flex items-baseline font-500">\n\t\t\t\t\t\t<text class="text-[24rpx] mr-[2rpx]">{{ systemStore.diyAddressInfo.city }}</text>\n\t\t\t\t\t\t<text class="iconfont iconxiaV6xx !text-[24rpx]"></text>\n\t\t\t\t\t</view> '),h(l,{class:"iconfont iconzuobiaofill !text-[28rpx]"}),k(a).diyAddressInfo.community?(b(),g(s,{key:0},{default:y((()=>[w(C(k(a).diyAddressInfo.community),1)])),_:1})):S("v-if",!0)])),_:1})):(b(),g(s,{key:1,onClick:t[1]||(t[1]=N((e=>k(x).reposition()),["stop"])),class:"layout-one-content"},{default:y((()=>[h(l,{class:"iconfont iconzuobiaofill !text-[28rpx]"}),h(l,{class:"ml-1 text-[#999]"},{default:y((()=>[w("点击获取位置信息")])),_:1})])),_:1}))])),_:1})):(b(),g(s,{key:3},{default:y((()=>[k(a).diyAddressInfo?(b(),g(s,{key:0,onClick:t[2]||(t[2]=N((e=>k(x).reposition()),["stop"])),class:"layout-one-content"},{default:y((()=>[h(s,{class:"flex items-baseline font-500"},{default:y((()=>[h(l,{class:"text-[24rpx] mr-[2rpx]"},{default:y((()=>[w(C(k(a).diyAddressInfo.city),1)])),_:1}),h(l,{class:"iconfont iconxiaV6xx !text-[24rpx]"})])),_:1}),k(a).diyAddressInfo.community?(b(),g(s,{key:0,class:"layout-one-content"},{default:y((()=>[w(C(k(a).diyAddressInfo.community),1)])),_:1})):S("v-if",!0)])),_:1})):(b(),g(s,{key:1,onClick:t[3]||(t[3]=N((e=>k(x).reposition()),["stop"])),class:"layout-one-content"},{default:y((()=>[h(l,{class:"iconfont iconzuobiaofill !text-[28rpx]"}),h(l,{class:"ml-1 text-[#999]"},{default:y((()=>[w("选择位置")])),_:1})])),_:1})),h(s,{class:"text-[var(--primary-color)] mt-1"},{default:y((()=>[w(" 当前定位 ")])),_:1})])),_:1})),S(' <input type="text" class="layout-one-content" :placeholder=""\n\t\t\t\tplaceholderClass="layout-one-input-placeholder"\n\t\t\t\t:placeholder-style="{\'font-size\': (diyComponent.fontSize * 2) + \'rpx\' }"\n\t\t\t\t:style="{\'color\': diyComponent.textColor,\'font-size\': (diyComponent.fontSize * 2) + \'rpx\'}"\n\t\t\t\tv-model="diyComponent.field.value" :disabled="isDisabled" /> '),S(' <view class="layout-one-attribute-wrap" v-if="inputAttribute().length">\n\t\t\t\t<view v-for="(item,index) in inputAttribute()" :key="index" @click="eventFn(item.type)"\n\t\t\t\t\tclass="layout-one-attribute-item">{{ item.title }}</view>\n\t\t\t</view> ')])),_:1})):S("v-if",!0),S(" <view class=\"relative\">\n\t\t\t<view class=\"p-[10rpx]  flex items-center \">\n\t\t\t\t<view class=\"w-[27%] mr-[10rpx] flex items-center\">\n\t\t\t\t\t<text class=\"text-overflow-ellipsis\"\n\t\t\t\t\t\t:style=\"{'color': diyComponent.textColor,'font-size': (diyComponent.fontSize * 2) + 'rpx' ,'font-weight': diyComponent.fontWeight}\">{{ diyComponent.field.name}}</text>\n\t\t\t\t\t<text class=\"text-[#ec0003]\">{{ diyComponent.field.required ? '*' : '' }}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t</view>\n\t\t</view> ")])),_:1},8,["style"])}}}),[["__scopeId","data-v-fcb64919"]]),il=Pe(n({__name:"index",props:["data"],setup(e,{expose:t}){const l=e,o=i((()=>l.data||"已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息")),a=u(!1),r=()=>{a.value=!1},n=()=>{he({phoneNumber:l.data})};return t({open:()=>{a.value=!0}}),(e,t)=>{const l=E,s=ee,i=x(v("u-popup"),Ve);return b(),g(l,{onTouchmove:t[0]||(t[0]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(i,{show:a.value,onClose:r,zIndex:"500",mode:"center",round:8},{default:y((()=>[h(l,{class:"w-[570rpx] popup-common center"},{default:y((()=>[h(l,{class:"text-center my-5"},{default:y((()=>[w(C(k(H)("diyForm.tips")),1),me("br"),w(C(k(o)),1)])),_:1}),h(l,{class:"flex justify-between"},{default:y((()=>[h(s,{class:"w-[50%] h-[100rpx] rounded-[0rpx] leading-[100rpx] !bg-[transform] border-solid border-[0] border-t-[2rpx] border-[#e6e6e6] !text-[#333]",onClick:n},{default:y((()=>[w(C(k(H)("diyForm.call")),1)])),_:1}),h(s,{class:"w-[50%] h-[100rpx] rounded-[0rpx] border-solid border-[0] border-t-[2rpx] border-l-[2rpx] bo border-[#e6e6e6] leading-[100rpx] !bg-[transform] !text-[var(--primary-color)]",onClick:r},{default:y((()=>[w(C(k(H)("diyForm.know")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-33cb0694"]]),dl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(null),r=u(null),n=u(null),m=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),x=i((()=>l.global)),v=i((()=>{const e=String(m.value.field.value);return m.value.field.privacyProtection?e.replace(/(\d{3})\d{4}(\d{3})/,"$1****$2"):e})),T=i((()=>{let e="";return e+=m.value.placeholder,e})),$=i((()=>`${m.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),I=()=>{let e=[];if(m.value.autofill){let t={title:"已自动填充"};e.push(t)}if(m.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},M=e=>{"privacy"==e&&r.value.open()},A=()=>{f((()=>{n.value?n.value.open():console.warn("formDetailPrivacyRef is not defined")}))},j=i((()=>{var e="";return e+="position:relative;",m.value.componentStartBgColor&&m.value.componentEndBgColor?e+=`background:linear-gradient(${m.value.componentGradientAngle},${m.value.componentStartBgColor},${m.value.componentEndBgColor});`:m.value.componentStartBgColor?e+="background-color:"+m.value.componentStartBgColor+";":m.value.componentEndBgColor&&(e+="background-color:"+m.value.componentEndBgColor+";"),m.value.componentBgUrl&&(e+=`background-image:url('${d(m.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),m.value.topRounded&&(e+="border-top-left-radius:"+2*m.value.topRounded+"rpx;"),m.value.topRounded&&(e+="border-top-right-radius:"+2*m.value.topRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*m.value.bottomRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*m.value.bottomRounded+"rpx;"),e}));c((()=>{N(),"decorate"==o.mode&&p((()=>m.value),((e,t)=>{e&&"FormMobile"==e.componentName&&N()}))}));const N=()=>{("decorate"==o.mode||""==m.value.field.value&&m.value.field.default)&&(m.value.field.value=m.value.field.default)},W=i((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return m.value.field.required&&""==m.value.field.value&&"decorate"!=o.mode?(e.code=!1,e.message=`${T.value}`):/^1[3-9]\d{9}$/.test(m.value.field.value)||"decorate"==o.mode||(e.code=!1,e.message="手机号格式不正确，请重新输入"),a.value=e,e},reset:()=>{m.value.field.value=""}}),(e,t)=>{const l=D,s=E,i=L;return k(m).viewFormDetail?(b(),g(s,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(x).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(s,{class:"detail-one-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(v)),1)])),_:1}),k(m).field.privacyProtection?(b(),g(l,{key:0,class:"ml-[20rpx] text-[var(--primary-color)]",onClick:A},{default:y((()=>[w(C(k(H)("diyForm.view")),1)])),_:1})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(x).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>[h(s,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(s,{class:"detail-two-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(v)),1)])),_:1}),k(m).field.privacyProtection?(b(),g(l,{key:0,class:"ml-[20rpx] text-[var(--primary-color)]",onClick:A},{default:y((()=>[w(C(k(H)("diyForm.view")),1)])),_:1})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0),h(il,{ref_key:"formDetailPrivacyRef",ref:n,data:k(m).field.value},null,8,["data"])])),_:1})):(b(),g(s,{key:1,style:_(k(j)),class:"form-item-frame"},{default:y((()=>["style-1"==k(x).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(m).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(m).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(i,{type:"number",class:"layout-one-content",placeholder:k(T),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(m).fontSize+"rpx"},style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),modelValue:k(m).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(m).field.value=e),disabled:k(W),maxlength:"11"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),I().length?(b(),g(s,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(I(),((e,t)=>(b(),g(s,{key:t,onClick:t=>M(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(x).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(m).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(s,{class:F(["layout-two-wrap",{"no-border":!k(x).borderControl}])},{default:y((()=>[h(s,{class:F(["layout-two-label",{"justify-start":"left"==k(x).completeAlign,"justify-end":"right"==k(x).completeAlign}])},{default:y((()=>[k(m).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{type:"number",class:"layout-two-content no-flex",placeholder:k(T),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(m).fontSize+"rpx"},style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),modelValue:k(m).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(m).field.value=e),disabled:k(W),maxlength:"11"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(m).field.remark.text?(b(),g(s,{key:2,class:"layout-two-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),I().length?(b(),g(s,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(I(),((e,t)=>(b(),g(s,{key:t,onClick:t=>M(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(b(),g(s,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Lt,{ref_key:"formPrivacyRef",ref:r,data:k($)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-97806c3d"]]),ul=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(null),r=u(null),n=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),f=i((()=>l.global)),m=i((()=>{let e="";return e+=n.value.placeholder,e})),x=i((()=>`${n.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),v=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}if(n.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},T=e=>{"privacy"==e&&r.value.open()},$=i((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{I(),"decorate"==o.mode&&p((()=>n.value),((e,t)=>{e&&"FormNumber"==e.componentName&&I()}))}));const I=()=>{("decorate"==o.mode||""==n.value.field.value&&n.value.field.default)&&(n.value.field.value=n.value.field.default)},M=i((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=o.mode&&(e.code=!1,e.message=`${m.value}`),a.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,s=E,i=L;return k(n).viewFormDetail?(b(),g(s,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>[h(s,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(s,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(s,{key:1,style:_(k($)),class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(s,{class:"layout-one-content flex items-center"},{default:y((()=>[h(i,{type:"number",class:"flex-1",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),k(n).unit?(b(),g(l,{key:0,class:"text-[#999] text-[28rpx]"},{default:y((()=>[w(C(k(n).unit),1)])),_:1})):S("v-if",!0)])),_:1}),v().length?(b(),g(s,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(v(),((e,t)=>(b(),g(s,{key:t,onClick:t=>T(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(s,{class:F(["layout-two-wrap",{"no-border":!k(f).borderControl}])},{default:y((()=>[h(s,{class:F(["layout-two-label",{"justify-start":"left"==k(f).completeAlign,"justify-end":"right"==k(f).completeAlign}])},{default:y((()=>[k(n).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{class:"layout-two-content"},{default:y((()=>[h(i,{type:"number",placeholder:k(m),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),k(n).unit?(b(),g(l,{key:0,class:"text-[#999] ml-[10rpx] pt-[2rpx] text-[28rpx]"},{default:y((()=>[w(C(k(n).unit),1)])),_:1})):S("v-if",!0)])),_:1})])),_:1},8,["class"]),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(b(),g(s,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),v().length?(b(),g(s,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(v(),((e,t)=>(b(),g(s,{key:t,onClick:t=>T(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(b(),g(s,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Lt,{ref_key:"formPrivacyRef",ref:r,data:k(x)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-24d4d724"]]),cl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u([]),r=u(!1),n=u([]),f=u(""),m=u(null),T=u(null),$=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),M=i((()=>l.global)),A=i((()=>`${$.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),j=()=>{let e=[];if($.value.autofill){let t={title:"已自动填充"};e.push(t)}if($.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},W=e=>{"privacy"==e&&T.value.open()},U=i((()=>{var e="";return e+="position:relative;",$.value.componentStartBgColor&&$.value.componentEndBgColor?e+=`background:linear-gradient(${$.value.componentGradientAngle},${$.value.componentStartBgColor},${$.value.componentEndBgColor});`:$.value.componentStartBgColor?e+="background-color:"+$.value.componentStartBgColor+";":$.value.componentEndBgColor&&(e+="background-color:"+$.value.componentEndBgColor+";"),$.value.componentBgUrl&&(e+=`background-image:url('${d($.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),$.value.topRounded&&(e+="border-top-left-radius:"+2*$.value.topRounded+"rpx;"),$.value.topRounded&&(e+="border-top-right-radius:"+2*$.value.topRounded+"rpx;"),$.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*$.value.bottomRounded+"rpx;"),$.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*$.value.bottomRounded+"rpx;"),e}));c((()=>{Y(),"decorate"==o.mode&&p((()=>$.value),((e,t)=>{e&&"FormRadio"==e.componentName&&Y()})),"style-3"==$.value.style&&$.value.field.value.length>0&&(n.value=$.value.field.value[0].id),$.value.field.value.length>0&&(f.value=$.value.field.value[0].id)}));const Y=()=>{},P=i((()=>{let e="";return e+=`请选择${$.value.field.name}`,e})),V=i((()=>{let e=[];return $.value.field.value.forEach((t=>{const l=$.value.options.find((e=>e.id===t.id));l&&e.push(l.text)})),e.join(", ")})),L=i((()=>"decorate"===o.mode)),O=()=>{L.value||(r.value=!0)},q=e=>{f.value=e;const t=$.value.options.find((t=>t.id===e));$.value.field.value=[{id:t.id,text:t.text}],r.value=!1},G=e=>{L.value||(f.value=e.id,$.value.field.value=[{id:e.id,text:e.text}])};return t({verify:()=>{const e={code:!0,message:""};return $.value.field.required&&""==$.value.field.value.length&&"decorate"!=o.mode&&(e.code=!1,e.message=P.value),m.value=e,e},reset:()=>{f.value="",n.value=[],a.value=[],$.value.field.value=[]}}),(e,t)=>{const l=D,a=E,s=x(v("u-radio"),Je),i=x(v("u-radio-group"),et),d=I,u=x(v("u-popup"),Ve);return k($).viewFormDetail?(b(),g(a,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(M).completeLayout?(b(),g(a,{key:0,class:"base-layout-one"},{default:y((()=>[h(a,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k($).field.name),1)])),_:1}),(b(!0),R(B,null,z(k($).field.value,((e,t)=>(b(),g(l,{key:t,class:"detail-one-content-value"},{default:y((()=>[w(C(e.text),1)])),_:2},1024)))),128))])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(M).completeLayout?(b(),g(a,{key:1,class:"base-layout-two"},{default:y((()=>[h(a,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k($).field.name),1)])),_:1}),(b(!0),R(B,null,z(k($).field.value,((e,t)=>(b(),g(a,{class:"detail-two-content-value w-[80%]",key:t},{default:y((()=>[w(C(e.text),1)])),_:2},1024)))),128))])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(a,{key:1,style:_(k(U)),class:"form-item-frame"},{default:y((()=>["style-1"==k(M).completeLayout?(b(),g(a,{key:0,class:"base-layout-one"},{default:y((()=>[h(a,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k($).textColor,"font-size":2*k($).fontSize+"rpx","font-weight":k($).fontWeight})},{default:y((()=>[w(C(k($).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k($).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k($).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k($).field.remark.text?(b(),g(a,{key:0,class:"layout-one-remark",style:_({color:k($).field.remark.color,fontSize:2*k($).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k($).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),m.value&&!m.value.code?(b(),g(a,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(m.value.message),1)])),_:1})):S("v-if",!0),"style-1"==k($).style?(b(),g(a,{key:2,class:"layout-one-content !flex-initial"},{default:y((()=>[h(i,{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=e=>f.value=e),onChange:q,iconPlacement:"left"},{default:y((()=>[(b(!0),R(B,null,z(k($).options,((e,t)=>(b(),g(a,{key:t,class:"mr-[40rpx]"},{default:y((()=>[h(s,{activeColor:"var(--primary-color)",labelSize:2*k($).fontSize+"rpx",labelColor:k($).textColor,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1024)))),128))])),_:1},8,["modelValue"])])),_:1})):S("v-if",!0),"style-2"==k($).style?(b(),g(i,{key:3,modelValue:f.value,"onUpdate:modelValue":t[1]||(t[1]=e=>f.value=e),onChange:q,iconPlacement:"left",placement:"column"},{default:y((()=>[(b(!0),R(B,null,z(k($).options,((e,t)=>(b(),g(a,{key:t,onClick:t=>G(e),class:F(["layout-one-content mb-[16rpx]",{"!mb-[0]":k($).options.length-1==t}])},{default:y((()=>[h(s,{activeColor:"var(--primary-color)",labelSize:2*k($).fontSize+"rpx",labelColor:k($).textColor,class:"mr-[20rpx]",label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick","class"])))),128))])),_:1},8,["modelValue"])):S("v-if",!0),"style-3"==k($).style?(b(),g(a,{key:4,onClick:O,class:"layout-one-content justify-between"},{default:y((()=>[k($).field.value.length>0?(b(),g(a,{key:0},{default:y((()=>[h(l,{class:"mr-[10rpx] text-[28rpx]",style:_({color:k($).textColor,"font-size":2*k($).fontSize+"rpx"})},{default:y((()=>[w(C(k(V)),1)])),_:1},8,["style"])])),_:1})):(b(),g(l,{key:1,class:"text-[28rpx] text-[#999]",style:_({"font-size":2*k($).fontSize+"rpx"})},{default:y((()=>[w(C(k(P)),1)])),_:1},8,["style"])),h(l,{class:F(["nc-iconfont nc-icon-xiaV6xx pull-down-arrow text-[#666]",{selected:r.value}]),style:_({"font-size":2*k($).fontSize+2+"rpx !important"})},null,8,["class","style"])])),_:1})):S("v-if",!0),j().length?(b(),g(a,{key:5,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(j(),((e,t)=>(b(),g(a,{key:t,onClick:t=>W(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(M).completeLayout?(b(),g(a,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k($).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(a,{class:F(["layout-two-wrap",{"!pb-[20rpx]":("style-2"==k($).style||"style-3"==k($).style)&&k(M).borderControl,"no-border":!k(M).borderControl}])},{default:y((()=>[h(a,{class:F(["layout-two-label",{"justify-start":"left"==k(M).completeAlign,"justify-end":"right"==k(M).completeAlign}])},{default:y((()=>[h(l,{class:"required"},{default:y((()=>[w(C(k($).field.required?"*":""),1)])),_:1}),h(l,{class:"name",style:_({color:k($).textColor,"font-size":2*k($).fontSize+"rpx","font-weight":k($).fontWeight})},{default:y((()=>[w(C(k($).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),"style-1"==k($).style?(b(),g(a,{key:0,class:"layout-two-content"},{default:y((()=>[h(a,{class:"justify-end"},{default:y((()=>[h(i,{modelValue:f.value,"onUpdate:modelValue":t[2]||(t[2]=e=>f.value=e),onChange:q,iconPlacement:"left"},{default:y((()=>[(b(!0),R(B,null,z(k($).options,((e,t)=>(b(),g(a,{key:t,class:"ml-[30rpx]"},{default:y((()=>[h(s,{activeColor:"var(--primary-color)",labelSize:2*k($).fontSize+"rpx",labelColor:k($).textColor,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1024)))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})):S("v-if",!0),"style-2"==k($).style?(b(),g(a,{key:1,class:"layout-two-content"},{default:y((()=>[h(a,{class:"justify-end w-full"},{default:y((()=>[h(i,{modelValue:f.value,"onUpdate:modelValue":t[3]||(t[3]=e=>f.value=e),onChange:q,placement:"column",iconPlacement:"left"},{default:y((()=>[(b(!0),R(B,null,z(k($).options,((e,t)=>(b(),g(a,{key:t,onClick:t=>G(e),class:F(["border-solid border-[2rpx] border-[#e6e6e6] rounded-[10rpx] flex items-center h-[80rpx] mb-[16rpx] px-[16rpx] box-border",{"mb-[0]":k($).options.length==t+1}])},{default:y((()=>[h(s,{activeColor:"var(--primary-color)",labelSize:2*k($).fontSize+"rpx",labelColor:k($).textColor,class:"!m-[0]",label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick","class"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})):S("v-if",!0),"style-3"==k($).style?(b(),g(a,{key:2,class:"layout-two-content"},{default:y((()=>[h(a,{onClick:O,class:"px-[16rpx] box-border h-[80rpx] flex items-center justify-between border-solid border-[2rpx] border-[#e6e6e6] rounded-[10rpx] w-[100%]"},{default:y((()=>[k($).field.value.length>0?(b(),g(a,{key:0},{default:y((()=>[h(l,{class:"mr-[10rpx] text-[28rpx]",style:_({color:k($).textColor,"font-size":2*k($).fontSize+"rpx"})},{default:y((()=>[w(C(k(V)),1)])),_:1},8,["style"])])),_:1})):(b(),g(l,{key:1,class:"text-[28rpx] text-[#999]",style:_({"font-size":2*k($).fontSize+"rpx"})},{default:y((()=>[w(C(k(P)),1)])),_:1},8,["style"])),h(l,{class:F(["nc-iconfont nc-icon-xiaV6xx pull-down-arrow text-[#666]",{selected:r.value}]),style:_({"font-size":2*k($).fontSize+2+"rpx !important"})},null,8,["class","style"])])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["class"]),m.value&&!m.value.code?(b(),g(a,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(m.value.message),1)])),_:1})):S("v-if",!0),k($).field.remark.text?(b(),g(a,{key:2,class:"layout-two-remark",style:_({color:k($).field.remark.color,fontSize:2*k($).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k($).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),j().length?(b(),g(a,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(j(),((e,t)=>(b(),g(a,{key:t,onClick:t=>W(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),S(" 样式三，下拉弹窗 "),h(u,{show:r.value,mode:"bottom",onClose:t[5]||(t[5]=e=>r.value=!1)},{default:y((()=>[h(a,{class:"p-[15rpx]"},{default:y((()=>[h(d,{"scroll-y":"true",class:"max-h-[450rpx] px-[14rpx] box-border"},{default:y((()=>[h(i,{modelValue:n.value,"onUpdate:modelValue":t[4]||(t[4]=e=>n.value=e),placement:"column",onChange:q,iconPlacement:"right"},{default:y((()=>[(b(!0),R(B,null,z(k($).options,((e,t)=>(b(),g(a,{class:"border-solid border-[0] border-b-[2rpx] border-[#e6e6e6] py-[20rpx]",onClick:N((t=>(e=>{r.value=!1,n.value=e.id,$.value.field.value=[{id:e.id,text:e.text}]})(e)),["stop"]),key:t},{default:y((()=>[h(s,{activeColor:"var(--primary-color)",labelSize:2*k($).fontSize+"rpx",labelColor:k($).textColor,style:{width:"100%"},label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["show"]),"decorate"==k(o).mode?(b(),g(a,{key:2,class:"form-item-mask"})):S("v-if",!0),S(" 隐私弹窗 "),h(Lt,{ref_key:"formPrivacyRef",ref:T,data:k(A)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-cac55a50"]]),pl=Pe(n({__name:"index",props:["component","index","global"],setup(e){const t=e,l=s(),o=u(),a=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),r=i((()=>t.global)),n=i((()=>{var e="";if(e+="position:relative;",a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:a.value.componentStartBgColor?e+="background-color:"+a.value.componentStartBgColor+";":a.value.componentEndBgColor&&(e+="background-color:"+a.value.componentEndBgColor+";"),a.value.componentBgUrl&&(e+=`background-image:url('${d(a.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),"decorate"!=l.mode&&"hover_screen_bottom"==a.value.btnPosition){e+="position: fixed !important;";var t=o.value?o.value.height:0;e+="left: 0;",e+="right: 0;",t&&r.value.bottomTabBarSwitch?e+=`bottom: ${t}px;`:e+="bottom: 0;",a.value.pageStartBgColor&&(a.value.pageStartBgColor&&a.value.pageEndBgColor?e+=`background:linear-gradient(${a.value.pageGradientAngle},${a.value.pageStartBgColor},${a.value.pageEndBgColor});`:a.value.pageStartBgColor?e+=`background: ${a.value.pageStartBgColor};`:a.value.pageEndBgColor&&(e+=`background: ${a.value.pageEndBgColor};`)),a.value.margin&&(a.value.margin.top>0&&(e+="padding-top:"+2*a.value.margin.top+"rpx;"),t&&r.value.bottomTabBarSwitch?e+="padding-bottom:"+2*a.value.margin.bottom+"rpx;":e+=`padding-bottom: ${2*(a.value.margin.bottom+U.value)}rpx;`,e+="padding-right:"+2*a.value.margin.both+"rpx;",e+="padding-left:"+2*a.value.margin.both+"rpx;")}else"hover_screen_bottom"==a.value.btnPosition&&(e+="position: fixed !important;",e+="left: 0;",e+="right: 0;",e+="bottom: 0;");return e})),x=i((()=>{var e="";return"hover_screen_bottom"==a.value.btnPosition&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:a.value.componentStartBgColor?e+="background-color:"+a.value.componentStartBgColor+";":a.value.componentEndBgColor&&(e+="background-color:"+a.value.componentEndBgColor+";")),e})),v=i((()=>{var e="";return a.value.componentBgUrl&&(e+="position:absolute;top:0;right:0;left:0;bottom:0;",e+=`background: rgba(0,0,0,${a.value.componentBgAlpha/10});`,a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;")),e})),R=i((()=>{var e="";return e+=`color: ${a.value.resetBtn.color};`,e+=`background-color: ${a.value.resetBtn.bgColor};`,a.value.topElementRounded&&(e+="border-top-left-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.topElementRounded&&(e+="border-top-right-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomElementRounded+"rpx;"),a.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomElementRounded+"rpx;"),e})),B=i((()=>{var e="";return e+=`color: ${a.value.submitBtn.color};`,e+=`background-color: ${a.value.submitBtn.bgColor};`,a.value.topElementRounded&&(e+="border-top-left-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.topElementRounded&&(e+="border-top-right-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomElementRounded+"rpx;"),a.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomElementRounded+"rpx;"),e})),z=i((()=>{let e="";return e+=`padding-bottom: ${N.value}px;`,e})),F=u([]);c((()=>{D(),"decorate"==l.mode?p((()=>a.value),((e,t)=>{e&&"FormSubmit"==e.componentName&&D()})):($(),p((()=>l.value),((e,t)=>{if(e){let t=j(e);t.components.length&&uni.setStorageSync("diyFormStorage_"+l.id,t)}}),{deep:!0})),f((()=>{o.value=uni.getStorageSync("tabbarInfo")}))}));const D=()=>{},$=()=>{for(let e=0;e<l.value.length;e++){let t=l.value[e];"diy_form"==t.componentType&&"FormSubmit"!=t.componentName&&F.value.push(t)}},I=u(!1),M=()=>{if("decorate"===l.mode)return;let e=!0;for(let r=0;r<F.value.length;r++){let t=F.value[r];if(t.field.required||t.field.value){let o=`diy${t.componentName}Ref`,a=!1;if(l.componentRefs[o]){for(let e=0;e<l.componentRefs[o].length;e++){let t=l.componentRefs[o][e].verify();if(t&&!t.code){a=!0,ye({title:t.message,icon:"none"});break}}if(a){e=!1;break}}}}if(!e)return;if(I.value)return;I.value=!0;let t=uni.getStorageSync("diyFormStorage_"+l.id),o={};o=t?t.components:j(F.value).components;const a={form_id:l.id,value:o,relate_id:""};tt(a).then((e=>{uni.removeStorageSync("diyFormStorage_"+l.id),we({url:"/app/pages/index/diy_form_result",param:{record_id:e.data,form_id:l.id},mode:"redirectTo"}),I.value=!1})).catch((()=>{I.value=!1}))},A=()=>{for(let e=0;e<F.value.length;e++){let t=F.value[e],o=`diy${t.componentName}Ref`;l.componentRefs[o]&&l.componentRefs[o].forEach((e=>{e.reset&&e.reset(t)}))}},j=e=>{let t={validTime:_e(5),components:[]};return e.forEach((e=>{if("diy_form"==e.componentType&&"FormSubmit"!=e.componentName&&e.field.cache){let l=ke(e.field);delete l.remark,delete l.detailComponent,delete l.default,t.components.push({id:e.id,componentName:e.componentName,componentType:e.componentType,componentTitle:e.componentTitle,isHidden:e.isHidden,field:l})}})),t};let N=u(0);const W=T();let U=u(0);return f((()=>{const e=m().in(W);e.select(".iphone-secure").boundingClientRect((e=>{U.value=e?e.height:0})).exec(),setTimeout((()=>{e.select(".submit-wrap").boundingClientRect((e=>{N.value=e?e.height:0})).exec()}),500)})),(e,t)=>{const o=E;return b(),g(o,null,{default:y((()=>[h(o,{class:"overflow-hidden"},{default:y((()=>[h(o,{style:_(k(v))},null,8,["style"]),h(o,{class:"relative submit-wrap z-10",style:_(k(n))},{default:y((()=>[h(o,{class:"flex flex-col items-center",style:_(k(x))},{default:y((()=>[h(o,{class:"w-[100%] h-[86rpx] text-[28rpx] flex items-center justify-center",onClick:M,style:_(k(B))},{default:y((()=>[w(C(k(a).submitBtn.text),1)])),_:1},8,["style"]),k(a).resetBtn.control?(b(),g(o,{key:0,class:"w-[100%] h-[86rpx] mt-[20rpx] text-[28rpx] flex items-center justify-center",onClick:A,style:_(k(R))},{default:y((()=>[w(C(k(a).resetBtn.text),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"])])),_:1},8,["style"])])),_:1}),"hover_screen_bottom"==k(a).btnPosition&&"decorate"!=k(l).mode?(b(),g(o,{key:0,class:"w-[100%]",style:_(k(z))},null,8,["style"])):S("v-if",!0),S(" 苹果安全距离，辅助计算 "),h(o,{class:"iphone-secure"}),S(" 遮罩层，装修使用 "),"decorate"==k(l).mode?(b(),g(o,{key:1,class:"form-item-mask"})):S("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-958939db"]]),fl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s();u({id:0,name:"",mobile:"",province_id:0,city_id:0,district_id:0,lat:"",lng:"",address:"",address_name:"",full_address:"",is_default:0,area:""}),u(Array(10).fill(!1));const a=i((()=>"decorate"==o.mode?o.value[l.index]:l.component));i((()=>"decorate"===o.mode));const r=u(null);u(!1),i((()=>[{name:"男",value:1},{name:"女",value:2}]));let n=new Date;n.setFullYear(n.getFullYear()+1),u(n.getTime());let f=new Date;u(f.getTime()),u(!1),u(!1),i((()=>r.value&&r.value.value?m(r.value.value,r.value.dateFormat):a.value.placeholder||"请选择日期"));const m=(e="",t)=>{console.log(e,t);let l="YYYY-MM-DD HH:mm"==t?e:ce(e),o=l>9999999999?new Date(l):new Date(1e3*l),a=o.getFullYear(),r=String(o.getMonth()+1).padStart(2,"0"),n=String(o.getDate()).padStart(2,"0");console.log(a);const s=String(o.getHours()).padStart(2,"0"),i=String(o.getMinutes()).padStart(2,"0");let d="";return"YYYY年M月D日"==t?d=`${a}年${r}月${n}日`:"YYYY-MM-DD"==t?d=`${a}-${r}-${n}`:"YYYY/MM/DD"==t?d=`${a}/${r}/${n}`:"YYYY-MM-DD HH:mm"==t&&(d=`${a}-${r}-${n} ${s}:${i}`),d};u(),u(!1);const x=i((()=>{var e="";return e+="position:relative;",a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.componentBgUrl&&(e+=`background-image:url('${d(a.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e}));c((()=>{v(),"decorate"==o.mode&&p((()=>a.value),((e,t)=>{e&&"FormTable"==e.componentName&&v()})),console.log(a.value)}));const v=()=>{};return t({verify:()=>({code:!0,message:""}),reset:()=>{}}),(e,t)=>{const l=E;return b(),g(l,{style:_(k(x)),class:"form-item-frame"},null,8,["style"])}}}),[["__scopeId","data-v-969b5ec1"]]),ml=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(null),r=u(null),n=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),f=i((()=>l.global)),m=i((()=>{let e="";return e+=n.value.placeholder,e})),x=i((()=>`${n.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),v=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}if(n.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},T=e=>{"privacy"==e&&r.value.open()},$=i((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{I(),"decorate"==o.mode&&p((()=>n.value),((e,t)=>{e&&"FormTextarea"==e.componentName&&I()}))}));const I=()=>{("decorate"==o.mode||""==n.value.field.value&&n.value.field.default)&&(n.value.field.value=n.value.field.default)},M=u("38rpx"),A=e=>{let t=e.detail.height/e.detail.lineCount;n.value.rowCount>e.detail.lineCount?M.value=e.detail.height?2*e.detail.height+"rpx":"38rpx":n.value.rowCount<=e.detail.lineCount&&(M.value=t?t*n.value.rowCount*2+"rpx":"38rpx")},j=i((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=o.mode&&(e.code=!1,e.message=`${m.value}`),a.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,s=E,i=O,d=de("viwe");return k(n).viewFormDetail?(b(),g(s,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>[h(s,{class:"detail-two-content"},{default:y((()=>[h(s,null,{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(s,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(s,{key:1,style:_(k($)),class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(s,{class:"layout-one-content !py-[20rpx] !h-[auto]"},{default:y((()=>[h(i,{class:"w-[100%]",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx",height:M.value}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(j),onLinechange:A,maxlength:"500"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1}),v().length?(b(),g(s,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(v(),((e,t)=>(b(),g(s,{key:t,onClick:t=>T(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(s,{class:F(["layout-two-wrap",{"no-border":!k(f).borderControl}])},{default:y((()=>[h(s,{class:F(["layout-two-label",{"justify-start":"left"==k(f).completeAlign,"justify-end":"right"==k(f).completeAlign}])},{default:y((()=>[k(n).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(d,{class:"layout-two-content"},{default:y((()=>[h(i,{class:"w-[100%]",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx",height:M.value}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(j),onLinechange:A,maxlength:"500"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1})])),_:1},8,["class"]),a.value&&!a.value.code?(b(),g(s,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(b(),g(s,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),v().length?(b(),g(s,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(v(),((e,t)=>(b(),g(s,{key:t,onClick:t=>T(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(b(),g(s,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Lt,{ref_key:"formPrivacyRef",ref:r,data:k(x)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-ad622a26"]]),xl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(!1);u(!1);const r=u(!1),n=u(null),f=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),m=i((()=>l.global)),T=()=>{let e=[];if(f.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},$=i((()=>{var e="";return f.value.field.value?(e=f.value.field.value,f.value.field.value=e):f.value.defaultControl?("current"==f.value.timeWay?e=N():"diy"==f.value.timeWay&&(e=f.value.field.default),f.value.field.value=e):e=f.value.placeholder,e})),I=i((()=>{var e="";return e+="position:relative;",f.value.componentStartBgColor&&f.value.componentEndBgColor?e+=`background:linear-gradient(${f.value.componentGradientAngle},${f.value.componentStartBgColor},${f.value.componentEndBgColor});`:f.value.componentStartBgColor?e+="background-color:"+f.value.componentStartBgColor+";":f.value.componentEndBgColor&&(e+="background-color:"+f.value.componentEndBgColor+";"),f.value.componentBgUrl&&(e+=`background-image:url('${d(f.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),f.value.topRounded&&(e+="border-top-left-radius:"+2*f.value.topRounded+"rpx;"),f.value.topRounded&&(e+="border-top-right-radius:"+2*f.value.topRounded+"rpx;"),f.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*f.value.bottomRounded+"rpx;"),f.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*f.value.bottomRounded+"rpx;"),e}));c((()=>{M(),"decorate"==o.mode&&p((()=>f.value),((e,t)=>{e&&"FormTime"==e.componentName&&M()}))}));const M=()=>{},A=()=>{"decorate"!==o.mode&&(r.value=!0)},j=e=>{f.value.field.value=e.value,a.value=!1},N=(e="")=>{let t=e?new Date(e):new Date,l="";return l=`${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`,l};return t({verify:()=>{const e={code:!0,message:""};return f.value.field.required&&""==f.value.field.value&&(e.code=!1,e.message=`请选择${f.value.placeholder}`),n.value=e,e},reset:()=>{f.value.field.value=""}}),(e,t)=>{const l=D,r=E,s=x(v("u-datetime-picker"),Qe);return k(f).viewFormDetail?(b(),g(r,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(m).completeLayout?(b(),g(r,{key:0,class:"base-layout-one"},{default:y((()=>[h(r,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(f).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(f).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(m).completeLayout?(b(),g(r,{key:1,class:"base-layout-two"},{default:y((()=>[h(r,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(f).field.name),1)])),_:1}),h(r,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(f).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(r,{key:1,style:_(k(I)),class:"form-item-frame"},{default:y((()=>["style-1"==k(m).completeLayout?(b(),g(r,{key:0,class:"base-layout-one"},{default:y((()=>[h(r,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k(f).textColor,"font-size":2*k(f).fontSize+"rpx","font-weight":k(f).fontWeight})},{default:y((()=>[w(C(k(f).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(f).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(f).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(f).field.remark.text?(b(),g(r,{key:0,class:"layout-one-remark",style:_({color:k(f).field.remark.color,fontSize:2*k(f).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(f).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),n.value&&!n.value.code?(b(),g(r,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(n.value.message),1)])),_:1})):S("v-if",!0),h(r,{class:"layout-one-content"},{default:y((()=>[h(r,{class:"nc-iconfont nc-icon-a-shijianV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(r,{class:F(["flex-1 text-overflow-ellipsis flex",{"!text-[#999]":!k(f).field.value&&!k(f).defaultControl}]),style:_({color:k(f).textColor,"font-size":2*k(f).fontSize+"rpx"}),onClick:t[0]||(t[0]=e=>a.value=!0)},{default:y((()=>[w(C(k($)),1)])),_:1},8,["class","style"])])),_:1}),T().length?(b(),g(r,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(T(),((e,t)=>(b(),g(r,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(m).completeLayout?(b(),g(r,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(f).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(r,{class:F(["layout-two-wrap",{"no-border":!k(m).borderControl}])},{default:y((()=>[h(r,{class:F(["layout-two-label",{"justify-start":"left"==k(m).completeAlign,"justify-end":"right"==k(m).completeAlign}])},{default:y((()=>[k(f).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(f).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(f).textColor,"font-size":2*k(f).fontSize+"rpx","font-weight":k(f).fontWeight})},{default:y((()=>[w(C(k(f).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(r,{class:"layout-two-content",onClick:A},{default:y((()=>[h(r,{class:F(["flex-1 text-overflow-ellipsis flex justify-end",{"!text-[#999]":!k(f).field.value&&!k(f).defaultControl}]),style:_({color:k(f).textColor,"font-size":2*k(f).fontSize+"rpx"}),onClick:t[1]||(t[1]=e=>a.value=!0)},{default:y((()=>[w(C(k($)),1)])),_:1},8,["class","style"]),h(l,{class:"nc-iconfont !text-[#666] !text-[36rpx] nc-icon-youV6xx -mr-[8rpx]"})])),_:1})])),_:1},8,["class"]),n.value&&!n.value.code?(b(),g(r,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(n.value.message),1)])),_:1})):S("v-if",!0),k(f).field.remark.text?(b(),g(r,{key:2,class:"layout-two-remark",style:_({color:k(f).field.remark.color,fontSize:2*k(f).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(f).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),T().length?(b(),g(r,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(T(),((e,t)=>(b(),g(r,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),h(s,{show:a.value,modelValue:k(f).field.value,"onUpdate:modelValue":t[2]||(t[2]=e=>k(f).field.value=e),mode:"time",onCancel:t[3]||(t[3]=e=>a.value=!1),onClose:t[4]||(t[4]=e=>a.value=!1),onConfirm:j,closeOnClickOverlay:"true"},null,8,["show","modelValue"]),S(" 遮罩层，装修使用 "),"decorate"==k(o).mode?(b(),g(r,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-b16db64e"]]),vl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u(!1),r=u(!1),n=u(!1),f=u(null),m=i((()=>"decorate"==o.mode?o.value[l.index]:l.component)),T=i((()=>l.global)),$=()=>{let e=[];if(m.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},I=i((()=>{let e="",t="";return m.value.field.value.start.date?(e=m.value.field.value.start.date,t=m.value.field.value.start.date):m.value.start.defaultControl?"current"==m.value.start.timeWay?(e=q(),t=q()):"diy"==m.value.start.timeWay&&(e=m.value.field.default.start.date,t=m.value.field.default.start.date):(e=m.value.start.placeholder,t=""),m.value.field.value.start.date=t,m.value.field.value.start.timestamp=t?G(t):0,e})),M=i((()=>{let e="",t="";if(m.value.field.value.end.date)e=m.value.field.value.end.date,t=m.value.field.value.end.date;else if(m.value.end.defaultControl)if("current"==m.value.end.timeWay){let l=new Date,o=new Date(l.getTime()+6e5);e=q(o),t=q(o)}else"diy"==m.value.end.timeWay&&(e=m.value.field.default.end.date,t=m.value.field.default.end.date);else e=m.value.end.placeholder,t="";return m.value.field.value.end.date=t,m.value.field.value.end.timestamp=t?G(t):0,e})),A=i((()=>{var e="";return e+="position:relative;",m.value.componentStartBgColor&&m.value.componentEndBgColor?e+=`background:linear-gradient(${m.value.componentGradientAngle},${m.value.componentStartBgColor},${m.value.componentEndBgColor});`:m.value.componentStartBgColor?e+="background-color:"+m.value.componentStartBgColor+";":m.value.componentEndBgColor&&(e+="background-color:"+m.value.componentEndBgColor+";"),m.value.componentBgUrl&&(e+=`background-image:url('${d(m.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),m.value.topRounded&&(e+="border-top-left-radius:"+2*m.value.topRounded+"rpx;"),m.value.topRounded&&(e+="border-top-right-radius:"+2*m.value.topRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*m.value.bottomRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*m.value.bottomRounded+"rpx;"),e}));c((()=>{j(),"decorate"==o.mode&&p((()=>m.value),((e,t)=>{e&&"FormTimeScope"==e.componentName&&j()}))}));const j=()=>{},N=()=>{"decorate"!==o.mode&&(n.value=!0)},W=e=>{m.value.field.value.start.date=e.value,m.value.field.value.start.timestamp=G(e.value);let t=e.value,l=new Date(`1970-01-01T${e.value}:00`);l.setMinutes(l.getMinutes()+10),t=q(l),m.value.field.value.end.date=t,m.value.field.value.end.timestamp=G(t),a.value=!1},U=e=>{m.value.field.value.end.date=e.value,m.value.field.value.end.timestamp=G(e.value),r.value=!1},Y=e=>{L.value||(L.value=m.value.field.value.end.date),m.value.field.value.end.date=e.value},P=()=>{L.value&&(m.value.field.value.end.date=L.value),L.value="",r.value=!1},V=i((()=>{let e=m.value.field.value.start.date.split(":");return Number(e[0]?e[0]:"0")}));let L=u("");const O=i({get:()=>{let e=m.value.field.value.start.date.split(":"),t=m.value.field.value.end.date.split(":"),l=e[0]==t[0]?e[1]:0;return Number(l||"0")},set:e=>{}}),q=(e="")=>{let t=e?new Date(e):new Date;return`${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},G=e=>{let t=e.split(":"),l=0;return t[0]&&(l+=60*t[0]*60),t[1]&&(l+=60*t[1]),t[2]&&(l+=t[2]),l};return t({verify:()=>{const e={code:!0,message:""};return m.value.field.required&&""==m.value.field.value.start.date?(e.code=!1,e.message=`请选择${m.value.start.placeholder}`):m.value.field.required&&""==m.value.field.value.end.date?(e.code=!1,e.message=`请选择${m.value.end.placeholder}`):m.value.field.value.start.timestamp>=m.value.field.value.end.timestamp&&m.value.field.value.start.timestamp&&m.value.field.value.end.timestamp&&(e.code=!1,e.message="开始时间不能大于等于结束时间"),f.value=e,e},reset:()=>{m.value.field.value.start.date="",m.value.field.value.start.timestamp=0,m.value.field.value.end.date="",m.value.field.value.end.timestamp=0}}),(e,t)=>{const l=D,n=E,s=x(v("u-datetime-picker"),Qe);return k(m).viewFormDetail?(b(),g(n,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(T).completeLayout?(b(),g(n,{key:0,class:"base-layout-one"},{default:y((()=>[k(m).field.value.start.date||k(m).field.value.end.date?(b(),g(n,{key:0,class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(n,{class:"detail-one-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(m).field.value.start.date),1)])),_:1}),k(m).field.value.start.date&&k(m).field.value.end.date?(b(),g(l,{key:0},{default:y((()=>[w(" -")])),_:1})):S("v-if",!0),h(l,null,{default:y((()=>[w(C(k(m).field.value.end.date),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(T).completeLayout?(b(),g(n,{key:1,class:"base-layout-two"},{default:y((()=>[k(m).field.value.start.date||k(m).field.value.end.date?(b(),g(n,{key:0,class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(n,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(m).field.value.start.date),1)])),_:1}),k(m).field.value.start.date&&k(m).field.value.end.date?(b(),g(l,{key:0},{default:y((()=>[w(" -")])),_:1})):S("v-if",!0),h(l,null,{default:y((()=>[w(C(k(m).field.value.end.date),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0)])),_:1})):(b(),g(n,{key:1,style:_(k(A)),class:"form-item-frame"},{default:y((()=>["style-1"==k(T).completeLayout?(b(),g(n,{key:0,class:"base-layout-one"},{default:y((()=>[h(n,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(m).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(m).field.remark.text?(b(),g(n,{key:0,class:"layout-one-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),f.value&&!f.value.code?(b(),g(n,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(f.value.message),1)])),_:1})):S("v-if",!0),h(n,{class:"flex items-center"},{default:y((()=>[h(n,{class:"layout-one-content flex-1",onClick:t[0]||(t[0]=e=>a.value=!0)},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-a-shijianV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(n,{class:F(["flex-1 text-overflow-ellipsis",{"!text-[#999]":!k(m).defaultControl&&!k(m).field.value.start.date}]),style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"})},{default:y((()=>[w(C(k(I)),1)])),_:1},8,["class","style"])])),_:1}),h(n,{class:"mx-[10rpx]",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"})},{default:y((()=>[w("-")])),_:1},8,["style"]),h(n,{class:"layout-one-content flex-1",onClick:t[1]||(t[1]=e=>r.value=!0)},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-a-shijianV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(n,{class:F(["flex-1 text-overflow-ellipsis",{"!text-[#999]":!k(m).defaultControl&&!k(m).field.value.end.date}]),style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"})},{default:y((()=>[w(C(k(M)),1)])),_:1},8,["class","style"])])),_:1})])),_:1}),$().length?(b(),g(n,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z($(),((e,t)=>(b(),g(n,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(T).completeLayout?(b(),g(n,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(m).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(n,{class:F(["layout-two-wrap",{"no-border":!k(T).borderControl}])},{default:y((()=>[h(n,{class:F(["layout-two-label",{"justify-start":"left"==k(T).completeAlign,"justify-end":"right"==k(T).completeAlign}])},{default:y((()=>[k(m).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(n,{class:"layout-two-content",onClick:N},{default:y((()=>[h(n,{class:F(["text-overflow-ellipsis flex justify-center",{"!text-[#999]":!k(m).field.value.start.date&&!k(m).defaultControl}]),style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),onClick:t[2]||(t[2]=e=>a.value=!0)},{default:y((()=>[w(C(k(I)),1)])),_:1},8,["class","style"]),h(n,{class:"mx-[10rpx]",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"})},{default:y((()=>[w("-")])),_:1},8,["style"]),h(n,{class:F(["text-overflow-ellipsis flex justify-center",{"!text-[#999]":!k(m).field.value.end.date&&!k(m).defaultControl}]),style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),onClick:t[3]||(t[3]=e=>r.value=!0)},{default:y((()=>[w(C(k(M)),1)])),_:1},8,["class","style"]),h(l,{class:"nc-iconfont !text-[#666] !text-[36rpx] nc-icon-youV6xx -mr-[8rpx]"})])),_:1})])),_:1},8,["class"]),f.value&&!f.value.code?(b(),g(n,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(f.value.message),1)])),_:1})):S("v-if",!0),k(m).field.remark.text?(b(),g(n,{key:2,class:"layout-two-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),$().length?(b(),g(n,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z($(),((e,t)=>(b(),g(n,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),h(s,{show:a.value,modelValue:k(m).field.value.start.date,"onUpdate:modelValue":t[4]||(t[4]=e=>k(m).field.value.start.date=e),mode:"time",onCancel:t[5]||(t[5]=e=>a.value=!1),onConfirm:W,onClose:t[6]||(t[6]=e=>a.value=!1),closeOnClickOverlay:"true"},null,8,["show","modelValue"]),h(s,{show:r.value,minHour:k(V),minMinute:k(O),modelValue:k(m).field.value.end.date,"onUpdate:modelValue":t[7]||(t[7]=e=>k(m).field.value.end.date=e),mode:"time",onCancel:P,onConfirm:U,onChange:Y,onClose:t[8]||(t[8]=e=>a.value=!1),closeOnClickOverlay:"true"},null,8,["show","minHour","minMinute","modelValue"]),S(" 遮罩层，装修使用 "),"decorate"==k(o).mode?(b(),g(n,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-38eafa44"]]),bl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=s(),a=u([]),r=u(null),n=i((()=>"decorate"==o.mode?o.value[l.index]:l.component));i((()=>{let e="";return n.value&&n.value.uploadMode&&("shoot_and_album"==n.value.uploadMode?e="album":"shoot_only"==n.value.uploadMode&&(e="camera")),e}));const f=i((()=>l.global)),m=i((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{j(),"decorate"==o.mode&&p((()=>n.value),((e,t)=>{e&&"FormVideo"==e.componentName&&j()}))})),i((()=>"decorate"===o.mode)),i((()=>a.value.map((e=>({url:d(e)})))));const T=e=>{e.file.forEach((e=>{I(e)}))},I=e=>{Ce({filePath:e.url,name:"file"}).then((e=>{n.value.field.value.push(e.data.url)})).catch((()=>{}))},M=e=>{n.value.field.value.splice(e.index,1)},A=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},j=()=>{};return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&(!n.value.field.value||n.value.field.value&&!n.value.field.value.length)&&(e.code=!1,e.message="请上传视频"),r.value=e,e},reset:()=>{n.value.field.value=[]}}),(e,t)=>{const l=D,a=$,s=E,i=x(v("u-upload"),Ke);return k(n).viewFormDetail?(b(),g(s,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(s,{class:"flex flex-wrap detail-one-content-value pt-[6rpx]"},{default:y((()=>[(b(!0),R(B,null,z(k(n).field.value,((t,l)=>(b(),g(s,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx]",key:l},{default:y((()=>[h(a,{class:"w-[100%] h-[100%]",src:k(d)(t),onClick:o=>e.handleImg(t,l),mode:"aspectFill"},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>[h(s,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(s,{class:"flex flex-wrap w-[80%] justify-end"},{default:y((()=>[(b(!0),R(B,null,z(k(n).field.value,((t,l)=>(b(),g(s,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx]",key:l},{default:y((()=>[h(a,{class:"w-[100%] h-[100%]",src:k(d)(t),onClick:o=>e.handleImg(t,l),mode:"aspectFill"},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(s,{key:1,style:_(k(m)),class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(b(),g(s,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),r.value&&!r.value.code?(b(),g(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),h(s,{class:"flex flex-wrap"},{default:y((()=>[(b(!0),R(B,null,z(k(n).field.value,((e,t)=>(b(),g(s,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx] layout-one-content",key:t},{default:y((()=>[h(a,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(s,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>M(t)},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),S(' \t<view class="flex items-center flex-1"\n\t\t\t\t\tv-if="diyComponent.uploadMode.length > 1 && diyComponent.field.value.length <= 0">\n\t\t\t\t\t<view class="layout-one-content !p-[0] flex-1 !items-stretch !h-[100rpx]">\n\t\t\t\t\t\t<u-upload accept="image" @afterRead="afterRead" multiple :maxCount="9" capture="camera">\n\t\t\t\t\t\t\t<view class="flex items-center h-[100%] w-[100%] pl-[30rpx] box-border">\n\t\t\t\t\t\t\t\t<text class="nc-iconfont nc-icon-xiangjiV6xx"></text>\n\t\t\t\t\t\t\t\t<text class="text-[28rpx] ml-[10rpx]">拍照上传</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</u-upload>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class="layout-one-content !p-[0] ml-[20rpx] !items-stretch flex-1 !h-[100rpx]">\n\t\t\t\t\t\t<u-upload accept="image" @afterRead="afterRead" multiple :maxCount="9" capture="album">\n\t\t\t\t\t\t\t<view class="flex items-center h-[100%] w-[100%] pl-[30rpx] box-border">\n\t\t\t\t\t\t\t\t<text class="nc-iconfont nc-icon-tupiandaohangpc"></text>\n\t\t\t\t\t\t\t\t<text class="text-[28rpx] ml-[10rpx]">从相册中选择</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</u-upload>\n\t\t\t\t\t</view>\n\t\t\t\t</view> '),h(s,{class:"layout-one-content h-[180rpx] w-[180rpx] !px-[0]"},{default:y((()=>["shoot_and_album"==k(n).uploadMode?(b(),g(i,{key:0,accept:"image",onAfterRead:T,capture:"album",maxCount:1},{default:y((()=>[h(s,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[36rpx] nc-icon-jiahaoV6xx mb-[16rpx]"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w("添加视频")])),_:1})])),_:1})])),_:1})):(b(),g(i,{key:1,accept:"image",onAfterRead:T,maxCount:1,maxDuration:60,capture:"camera"},{default:y((()=>[h(s,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[36rpx] nc-icon-jiahaoV6xx mb-[16rpx]"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w("拍摄上传")])),_:1})])),_:1})])),_:1}))])),_:1})])),_:1}),A().length?(b(),g(s,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(A(),((t,l)=>(b(),g(s,{key:l,onClick:l=>e.eventFn(t.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(s,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(s,{class:F(["layout-two-wrap",{"no-border":!k(f).borderControl}])},{default:y((()=>[h(s,{class:F(["layout-two-label",{"justify-start":"left"==k(f).completeAlign,"justify-end":"right"==k(f).completeAlign}])},{default:y((()=>[k(n).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{class:"layout-two-content flex-wrap"},{default:y((()=>[(b(!0),R(B,null,z(k(n).field.value,((e,t)=>(b(),g(s,{class:"relative border-box w-[180rpx] !h-[180rpx] ml-[16rpx] mb-[16rpx] border-box border-[2rpx] border-solid border-[#e6e6e6] rounded-[10rpx] flex items-center",key:t},{default:y((()=>[h(a,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(s,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>M(t)},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),h(s,{class:"items-start border-box border-[2rpx] ml-[16rpx] mb-[16rpx] border-solid border-[#e6e6e6] rounded-[10rpx]"},{default:y((()=>["shoot_and_album"==k(n).uploadMode?(b(),g(i,{key:0,accept:"video",onAfterRead:T,maxCount:1,capture:"album"},{default:y((()=>[h(s,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[36rpx] nc-icon-jiahaoV6xx mb-[16rpx]"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w("添加视频")])),_:1})])),_:1})])),_:1})):(b(),g(i,{key:1,accept:"video",onAfterRead:T,maxCount:1,maxDuration:60,capture:"camera"},{default:y((()=>[h(s,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[36rpx] nc-icon-jiahaoV6xx mb-[16rpx]"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w("拍摄上传")])),_:1})])),_:1})])),_:1}))])),_:1})])),_:1})])),_:1},8,["class"]),r.value&&!r.value.code?(b(),g(s,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(b(),g(s,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),A().length?(b(),g(s,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(b(!0),R(B,null,z(A(),((t,l)=>(b(),g(s,{key:l,onClick:l=>e.eventFn(t.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(b(),g(s,{key:2,class:"form-item-mask"})):S("v-if",!0),S(' <view class="relative">\n\t\t\t<view class="p-[10rpx] flex items-center ">\n\t\t\t\t<view class="w-[25%] flex items-center">\n\t\t\t\t\t<text class="text-overflow-ellipsis"\n\t\t\t\t\t\t:style="{\'color\': diyComponent.textColor,\'font-size\': (diyComponent.fontSize * 2) + \'rpx\' ,\'font-weight\': diyComponent.fontWeight}">{{ diyComponent.field.name }}</text>\n\t\t\t\t\t<text class="text-[#ec0003]">{{ diyComponent.field.required ? \'*\' : \'\' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class="w-[75%] flex justify-center items-center">\n\t\t\t\t\t<u-upload :fileList="imgListPreview" :disabled="isDisabled" @afterRead="afterRead"\n\t\t\t\t\t\t@delete="deletePic" multiple :maxCount="diyComponent.limit" />\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t</view> ')])),_:1},8,["style"]))}}}),[["__scopeId","data-v-d2b6b77b"]]),gl=Pe(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=Se(),a=s();u(!1);const r=u(null),n=i((()=>"decorate"==a.mode?a.value[l.index]:l.component)),f=i((()=>l.global)),m=()=>{x()},x=async()=>{o&&(console.log(o),n.value.field.value=o.info.nickname)},v=i((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{R(),"decorate"==a.mode&&p((()=>n.value),((e,t)=>{e&&"FormWechatName"==e.componentName&&R()}))}));const R=()=>{},B=i((()=>"decorate"==a.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=a.mode&&(e.code=!1,e.message="111"),r.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,o=E,s=L;return k(n).viewFormDetail?(b(),g(o,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(o,{key:0,class:"base-layout-one"},{default:y((()=>[h(o,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(o,{key:1,class:"base-layout-two"},{default:y((()=>[h(o,{class:"detail-two-content"},{default:y((()=>[h(o,null,{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(o,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(b(),g(o,{key:1,style:_(k(v)),class:"form-item-frame"},{default:y((()=>["style-1"==k(f).completeLayout?(b(),g(o,{key:0,class:"base-layout-one"},{default:y((()=>[h(o,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(a).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(b(),g(o,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),r.value&&!r.value.code?(b(),g(o,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),h(s,{type:"nickname",class:"layout-one-content",placeholder:"获取填表人的微信名",placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(B),onClick:m},null,8,["placeholder-style","style","modelValue","disabled"]),S(' <view class="layout-one-attribute-wrap" v-if="inputAttribute().length">\n\t\t        <view v-for="(item,index) in inputAttribute()" :key="index" @click="eventFn(item.type)" class="layout-one-attribute-item">{{ item.title }}</view>\n\t\t    </view> ')])),_:1})):S("v-if",!0),"style-2"==k(f).completeLayout?(b(),g(o,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(a).mode&&k(n).isHidden?(b(),g(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(H)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(o,{class:F(["layout-two-wrap",{"no-border":!k(f).borderControl}])},{default:y((()=>[h(o,{class:F(["layout-two-label",{"justify-start":"left"==k(f).completeAlign,"justify-end":"right"==k(f).completeAlign}])},{default:y((()=>[k(n).field.required?(b(),g(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{type:"nickname",class:"layout-two-content no-flex",placeholder:"获取填表人的微信名",placeholderClass:"layout-two-input-placeholder",onClick:m,"placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(B)},null,8,["placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),r.value&&!r.value.code?(b(),g(o,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(b(),g(o,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),S(' <view class="layout-two-attribute-wrap" v-if="inputAttribute().length">\n\t\t        <view v-for="(item,index) in inputAttribute()" :key="index" @click="eventFn(item.type)" class="layout-two-attribute-item">{{ item.title }}</view>\n\t\t    </view> ')])),_:1})):S("v-if",!0),"decorate"==k(a).mode?(b(),g(o,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-beba8b3d"]]),yl=Pe(n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>o.value.list.length>o.value.pageCount*o.value.rowCount)),r=i((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),n=i((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${H.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e})),M=e=>{let t={width:""};return t.width=100/o.value.rowCount+"%",t},A=u(0),j=e=>{A.value=e.detail.current},N=(e,t)=>{let l=o.value.pageCount*o.value.rowCount;return e>=[(t-1)*l]&&e<[t*l]},W="graphic_nav_horizontal_page_slide_swiperheight_"+t.index+"_"+o.value.list.length,U=u(uni.getStorageSync(W)||"");c((()=>{L(),"decorate"==l.mode&&p((()=>o.value),((e,t)=>{e&&"GraphicNav"==e.componentName&&L()}))}));const V=T(),H=u(0),L=()=>{f((()=>{(()=>{if("horizontal"==o.value.layout&&"pageSlide"==o.value.showStyle){var e=0;m().in(V).select(".graphic-nav-item").boundingClientRect((t=>{let l=1;2==o.value.pageCount&&(l=o.value.list.length/o.value.rowCount>1?2:1),e=t.height*l,U.value=e+"px",uni.setStorageSync(W,U.value)})).exec()}})();m().in(V).select(".diy-graphic-nav").boundingClientRect((e=>{e&&(H.value=e.height)})).exec()}))},O=()=>{let e=1;return 2==o.value.pageCount&&(e=o.value.list.length>o.value.rowCount?2:1),e},q=(e,t)=>{let l=e+1;if(2==o.value.pageCount){let e=Math.ceil(o.value.list.length/o.value.rowCount);for(let a=1;a<=e;a++){if(1==t&&a%2!=0&&l>(a-1)*o.value.rowCount&&l<=a*o.value.rowCount)return!0;if(2==t&&a%2==0&&l>(a-1)*o.value.rowCount&&l<=a*o.value.rowCount)return!0}}return!1};return(e,t)=>{const s=E,i=$,u=D,c=x(v("u-icon"),Ye),p=Y,f=P,m=I;return b(),g(s,{style:_(k(r))},{default:y((()=>[h(s,{style:_(k(n))},null,8,["style"]),h(s,{class:"diy-graphic-nav relative"},{default:y((()=>["vertical"==k(o).layout?(b(),g(s,{key:0,class:"graphic-nav"},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,((e,t)=>(b(),g(s,{class:"graphic-nav-item",key:e.id},{default:y((()=>[h(s,{onClick:t=>k(l).toRedirect(e.link),class:F(["flex items-center justify-between py-3 px-4",0==t?"border-t-0":"border-t"])},{default:y((()=>["text"!=k(o).mode?(b(),g(s,{key:0,class:"graphic-img relative flex items-center w-10 h-10 mr-[20rpx]",style:_({width:2*k(o).imageSize+"rpx",height:2*k(o).imageSize+"rpx"})},{default:y((()=>[e.imageUrl?(b(),g(i,{key:0,src:k(d)(e.imageUrl),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])):(b(),g(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])),e.label.control?(b(),g(u,{key:2,class:"tag absolute -top-[10rpx] -right-[24rpx] text-white rounded-[24rpx] rounded-bl-none transform scale-80 py-1 px-2 text-xs",style:_({color:e.label.textColor,backgroundImage:"linear-gradient("+e.label.bgColorStart+","+e.label.bgColorEnd+")"})},{default:y((()=>[w(C(e.label.text),1)])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1032,["style"])):S("v-if",!0),"img"!=k(o).mode?(b(),g(u,{key:1,class:"graphic-text w-full truncate leading-normal",style:_({fontSize:2*k(o).font.size+"rpx",fontWeight:k(o).font.weight,color:k(o).font.color})},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["style"])):S("v-if",!0),h(c,{name:"arrow-right",color:"#999999",size:"12"})])),_:2},1032,["onClick","class"])])),_:2},1024)))),128))])),_:1})):"horizontal"==k(o).layout&&"pageSlide"==k(o).showStyle?(b(),g(s,{key:1,class:"pt-[10rpx]"},{default:y((()=>[h(f,{class:"graphic-nav swiper relative",style:_({height:U.value,width:"95%",margin:"0 auto",opacity:U.value?1:0}),circular:"",onChange:j},{default:y((()=>[(b(!0),R(B,null,z(Math.ceil(k(o).list.length/(k(o).pageCount*k(o).rowCount)),((e,t)=>(b(),g(p,{class:"graphic-nav-wrap flex flex-wrap"},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,((t,a)=>(b(),R(B,null,[N(a,e)?(b(),g(s,{class:F([k(o).mode]),key:t.id,style:_({width:100/k(o).rowCount+"%"})},{default:y((()=>[h(s,{onClick:e=>k(l).toRedirect(t.link),class:"graphic-nav-item flex flex-col items-center py-2"},{default:y((()=>["text"!=k(o).mode?(b(),g(s,{key:0,class:"graphic-img relative flex items-center justify-center w-10 h-10",style:_({width:2*k(o).imageSize+"rpx",height:2*k(o).imageSize+"rpx"})},{default:y((()=>[t.imageUrl?(b(),g(i,{key:0,src:k(d)(t.imageUrl),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])):(b(),g(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])),t.label.control?(b(),g(u,{key:2,class:"tag absolute -top-[10rpx] -right-[24rpx] text-white rounded-[24rpx] rounded-bl-none transform scale-80 py-1 px-2 text-xs",style:_({color:t.label.textColor,backgroundImage:"linear-gradient("+t.label.bgColorStart+","+t.label.bgColorEnd+")"})},{default:y((()=>[w(C(t.label.text),1)])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1032,["style"])):S("v-if",!0),"img"!=k(o).mode?(b(),g(u,{key:1,class:F(["graphic-text w-full text-center truncate leading-normal",{"pt-[16rpx]":"text"!=k(o).mode}]),style:_({fontSize:2*k(o).font.size+"rpx",fontWeight:k(o).font.weight,color:k(o).font.color})},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["class","style"])):S("v-if",!0)])),_:2},1032,["onClick"])])),_:2},1032,["class","style"])):S("v-if",!0)],64)))),256))])),_:2},1024)))),256))])),_:1},8,["style"]),k(a)&&U.value?(b(),g(s,{key:0,class:"graphic-nav-indicator-dot"},{default:y((()=>[h(s,{class:F(["dots-wrap",[k(o).swiper.indicatorAlign]])},{default:y((()=>[(b(!0),R(B,null,z(Math.ceil(k(o).list.length/(k(o).pageCount*k(o).rowCount)),((e,t)=>(b(),g(s,{class:F(["dot",t==A.value?"dot-active":"",k(o).swiper.indicatorStyle]),style:_({background:t==A.value?k(o).swiper.indicatorActiveColor:k(o).swiper.indicatorColor})},null,8,["class","style"])))),256))])),_:1},8,["class"])])),_:1})):S("v-if",!0)])),_:1})):"horizontal"==k(o).layout&&2==k(o).pageCount&&"singleSlide"==k(o).showStyle?(b(),g(s,{key:2,style:{width:"98%",margin:"0 auto"},class:F([["graphic-nav multiple-lines","graphic-nav-"+k(o).showStyle],"py-[10rpx]"])},{default:y((()=>[(b(!0),R(B,null,z(O(),((e,t)=>(b(),g(m,{class:"graphic-nav-wrap whitespace-nowrap","scroll-x":"singleSlide"==k(o).showStyle},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,((t,a)=>(b(),R(B,null,[q(a,e)?(b(),g(s,{key:0,onClick:e=>k(l).toRedirect(t.link),style:_(M()),class:"graphic-nav-item inline-flex flex-col items-center box-border py-2"},{default:y((()=>["text"!=k(o).mode?(b(),g(s,{key:0,class:"graphic-img relative flex items-center justify-center w-10 h-10",style:_({width:2*k(o).imageSize+"rpx",height:2*k(o).imageSize+"rpx"})},{default:y((()=>[t.imageUrl?(b(),g(i,{key:0,src:k(d)(t.imageUrl),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])):(b(),g(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])),t.label.control?(b(),g(u,{key:2,class:F(["tag absolute -top-[10rpx] -right-[24rpx] text-white rounded-[24rpx] rounded-bl-none transform scale-80 py-1 px-2 text-xs"]),style:_({color:t.label.textColor,backgroundImage:"linear-gradient("+t.label.bgColorStart+","+t.label.bgColorEnd+")"})},{default:y((()=>[w(C(t.label.text),1)])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1032,["style"])):S("v-if",!0),"img"!=k(o).mode?(b(),g(u,{key:1,class:F(["graphic-text w-full text-center truncate leading-normal",{"pt-[16rpx]":"text"!=k(o).mode}]),style:_({fontSize:2*k(o).font.size+"rpx",fontWeight:k(o).font.weight,color:k(o).font.color})},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["class","style"])):S("v-if",!0)])),_:2},1032,["onClick","style"])):S("v-if",!0)],64)))),256))])),_:2},1032,["scroll-x"])))),256))])),_:1},8,["class"])):(b(),g(m,{key:3,"scroll-x":"singleSlide"==k(o).showStyle,class:F([["graphic-nav","graphic-nav-"+k(o).showStyle],"py-[10rpx]"])},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,((e,t)=>(b(),g(s,{class:F(["graphic-nav-item",{"flex-shrink-0":"singleSlide"==k(o).showStyle}]),key:e.id,style:_({width:100/k(o).rowCount+"%"})},{default:y((()=>[h(s,{onClick:t=>k(l).toRedirect(e.link),class:"flex flex-col items-center box-border py-2"},{default:y((()=>["text"!=k(o).mode?(b(),g(s,{key:0,class:"graphic-img relative flex items-center justify-center w-10 h-10",style:_({width:2*k(o).imageSize+"rpx",height:2*k(o).imageSize+"rpx"})},{default:y((()=>[e.imageUrl?(b(),g(i,{key:0,src:k(d)(e.imageUrl),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])):(b(),g(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])),e.label.control?(b(),g(u,{key:2,class:F(["tag absolute -top-[10rpx] -right-[24rpx] text-white rounded-[24rpx] rounded-bl-none transform scale-80 py-1 px-2 text-xs"]),style:_({color:e.label.textColor,backgroundImage:"linear-gradient("+e.label.bgColorStart+","+e.label.bgColorEnd+")"})},{default:y((()=>[w(C(e.label.text),1)])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1032,["style"])):S("v-if",!0),"img"!=k(o).mode?(b(),g(u,{key:1,class:F(["graphic-text w-full text-center truncate leading-normal",{"pt-[16rpx]":"text"!=k(o).mode}]),style:_({fontSize:2*k(o).font.size+"rpx",fontWeight:k(o).font.weight,color:k(o).font.color})},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["class","style"])):S("v-if",!0)])),_:2},1032,["onClick"])])),_:2},1032,["class","style"])))),128))])),_:1},8,["scroll-x","class"]))])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-1c87432a"]]),hl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return e+="height:"+2*o.value.height+"rpx;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e}));return(e,t)=>{const l=E;return b(),g(l,{style:_(k(a))},null,8,["style"])}}}),_l=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return e+="border-top:"+2*o.value.borderWidth+"rpx "+o.value.borderStyle+" "+o.value.borderColor+";"}));return(e,t)=>{const o=E;return b(),g(o,{class:"horz-line-wrap"},{default:y((()=>["decorate"==k(l).mode?(b(),g(o,{key:0,class:"h-[30rpx]"})):S("v-if",!0),h(o,{style:_(k(a))},null,8,["style"]),"decorate"==k(l).mode?(b(),g(o,{key:1,class:"h-[30rpx]"})):S("v-if",!0)])),_:1})}}}),kl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),e})),r=i((()=>{var e="";return e+="height:"+o.value.imgHeight+";",o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e}));c((()=>{n(),p((()=>o.value),((e,t)=>{e&&"HotArea"==e.componentName&&n()}))}));const n=()=>{"decorate"==l.mode&&""==o.value.imageUrl&&(o.value.imgWidth=690,o.value.imgHeight=330)};return(e,t)=>{const n=$,s=E;return b(),g(s,{style:_(k(a))},{default:y((()=>[h(s,{class:"simple-graph-wrap overflow-hidden relative leading-0"},{default:y((()=>[k(o).imageUrl?(b(),g(n,{key:0,style:_(k(r)),src:k(d)(k(o).imageUrl),mode:"widthFix","show-menu-by-longpress":!0,class:"w-full"},null,8,["style","src"])):(b(),g(n,{key:1,style:_(k(r)),src:k(d)("static/resource/images/diy/figure.png"),mode:"widthFix","show-menu-by-longpress":!0,class:"w-full"},null,8,["style","src"])),"decorate"!=k(l).mode?(b(),R(B,{key:2},[S(" 热区功能 "),(b(!0),R(B,null,z(k(o).heatMapData,((e,t)=>(b(),g(s,{onClick:t=>k(l).toRedirect(e.link),class:"absolute",key:t,style:_({width:e.width+"%",height:e.height+"%",left:e.left+"%",top:e.top+"%"})},null,8,["onClick","style"])))),128))],64)):S("v-if",!0)])),_:1})])),_:1},8,["style"])}}}),wl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=A(),o=s(),a=i((()=>"decorate"==o.mode?o.value[t.index]:t.component)),r=()=>{let e="";return a.value.isSameScreen&&0==t.index&&(e="ios"===l.platform?"margin-top: -55px;":"margin-top: -44.5px;"),e},n=i((()=>{var e="";return e+="position:relative;",a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.componentBgUrl&&(e+=`background-image:url('${d(a.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),e})),f=i((()=>{var e="";return a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e})),m=i((()=>2*a.value.imageHeight+"rpx")),x=u(0),v=e=>{x.value=e.detail.current};c((()=>{w(),"decorate"==o.mode&&p((()=>a.value),((e,t)=>{e&&"ImageAds"==e.componentName&&w()}))}));const w=()=>{"decorate"==o.mode?a.value.list.forEach((e=>{""==e.imageUrl&&(e.imgWidth=690,e.imgHeight=330)})):uni.removeStorageSync("imageAdsSameScreen")};return(e,t)=>{const l=$,s=E,i=Y,u=P;return b(),g(s,{style:_(k(n))},{default:y((()=>[h(s,{class:"diy-image-ads",style:_(r())},{default:y((()=>[1==k(a).list.length?(b(),g(s,{key:0,class:"leading-0 overflow-hidden",style:_(k(f))},{default:y((()=>[h(s,{onClick:t[0]||(t[0]=e=>k(o).toRedirect(k(a).list[0].link))},{default:y((()=>[k(a).list[0].imageUrl?(b(),g(l,{key:0,src:k(d)(k(a).list[0].imageUrl),style:_({height:k(m)}),mode:"heightFix",class:"!w-full","show-menu-by-longpress":!0},null,8,["src","style"])):(b(),g(l,{key:1,src:k(d)("static/resource/images/diy/figure.png"),style:_({height:k(m)}),mode:"heightFix",class:"!w-full","show-menu-by-longpress":!0},null,8,["src","style"]))])),_:1})])),_:1},8,["style"])):(b(),g(u,{key:1,class:"swiper",style:_({height:k(m)}),autoplay:"true",circular:"true",onChange:v},{default:y((()=>[(b(!0),R(B,null,z(k(a).list,(e=>(b(),g(i,{class:"swiper-item",key:e.id,style:_(k(f))},{default:y((()=>[h(s,{onClick:t=>k(o).toRedirect(e.link)},{default:y((()=>[h(s,{class:"item",style:_({height:k(m)})},{default:y((()=>[e.imageUrl?(b(),g(l,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"])):(b(),g(l,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"]))])),_:2},1032,["style"])])),_:2},1032,["onClick"])])),_:2},1032,["style"])))),128))])),_:1},8,["style"]))])),_:1},8,["style"])])),_:1},8,["style"])}}}),Cl=n({__name:"index",props:["component","index","global"],setup(e){const t=e,l=Re(),o=s(),a=i((()=>"decorate"==o.mode?o.value[t.index]:t.component)),r=i((()=>{var e="";return a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.bgUrl&&(e+="background-image:url("+d(a.value.bgUrl)+");",e+="background-size: 100%;",e+="background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e})),n=Se(),{query:c}=Be(location.href);c.code&&ze()&&Fe()&&Te({code:c.code}).then((e=>{n.getMemberInfo()}));const p=i((()=>"decorate"==o.mode?{headimg:"",nickname:"昵称",balance:0,point:0,money:0,member_no:"NIU0000021"}:n.info)),f=i((()=>{if(p.value){let e=parseFloat(p.value.balance)+parseFloat(p.value.money);return Ee(e.toString())}return 0})),m=()=>{let e=!l.login.is_username&&!l.login.is_mobile&&!l.login.is_bind_mobile,t=!l.login.is_auth_register;ze()?e&&t?ye({title:"商家未开启登录注册",icon:"none"}):l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile?De().setLoginBack({url:"/app/pages/member/index"}):e&&l.login.is_auth_register&&l.login.is_force_access_user_info?De().getAuthCode({scopes:"snsapi_userinfo"}):e&&l.login.is_auth_register&&!l.login.is_force_access_user_info&&De().getAuthCode({scopes:"snsapi_base"}):e?ye({title:"商家未开启登录注册",icon:"none"}):(l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile)&&De().setLoginBack({url:"/app/pages/member/index"})};u(!1);const R=()=>{ze()?De().getAuthCode({scopes:"snsapi_userinfo"}):we({url:"/app/pages/member/personal"})};return(e,t)=>{const l=x(v("u-avatar"),lt),o=E,n=D;return b(),g(o,{style:_(k(r))},{default:y((()=>[h(o,{class:"pt-[34rpx] member-info"},{default:y((()=>[k(p)?(b(),g(o,{key:0,class:"flex ml-[32rpx] mr-[52rpx] items-center relative"},{default:y((()=>[S(" 唤起获取微信 "),h(l,{src:k(d)(k(p).headimg),size:"55",leftIcon:"none","default-url":k(d)("static/resource/images/default_headimg.png"),onClick:R},null,8,["src","default-url"]),h(o,{class:"ml-[22rpx]"},{default:y((()=>[h(o,{class:"text-[#222222] flex pr-[50rpx] flex-wrap items-center"},{default:y((()=>[h(o,{class:"text-[#222222] truncate max-w-[320rpx] font-bold text-lg mr-[16rpx]",style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(p).nickname),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-[#696B70] text-[24rpx] mt-[10rpx]",style:_({color:k(a).textColor})},{default:y((()=>[w("UID："+C(k(p).member_no),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"set-icon flex items-center absolute right-0 top-2"},{default:y((()=>[h(o,{onClick:t[0]||(t[0]=e=>k(we)({url:"/app/pages/setting/index"}))},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-shezhiV6xx-1 text-[40rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})])),_:1})])),_:1})):(b(),g(o,{key:1,class:"flex ml-[32rpx] mr-[52rpx] items-center relative"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/default_headimg.png"),size:"55",onClick:m},null,8,["src"]),h(o,{class:"ml-[22rpx]",onClick:m},{default:y((()=>[h(o,{class:"text-[#222222] font-bold text-lg",style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(H)("login"))+"/"+C(k(H)("register")),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"set-icon flex items-center absolute right-0 top-2",onClick:t[1]||(t[1]=e=>k(we)({url:"/app/pages/setting/index"}))},{default:y((()=>[h(o,null,{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-shezhiV6xx-1 text-[40rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})])),_:1})])),_:1})),h(o,{class:"flex m-[30rpx] mb-0 py-[30rpx] items-center"},{default:y((()=>[h(o,{class:"flex-1 text-center"},{default:y((()=>[h(o,{class:"font-bold"},{default:y((()=>[h(o,{onClick:t[2]||(t[2]=e=>k(we)({url:k(p)?"/app/pages/member/balance":""})),style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(f)),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-sm mt-[10rpx]"},{default:y((()=>[h(o,{onClick:t[3]||(t[3]=e=>k(we)({url:k(p)?"/app/pages/member/balance":""})),style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(H)("balance")),1)])),_:1},8,["style"])])),_:1})])),_:1}),h(o,{class:"border-solid border-white border-l border-b-0 border-t-0 border-r-0 h-[60rpx]"}),h(o,{class:"flex-1 text-center"},{default:y((()=>[h(o,{class:"font-bold"},{default:y((()=>[h(o,{onClick:t[4]||(t[4]=e=>k(we)({url:k(p)?"/app/pages/member/point":""})),style:_({color:k(a).textColor})},{default:y((()=>{var e;return[w(C(parseInt(null==(e=k(p))?void 0:e.point)||0),1)]})),_:1},8,["style"])])),_:1}),h(o,{class:"text-sm mt-[10rpx]"},{default:y((()=>[h(o,{onClick:t[5]||(t[5]=e=>k(we)({url:k(p)?"/app/pages/member/point":""})),style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(H)("point")),1)])),_:1},8,["style"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])}}}),Sl=Pe(n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=Se(),a=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),r=i((()=>{var e="";return a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e})),n=u(0),c=u(0),p=u(-1),f=u([]);u(uni.getStorageSync("wap_member_info"));const m=i((()=>"decorate"==l.mode?(n.value=0,f.value=[{title:"商品包邮"}],c.value=1,{member_level_name:"会员等级",growth:5}):o.info||{})),x=i((()=>"decorate"==l.mode?[{}]:(v(o.levelList),o.levelList))),v=e=>{if(!e||!e.length)return!1;let t=!1;m.value&&m.value.member_level&&e&&e.length&&e.forEach(((e,l)=>{e.level_id==m.value.member_level&&(c.value=l+1,e.level_benefits&&Object.values(e.level_benefits).forEach((e=>{e.content&&f.value.push(e.content)}))),e.growth>m.value.growth&&e.level_id!=m.value.member_level&&!t&&(p.value=l,t=!0)})),m.value.member_level?(-1==p.value&&(p.value=e.length-1),e[p.value]&&e[p.value].growth&&(n.value=e[p.value].growth-m.value.growth)):(m.value.member_level_name=e[0].level_name,n.value=e[0].growth-(m.value.growth||0),p.value=0,c.value=1)};let R=()=>{let e=100;return x.value[p.value]&&x.value[p.value].growth&&(e=m.value.growth?m.value.growth/x.value[p.value].growth*100:0),e};const B=e=>{if("decorate"==l.mode)return!1;we({url:e})};return(e,t)=>{const l=$,o=D,s=E,i=$e;return k(m)&&k(x)&&k(x).length?(b(),g(s,{key:0,style:_(k(r)),class:"overflow-hidden"},{default:y((()=>["style-1"==k(a).style?(b(),g(s,{key:0,class:"flex items-center justify-between style-bg-1 py-[22rpx] px-[30rpx]"},{default:y((()=>[h(s,{class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/diy/member/VIP_02.png"),mode:"aspectFit",class:"w-[50rpx] h-[36rpx]"},null,8,["src"]),h(o,{class:"text-[30rpx] text-[#FFDAA8] ml-[10rpx] font-500 max-w-[440rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),h(s,{class:"flex items-center justify-center rounded-[30rpx] box-border style-btn w-[140rpx] h-[56rpx]",onClick:t[0]||(t[0]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"text-[24rpx] text-[#333]"},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1}),h(o,{class:"iconfont iconxiayibu1 ml-[4rpx] -mb-[2rpx] !text-[14rpx] text-[#333]"})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(a).style?(b(),g(s,{key:1,class:"flex items-center justify-between style-bg-2 p-[30rpx]"},{default:y((()=>[h(s,{class:"flex flex-col"},{default:y((()=>[h(s,{class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/diy/member/VIP_01.png"),mode:"aspectFit",class:"w-[74rpx] h-[30rpx]"},null,8,["src"]),h(o,{class:"text-[32rpx] text-[#FFE3B1] leading-[normal] ml-[14rpx] font-500 max-w-[420rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),f.value&&f.value.length?(b(),g(o,{key:0,class:"text-[#FFE3B1] opacity-80 text-[24rpx] mt-[10rpx] leading-[32rpx]"},{default:y((()=>[w(C(k(m).member_level_name)+"购物享"+C(f.value[0].title),1)])),_:1})):S("v-if",!0)])),_:1}),h(s,{class:"flex items-center justify-center rounded-[30rpx] box-border style-btn w-[140rpx] h-[56rpx]",onClick:t[1]||(t[1]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"text-[24rpx] text-[#333]"},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1}),h(o,{class:"iconfont iconxiayibu1 ml-[4rpx] -mb-[2rpx] !text-[14rpx] text-[#333]"})])),_:1})])),_:1})):S("v-if",!0),"style-3"==k(a).style?(b(),g(s,{key:2,class:"style-bg-3 py-[var(--pad-top-m)] px-[var(--pad-sidebar-m)]"},{default:y((()=>[h(s,{class:"flex items-center justify-between style-border-3 mb-[30rpx] pb-[40rpx]"},{default:y((()=>[h(s,{class:"flex flex-col flex-1"},{default:y((()=>[h(s,{class:"flex items-center justify-between"},{default:y((()=>[h(s,{class:"flex items-center"},{default:y((()=>[h(s,{class:"flex font-500 leading-[30rpx] box-border text-[#fff] pl-[50rpx] text-[24rpx] w-[120rpx] h-[30rpx] bg-contain bg-no-repeat",style:_({backgroundImage:"url("+k(d)("static/resource/images/diy/member/VIP.png")+")"})},{default:y((()=>[w("VIP."+C(c.value),1)])),_:1},8,["style"]),h(o,{class:"text-[#733F02] ml-[8rpx] text-[30rpx] font-500 max-w-[380rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),h(s,{class:"flex items-center",onClick:t[2]||(t[2]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(s,{class:"inline-block"},{default:y((()=>[h(o,{class:"nc-iconfont nc-icon-a-bangzhuV6xx-36 !text-[22rpx] text-[#733F02]"}),h(o,{class:"text-[22rpx] text-[#733F02] ml-[6rpx] leading-[24rpx]"},{default:y((()=>[w("规则")])),_:1})])),_:1}),h(s,{class:"ml-[2rpx] -mb-[4rpx] text-[#733F02] !text-[24rpx] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1}),h(o,{class:"text-[24rpx] text-[#794200] mt-[10rpx]"},{default:y((()=>[w("购物或邀请好友可以提升等级")])),_:1})])),_:1})])),_:1}),h(s,{class:"flex items-center justify-between"},{default:y((()=>[h(s,{class:"flex flex-col flex-1 mt-[2rpx]"},{default:y((()=>[h(s,{class:"overflow-hidden rounded-[20rpx]"},{default:y((()=>[h(i,{percent:k(R)(),activeColor:"#fff",backgroundColor:"rgba(255,5,5,0.1)","stroke-width":"4"},null,8,["percent"])])),_:1}),n.value>0?(b(),g(o,{key:0,class:"text-[22rpx] ml-[2rpx] leading-[1.4] text-[#794200] mt-[16rpx]"},{default:y((()=>[w("还差"+C(n.value)+"成长值即可升级为"+C(k(x)[p.value].level_name),1)])),_:1})):(b(),g(o,{key:1,class:"text-[22rpx] ml-[2rpx] text-[#794200] mt-[16rpx]"},{default:y((()=>[w("恭喜您升级为最高等级")])),_:1}))])),_:1}),h(s,{class:"flex items-center rounded-[30rpx] bg-[rgb(245,230,185)] px-[24rpx] text-[22rpx] text-[#733F02] h-[56rpx] ml-[40rpx] leading-normal",onClick:t[3]||(t[3]=e=>B("/app/pages/member/level"))},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-4"==k(a).style?(b(),g(s,{key:3,class:"flex items-center justify-between style-4 px-[24rpx] py-[20rpx]",style:_({backgroundImage:"url("+k(d)("static/resource/images/diy/member/style4_bg.jpg")+")"})},{default:y((()=>[h(s,{class:"flex flex-col"},{default:y((()=>[h(s,{class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/diy/member/style4_vip.png"),mode:"aspectFit",class:"w-[70rpx] h-[32rpx] pt-[1rpx]"},null,8,["src"]),h(o,{class:"text-[30rpx] text-[#FFEFB0] leading-[normal] ml-[8rpx] font-500 max-w-[420rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),f.value&&f.value.length?(b(),g(s,{key:0,class:"text-[#B0B0B0] text-[24rpx] mt-[10rpx] leading-[32rpx]"},{default:y((()=>[h(o,null,{default:y((()=>[w(C(k(m).member_level_name)+"购物享",1)])),_:1}),h(o,{class:"text-[#FFEFB0]"},{default:y((()=>[w(C(f.value[0].title),1)])),_:1})])),_:1})):S("v-if",!0)])),_:1}),h(s,{class:"flex items-center justify-center rounded-[30rpx] box-border style-btn w-[150rpx] h-[50rpx]",onClick:t[4]||(t[4]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"text-[22rpx] text-[#333] mr-[8rpx]"},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1}),h(l,{src:k(d)("static/resource/images/diy/member/style4_arrow.png"),mode:"aspectFit",class:"w-[26rpx] h-[26rpx] pt-[2rpx]"},null,8,["src"])])),_:1})])),_:1},8,["style"])):S("v-if",!0),"style-5"==k(a).style?(b(),g(s,{key:4,class:"style-5",style:_({backgroundImage:"url("+k(d)("static/resource/images/diy/member/style5_bg.jpg")+")"})},{default:y((()=>[h(s,{class:"content-head pt-[16rpx] pb-[10rpx] px-[24rpx] flex items-center justify-between"},{default:y((()=>[h(s,{class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/diy/member/style5_vip.png"),mode:"aspectFit",class:"w-[40rpx] h-[40rpx]"},null,8,["src"]),h(o,{class:"text-[#FFFBE2] ml-[10rpx] text-[30rpx] font-500 max-w-[470rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),h(s,{class:"flex items-center rounded-[30rpx] pl-[16rpx] pr-[12rpx] h-[44rpx] leading-normal style-btn",onClick:t[5]||(t[5]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"text-[22rpx] text-[#333] font-500 pb-[2rpx]"},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1}),h(l,{src:k(d)("static/resource/images/diy/member/style5_arrow_01.png"),mode:"aspectFit",class:"w-[22rpx] h-[22rpx] pb-[1rpx]"},null,8,["src"])])),_:1})])),_:1}),h(s,{class:"flex flex-col pt-[28rpx] pb-[30rpx] px-[24rpx]"},{default:y((()=>[h(s,{class:"flex items-center justify-between pb-[16rpx]"},{default:y((()=>[n.value>0?(b(),g(o,{key:0,class:"text-[22rpx] ml-[2rpx] leading-[1.4] text-[#FFFBE2]"},{default:y((()=>[w("还差"+C(n.value)+"成长值即可升级为"+C(k(x)[p.value].level_name),1)])),_:1})):(b(),g(o,{key:1,class:"text-[22rpx] ml-[2rpx] text-[#FFFBE2]"},{default:y((()=>[w("恭喜您升级为最高等级")])),_:1})),h(s,{class:"flex items-center",onClick:t[6]||(t[6]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"nc-iconfont nc-icon-a-bangzhuV6xx-36 !text-[22rpx] text-[#FFFBE2]"}),h(o,{class:"text-[22rpx] text-[#FFFBE2] ml-[6rpx] leading-[24rpx]"},{default:y((()=>[w("规则")])),_:1}),h(s,{class:"ml-[2rpx] -mb-[4rpx] text-[#FFFBE2] !text-[24rpx] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1}),h(s,{class:"overflow-hidden rounded-[20rpx]"},{default:y((()=>[h(i,{percent:k(R)(),activeColor:"#fff",backgroundColor:"rgba(255,255,255,0.4)","stroke-width":"4"},null,8,["percent"])])),_:1})])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"])):S("v-if",!0)}}}),[["__scopeId","data-v-fb31e6f0"]]),Rl=Pe(n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=u(!1),a=u(""),r=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),n=i((()=>{var e="";return e+="position:relative;",r.value.componentStartBgColor&&(r.value.componentStartBgColor&&r.value.componentEndBgColor?e+=`background:linear-gradient(${r.value.componentGradientAngle},${r.value.componentStartBgColor},${r.value.componentEndBgColor});`:e+="background-color:"+r.value.componentStartBgColor+";"),r.value.componentBgUrl&&(e+=`background-image:url('${d(r.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;"),e})),M=i((()=>{var e="";return r.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${r.value.componentBgAlpha/10});`,e+=`height:${O.value}px;`,r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;")),e})),A=u(0),j=u(0),W=u(""),U=u(0),V=u(800),H=()=>{"horizontal"==r.value.scrollWay&&setTimeout((()=>{f((()=>{let e=window.document.getElementById("horizontal-body-"+r.value.id),t=window.document.getElementById("marquee-one");e&&t&&(A.value=e.offsetWidth,j.value=t.offsetWidth,U.value=Math.ceil(14*j.value),A.value>j.value-30?W.value="animation: none;":W.value=`animation-duration: ${U.value}ms;animation-delay: ${V.value}ms;`)}))}))};c((()=>{H(),q(),"decorate"==l.mode&&p((()=>r.value),((e,t)=>{e&&"Notice"==e.componentName&&(H(),q())}))}));const L=T(),O=u(0),q=()=>{f((()=>{m().in(L).select(".diy-notice").boundingClientRect((e=>{O.value=e.height})).exec()}))},G=e=>{if("decorate"==l.mode)return!1;"popup"==r.value.showType?(o.value=!0,a.value=e.text):l.toRedirect(e.link)};return(e,t)=>{const l=E,s=$,i=D,u=Y,c=P,p=I,f=ee,m=x(v("u-popup"),Ve);return b(),g(l,{style:_(k(n))},{default:y((()=>[h(l,{style:_(k(M))},null,8,["style"]),h(l,{class:"diy-notice relative overflow-hidden"},{default:y((()=>[h(l,{class:"flex items-center pl-[28rpx] p-[22rpx]"},{default:y((()=>["img"==k(r).noticeType?(b(),g(l,{key:0,class:"min-w-[60rpx] flex items-center"},{default:y((()=>["system"==k(r).imgType?(b(),R(B,{key:0},["style_1"==k(r).systemUrl?(b(),g(s,{key:0,src:k(d)(`static/resource/images/diy/notice/${k(r).systemUrl}.png`),class:"h-[40rpx] w-[auto] mr-[20rpx] flex-shrink-0",mode:"heightFix"},null,8,["src"])):"style_2"==k(r).systemUrl?(b(),g(s,{key:1,src:k(d)(`static/resource/images/diy/notice/${k(r).systemUrl}.png`),class:"w-[200rpx] mr-[20rpx] h-[30rpx] flex-shrink-0",mode:"heightFix"},null,8,["src"])):S("v-if",!0)],64)):"diy"==k(r).imgType?(b(),g(s,{key:1,src:k(d)(k(r).imageUrl||""),class:"w-[200rpx] h-[30rpx] mr-[20rpx] flex-shrink-0",mode:"heightFix"},null,8,["src"])):S("v-if",!0)])),_:1})):S("v-if",!0),"text"==k(r).noticeType&&k(r).noticeTitle?(b(),g(l,{key:1,class:"max-w-[128rpx] px-[12rpx] text-[26rpx] h-[40rpx] leading-[40rpx] text-[var(--primary-color)] bg-[var(--primary-color-light)] truncate rounded-[8rpx] mr-[20rpx] flex-shrink-0"},{default:y((()=>[w(C(k(r).noticeTitle),1)])),_:1})):S("v-if",!0),h(l,{class:F(["flex-1 flex overflow-hidden horizontal-body",{"items-center":"upDown"==k(r).scrollWay}]),id:"horizontal-body-"+k(r).id},{default:y((()=>[S(" 横向滚动 "),"horizontal"==k(r).scrollWay?(b(),g(l,{key:0,class:"horizontal-wrap",style:_(W.value)},{default:y((()=>[h(l,{class:"marquee marquee-one",id:"marquee-one"},{default:y((()=>[(b(!0),R(B,null,z(k(r).list,((e,t)=>(b(),g(l,{class:F(["item flex-shrink-0 !leading-[40rpx] h-[40rpx]",{"ml-[80rpx]":t}]),key:t,onClick:t=>G(e),style:_({color:k(r).textColor,fontSize:2*k(r).fontSize+"rpx",fontWeight:k(r).fontWeight})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["class","onClick","style"])))),128))])),_:1}),A.value<j.value-30?(b(),g(l,{key:0,class:"marquee"},{default:y((()=>[(b(!0),R(B,null,z(k(r).list,((e,t)=>(b(),g(l,{class:F(["item flex-shrink-0 !leading-[40rpx] h-[40rpx]",{"ml-[80rpx]":t}]),key:t,onClick:t=>G(e),style:_({color:k(r).textColor,fontSize:2*k(r).fontSize+"rpx",fontWeight:k(r).fontWeight})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["class","onClick","style"])))),128))])),_:1})):S("v-if",!0)])),_:1},8,["style"])):S("v-if",!0),S(" 上下滚动 "),"upDown"==k(r).scrollWay?(b(),R(B,{key:1},[h(c,{vertical:!0,duration:500,autoplay:"true",circular:"true",class:"flex-1"},{default:y((()=>[(b(!0),R(B,null,z(k(r).list,((e,l)=>(b(),g(u,{key:l,onTouchmove:t[0]||(t[0]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(i,{onClick:t=>G(e),class:"beyond-hiding truncate",style:_({color:k(r).textColor,fontSize:2*k(r).fontSize+"rpx",fontWeight:k(r).fontWeight})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["onClick","style"])])),_:2},1024)))),128))])),_:1}),h(i,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] -ml-[8rpx] pl-[30rpx]",style:_({color:"#999",fontWeight:k(r).fontWeight})},null,8,["style"])],64)):S("v-if",!0)])),_:1},8,["id","class"])])),_:1}),h(l,{onTouchmove:t[3]||(t[3]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(m,{show:o.value,onClose:t[2]||(t[2]=e=>o.value=!1),mode:"center",round:"var(--rounded-big)",safeAreaInsetBottom:!1},{default:y((()=>[h(l,{class:"w-[570rpx] px-[32rpx] popup-common center"},{default:y((()=>[h(l,{class:"title"},{default:y((()=>[w("公告")])),_:1}),h(p,{"scroll-y":!0,class:"px-[30rpx] box-border h-[260rpx]"},{default:y((()=>[(b(!0),R(B,null,z(a.value.split("\n"),(e=>(b(),g(l,{class:"text-[28rpx] leading-[40rpx] mb-[20rpx]"},{default:y((()=>[w(C(e),1)])),_:2},1024)))),256))])),_:1}),h(l,{class:"btn-wrap !pt-[40rpx]"},{default:y((()=>[h(f,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:t[1]||(t[1]=e=>o.value=!1)},{default:y((()=>[w("我知道了")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-646198ff"]]),Bl=n({__name:"index",props:["component","index","global","scrollBool"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=i((()=>{var e="";return o.value.moduleOne.listFrame&&o.value.moduleOne.listFrame.startColor&&o.value.moduleOne.listFrame.endColor&&(e+=`background:linear-gradient(${o.value.moduleOne.listFrame.startColor},${o.value.moduleOne.listFrame.endColor});`),o.value.moduleRounded.topRounded&&(e+="border-top-left-radius:"+2*o.value.moduleRounded.topRounded+"rpx;"),o.value.moduleRounded.topRounded&&(e+="border-top-right-radius:"+2*o.value.moduleRounded.topRounded+"rpx;"),o.value.moduleRounded.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.moduleRounded.bottomRounded+"rpx;"),o.value.moduleRounded.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.moduleRounded.bottomRounded+"rpx;"),o.value.margin&&o.value.margin.both?e+="width: calc((100vw - "+4*o.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );",e})),n=i((()=>{var e="";return o.value.moduleTwo.listFrame&&o.value.moduleTwo.listFrame.startColor&&o.value.moduleTwo.listFrame.endColor&&(e+=`background:linear-gradient(${o.value.moduleTwo.listFrame.startColor},${o.value.moduleTwo.listFrame.endColor});`),o.value.moduleRounded.topRounded&&(e+="border-top-left-radius:"+2*o.value.moduleRounded.topRounded+"rpx;"),o.value.moduleRounded.topRounded&&(e+="border-top-right-radius:"+2*o.value.moduleRounded.topRounded+"rpx;"),o.value.moduleRounded.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.moduleRounded.bottomRounded+"rpx;"),o.value.moduleRounded.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.moduleRounded.bottomRounded+"rpx;"),o.value.margin&&o.value.margin.both?e+="width: calc((100vw - "+4*o.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );",e})),u=e=>{var t="";return e.btnTitle.color&&(t+="color:"+e.btnTitle.color+";"),o.value.moduleTwo.listFrame.startColor&&o.value.moduleTwo.listFrame.endColor&&(t+=`background:linear-gradient(${e.btnTitle.startColor},${e.btnTitle.endColor});`),t};c((()=>{f(),"decorate"==l.mode&&p((()=>o.value),((e,t)=>{e&&"PictureShow"==e.componentName&&f()}))}));const f=()=>{};return(e,t)=>{const s=$,i=D,c=E,p=x(v("u-icon"),Ye);return b(),g(c,{style:_(k(a)),class:"flex justify-between overflow-hidden"},{default:y((()=>[h(c,{class:"p-[20rpx] box-border overflow-hidden",style:_(k(r))},{default:y((()=>[k(o).moduleOne.head.textImg||k(o).moduleOne.head.subText?(b(),g(c,{key:0,class:"flex items-center pb-[30rpx] pt-[6rpx]"},{default:y((()=>[k(o).moduleOne.head.textImg?(b(),g(s,{key:0,class:"h-[28rpx]",src:k(d)(k(o).moduleOne.head.textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),k(o).moduleOne.head.textImg&&k(o).moduleOne.head.subText?(b(),g(i,{key:1,class:"w-[2rpx] mx-[10rpx] h-[22rpx]",style:_({backgroundColor:k(o).moduleOne.head.subTextColor})},null,8,["style"])):S("v-if",!0),k(o).moduleOne.head.subText?(b(),g(i,{key:2,class:"text-[22rpx] truncate max-w-[164rpx]",style:_({color:k(o).moduleOne.head.subTextColor})},{default:y((()=>[w(C(k(o).moduleOne.head.subText),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),h(c,{class:"flex items-center"},{default:y((()=>[(b(!0),R(B,null,z(k(o).moduleOne.list,((e,t)=>(b(),g(c,{key:t,class:F(["flex flex-col items-center",{"mr-[10rpx]":0==t}]),onClick:t=>k(l).toRedirect(e.link)},{default:y((()=>[h(c,{class:"bg-[#fff] flex items-center justify-center w-[148rpx] h-[148rpx] rounded-[12rpx] mb-[16rpx]"},{default:y((()=>[e.imageUrl?(b(),g(s,{key:0,class:"w-[102rpx] h-[102rpx]",src:k(d)(e.imageUrl),mode:"aspectFill"},null,8,["src"])):(b(),g(p,{key:1,name:"photo",color:"#999",size:"50"}))])),_:2},1024),h(c,{class:"w-[132rpx] h-[44rpx] rounded-[30rpx] flex items-center justify-center text-[22rpx]",style:_(u(e))},{default:y((()=>[w(C(e.btnTitle.text),1)])),_:2},1032,["style"])])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1},8,["style"]),h(c,{class:"p-[20rpx] box-border overflow-hidden",style:_(k(n))},{default:y((()=>[k(o).moduleTwo.head.textImg||k(o).moduleTwo.head.subText?(b(),g(c,{key:0,class:"flex items-center pb-[30rpx] pt-[6rpx]"},{default:y((()=>[k(o).moduleTwo.head.textImg?(b(),g(s,{key:0,class:"h-[28rpx] w-[auto]",src:k(d)(k(o).moduleTwo.head.textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),k(o).moduleTwo.head.textImg&&k(o).moduleTwo.head.subText?(b(),g(i,{key:1,class:"w-[2rpx] mx-[10rpx] h-[22rpx]",style:_({backgroundColor:k(o).moduleTwo.head.subTextColor})},null,8,["style"])):S("v-if",!0),k(o).moduleTwo.head.subText?(b(),g(i,{key:2,class:"text-[22rpx] truncate max-w-[164rpx]",style:_({color:k(o).moduleTwo.head.subTextColor})},{default:y((()=>[w(C(k(o).moduleTwo.head.subText),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),h(c,{class:"flex items-center"},{default:y((()=>[(b(!0),R(B,null,z(k(o).moduleTwo.list,((e,t)=>(b(),g(c,{key:t,class:F(["flex flex-col items-center",{"mr-[10rpx]":0==t}]),onClick:t=>k(l).toRedirect(e.link)},{default:y((()=>[h(c,{class:"bg-[#fff] flex items-center justify-center w-[148rpx] h-[148rpx] rounded-[12rpx] mb-[16rpx]"},{default:y((()=>[e.imageUrl?(b(),g(s,{key:0,class:"w-[102rpx] h-[102rpx]",src:k(d)(e.imageUrl),mode:"aspectFill"},null,8,["src"])):(b(),g(p,{key:1,name:"photo",color:"#999",size:"50"}))])),_:2},1024),h(c,{class:"w-[132rpx] h-[44rpx] rounded-[30rpx] flex items-center justify-center text-[22rpx]",style:_(u(e))},{default:y((()=>[w(C(e.btnTitle.text),1)])),_:2},1032,["style"])])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1},8,["style"])])),_:1},8,["style"])}}}),zl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=i((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${C.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e}));c((()=>{S(),"decorate"==l.mode&&p((()=>o.value),((e,t)=>{e&&"RichText"==e.componentName&&S()}))}));const n=T(),C=u(0),S=()=>{f((()=>{m().in(n).select(".diy-rich-text").boundingClientRect((e=>{C.value=e.height})).exec()}))};return(e,t)=>{const l=E,n=x(v("u-parse"),ot),s=D;return b(),g(l,{style:_(k(a))},{default:y((()=>[h(l,{style:_(k(r))},null,8,["style"]),h(l,{class:"diy-rich-text relative"},{default:y((()=>[k(o).html&&"<p><br></p>"!=k(o).html?(b(),g(l,{key:0},{default:y((()=>[h(n,{content:k(o).html,tagStyle:{img:"vertical-align: top;"}},null,8,["content"])])),_:1})):(b(),R(B,{key:1},[h(l,null,{default:y((()=>[w("点此编辑『富文本』内容 ——>")])),_:1}),h(l,null,{default:y((()=>[h(s,null,{default:y((()=>[w("你可以对文字进行")])),_:1}),h(s,null,{default:y((()=>[w("、")])),_:1}),h(s,{class:"font-bold"},{default:y((()=>[w("加粗")])),_:1}),h(s,null,{default:y((()=>[w("、")])),_:1}),h(s,{class:"italic"},{default:y((()=>[w("斜体")])),_:1}),h(s,null,{default:y((()=>[w("、")])),_:1}),h(s,{class:"underline"},{default:y((()=>[w("下划线")])),_:1}),h(s,null,{default:y((()=>[w("、")])),_:1}),h(s,{class:"line-through"},{default:y((()=>[w("删除线")])),_:1}),h(s,null,{default:y((()=>[w("、文字")])),_:1}),h(s,{style:{color:"rgb(0, 176, 240)"}},{default:y((()=>[w("颜色")])),_:1}),h(s,null,{default:y((()=>[w("、")])),_:1}),h(s,{style:{"background-color":"rgb(255, 192, 0)",color:"rgb(255, 255, 255)"}},{default:y((()=>[w("背景色")])),_:1}),h(s,null,{default:y((()=>[w("、以及字号")])),_:1}),h(s,{class:"text-lg"},{default:y((()=>[w("大")])),_:1}),h(s,{class:"text-sm"},{default:y((()=>[w("小")])),_:1}),h(s,{class:"pl-[10rpx]"},{default:y((()=>[w("等简单排版操作。")])),_:1})])),_:1}),h(l,null,{default:y((()=>[w("也可在这里插入图片、并对图片加上超级链接，方便用户点击。")])),_:1})],64))])),_:1})])),_:1},8,["style"])}}}),Fl=Pe(n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=e=>Me(e)+1,r=i((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),n=i((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${v.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e}));c((()=>{w(),"decorate"==l.mode?p((()=>o.value),((e,t)=>{e&&"RubikCube"==e.componentName&&w()})):p((()=>o.value),((e,t)=>{w()}))}));const x=T(),v=u(0),w=()=>{"decorate"==l.mode&&o.value.list.forEach((e=>{""==e.imageUrl&&(e.imgWidth=690,e.imgHeight=330)})),C(),f((()=>{m().in(x).select(".rubik-cube").boundingClientRect((e=>{v.value=e.height})).exec()}))},C=()=>{var e={"row1-of2":{ratio:2,width:"calc((100% - "+a(2*o.value.imageGap)+"px) / 2)"},"row1-of3":{ratio:3,width:"calc((100% - "+a(4*o.value.imageGap)+"px) / 3)"},"row1-of4":{ratio:4,width:"calc((100% - "+a(6*o.value.imageGap)+"px) / 4)"}};o.value.list.forEach(((e,t)=>{e.pageItemStyle=((e,t)=>{var l="";return"right"==o.value.elementAngle||{"row1-lt-of2-rt":[["border-top-right-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row1-lt-of1-tp-of2-bm":[["border-top-right-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-bottom-right-radius"],["border-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row1-tp-of2-bm":[["border-bottom-left-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-right-radius","border-top-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row2-lt-of2-rt":[["border-top-right-radius","border-bottom-left-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-right-radius","border-bottom-left-radius"],["border-top-left-radius","border-bottom-right-radius","border-top-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row1-of4":[["border-top-right-radius","border-bottom-right-radius"],["border-radius"],["border-radius"],["border-top-left-radius","border-bottom-left-radius"]],"row1-of3":[["border-top-right-radius","border-bottom-right-radius"],["border-radius"],["border-top-left-radius","border-bottom-left-radius"]],"row1-of2":[["border-top-right-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius"]]}[e][t].forEach(((e,t)=>{l+="border-top-left-radius:"+2*o.value.topElementRounded+"rpx;",l+="border-top-right-radius:"+2*o.value.topElementRounded+"rpx;",l+="border-bottom-left-radius:"+2*o.value.bottomElementRounded+"rpx;",l+="border-bottom-right-radius:"+2*o.value.bottomElementRounded+"rpx;"})),l})(o.value.mode,t)})),e[o.value.mode]?D(e[o.value.mode]):"row2-lt-of2-rt"==o.value.mode?I():"row1-lt-of2-rt"==o.value.mode?M():"row1-tp-of2-bm"==o.value.mode?A():"row1-lt-of1-tp-of2-bm"==o.value.mode&&j()},D=e=>{Ie({success:t=>{let l=0;o.value.list.forEach(((r,n)=>{var s=r.imgHeight/r.imgWidth;let i=t.windowWidth-a(2*o.value.margin.both);o.value.imageGap>0&&(i-=a(e.ratio*o.value.imageGap*2)),r.imgWidth=i/e.ratio,r.imgHeight=r.imgWidth*s,(0==l||l<r.imgHeight)&&(l=r.imgHeight)})),o.value.list.forEach(((t,o)=>{t.widthStyle=e.width,t.imgHeight=l}))}})},I=()=>{Ie({success:e=>{let t=0,l=0;o.value.list.forEach(((r,n)=>{var s=r.imgHeight/r.imgWidth;r.imgWidth=e.windowWidth,r.imgWidth-=a(4*o.value.margin.both),o.value.imageGap>0&&(r.imgWidth-=a(2*o.value.imageGap)),r.imgWidth=r.imgWidth/2,r.imgHeight=r.imgWidth*s,n<=1?(0==t||t<r.imgHeight)&&(t=r.imgHeight):n>1&&(0==l||l<r.imgHeight)&&(l=r.imgHeight)})),o.value.list.forEach(((e,r)=>{e.imgWidth="calc((100% - "+a(2*o.value.imageGap)+"px) / 2)",e.widthStyle=e.imgWidth,r<=1?e.imgHeight=t:r>1&&(e.imgHeight=l)}))}})},M=()=>{let e=0;o.value.list[1].imgWidth,o.value.list[2].imgWidth,Ie({success:t=>{o.value.list.forEach(((l,r)=>{if(0==r){var n=l.imgHeight/l.imgWidth;l.imgWidth=t.windowWidth-a(4*o.value.margin.both)-a(2*o.value.imageGap),l.imgWidth=l.imgWidth/2,l.imgHeight=l.imgWidth*n,e=(l.imgHeight-a(2*o.value.imageGap))/2,l.imgWidth+="px"}else l.imgWidth=o.value.list[0].imgWidth,l.imgHeight=e}))}})},A=()=>{var e=0;Ie({success:t=>{o.value.list.forEach(((l,r)=>{var n=l.imgHeight/l.imgWidth;0==r?l.imgWidth=t.windowWidth-a(4*o.value.margin.both):r>0&&(l.imgWidth=t.windowWidth-a(4*o.value.margin.both)-a(2*o.value.imageGap),l.imgWidth=l.imgWidth/2),l.imgHeight=l.imgWidth*n,r>0&&(0==e||e<l.imgHeight)&&(e=l.imgHeight)})),o.value.list.forEach(((t,l)=>{t.imgWidth+="px",t.widthStyle=t.imgWidth,l>0&&(t.imgHeight=e)}))}})},j=()=>{Ie({success:e=>{o.value.list.forEach(((t,l)=>{if(0==l){var r=t.imgHeight/t.imgWidth;t.imgWidth=e.windowWidth-a(4*o.value.margin.both)-a(2*o.value.imageGap),t.imgWidth=t.imgWidth/2,t.imgHeight=t.imgWidth*r}else 1==l?(t.imgWidth=o.value.list[0].imgWidth,t.imgHeight=(o.value.list[0].imgHeight-a(2*o.value.imageGap))/2):l>1&&(t.imgWidth=(o.value.list[0].imgWidth-a(2*o.value.imageGap))/2,t.imgHeight=o.value.list[1].imgHeight)})),o.value.list.forEach(((e,t)=>{e.imgWidth+="px"}))}})};return(e,t)=>{const a=E,s=$;return b(),g(a,{style:_(k(r))},{default:y((()=>[h(a,{style:_(k(n))},null,8,["style"]),h(a,{class:F(["rubik-cube relative",k(l).mode])},{default:y((()=>[S(" 1左2右 "),"row1-lt-of2-rt"==k(o).mode?(b(),R(B,{key:0},[h(a,{class:"template-left"},{default:y((()=>[h(a,{onClick:t[0]||(t[0]=e=>k(l).toRedirect(k(o).list[0].link)),class:F(["item",k(o).mode]),style:_({marginRight:2*k(o).imageGap+"rpx",width:k(o).list[0].imgWidth,height:k(o).list[0].imgHeight+"px"})},{default:y((()=>[k(o).list[0].imageUrl?(b(),g(s,{key:0,src:k(d)(k(o).list[0].imageUrl),mode:"scaleToFill",style:_(k(o).list[0].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(b(),g(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(k(o).list[0].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:1},8,["class","style"])])),_:1}),h(a,{class:"template-right"},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,((e,t)=>(b(),R(B,{key:t},[t>0?(b(),g(a,{key:0,onClick:t=>k(l).toRedirect(e.link),class:F(["item",k(o).mode]),style:_({marginBottom:2*k(o).imageGap+"rpx",width:e.imgWidth,height:e.imgHeight+"px"})},{default:y((()=>[e.imageUrl?(b(),g(s,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(b(),g(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:2},1032,["onClick","class","style"])):S("v-if",!0)],64)))),128))])),_:1})],64)):"row1-lt-of1-tp-of2-bm"==k(o).mode?(b(),R(B,{key:1},[S(" 1左3右 "),h(a,{class:"template-left"},{default:y((()=>[h(a,{onClick:t[1]||(t[1]=e=>k(l).toRedirect(k(o).list[0].link)),class:F(["item",k(o).mode]),style:_({marginRight:2*k(o).imageGap+"rpx",width:k(o).list[0].imgWidth,height:k(o).list[0].imgHeight+"px"})},{default:y((()=>[k(o).list[0].imageUrl?(b(),g(s,{key:0,src:k(d)(k(o).list[0].imageUrl),mode:"scaleToFill",style:_(k(o).list[0].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(b(),g(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(k(o).list[0].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:1},8,["class","style"])])),_:1}),h(a,{class:"template-right"},{default:y((()=>[h(a,{onClick:t[2]||(t[2]=e=>k(l).toRedirect(k(o).list[1].link)),class:F(["item",k(o).mode]),style:_({marginBottom:2*k(o).imageGap+"rpx",width:k(o).list[1].imgWidth,height:k(o).list[1].imgHeight+"px"})},{default:y((()=>[k(o).list[1].imageUrl?(b(),g(s,{key:0,src:k(d)(k(o).list[1].imageUrl),mode:"scaleToFill",style:_(k(o).list[1].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(b(),g(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(k(o).list[1].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:1},8,["class","style"]),h(a,{class:"template-bottom"},{default:y((()=>[(b(!0),R(B,null,z(k(o).list,((e,t)=>(b(),R(B,{key:t},[t>1?(b(),g(a,{key:0,onClick:t=>k(l).toRedirect(e.link),class:F(["item",k(o).mode]),style:_({marginRight:2*k(o).imageGap+"rpx",width:e.imgWidth,height:e.imgHeight+"px"})},{default:y((()=>[e.imageUrl?(b(),g(s,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(b(),g(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:2},1032,["onClick","class","style"])):S("v-if",!0)],64)))),128))])),_:1})])),_:1})],64)):(b(!0),R(B,{key:2},z(k(o).list,((e,t)=>(b(),g(a,{class:F(["item",k(o).mode]),key:t,onClick:t=>k(l).toRedirect(e.link),style:_({marginRight:2*k(o).imageGap+"rpx",marginBottom:2*k(o).imageGap+"rpx",width:e.widthStyle,height:e.imgHeight+"px"})},{default:y((()=>[e.imageUrl?(b(),g(s,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(b(),g(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:2},1032,["class","onClick","style"])))),128))])),_:1},8,["class"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-23f821fa"]]),Tl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>({fontSize:2*o.value.fontSize+"rpx",color:o.value.textColor,fontWeight:"normal"===o.value.fontWeight?500:o.value.fontWeight,textAlign:o.value.textAlign}))),r=i((()=>({fontSize:2*o.value.fontSize+"rpx",color:o.value.textColor,fontWeight:"normal"===o.value.fontWeight?500:o.value.fontWeight}))),n=i((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),x=i((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${R.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e}));c((()=>{B(),"decorate"==l.mode&&p((()=>o.value),((e,t)=>{e&&"Text"==e.componentName&&B()}))}));const v=T(),R=u(0),B=()=>{f((()=>{m().in(v).select(".diy-text").boundingClientRect((e=>{R.value=e.height})).exec()}))};return(e,t)=>{const s=E,i=D;return b(),g(s,{style:_(k(n))},{default:y((()=>[h(s,{style:_(k(x))},null,8,["style"]),h(s,{class:"diy-text relative"},{default:y((()=>["style-1"==k(o).style?(b(),g(s,{key:0,class:"px-[var(--pad-sidebar-m)]"},{default:y((()=>[h(s,{onClick:t[0]||(t[0]=e=>k(l).toRedirect(k(o).link))},{default:y((()=>[h(s,{class:"leading-[1]",style:_(k(a))},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"])])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(o).style?(b(),g(s,{key:1,class:"px-[20rpx] flex items-center"},{default:y((()=>[h(s,{onClick:t[1]||(t[1]=e=>k(l).toRedirect(k(o).link))},{default:y((()=>[h(s,{class:"max-w-[200rpx] truncate leading-[1]",style:_(k(r))},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"])])),_:1}),k(o).subTitle.text?(b(),g(i,{key:0,style:_({background:k(o).subTitle.color}),class:"mx-[10rpx] w-[2rpx] h-[24rpx] opacity-70"},null,8,["style"])):S("v-if",!0),h(i,{class:"max-w-[300rpx] truncate",style:_({color:k(o).subTitle.color,fontSize:2*k(o).subTitle.fontSize+"rpx"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"]),k(o).more.isShow?(b(),g(s,{key:1,class:"ml-auto text-right",style:_({color:k(o).more.color})},{default:y((()=>[h(s,{onClick:t[2]||(t[2]=e=>k(l).toRedirect(k(o).more.link)),class:"flex items-center"},{default:y((()=>[h(i,{class:"max-w-[200rpx] truncate text-[26rpx]"},{default:y((()=>[w(C(k(o).more.text),1)])),_:1}),h(i,{class:"nc-iconfont nc-icon-youV6xx text-[24rpx]",style:_({color:k(o).more.color})},null,8,["style"])])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0)])),_:1})])),_:1},8,["style"])}}}),El=Pe(n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=Se(),a=i((()=>o.info)),r=j({type:"banner",loading:!1,config:{gridRows:1,gridRowsGap:"0rpx",headHeight:"170rpx"}}),n=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),p=i((()=>{var e="";return n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e})),f=i((()=>{var e="";return n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+(n.value.componentStartBgColor||n.value.componentEndBgColor)+";",e})),m=e=>{"decorate"!=l.mode&&we({url:e})},T=u([]),$=e=>{A(e.id)};c((()=>{M()}));const M=()=>{if("decorate"==l.mode){let e={title:"满减券",type_name:"通用券",price:100,min_condition_money:0};for(let t=0;t<4;t++)T.value.push(e)}else(()=>{let e={num:"all"==n.value.source?n.value.num:"",coupon_ids:"custom"==n.value.source?n.value.couponIds:""};st(e).then((e=>{T.value=e.data,r.loading=!1}))})()},A=e=>{if("decorate"!=l.mode)return a.value?void it({coupon_id:e,number:1}).then((e=>{})):(De().setLoginBack({url:"/addon/shop/pages/coupon/list"}),!1)};return(e,t)=>{const l=D,o=E,a=I,s=x(v("x-skeleton"),rt);return T.value&&Object.keys(T.value).length>0?(b(),g(s,{key:0,type:r.type,loading:r.loading,config:r.config},{default:y((()=>[h(o,{style:_(k(p)),class:"overflow-hidden"},{default:y((()=>["style-1"==k(n).style?(b(),g(o,{key:0,class:"coupon-wrap style-1 relative"},{default:y((()=>[h(a,{"scroll-x":"true",class:"coupon-list",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style1_bg2.png")+")","background-size":"100%","background-repeat":"no-repeat"})},{default:y((()=>[h(o,{class:"coupon-class"},{default:y((()=>[T.value.length>1?(b(!0),R(B,{key:0},z(T.value,((e,t)=>(b(),g(o,{key:t,class:F(["rounded-[16rpx] box-border pt-[14rpx] inline-flex flex-col items-center relative w-[150rpx] h-[130rpx]",{"mr-[20rpx]":t!=T.value.length-1}]),style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/coupon_item_bg.png")+")","background-size":"100%","background-repeat":"no-repeat"}),onClick:t=>$(e)},{default:y((()=>[h(o,{class:"truncate w-full flex items-baseline justify-center price-font text-[var(--price-text-color)]"},{default:y((()=>[h(l,{class:"text-[26rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[36rpx] truncate font-500"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1024),h(o,{class:"text-[#303133] text-[20rpx] mt-[12rpx]"},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1024),h(o,{class:"mt-[auto] rounded-b-[12rpx] text-[#f2333c] text-[20rpx] w-[100%] h-[36rpx] flex items-center justify-center bg-[#fff5f2]"},{default:y((()=>[w(C(e.type_name),1)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128)):(b(!0),R(B,{key:1},z(T.value,((e,t)=>(b(),g(o,{key:t,class:"rounded-[16rpx] box-border pt-[14rpx] pl-[44rpx] pr-[44rpx] inline-flex items-center justify-between relative w-[100%] h-[130rpx]",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style1_bg4.png")+")","background-size":"100%","background-repeat":"no-repeat"}),onClick:t=>$(e)},{default:y((()=>[h(o,{class:"flex price-font text-[var(--price-text-color)] items-baseline"},{default:y((()=>[h(l,{class:"text-[36rpx] mt-[16rpx] mr-[4rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[85rpx] font-500 max-w-[170rpx] truncate"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1024),h(o,{class:"border-0 border-dashed border-r-[2rpx] border-[#FF323C] absolute left-[40%] top-[46rpx] bottom-0 w-[2rpx]"}),h(o,{class:"w-[270rpx]"},{default:y((()=>[h(o,{class:"flex items-center mt-[auto]"},{default:y((()=>[h(l,{class:"rounded-[4rpx] bg-[#fff3f0] text-[#f2333c] border-[2rpx] border-solid border-[#f2333c] text-[22rpx] px-[6rpx] pb-[4rpx] pt-[6rpx] flex items-center justify-center whitespace-nowrap"},{default:y((()=>[w(C(e.type_name),1)])),_:2},1024),h(l,{class:"ml-[4rpx] text-[#f2333c] max-w-[184rpx] truncate"},{default:y((()=>[w(C(e.title),1)])),_:2},1024)])),_:2},1024),h(o,{class:"text-[#f2333c] text-[30rpx] font-500 mt-[10rpx] w-[270rpx] truncate"},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"消费满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["style","onClick"])))),128))])),_:1})])),_:1},8,["style"]),h(o,{class:"w-[100%] h-[130rpx] pt-[24rpx] px-[26rpx] box-border flex items-center justify-between absolute left-0 right-0 bottom-0",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style1_bg.png")+")","background-size":"100% 130rpx","background-repeat":"no-repeat"})},{default:y((()=>[h(o,{class:"flex flex-col"},{default:y((()=>[h(l,{class:"text-[30rpx] text-[#fff] font-400"},{default:y((()=>[w(C(k(n).couponTitle),1)])),_:1}),h(l,{class:"text-[20rpx] text-[rgba(255,255,255,.8)] mt-[10rpx]"},{default:y((()=>[w(C(k(n).couponSubTitle),1)])),_:1})])),_:1}),k(n).btnText?(b(),g(l,{key:0,onClick:t[0]||(t[0]=e=>m("/addon/shop/pages/coupon/list")),class:"bg-[#fff] flex items-center justify-center text-[#FF4142] text-[22rpx] min-w-[100rpx] px-[24rpx] box-border h-[50rpx] coupon-buy-btn"},{default:y((()=>[w(C(k(n).btnText),1)])),_:1})):S("v-if",!0)])),_:1},8,["style"])])),_:1})):"style-2"==k(n).style?(b(),g(o,{key:1,class:"coupon-wrap style-2 relative"},{default:y((()=>[h(a,{"scroll-x":"true",class:"coupon-list"},{default:y((()=>[(b(!0),R(B,null,z(T.value,((e,t)=>(b(),g(o,{key:t,class:F(["box-border pt-[14rpx] inline-flex flex-col items-center relative w-[140rpx] h-[130rpx] rounded-[10rpx]",{"mr-[20rpx]":t!=T.value.length-1,"mr-[290rpx]":t==T.value.length-1}]),style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/coupon_item_bg.png")+")","background-size":"100%","background-repeat":"no-repeat"}),onClick:t=>$(e)},{default:y((()=>[h(o,{class:"flex items-baseline justify-center w-full truncate price-font text-[var(--price-text-color)]"},{default:y((()=>[h(l,{class:"text-[24rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[38rpx] font-bold truncate"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1024),h(o,{class:"text-[#303133] text-[20rpx] truncate max-w-[120rpx] mt-[12rpx]"},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1024),h(o,{class:"mt-[auto] rounded-b-[12rpx] text-[#f2333c] text-[20rpx] w-[100%] h-[36rpx] flex items-center justify-center bg-[#fff5f2]"},{default:y((()=>[w(C(e.type_name),1)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1}),h(o,{class:"w-[290rpx] h-[170rpx] py-[20rpx] pl-[30rpx] box-border flex flex-col items-center justify-between absolute right-0 bottom-0",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style2_bg.png")+")","background-size":"290rpx 170rpx","background-repeat":"no-repeat"})},{default:y((()=>[h(l,{class:"text-[30rpx] text-[#fff] font-500"},{default:y((()=>[w(C(k(n).couponTitle),1)])),_:1}),h(l,{class:"text-[20rpx] text-[rgba(255,255,255,.8)] mt-[14rpx]"},{default:y((()=>[w(C(k(n).couponSubTitle),1)])),_:1}),k(n).btnText?(b(),g(l,{key:0,onClick:t[1]||(t[1]=e=>m("/addon/shop/pages/coupon/list")),class:"bg-[#fff] text-[#FF4142] text-[22rpx] min-w-[100rpx] px-[24rpx] box-border h-[50rpx] leading-[50rpx] text-center coupon-buy-btn mt-auto"},{default:y((()=>[w(C(k(n).btnText),1)])),_:1})):S("v-if",!0)])),_:1},8,["style"])])),_:1})):"style-3"==k(n).style?(b(),g(o,{key:2,class:"coupon-wrap style-3 relative",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style3_bg.jpg")+")","background-size":"100% 204rpx","background-repeat":"no-repeat"})},{default:y((()=>[h(o,{class:"desc flex flex-col"},{default:y((()=>[h(l,{class:"text-[30rpx] text-[#fff] font-500"},{default:y((()=>[w(C(k(n).couponTitle),1)])),_:1}),h(l,{class:"text-[22rpx] text-[rgba(255,255,255,.8)] mt-[10rpx]"},{default:y((()=>[w(C(k(n).couponSubTitle),1)])),_:1}),k(n).btnText?(b(),g(l,{key:0,onClick:t[2]||(t[2]=e=>m("/addon/shop/pages/coupon/list")),class:"bg-[#fff] text-[#FF4142] text-[24rpx] w-[140rpx] box-border h-[50rpx] leading-[50rpx] text-center coupon-buy-btn mt-auto"},{default:y((()=>[w(C(k(n).btnText),1)])),_:1})):S("v-if",!0)])),_:1}),T.value.length>1?(b(),g(a,{key:0,"scroll-x":"true",class:"coupon-list"},{default:y((()=>[(b(!0),R(B,null,z(T.value,((e,t)=>(b(),g(o,{key:t,class:"bg-[#fff] box-border p-[8rpx] pb-[12rpx] inline-flex flex-col items-center relative rounded-[20rpx] ml-[12rpx]",onClick:t=>$(e)},{default:y((()=>[h(o,{class:"coupon-item-content"},{default:y((()=>[h(o,{class:"text-[20rpx] text-[#fff]"},{default:y((()=>[w(C(e.type_name),1)])),_:2},1024),h(o,{class:"mt-[auto] flex items-baseline justify-center w-full truncate price-font text-[#fff]"},{default:y((()=>[h(l,{class:"text-[24rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[44rpx] font-bold truncate"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),h(o,{class:"text-[#303133] text-[22rpx] truncate max-w-[120rpx] mt-[12rpx]"},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0),1==T.value.length?(b(),g(o,{key:1,class:"bg-[#fff] box-border p-[10rpx] relative rounded-[20rpx] single-coupon flex-1",onClick:t[3]||(t[3]=e=>$(T.value[0]))},{default:y((()=>[h(o,{class:"flex items-center coupon-item-content"},{default:y((()=>[h(o,{class:"coupon-left flex items-center justify-center text-[#fff] w-[156rpx] mr-[30rpx]"},{default:y((()=>[h(l,{class:"text-[24rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[44rpx] font-[500]"},{default:y((()=>[w(C(parseFloat(T.value[0].price)),1)])),_:1})])),_:1}),h(o,{class:"flex flex-col"},{default:y((()=>[h(o,{class:"text-[#fff] text-[28rpx] mb-[14rpx]"},{default:y((()=>[w(C("0.00"==T.value[0].min_condition_money?"无门槛":"满"+parseFloat(T.value[0].min_condition_money)+"元可用"),1)])),_:1}),h(o,{class:"flex items-center"},{default:y((()=>[h(l,{class:"bg-[#fff] mr-[10rpx] text-[red] text-[20rpx] px-[10rpx] py-[8rpx] rounded-[20rpx]"},{default:y((()=>[w(C(T.value[0].type_name),1)])),_:1}),h(l,{class:"text-[#fff] text-[24rpx]"},{default:y((()=>[w("店铺优惠券")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["style"])):"style-4"==k(n).style?(b(),g(o,{key:3,class:"coupon-wrap style-4 relative",style:_(k(f))},{default:y((()=>[h(o,{class:"desc flex items-center pt-[6rpx] pb-[26rpx]",onClick:t[4]||(t[4]=e=>m("/addon/shop/pages/coupon/list"))},{default:y((()=>[h(l,{class:"text-[32rpx] text-[#fff] font-500",style:_({color:k(n).titleColor})},{default:y((()=>[w(C(k(n).couponTitle),1)])),_:1},8,["style"]),h(l,{class:"text-[22rpx] text-[rgba(255,255,255,.8)] ml-[10rpx]",style:_({color:k(n).subTitleColor})},{default:y((()=>[w(C(k(n).couponSubTitle),1)])),_:1},8,["style"])])),_:1}),h(a,{"scroll-x":"true",class:"coupon-list"},{default:y((()=>[(b(!0),R(B,null,z(T.value,((e,t)=>(b(),g(o,{key:t,class:"px-[10rpx] h-[120rpx] inline-flex items-center relative mr-[12rpx] coupon-item box-border min-w-[310rpx]",style:_({"background-color":k(n).couponItem.bgColor,"border-radius":2*k(n).couponItem.aroundRadius+"rpx"}),onClick:t=>$(e)},{default:y((()=>[h(o,{class:"flex min-w-[110rpx] max-w-[120rpx] items-baseline justify-center truncate price-font mr-[10rpx]",style:_({color:k(n).couponItem.moneyColor})},{default:y((()=>[h(l,{class:"text-[26rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[46rpx] font-bold truncate"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1032,["style"]),h(o,{class:"flex flex-col"},{default:y((()=>[h(o,{class:"text-[28rpx] font-500",style:_({color:k(n).couponItem.textColor})},{default:y((()=>[w(C(e.type_name),1)])),_:2},1032,["style"]),h(o,{class:"text-[#666] text-[24rpx] truncate max-w-[180rpx] mt-[12rpx]",style:_({color:k(n).couponItem.subTextColor})},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1032,["style"])])),_:2},1024)])),_:2},1032,["style","onClick"])))),128))])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"])])),_:1},8,["type","loading","config"])):S("v-if",!0)}}}),[["__scopeId","data-v-523af30c"]]),Dl=Pe(n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e}));c((()=>{r(),"decorate"==l.mode&&p((()=>o.value),((e,t)=>{e&&"ManyGoodsList"==e.componentName&&r()}))}));const r=()=>{"style-3"==o.value.headStyle&&"goods_category"==o.value.source&&o.value.goods_category?T(o.value.goods_category):m(o.value.list[0],0,!0)},n=u(0),f=u(null),m=(e,t,o=!1)=>{("decorate"!=l.mode||o)&&(n.value=t,A({source:e.source,goods_category:e.goods_category,goods_ids:e.goods_ids}))},x=u([]),v=u(0),T=e=>{ut({pid:e}).then((e=>{1==e.code&&(x.value=e.data,x.value&&A({source:"category",goods_category:""}))}))},M=e=>{"decorate"!=l.mode&&(v.value=e.category_id,A({source:"category",goods_category:v.value}))},A=e=>{f.value={style:o.value.style,margin:o.value.margin,source:e.source,num:o.value.num,sortWay:o.value.sortWay,goodsNameStyle:o.value.goodsNameStyle,priceStyle:o.value.priceStyle,saleStyle:o.value.saleStyle,btnStyle:o.value.btnStyle,labelStyle:o.value.labelStyle,imgElementRounded:o.value.imgElementRounded,elementBgColor:o.value.elementBgColor,topElementRounded:o.value.topElementRounded,bottomElementRounded:o.value.bottomElementRounded},e.goods_category&&(f.value.goods_category=e.goods_category),e.goods_ids&&e.goods_ids.length&&(f.value.goods_ids=e.goods_ids)};return(e,t)=>{const l=$,r=D,s=E,i=I;return b(),g(s,{class:"overflow-hidden"},{default:y((()=>[h(i,{"scroll-x":"true",class:F(["many-goods-list-head",k(o).headStyle]),"scroll-into-view":"a"+n.value,style:_(k(a))},{default:y((()=>["style-3"==k(o).headStyle?(b(),R(B,{key:0},["custom"==k(o).source?(b(!0),R(B,{key:0},z(k(o).list,((e,t)=>(b(),g(s,{key:t,class:F(["flex-col inline-flex items-center justify-center",{"pr-[40rpx]":t!=k(o).list.length-1}]),onClick:l=>m(e,t)},{default:y((()=>[e.imageUrl?(b(),g(l,{key:0,style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:F(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":t==n.value}]),src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["style","class","src"])):(b(),g(l,{key:1,style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:F(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":t==n.value}]),src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill"},null,8,["style","class","src"])),h(r,{class:F(["text-[28rpx] mt-[16rpx]",{"font-500 text-[var(--primary-color)]":t==n.value}])},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["class"])])),_:2},1032,["class","onClick"])))),128)):"goods_category"==k(o).source?(b(),R(B,{key:1},[h(s,{class:"pr-[40rpx] inline-flex flex-col items-center justify-center",onClick:t[0]||(t[0]=e=>M({category_id:0}))},{default:y((()=>[h(l,{style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:F(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":0==v.value}]),src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill"},null,8,["style","class","src"]),h(r,{class:F(["text-[28rpx] mt-[16rpx]",{"font-500 text-[var(--primary-color)]":0==v.value}])},{default:y((()=>[w("全部")])),_:1},8,["class"])])),_:1}),(b(!0),R(B,null,z(x.value,((e,t)=>(b(),g(s,{key:t,class:F(["flex-col inline-flex items-center justify-center",{"pr-[40rpx]":t!=x.value.length-1}]),onClick:t=>M(e)},{default:y((()=>[e.image?(b(),g(l,{key:0,style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:F(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":v.value==e.category_id}]),src:k(d)(e.image),mode:"aspectFit"},null,8,["style","class","src"])):(b(),g(l,{key:1,style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:F(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":v.value==e.category_id}]),src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill"},null,8,["style","class","src"])),h(r,{class:F(["text-[28rpx] mt-[16rpx]",{"font-500 text-[var(--primary-color)]":v.value==e.category_id}])},{default:y((()=>[w(C(e.category_name),1)])),_:2},1032,["class"])])),_:2},1032,["class","onClick"])))),128))],64)):S("v-if",!0)],64)):(b(!0),R(B,{key:1},z(k(o).list,((e,t)=>(b(),g(s,{class:F(["scroll-item",[k(o).headStyle,{active:t==n.value}]]),id:"a"+t,key:t,onClick:l=>m(e,t)},{default:y((()=>["style-1"==k(o).headStyle?(b(),g(s,{key:0,class:"cate"},{default:y((()=>[h(s,{class:"name"},{default:y((()=>[w(C(e.title),1)])),_:2},1024),h(s,{class:"desc","v-if":e.desc},{default:y((()=>[w(C(e.desc),1)])),_:2},1032,["v-if"])])),_:2},1024)):S("v-if",!0),"style-2"==k(o).headStyle?(b(),g(s,{key:1,class:"cate"},{default:y((()=>[h(s,{class:"name"},{default:y((()=>[w(C(e.title),1)])),_:2},1024),t==n.value?(b(),g(r,{key:0,class:"nc-iconfont nc-icon-xiaolian-2 !text-[40rpx] text-[var(--primary-color)] transform"})):S("v-if",!0)])),_:2},1024)):S("v-if",!0),"style-4"==k(o).headStyle?(b(),g(s,{key:2,class:"cate"},{default:y((()=>[h(s,{class:"name"},{default:y((()=>[w(C(e.title),1)])),_:2},1024)])),_:2},1024)):S("v-if",!0)])),_:2},1032,["class","id","onClick"])))),128))])),_:1},8,["class","scroll-into-view","style"]),f.value?(b(),g(nt,{key:0,class:"many-goods-list-body",value:f.value},null,8,["value"])):S("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-df5c933e"]]),$l=n({__name:"index",props:["component","index","value"],setup(e){const t=e,l=s(),o=j({type:"",loading:"decorate"!=l.mode,config:{}}),a=u([]),r=i((()=>t.value?t.value:"decorate"==l.mode?l.value[t.index]:t.component)),n=i((()=>{var e="";return e+="position:relative;",r.value.componentStartBgColor&&(r.value.componentStartBgColor&&r.value.componentEndBgColor?e+=`background:linear-gradient(${r.value.componentGradientAngle},${r.value.componentStartBgColor},${r.value.componentEndBgColor});`:e+="background-color:"+r.value.componentStartBgColor+";"),r.value.componentBgUrl&&(e+=`background-image:url('${d(r.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;"),e})),I=i((()=>{var e={val:"",style:""};return r.value.imgElementRounded&&(e.val=2*r.value.imgElementRounded+"rpx",e.style+="border-radius:"+2*r.value.imgElementRounded+"rpx;"),e})),M=i((()=>{var e="";return r.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${r.value.componentBgAlpha/10});`,e+=`height:${U.value}px;`,r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;")),e})),A=i((()=>{var e="";return r.value.topElementRounded&&(e+="border-top-left-radius:"+2*r.value.topElementRounded+"rpx;"),r.value.topElementRounded&&(e+="border-top-right-radius:"+2*r.value.topElementRounded+"rpx;"),r.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomElementRounded+"rpx;"),r.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomElementRounded+"rpx;"),e})),N=i((()=>{var e="";return r.value.margin&&r.value.margin.both?e+="calc((100vw - "+4*r.value.margin.both+"rpx - 20rpx) / 2)":e+="calc((100vw - 20rpx) / 2 )",e})),W=T(),U=u(0);c((()=>{Y(),"decorate"==l.mode?p((()=>r.value),((e,t)=>{e&&"ShopExchangeGoods"==e.componentName&&f((()=>{m().in(W).select(".diy-shop-exchange-goods-list").boundingClientRect((e=>{U.value=e.height})).exec()}))})):p((()=>r.value),((e,t)=>{Y()}),{deep:!0})}));const Y=()=>{if("decorate"==l.mode){let e={image:"",names:"商品名称",total_exchange_num:100,point:100,price:100};a.value.push(e),a.value.push(e)}else"style-1"==r.value.style?(o.type="list",o.type="list",o.config={textRows:2}):"style-2"==r.value.style?(o.type="waterfall",o.config={headHeight:"320rpx",gridRows:1,textRows:2,textWidth:["100%","80%"]}):"style-3"==r.value.style&&(o.type="waterfall",o.config={gridRows:1,gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]}),(()=>{let e={order:r.value.sortWay,num:"all"==r.value.source?r.value.num:"",ids:"custom"==r.value.source?r.value.goods_ids.join(","):""};ft(e).then((e=>{a.value=e.data,o.loading=!1,r.value.componentBgUrl&&setTimeout((()=>{m().in(W).select(".diy-shop-exchange-goods-list").boundingClientRect((e=>{U.value=e.height})).exec()}),1e3)}))})()};return(e,t)=>{const l=E,s=$,i=x(v("u--image"),pt),u=D,c=x(v("x-skeleton"),rt);return b(),g(c,{type:o.type,loading:o.loading,config:o.config},{default:y((()=>[h(l,{class:"overflow-hidden",style:_(k(n))},{default:y((()=>[h(l,{style:_(k(M))},null,8,["style"]),a.value.length?(b(),g(l,{key:0,class:"diy-shop-exchange-goods-list relative flex flex-wrap justify-between"},{default:y((()=>[(b(!0),R(B,null,z(a.value,((e,t)=>(b(),g(l,{class:F(["overflow-hidden bg-[#fff] flex flex-col box-border w-[calc(50%-10rpx)]",{"mt-[20rpx]":t>1}]),style:_(k(A)),key:e.goods_id,onClick:t=>{we({url:"/addon/shop/pages/point/detail",param:{id:e.id}})}},{default:y((()=>[h(i,{width:k(N),height:k(N),radius:k(I).val,src:k(d)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:y((()=>[h(s,{class:"overflow-hidden",style:_({width:k(N),height:k(N),"border-radius":k(I).val}),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["width","height","radius","src"]),h(l,{class:"flex-1 pt-[10rpx] pb-[20rpx] px-[16rpx] flex flex-col justify-between"},{default:y((()=>[h(l,{class:"text-[#333] leading-[40rpx] text-[28rpx] multi-hidden",style:_({color:k(r).goodsNameStyle.color,fontWeight:k(r).goodsNameStyle.fontWeight})},{default:y((()=>[w(C(e.names),1)])),_:2},1032,["style"]),h(l,{class:"text-[22rpx] leading-[28rpx] mt-[10rpx] text-[var(--text-color-light9)]",style:_({color:k(r).saleStyle.color})},{default:y((()=>[w("已兑"+C(e.total_exchange_num)+"人",1)])),_:2},1032,["style"]),h(l,{class:"flex justify-between flex-wrap items-center mt-[16rpx]"},{default:y((()=>[h(l,{class:"flex flex-col"},{default:y((()=>[h(l,{style:_({color:k(r).priceStyle.mainColor}),class:"text-[var(--price-text-color)] price-font ml-[2rpx] flex items-baseline"},{default:y((()=>[h(u,{class:"text-[32rpx]"},{default:y((()=>[w(C(e.point),1)])),_:2},1024),h(u,{class:"text-[24rpx] ml-[4rpx]"},{default:y((()=>[w("积分")])),_:1})])),_:2},1032,["style"]),e.price&&e.price>0?(b(),g(l,{key:0,class:"flex items-center mt-[6rpx] price-font"},{default:y((()=>[h(u,{style:_({color:k(r).priceStyle.mainColor}),class:"text-[#333] font-400 text-[32rpx]"},{default:y((()=>[w("+"+C(parseFloat(e.price).toFixed(2)),1)])),_:2},1032,["style"]),h(u,{style:_({color:k(r).priceStyle.mainColor}),class:"text-[var(--price-text-color)] font-400 ml-[4rpx] text-[24rpx]"},{default:y((()=>[w("元")])),_:1},8,["style"])])),_:2},1024)):S("v-if",!0)])),_:2},1024),h(l,{class:"w-[120rpx] h-[54rpx] text-[22rpx] flex-center !text-[#fff] m-0 rounded-full primary-btn-bg remove-border text-center",shape:"circle"},{default:y((()=>[w("去兑换")])),_:1})])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1})):a.value.length?S("v-if",!0):(b(),g(l,{key:1,class:"empty-page"},{default:y((()=>[h(s,{class:"img",src:k(d)("static/resource/images/system/empty.png"),model:"aspectFit"},null,8,["src"]),h(l,{class:"desc"},{default:y((()=>[w("暂无商品")])),_:1})])),_:1}))])),_:1},8,["style"])])),_:1},8,["type","loading","config"])}}}),Il=Pe(n({__name:"index",props:["component","index","global"],setup(e){const t=e,l=s(),o=u(!0),a=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),r=i((()=>{var e="";return a.value.bgUrl&&(e+="background-image:url("+d(a.value.bgUrl)+");",e+="background-size: 100%;",e+="backgroundPosition: bottom;",e+="background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e}));c((()=>{m()})),p((()=>a.value),((e,t)=>{m()}),{deep:!0});const n=u({}),f=u(Fe()),m=()=>{if("decorate"==l.mode)n.value={point:10500,today_point:500};else{if(!f)return;x()}},x=async()=>{const e=await mt();n.value=e.data,o.value=!1};return i((()=>"")),(e,t)=>{const l=E;return b(),g(l,{style:_(k(r)),class:"shop-exchange-info-wrap"},{default:y((()=>[h(l,{class:"pl-[60rpx] pt-[71rpx] pb-[133rpx] min-h-[382rpx] box-border text-[#fff]"},{default:y((()=>[f.value?(b(),R(B,{key:0},[h(l,{class:"text-[34rpx] leading-[48rpx]"},{default:y((()=>[w("我的积分")])),_:1}),h(l,{class:"text-[80rpx] font-500 price-font leading-[112rpx]"},{default:y((()=>[w(C(n.value.point||0),1)])),_:1}),h(l,{class:"text-[24rpx] font-400 leading-[34rpx]"},{default:y((()=>[w("今日获得："+C(n.value.today_point||0),1)])),_:1})],64)):(b(),R(B,{key:1},[h(l,{class:"pt-[42rpx] title"},{default:y((()=>[w("积分当钱花")])),_:1}),h(l,{class:"text-[26rpx] leading-[36rpx] text-[#FEF2C0] mt-[10rpx]"},{default:y((()=>[w("做任务可领积分")])),_:1})],64))])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-138b3016"]]),Ml=n({__name:"index",props:["component","index"],setup(e){const t=e,l=vt(),o=s(),a=i((()=>"decorate"==o.mode?o.value[t.index]:t.component)),r=i((()=>{var e="";return e+="position:relative;",a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e}));p((()=>a.value),((e,t)=>{n()}),{deep:!0});const n=()=>{if("decorate"==o.mode){if(a.value&&"ShopGoodsRanking"==a.value.componentName){const e={goods_name:"商品名称",goods_cover_thumb_mid:"",goodsSku:{show_price:10},rank_num:0};a.value.list.forEach((t=>{if(t.goodsList||(t.goodsList=[]),0===t.goodsList.length){const l=[];for(let t=0;t<3;t++){const o={...e,rank_num:t+1};l.push(o)}u[t.rankIds[0]]=l}}))}}else m()},u=j({}),f=j({}),m=()=>{a.value.list.forEach((e=>{const t=Array.isArray(e.rankIds)?e.rankIds[0]:0,l={rank_id:"custom"===e.source?t:0};xt(l).then((t=>{t.data&&t.data.goods_list&&t.data.goods_list.length>0&&(u[e.rankIds[0]]=t.data.goods_list,f[e.rankIds[0]]=t.data.name)})).catch((e=>{console.error("获取商品数据失败:",e)}))}))};function T(e){switch(e){case 1:return d("addon/shop/rank/rank_first.png");case 2:return d("addon/shop/rank/rank_second.png");case 3:return d("addon/shop/rank/rank_third.png");default:return d("addon/shop/rank/rank.png")}}return c((()=>{n()})),(e,t)=>{const n=$,s=D,i=E,c=x(v("u--image"),pt),p=I;return b(),g(i,{style:_(k(r)),class:"overflow-hidden"},{default:y((()=>[h(p,{"scroll-x":"true",class:"w-[100%] whitespace-nowrap"},{default:y((()=>[h(i,{class:"flex items-start"},{default:y((()=>[(b(!0),R(B,null,z(k(a).list,((e,t)=>(b(),g(i,{class:"inline-block",key:t},{default:y((()=>{return[u[e.rankIds[0]]&&u[e.rankIds[0]].length>0?(b(),g(i,{key:0,class:F(["w-[460rpx] px-[20rpx] pb-[20rpx] pt-[20rpx] flex flex-col items-start",{"mr-[20rpx]":k(a).list.length-1!=t}]),style:_((r=e,p="",r.listFrame.startColor&&(r.listFrame.startColor&&r.listFrame.endColor?p+="background: linear-gradient(90deg, "+r.listFrame.startColor+", "+r.listFrame.endColor+");":p="background-color:"+r.listFrame.startColor+";"),r.bgUrl&&(p+="background-image:url("+d(r.bgUrl)+");",p+="background-size: 100%;",p+="background-repeat: no-repeat;"),a.value.topElementRounded&&(p+="border-top-left-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.topElementRounded&&(p+="border-top-right-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.bottomElementRounded&&(p+="border-bottom-left-radius:"+2*a.value.bottomElementRounded+"rpx;"),a.value.bottomElementRounded&&(p+="border-bottom-right-radius:"+2*a.value.bottomElementRounded+"rpx;"),p))},{default:y((()=>[h(i,{class:"flex items-center h-[50rpx]"},{default:y((()=>[e.imgUrl?(b(),g(n,{key:0,class:"w-[30rpx] h-[30rpx] mr-[10rpx]",src:k(d)(e.imgUrl),mode:"aspectFill"},null,8,["src"])):S("v-if",!0),h(i,{style:_({color:e.textColor})},{default:y((()=>[e.text?(b(),g(s,{key:0,class:"text-[30rpx] font-bold"},{default:y((()=>[w(C(e.text),1)])),_:2},1024)):(b(),g(s,{key:1,class:"text-[30rpx] font-bold"},{default:y((()=>[w(C(f[e.rankIds[0]]),1)])),_:2},1024))])),_:2},1032,["style"])])),_:2},1024),h(i,{class:"flex items-center mt-[6rpx]",style:_({color:e.subTitle.textColor}),onClick:t=>k(o).toRedirect(e.subTitle.link)},{default:y((()=>[e.subTitle.text?(b(),g(s,{key:0,class:"text-[24rpx] font-500"},{default:y((()=>[w(C(e.subTitle.text),1)])),_:2},1024)):S("v-if",!0),e.subTitle.text?(b(),g(s,{key:1,class:"iconfont iconyouV6xx !text-[24rpx]"})):S("v-if",!0)])),_:2},1032,["style","onClick"]),h(i,{class:"mt-[24rpx]"},{default:y((()=>[(b(!0),R(B,null,z(u[e.rankIds[0]],((t,o)=>(b(),g(i,{class:F(["flex bg-[rgba(255,255,255,0.94)] p-[10rpx] rounded-[16rpx] mb-[16rpx]",{"mb-0":o===u[e.rankIds[0]].length-1}]),onClick:e=>{return l=t.goods_id,void we({url:"/addon/shop/pages/goods/detail",param:{goods_id:l},mode:"navigateTo"});var l}},{default:y((()=>[h(i,{class:"relative w-[130rpx] h-[130rpx] mr-[16rpx]"},{default:y((()=>[h(n,{class:"absolute top-[6rpx] left-[8rpx] w-[30rpx] h-[36rpx]",style:{zIndex:2},src:T(t.rank_num),mode:"aspectFill"},null,8,["src"]),h(i,{class:"absolute top-[2rpx] left-[-3rpx] flex items-center justify-center w-[50rpx] h-[50rpx]",style:{zIndex:3}},{default:y((()=>[h(s,{class:"text-[20rpx] font-bold text-[#fff]"},{default:y((()=>[w(C(t.rank_num),1)])),_:2},1024)])),_:2},1024),h(c,{radius:"var(--goods-rounded-big)",width:"130rpx",height:"130rpx",src:k(d)(t.goods_cover_thumb_mid||""),model:"aspectFill"},{error:y((()=>[h(n,{class:"w-[130rpx] h-[130rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1024),h(i,{class:"flex flex-col"},{default:y((()=>[h(i,{class:"leading-[1.3] multi-hidden w-[290rpx] text-[28rpx] whitespace-normal"},{default:y((()=>[w(C(t.goods_name),1)])),_:2},1024),h(i,{class:"flex items-center justify-between mt-[auto]"},{default:y((()=>[h(i,{class:"text-[var(--price-text-color)] price-font flex items-baseline"},{default:y((()=>[h(s,{class:"text-[24rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(s,{class:"text-[40rpx] font-500"},{default:y((()=>[w(C(k(l).goodsPrice(t).toFixed(2).split(".")[0]),1)])),_:2},1024),h(s,{class:"text-[24rpx] font-500"},{default:y((()=>[w("."+C(k(l).goodsPrice(t).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),h(i,null,{default:y((()=>[h(s,{class:"iconfont icongouwuche3 text-[var(--primary-color)] border-[2rpx] border-solid border-[var(--primary-color)] rounded-[50%] text-[22rpx] p-[6rpx]"})])),_:1})])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),256))])),_:2},1024)])),_:2},1032,["class","style"])):S("v-if",!0)];var r,p})),_:2},1024)))),128))])),_:1})])),_:1})])),_:1},8,["style"])}}}),Al=n({__name:"index",props:["component","index","value"],emits:["loadingFn"],setup(e,{emit:t}){const l=e,o=vt(),a=s(),r=u(0),n=j({type:"",loading:!1,config:{}}),m=u([]),T=i((()=>l.value?l.value:"decorate"==a.mode?a.value[l.index]:l.component)),M=i((()=>{var e="";return e+="position:relative;",T.value.componentStartBgColor&&(T.value.componentStartBgColor&&T.value.componentEndBgColor?e+=`background:linear-gradient(${T.value.componentGradientAngle},${T.value.componentStartBgColor},${T.value.componentEndBgColor});`:e+="background-color:"+T.value.componentStartBgColor+";"),T.value.topRounded&&(e+="border-top-left-radius:"+2*T.value.topRounded+"rpx;"),T.value.topRounded&&(e+="border-top-right-radius:"+2*T.value.topRounded+"rpx;"),T.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*T.value.bottomRounded+"rpx;"),T.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*T.value.bottomRounded+"rpx;"),e})),A=i((()=>{var e={val:"",style:""};return T.value.imgElementRounded&&(e.val=2*T.value.imgElementRounded+"rpx",e.style+="border-radius:"+2*T.value.imgElementRounded+"rpx;"),e})),N=i((()=>{var e="";return T.value.elementBgColor&&(e+="background-color:"+T.value.elementBgColor+";"),T.value.topElementRounded&&(e+="border-top-left-radius:"+2*T.value.topElementRounded+"rpx;"),T.value.topElementRounded&&(e+="border-top-right-radius:"+2*T.value.topElementRounded+"rpx;"),T.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*T.value.bottomElementRounded+"rpx;"),T.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*T.value.bottomElementRounded+"rpx;"),e})),W=e=>{var t="";return e.listFrame.startColor&&(t=e.listFrame.startColor&&e.listFrame.endColor?`background:linear-gradient( 110deg, ${e.listFrame.startColor} 0%, ${e.listFrame.endColor} 100%);`:"background-color:"+e.listFrame.startColor+";"),t},U=e=>{var t="";return e.moreTitle.startColor&&(t=e.moreTitle.startColor&&e.moreTitle.endColor?`background:linear-gradient( 0deg, ${e.moreTitle.startColor} 0%, ${e.moreTitle.endColor} 100%);`:"background-color:"+e.moreTitle.startColor+";"),t},Y=u(""),P=()=>{T.value.margin&&T.value.margin.both?Y.value="width: calc((100vw - "+4*T.value.margin.both+"rpx - 40rpx) / 3);":Y.value="width: calc((100vw - 40rpx) / 3 );"};P();c((()=>{V()})),p((()=>T.value),((e,t)=>{V()}));const V=()=>{"decorate"==a.mode?f((()=>{T.value&&T.value.list&&(m.value=T.value.list.map((e=>{let t=ke(e);return t.info={goods_cover_thumb_mid:"",goodsSku:{show_price:"10.00"}},t})),r.value=3,P())})):(()=>{let e={};"all"==T.value.source?e.num=T.value.list.length:"custom"==T.value.source&&(e.goods_ids=T.value.goods_ids),ct(e).then((e=>{let t=e.data;r.value=t.length||0,T.value.list.filter(((e,l)=>{e.info=ke(t[l])})),m.value=ke(T.value.list),n.loading=!1}))})()};return(e,t)=>{const l=E,a=$,s=x(v("u--image"),pt),i=D,u=I,c=x(v("x-skeleton"),rt);return b(),g(c,{type:n.type,loading:n.loading,config:n.config},{default:y((()=>[r.value?(b(),g(l,{key:0,style:_(k(M))},{default:y((()=>[h(l,{class:"w-full"},{default:y((()=>[h(u,{id:"warpStyle-"+k(T).id,class:"whitespace-nowrap h-[341rpx] w-full","scroll-x":!0},{default:y((()=>[(b(!0),R(B,null,z(m.value,((e,t)=>(b(),R(B,{key:t},[e.info?(b(),g(l,{key:0,id:"item"+t+k(T).id,class:F(["w-[224rpx] h-[341rpx] mr-[20rpx] inline-block bg-[#fff] box-border overflow-hidden",{"!mr-[0rpx]":t==m.value.length-1}]),style:_(k(N)+Y.value),onClick:t=>{we({url:"/addon/shop/pages/goods/detail",param:{goods_id:e.info.goods_id}})}},{default:y((()=>[h(l,{class:"w-full h-[134rpx]",style:_(W(e))},{default:y((()=>[h(l,{class:"flex pl-[16rpx] pr-[20rpx] justify-between h-[63rpx] items-center"},{default:y((()=>[h(l,{class:"text-[28rpx] leading-[34rpx] flex-1 mr-[8rpx]",style:_({color:e.title.textColor})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"]),h(l,{class:"w-[68rpx] h-[34rpx] text-[22rpx] text-center leading-[34rpx] text-[#fff] rounded-[17rpx]",style:_(U(e))},{default:y((()=>[w(C(e.moreTitle.text),1)])),_:2},1032,["style"])])),_:2},1024)])),_:2},1032,["style"]),h(l,{class:"mt-[-71rpx] h-[278rpx] w-full px-[20rpx] pt-[18rpx] box-border bg-white",style:_(k(N))},{default:y((()=>[h(l,{class:"flex items-center justify-center w-[184rpx] h-[184rpx]"},{default:y((()=>[h(s,{width:"184rpx",height:"184rpx",radius:k(A).val,src:k(d)(e.info.goods_cover_thumb_mid||""),model:"aspectFill"},{error:y((()=>[h(a,{class:"w-[184rpx] h-[184rpx]",style:_(k(A).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"])])),_:2},1024),h(l,{class:"pt-[12rpx]"},{default:y((()=>[h(l,{class:"h-[44rpx] pl-[4rpx] min-w-[168rpx] box-border flex justify-between items-center mx-auto border-[2rpx] border-solid border-[var(--primary-color)] rounded-[20rpx]",style:_({"border-color":e.button.color})},{default:y((()=>[h(l,{class:"text-[var(--price-text-color)] font-bold price-font flex items-baseline leading-[40rpx] flex-1 justify-center"},{default:y((()=>[h(l,{class:"leading-[1] max-w-[105rpx] truncate",style:_({color:k(T).priceStyle.mainColor})},{default:y((()=>[h(i,{class:"text-[18rpx] font-400 mr-[2rpx]"},{default:y((()=>[w("￥")])),_:1}),h(i,{class:"text-[28rpx] font-500"},{default:y((()=>[w(C(parseFloat(k(o).goodsPrice(e.info)).toFixed(2)),1)])),_:2},1024)])),_:2},1032,["style"])])),_:2},1024),h(l,{class:"w-[70rpx] box-border text-right text-[#fff] pr-[8rpx] text-[22rpx] font-500 leading-[44rpx] rounded-tr-[20rpx] rounded-br-[20rpx] rounded-tl-[24rpx] relative",style:_({"background-color":e.button.color})},{default:y((()=>[h(i,null,{default:y((()=>[w(C(e.button.text),1)])),_:2},1024),h(a,{class:"w-[24rpx] h-[44rpx] absolute top-[-2rpx] left-0",src:k(d)("/addon/shop/Union.png")},null,8,["src"])])),_:2},1032,["style"])])),_:2},1032,["style"])])),_:2},1024)])),_:2},1032,["style"])])),_:2},1032,["id","class","style","onClick"])):S("v-if",!0)],64)))),128))])),_:1},8,["id"])])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["type","loading","config"])}}}),jl=n({__name:"index",props:["component","index","global"],setup(e){const t=e,l=Re(),o=s(),a=i((()=>"decorate"==o.mode?o.value[t.index]:t.component)),r=i((()=>{var e="";return a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.bgUrl&&(e+="background-image:url("+d(a.value.bgUrl)+");",e+="background-size: 100%;",e+="background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e})),n=Se(),{query:c}=Be(location.href);c.code&&ze()&&setTimeout((()=>{Te({code:c.code}).then((e=>{n.getMemberInfo()}))}),1500);const p=i((()=>"decorate"==o.mode?{headimg:"",nickname:"昵称",member_level_name:"普通会员",balance:0,point:0,money:0,mobile:"155****0549",member_no:"NIU0000021"}:(z(),n.info))),f=i((()=>{if(p.value){let e=parseFloat(p.value.balance)+parseFloat(p.value.money);return Ee(e.toString())}return 0})),m=()=>{let e=!l.login.is_username&&!l.login.is_mobile&&!l.login.is_bind_mobile,t=!l.login.is_auth_register;ze()?e&&t?ye({title:"商家未开启登录注册",icon:"none"}):l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile?De().setLoginBack({url:"/addon/shop/pages/member/index"}):e&&l.login.is_auth_register&&l.login.is_force_access_user_info?De().getAuthCode({scopes:"snsapi_userinfo"}):e&&l.login.is_auth_register&&!l.login.is_force_access_user_info&&De().getAuthCode({scopes:"snsapi_base"}):e?ye({title:"商家未开启登录注册",icon:"none"}):(l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile)&&De().setLoginBack({url:"/addon/shop/pages/member/index"})};u(!1);const R=()=>{ze()?De().getAuthCode({scopes:"snsapi_userinfo"}):we({url:"/app/pages/member/personal"})},B=u(0),z=async()=>{try{const e=await dt({status:1});B.value=e.data}catch(e){B.value=0}},T=u(null),$=()=>{T.value.open()};return i((()=>"")),(e,t)=>{const l=x(v("u-avatar"),lt),o=E,n=D;return b(),g(o,{style:_(k(r))},{default:y((()=>[h(o,{class:F(["px-[30rpx] pt-[30rpx] box-border pb-[30rpx]",{"!pb-[120rpx]":k(a).isShowAccount}])},{default:y((()=>[k(p)?(b(),g(o,{key:0,class:"flex items-center"},{default:y((()=>[S(" 唤起获取微信 "),h(l,{src:k(d)(k(p).headimg),size:"110rpx",leftIcon:"none","default-url":k(d)("static/resource/images/default_headimg.png"),onClick:R},null,8,["src","default-url"]),h(o,{class:"ml-[20rpx] flex-1"},{default:y((()=>[h(o,{class:"text-[#ffffff] flex items-baseline flex-wrap",style:_({color:k(a).textColor})},{default:y((()=>[h(o,{class:"text-[32rpx] truncate max-w-[320rpx] font-500 leading-[38rpx]"},{default:y((()=>[w(C(k(p).nickname),1)])),_:1}),k(p).mobile?(b(),g(o,{key:0,class:"text-[26rpx] font-400 leading-[28rpx] ml-[10rpx]"},{default:y((()=>[w(C(k(p).mobile.replace(k(p).mobile.substring(3,7),"****")),1)])),_:1})):k(p).mobile?S("v-if",!0):(b(),g(o,{key:1,onClick:$,class:"text-[22rpx] ml-[10rpx] px-[6rpx] border-[1rpx] border-solid border-[#E3E4E9] rounded-[8rpx] h-[34rpx] flex-center",style:_(k(a).textColor?{boxShadow:"0 0 0 1rpx "+k(a).textColor,border:"none"}:{})},{default:y((()=>[w(C(k(H)("bindMobile")),1)])),_:1},8,["style"]))])),_:1},8,["style"]),h(o,{class:"text-[#666] text-[24rpx] font-400 leading-[28rpx] mt-[14rpx]",style:_({color:k(a).uidTextColor})},{default:y((()=>[w("UID："+C(k(p).member_no),1)])),_:1},8,["style"])])),_:1}),h(n,{onClick:t[0]||(t[0]=e=>k(we)({url:"/app/pages/setting/index"})),class:"nc-iconfont nc-icon-shezhiV6xx1 text-[38rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})):(b(),g(o,{key:1,class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/default_headimg.png"),size:"100rpx",onClick:m},null,8,["src"]),h(o,{class:"ml-[20rpx] flex-1",onClick:m},{default:y((()=>[h(o,{class:"text-[32rpx] font-500 leading-[38rpx]",style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(H)("login"))+"/"+C(k(H)("register")),1)])),_:1},8,["style"])])),_:1}),h(o,{onClick:t[1]||(t[1]=e=>k(we)({url:"/app/pages/setting/index"}))},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-shezhiV6xx1 text-[38rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})])),_:1})),k(a).isShowAccount?(b(),g(o,{key:2,class:"flex mt-[40rpx] items-center"},{default:y((()=>[h(o,{class:"text-center w-[33.333%] flex-shrink-0"},{default:y((()=>[h(o,{class:"text-[36rpx] mb-[20rpx] font-500 price-font"},{default:y((()=>[h(o,{onClick:t[2]||(t[2]=e=>k(we)({url:"/app/pages/member/balance"})),style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(f)),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-[22rpx] font-400"},{default:y((()=>[h(o,{onClick:t[3]||(t[3]=e=>k(we)({url:"/app/pages/member/balance"})),style:_({color:k(a).accountTextColor})},{default:y((()=>[w(C(k(H)("balance")),1)])),_:1},8,["style"])])),_:1})])),_:1}),h(o,{class:"text-center w-[33.333%] flex-shrink-0"},{default:y((()=>[h(o,{class:"text-[36rpx] mb-[20rpx] font-500 price-font"},{default:y((()=>[h(o,{onClick:t[4]||(t[4]=e=>k(we)({url:"/app/pages/member/point"})),style:_({color:k(a).textColor})},{default:y((()=>{var e;return[w(C(parseInt(null==(e=k(p))?void 0:e.point)||0),1)]})),_:1},8,["style"])])),_:1}),h(o,{class:"text-[22rpx] font-400"},{default:y((()=>[h(o,{onClick:t[5]||(t[5]=e=>k(we)({url:"/app/pages/member/point"})),style:_({color:k(a).accountTextColor})},{default:y((()=>[w(C(k(H)("point")),1)])),_:1},8,["style"])])),_:1})])),_:1}),h(o,{class:"text-center w-[33.333%] flex-shrink-0",onClick:t[6]||(t[6]=e=>k(we)({url:"/addon/shop/pages/member/my_coupon"}))},{default:y((()=>[h(o,{class:"text-[36rpx] mb-[20rpx] font-500 price-font"},{default:y((()=>[h(o,{style:_({color:k(a).textColor})},{default:y((()=>[w(C(B.value),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-[22rpx] font-400"},{default:y((()=>[h(o,{style:_({color:k(a).accountTextColor})},{default:y((()=>[w(C(k(H)("coupon")),1)])),_:1},8,["style"])])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["class"]),S(" 强制绑定手机号 "),h(bt,{ref_key:"bindMobileRef",ref:T},null,512)])),_:1},8,["style"])}}}),Nl=n({__name:"index",props:["component","index","global"],setup(e){const t=e,l=Re(),o=s(),a=i((()=>"decorate"==o.mode?o.value[t.index]:t.component)),r=i((()=>{var e="";return a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.bgUrl&&(e+="background-image:url("+d(a.value.bgUrl)+");",e+="background-size: 100%;",e+="background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e})),n=Se(),{query:c}=Be(location.href);c.code&&ze()&&setTimeout((()=>{Te({code:c.code}).then((e=>{n.getMemberInfo()}))}),1500);const p=i((()=>"decorate"==o.mode?{headimg:"",nickname:"昵称",member_level_name:"普通会员",balance:0,point:0,money:0,mobile:"155****0549",member_no:"NIU0000021"}:(z(),n.info))),f=i((()=>{if(p.value){let e=parseFloat(p.value.balance)+parseFloat(p.value.money);return Ee(e.toString())}return 0})),m=()=>{let e=!l.login.is_username&&!l.login.is_mobile&&!l.login.is_bind_mobile,t=!l.login.is_auth_register;ze()?e&&t?ye({title:"商家未开启登录注册",icon:"none"}):l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile?De().setLoginBack({url:"/addon/yz_she/pages/member/index"}):e&&l.login.is_auth_register&&l.login.is_force_access_user_info?De().getAuthCode({scopes:"snsapi_userinfo"}):e&&l.login.is_auth_register&&!l.login.is_force_access_user_info&&De().getAuthCode({scopes:"snsapi_base"}):e?ye({title:"商家未开启登录注册",icon:"none"}):(l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile)&&De().setLoginBack({url:"/addon/yz_she/pages/member/index"})};u(!1);const R=()=>{ze()?De().getAuthCode({scopes:"snsapi_userinfo"}):we({url:"/app/pages/member/personal"})},B=u(0),z=async()=>{try{const e=await gt();B.value=e.data}catch(e){B.value=0}},T=u(null),$=()=>{T.value.open()};return i((()=>"")),(e,t)=>{const l=x(v("u-avatar"),lt),o=E,n=D;return b(),g(o,{style:_(k(r))},{default:y((()=>[h(o,{class:F(["px-[30rpx] pt-[30rpx] box-border pb-[30rpx]",{"!pb-[120rpx]":k(a).isShowAccount}])},{default:y((()=>[k(p)?(b(),g(o,{key:0,class:"flex items-center"},{default:y((()=>[S(" 唤起获取微信 "),h(l,{src:k(d)(k(p).headimg),size:"110rpx",leftIcon:"none","default-url":k(d)("static/resource/images/default_headimg.png"),onClick:R},null,8,["src","default-url"]),h(o,{class:"ml-[20rpx] flex-1"},{default:y((()=>[h(o,{class:"text-[#ffffff] flex items-baseline flex-wrap",style:_({color:k(a).textColor})},{default:y((()=>[h(o,{class:"text-[32rpx] truncate max-w-[320rpx] font-500 leading-[38rpx]"},{default:y((()=>[w(C(k(p).nickname),1)])),_:1}),k(p).mobile?(b(),g(o,{key:0,class:"text-[26rpx] font-400 leading-[28rpx] ml-[10rpx]"},{default:y((()=>[w(C(k(p).mobile.replace(k(p).mobile.substring(3,7),"****")),1)])),_:1})):k(p).mobile?S("v-if",!0):(b(),g(o,{key:1,onClick:$,class:"text-[22rpx] ml-[10rpx] px-[6rpx] border-[1rpx] border-solid border-[#E3E4E9] rounded-[8rpx] h-[34rpx] flex-center",style:_(k(a).textColor?{boxShadow:"0 0 0 1rpx "+k(a).textColor,border:"none"}:{})},{default:y((()=>[w(" 绑定手机号 ")])),_:1},8,["style"]))])),_:1},8,["style"]),h(o,{class:"text-[#666] text-[24rpx] font-400 leading-[28rpx] mt-[14rpx]",style:_({color:k(a).uidTextColor})},{default:y((()=>[w("UID："+C(k(p).member_no),1)])),_:1},8,["style"])])),_:1}),h(n,{onClick:t[0]||(t[0]=e=>k(we)({url:"/app/pages/setting/index"})),class:"nc-iconfont nc-icon-shezhiV6xx1 text-[38rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})):(b(),g(o,{key:1,class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/default_headimg.png"),size:"100rpx",onClick:m},null,8,["src"]),h(o,{class:"ml-[20rpx] flex-1",onClick:m},{default:y((()=>[h(o,{class:"text-[32rpx] font-500 leading-[38rpx]",style:_({color:k(a).textColor})},{default:y((()=>[w("登录/注册")])),_:1},8,["style"])])),_:1}),h(o,{onClick:t[1]||(t[1]=e=>k(we)({url:"/app/pages/setting/index"}))},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-shezhiV6xx1 text-[38rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})])),_:1})),k(a).isShowAccount?(b(),g(o,{key:2,class:"flex mt-[40rpx] items-center"},{default:y((()=>[h(o,{class:"text-center w-[33.333%] flex-shrink-0"},{default:y((()=>[h(o,{class:"text-[36rpx] mb-[20rpx] font-500 price-font"},{default:y((()=>[h(o,{onClick:t[2]||(t[2]=e=>k(we)({url:"/app/pages/member/balance"})),style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(f)),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-[22rpx] font-400"},{default:y((()=>[h(o,{onClick:t[3]||(t[3]=e=>k(we)({url:"/app/pages/member/balance"})),style:_({color:k(a).accountTextColor})},{default:y((()=>[w("余额")])),_:1},8,["style"])])),_:1})])),_:1}),h(o,{class:"text-center w-[33.333%] flex-shrink-0"},{default:y((()=>[h(o,{class:"text-[36rpx] mb-[20rpx] font-500 price-font"},{default:y((()=>[h(o,{onClick:t[4]||(t[4]=e=>k(we)({url:"/app/pages/member/point"})),style:_({color:k(a).textColor})},{default:y((()=>{var e;return[w(C(parseInt(null==(e=k(p))?void 0:e.point)||0),1)]})),_:1},8,["style"])])),_:1}),h(o,{class:"text-[22rpx] font-400"},{default:y((()=>[h(o,{onClick:t[5]||(t[5]=e=>k(we)({url:"/app/pages/member/point"})),style:_({color:k(a).accountTextColor})},{default:y((()=>[w("积分")])),_:1},8,["style"])])),_:1})])),_:1}),h(o,{class:"text-center w-[33.333%] flex-shrink-0",onClick:t[6]||(t[6]=e=>k(we)({url:"/addon/yz_she/pages/voucher/index"}))},{default:y((()=>[h(o,{class:"text-[36rpx] mb-[20rpx] font-500 price-font"},{default:y((()=>[h(o,{style:_({color:k(a).textColor})},{default:y((()=>[w(C(B.value),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-[22rpx] font-400"},{default:y((()=>[h(o,{style:_({color:k(a).accountTextColor})},{default:y((()=>[w("加价券")])),_:1},8,["style"])])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["class"]),S(" 强制绑定手机号 "),h(bt,{ref_key:"bindMobileRef",ref:T},null,512)])),_:1},8,["style"])}}}),Wl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component));c((()=>{a()})),p((()=>o.value),((e,t)=>{a()}),{deep:!0});const a=()=>{"decorate"==l.mode?r.value={quote_count:2,recycle_count:0}:f()},r=u({quote_count:0,recycle_count:0}),n=i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+="background-image:url("+d(o.value.componentBgUrl)+");",e+="background-size: 100% 100%;",e+="background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),f=async()=>{try{const e=await yt();r.value.quote_count=e.data||0,r.value.recycle_count=0}catch(e){r.value={quote_count:0,recycle_count:0}}};return(e,t)=>{const l=$,a=E,s=D;return b(),g(a,{style:_(k(n))},{default:y((()=>[h(a,{class:"px-[20rpx] py-[20rpx] flex gap-[20rpx]"},{default:y((()=>[S(" 估价订单卡片 "),h(a,{class:"flex-1 rounded-[16rpx] px-[30rpx] py-[30rpx] flex items-center shadow-sm",style:_({backgroundColor:k(o).cardBgColor||"#ffffff"}),onClick:t[0]||(t[0]=e=>{we({url:"/addon/yz_she/pages/order/quote-list"})})},{default:y((()=>[h(a,{class:"relative mr-[20rpx]"},{default:y((()=>[S(" 自定义图标 "),k(o).quoteIcon?(b(),g(a,{key:0,class:"w-[60rpx] h-[60rpx] rounded-[12rpx] overflow-hidden"},{default:y((()=>[h(l,{class:"w-full h-full",src:k(d)(k(o).quoteIcon),mode:"aspectFill"},null,8,["src"])])),_:1})):(b(),R(B,{key:1},[S(" 默认图标 "),h(a,{class:"w-[60rpx] h-[60rpx] rounded-[12rpx] flex items-center justify-center",style:_({backgroundColor:k(o).iconColor||"#FF6B35"})},{default:y((()=>[h(s,{class:"nc-iconfont nc-icon-biaoqianV6xx text-[32rpx] text-white"})])),_:1},8,["style"])],2112)),r.value.quote_count?(b(),g(a,{key:2,class:F(["absolute right-[-8rpx] top-[-8rpx] rounded-[20rpx] h-[32rpx] min-w-[32rpx] text-center leading-[32rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.quote_count>9?"px-[8rpx]":""])},{default:y((()=>[w(C(r.value.quote_count>99?"99+":r.value.quote_count),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"flex-1"},{default:y((()=>[h(a,{class:"text-[28rpx] font-500 mb-[4rpx]",style:_({color:k(o).textColor||"#333333"})},{default:y((()=>[w("我的评估")])),_:1},8,["style"])])),_:1})])),_:1},8,["style"]),S(" 回收订单卡片 "),h(a,{class:"flex-1 rounded-[16rpx] px-[30rpx] py-[30rpx] flex items-center shadow-sm",style:_({backgroundColor:k(o).cardBgColor||"#ffffff"}),onClick:t[1]||(t[1]=e=>{we({url:"/addon/yz_she/pages/order/order-list"})})},{default:y((()=>[h(a,{class:"relative mr-[20rpx]"},{default:y((()=>[S(" 自定义图标 "),k(o).recycleIcon?(b(),g(a,{key:0,class:"w-[60rpx] h-[60rpx] rounded-[12rpx] overflow-hidden"},{default:y((()=>[h(l,{class:"w-full h-full",src:k(d)(k(o).recycleIcon),mode:"aspectFill"},null,8,["src"])])),_:1})):(b(),R(B,{key:1},[S(" 默认图标 "),h(a,{class:"w-[60rpx] h-[60rpx] rounded-[12rpx] flex items-center justify-center",style:_({backgroundColor:k(o).iconColor||"#FF6B35"})},{default:y((()=>[h(s,{class:"nc-iconfont nc-icon-dingdanV6xx text-[32rpx] text-white"})])),_:1},8,["style"])],2112)),r.value.recycle_count?(b(),g(a,{key:2,class:F(["absolute right-[-8rpx] top-[-8rpx] rounded-[20rpx] h-[32rpx] min-w-[32rpx] text-center leading-[32rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.recycle_count>9?"px-[8rpx]":""])},{default:y((()=>[w(C(r.value.recycle_count>99?"99+":r.value.recycle_count),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"flex-1"},{default:y((()=>[h(a,{class:"text-[28rpx] font-500 mb-[4rpx]",style:_({color:k(o).textColor||"#333333"})},{default:y((()=>[w("我的订单")])),_:1},8,["style"])])),_:1})])),_:1},8,["style"])])),_:1})])),_:1},8,["style"])}}}),Ul=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+="background-image:url("+d(o.value.componentBgUrl)+");",e+="background-size: 100% 100%;",e+="background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=()=>{o.value.moreLink&&o.value.moreLink.name&&l.toRedirect(o.value.moreLink)};return(e,t)=>{const l=E,n=D,s=$;return b(),g(l,{style:_(k(a))},{default:y((()=>[h(l,{class:"px-[30rpx] py-[30rpx]"},{default:y((()=>[S(" 标题栏 "),h(l,{class:"flex items-center justify-between mb-[40rpx]"},{default:y((()=>[h(l,{class:"flex items-center"},{default:y((()=>[h(l,{class:"font-600",style:_({fontSize:2*k(o).titleSize+"rpx",color:k(o).titleColor})},{default:y((()=>[w(C(k(o).title),1)])),_:1},8,["style"])])),_:1}),k(o).showMore&&k(o).moreText?(b(),g(l,{key:0,class:"flex items-center",onClick:r},{default:y((()=>[h(n,{class:"text-[26rpx]",style:_({color:k(o).moreColor})},{default:y((()=>[w(C(k(o).moreText),1)])),_:1},8,["style"]),h(n,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] ml-[8rpx]",style:_({color:k(o).moreColor})},null,8,["style"])])),_:1})):S("v-if",!0)])),_:1}),S(" 流程步骤 "),h(l,{class:"flex items-start justify-between relative"},{default:y((()=>[(b(!0),R(B,null,z(k(o).steps,((e,t)=>(b(),g(l,{key:t,class:"flex flex-col items-center flex-1 relative"},{default:y((()=>[S(" 步骤图标 "),h(l,{class:"w-[100rpx] h-[100rpx] mb-[24rpx] flex items-center justify-center relative z-10"},{default:y((()=>[S(" 自定义图标 "),e.icon?(b(),g(s,{key:0,class:"w-[80rpx] h-[80rpx]",src:k(d)(e.icon),mode:"aspectFit"},null,8,["src"])):(b(),R(B,{key:1},[S(" 默认图标 "),h(l,{class:"w-[80rpx] h-[80rpx] bg-[#F8F9FA] rounded-full flex items-center justify-center border-[2rpx] border-[#E9ECEF]"},{default:y((()=>[h(n,{class:F(["nc-iconfont text-[40rpx] text-[#6C757D] font-600",e.defaultIcon])},null,8,["class"])])),_:2},1024)],2112))])),_:2},1024),S(" 步骤文字 "),h(l,{class:"text-center font-500",style:_({fontSize:2*(k(o).stepTextSize+2)+"rpx",color:k(o).stepTextColor})},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["style"]),S(" 箭头 "),t<k(o).steps.length-1?(b(),g(l,{key:0,class:"absolute top-[50rpx] left-[calc(100%-20rpx)] w-[40rpx] h-[40rpx] flex items-center justify-center z-20"},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-youV6xx text-[38rpx] text-[#999999] font-700"})])),_:1})):S("v-if",!0)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1},8,["style"])}}}),Yl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=u(0);let r=null;const n=i((()=>{const e=o.value.reviews||[];return 0===e.length?[]:[...e,...e]})),f=i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+="background-image:url("+d(o.value.componentBgUrl)+");",e+="background-size: 100% 100%;",e+="background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),m=()=>{o.value.moreLink&&o.value.moreLink.name&&l.toRedirect(o.value.moreLink)},x=()=>{if(!o.value.autoScroll||"decorate"===l.mode)return;const e=o.value.reviews||[];if(0===e.length)return;const t=o.value.scrollSpeed||3e3;r=setInterval((()=>{a.value++,a.value>=e.length&&setTimeout((()=>{a.value=0}),500)}),t)},v=()=>{r&&(clearInterval(r),r=null)};return c((()=>{setTimeout((()=>{x()}),1e3)})),Ae((()=>{v()})),p((()=>o.value.autoScroll),(e=>{v(),a.value=0,e&&setTimeout((()=>{x()}),100)})),p((()=>o.value.scrollSpeed),(()=>{o.value.autoScroll&&(v(),setTimeout((()=>{x()}),100))})),p((()=>o.value.reviews),(()=>{v(),a.value=0,o.value.autoScroll&&setTimeout((()=>{x()}),100)}),{deep:!0}),(e,t)=>{const l=E,r=D,s=$;return b(),g(l,{style:_(k(f))},{default:y((()=>[h(l,{class:"px-[30rpx] py-[30rpx]"},{default:y((()=>[S(" 标题栏 "),h(l,{class:"flex items-center justify-between mb-[30rpx]"},{default:y((()=>[h(l,{class:"flex items-center"},{default:y((()=>[h(l,{class:"text-[30rpx] font-500",style:_({fontSize:2*k(o).titleSize+"rpx",color:k(o).titleColor})},{default:y((()=>[w(C(k(o).title),1)])),_:1},8,["style"])])),_:1}),k(o).showMore&&k(o).moreText?(b(),g(l,{key:0,class:"flex items-center",onClick:m},{default:y((()=>[h(r,{class:"text-[24rpx]",style:_({color:k(o).moreColor})},{default:y((()=>[w(C(k(o).moreText),1)])),_:1},8,["style"]),h(r,{class:"nc-iconfont nc-icon-youV6xx text-[24rpx] ml-[8rpx]",style:_({color:k(o).moreColor})},null,8,["style"])])),_:1})):S("v-if",!0)])),_:1}),S(" 评价滚动区域 "),h(l,{class:"h-[300rpx] overflow-hidden relative"},{default:y((()=>[h(l,{class:"h-full relative"},{default:y((()=>[h(l,{class:"absolute w-full transition-transform duration-500 ease-in-out",style:_({transform:`translateY(${310*-a.value}rpx)`})},{default:y((()=>[(b(!0),R(B,null,z(k(n),((e,t)=>(b(),g(l,{key:"review-"+t,class:"h-[300rpx] flex items-center"},{default:y((()=>[h(l,{class:"w-full bg-white rounded-[16rpx] p-[24rpx] shadow-sm relative overflow-hidden"},{default:y((()=>[S(" 背景渐变 "),h(l,{class:"absolute inset-0 opacity-30 rounded-[16rpx]",style:_({background:`linear-gradient(135deg, ${k(o).bgGradient.startColor}, ${k(o).bgGradient.endColor})`})},null,8,["style"]),S(" 内容区域 "),h(l,{class:"relative z-10"},{default:y((()=>[S(" 用户信息 "),h(l,{class:"flex items-center mb-[16rpx]"},{default:y((()=>[e.avatar?(b(),g(s,{key:0,class:"w-[60rpx] h-[60rpx] rounded-full mr-[16rpx]",src:k(d)(e.avatar),mode:"aspectFill"},null,8,["src"])):(b(),g(l,{key:1,class:"w-[60rpx] h-[60rpx] rounded-full mr-[16rpx] bg-[#F5F5F5] flex items-center justify-center"},{default:y((()=>[h(r,{class:"nc-iconfont nc-icon-yonghuV6xx text-[32rpx] text-[#999999]"})])),_:1})),h(l,{class:"flex-1"},{default:y((()=>[h(l,{class:"flex items-center"},{default:y((()=>[h(r,{class:"text-[28rpx] font-500 text-[#333333] mr-[16rpx]"},{default:y((()=>[w(C(e.nickname),1)])),_:2},1024),h(r,{class:"text-[24rpx] text-[#666666]"},{default:y((()=>[w(C(e.time),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),S(" 评价内容 "),h(l,{class:"text-[26rpx] text-[#333333] leading-[1.5] mb-[16rpx]"},{default:y((()=>[w(C(e.content),1)])),_:2},1024),S(" 价格信息 "),h(l,{class:"flex items-center justify-between"},{default:y((()=>[h(l,{class:"flex items-center"},{default:y((()=>[h(r,{class:"text-[24rpx] text-[#666666] mr-[8rpx]"},{default:y((()=>[w("预估价¥")])),_:1}),h(r,{class:"text-[24rpx] text-[#666666] line-through mr-[16rpx]"},{default:y((()=>[w(C(e.originalPrice),1)])),_:2},1024),h(r,{class:"text-[24rpx] text-[#666666] mr-[8rpx]"},{default:y((()=>[w("最终鉴定价")])),_:1}),h(r,{class:"text-[32rpx] font-600",style:_({color:e.priceColor})},{default:y((()=>[w("¥"+C(e.finalPrice),1)])),_:2},1032,["style"])])),_:2},1024),S(" 印章 "),h(l,{class:"relative"},{default:y((()=>[e.stampImage?(b(),g(s,{key:0,class:"w-[80rpx] h-[80rpx]",src:k(d)(e.stampImage),mode:"aspectFit"},null,8,["src"])):(b(),g(l,{key:1,class:"w-[80rpx] h-[80rpx] rounded-full border-[4rpx] border-[#FF4444] flex items-center justify-center transform rotate-12"},{default:y((()=>[h(r,{class:"text-[20rpx] text-[#FF4444] font-600"},{default:y((()=>[w("专业"),me("br"),w("鉴定")])),_:1})])),_:1}))])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["style"])])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])}}}),Pl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=u(!1),r=u([]),n=i((()=>{const e=o.value.faqList||[];return a.value||e.length<=o.value.defaultShowCount?e:e.slice(0,o.value.defaultShowCount)})),c=i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+="background-image:url("+d(o.value.componentBgUrl)+");",e+="background-size: 100% 100%;",e+="background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),p=()=>{o.value.moreLink&&o.value.moreLink.name&&l.toRedirect(o.value.moreLink)},f=()=>{a.value=!a.value,r.value=[]};return(e,t)=>{const l=E,s=D;return b(),g(l,{style:_(k(c))},{default:y((()=>[h(l,{class:"px-[30rpx] py-[30rpx]"},{default:y((()=>[S(" 标题栏 "),h(l,{class:"flex items-center justify-between mb-[30rpx]"},{default:y((()=>[h(l,{class:"flex items-center"},{default:y((()=>[h(l,{class:"font-600",style:_({fontSize:2*k(o).titleSize+"rpx",color:k(o).titleColor})},{default:y((()=>[w(C(k(o).title),1)])),_:1},8,["style"])])),_:1}),k(o).showMore&&k(o).moreText?(b(),g(l,{key:0,class:"flex items-center",onClick:p},{default:y((()=>[h(s,{class:"text-[26rpx]",style:_({color:k(o).moreColor})},{default:y((()=>[w(C(k(o).moreText),1)])),_:1},8,["style"]),h(s,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] ml-[8rpx]",style:_({color:k(o).moreColor})},null,8,["style"])])),_:1})):S("v-if",!0)])),_:1}),S(" 问题列表 "),h(l,{class:"space-y-[20rpx]"},{default:y((()=>[(b(!0),R(B,null,z(k(n),((e,t)=>(b(),g(l,{key:t,class:"rounded-[16rpx] overflow-hidden border-[2rpx]",style:_({backgroundColor:k(o).itemBgColor,borderColor:k(o).borderColor})},{default:y((()=>[h(l,{class:"p-[24rpx] flex items-center justify-between",onClick:e=>(e=>{const t=r.value.indexOf(e);t>-1?r.value.splice(t,1):r.value.push(e)})(t)},{default:y((()=>[h(l,{class:"flex-1 pr-[20rpx]"},{default:y((()=>[h(s,{class:"font-500",style:_({fontSize:2*k(o).questionSize+"rpx",color:k(o).questionColor})},{default:y((()=>[w(C(e.question),1)])),_:2},1032,["style"])])),_:2},1024),h(l,{class:"w-[40rpx] h-[40rpx] flex items-center justify-center"},{default:y((()=>[h(s,{class:F(["nc-iconfont text-[24rpx] text-[#999999] transition-transform duration-300",[r.value.includes(t)?"nc-icon-shangV6xx":"nc-icon-xiaV6xx"]])},null,8,["class"])])),_:2},1024)])),_:2},1032,["onClick"]),S(" 回答内容 "),r.value.includes(t)?(b(),g(l,{key:0,class:"px-[24rpx] pb-[24rpx] border-t-[1rpx]",style:_({borderColor:k(o).borderColor})},{default:y((()=>[h(s,{class:"leading-[1.6]",style:_({fontSize:2*k(o).answerSize+"rpx",color:k(o).answerColor})},{default:y((()=>[w(C(e.answer),1)])),_:2},1032,["style"])])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1032,["style"])))),128))])),_:1}),S(" 查看全部/收起按钮 "),k(o).faqList.length>k(o).defaultShowCount?(b(),g(l,{key:0,class:"mt-[40rpx] flex justify-center"},{default:y((()=>[h(l,{class:"px-[40rpx] py-[20rpx] rounded-[40rpx] border-[2rpx] border-[#E5E5E5] bg-[#FAFAFA]",onClick:f},{default:y((()=>[h(s,{class:"text-[26rpx] text-[#666666] flex items-center"},{default:y((()=>[w(C(a.value?k(o).collapseText:k(o).expandText)+" ",1),h(s,{class:F(["nc-iconfont ml-[8rpx] text-[20rpx] transition-transform duration-300",[a.value?"nc-icon-shangV6xx":"nc-icon-xiaV6xx"]])},null,8,["class"])])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})])),_:1},8,["style"])}}}),Vl=Pe(n({__name:"index",props:["component","index","value"],setup(e){const t=e,l=s(),o=i((()=>t.value?t.value:"decorate"==l.mode?l.value[t.index]:t.component)),a=u({}),r=e=>{a.value=e},n=u(""),p=e=>{let t=0;return e&&e.newcomer_price&&(t=Number(e.newcomer_price).toFixed(2)),t},f=()=>{let e=24*a.value.days+a.value.hours;return e=e||0,e=e>=10?e:"0"+e,e},m=()=>{let e=!0;return"decorate"!=l.mode&&a.value.days<=0&&a.value.hours<=0&&a.value.minutes<=0&&a.value.seconds<=0&&a.value.milliseconds<=0&&(e=!1),e},T=i((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),M=i((()=>{var e={val:"",style:""};return o.value.imgElementRounded&&(e.val=2*o.value.imgElementRounded+"rpx",e.style+="border-radius:"+2*o.value.imgElementRounded+"rpx;"),e})),A=i((()=>{var e="";return o.value.countDown&&o.value.countDown.numberBg&&(o.value.countDown.numberBg.startColor&&o.value.countDown.numberBg.endColor?e+=`background:linear-gradient(${o.value.countDown.numberBg.startColor},${o.value.countDown.numberBg.endColor});`:e+="background-color:"+(o.value.countDown.numberBg.startColor||o.value.countDown.numberBg.endColor)+";"),o.value.countDown.numberColor&&(e+="color:"+o.value.countDown.numberColor+";"),e})),j=()=>{var e="";return o.value.topElementRounded&&(e+="border-top-left-radius:"+2*o.value.topElementRounded+"rpx;"),o.value.topElementRounded&&(e+="border-top-right-radius:"+2*o.value.topElementRounded+"rpx;"),o.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomElementRounded+"rpx;"),o.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomElementRounded+"rpx;"),e},W=i((()=>{var e="";return o.value.subTitle&&(o.value.subTitle.startColor&&o.value.subTitle.endColor?e+=`background:linear-gradient(to right, ${o.value.subTitle.startColor},${o.value.subTitle.endColor});`:e+="background-color:"+(o.value.subTitle.startColor||o.value.subTitle.endColor)+";"),o.value.subTitle.textColor&&(e+="color:"+o.value.subTitle.textColor+";"),e})),U=u([]);c((()=>{if("decorate"==l.mode){let e={goods:{goods_name:"商品名称"},sku_image:"",newcomer_price:.01};U.value.push(e),U.value.push(e),U.value.push(e)}else(()=>{let e={limit:"all"==o.value.source?o.value.num:"",sku_ids:"custom"==o.value.source?o.value.goods_ids:""};ht(e).then((e=>{n.value=e.data.validity_time;let t=(new Date).getTime();n.value=1e3*Number(n.value)-t,U.value=e.data.goods_list}))})()}));const Y=()=>{we({url:"/addon/shop/pages/newcomer/list"})},P=e=>{we({url:"/addon/shop/pages/goods/detail",param:{sku_id:e.sku_id,type:"newcomer_discount"}})};return(e,t)=>{const s=$,i=D,u=E,c=x(v("up-count-down"),_t),V=x(v("u--image"),pt),H=I;return U.value&&Object.keys(U.value).length?(b(),g(u,{key:0,class:"shop-newcomer overflow-hidden",style:_(k(T))},{default:y((()=>["style-1"==k(o).style.value?(b(),g(u,{key:0,class:"style-1 p-[20rpx]"},{default:y((()=>[h(u,{class:"head flex justify-between items-center mb-[16rpx]",onClick:t[0]||(t[0]=e=>Y())},{default:y((()=>[k(o).textImg?(b(),g(s,{key:0,class:"h-[34rpx] w-[auto]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),je(h(u,{class:"time-wrap flex items-center ml-[auto]"},{default:y((()=>[k(Fe)()||"decorate"==k(l).mode?m()?(b(),R(B,{key:1},[h(i,{style:_({color:k(o).countDown.otherColor}),class:"mr-[10rpx] text-[24rpx]"},{default:y((()=>[w("距结束还有")])),_:1},8,["style"]),h(c,{class:"text-[#fff] text-[28rpx]",time:n.value,format:"HH:mm:ss",onChange:r},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[f()?(b(),g(i,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(f()),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(i,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.minutes?(b(),g(i,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.minutes>=10?a.value.minutes:"0"+a.value.minutes),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(i,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.seconds?(b(),g(i,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.seconds<10?"0"+a.value.seconds:a.value.seconds),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"]))])),_:1})])),_:1})])),_:1},8,["time"])],64)):(b(),g(i,{key:2,class:"text-[28rpx]",style:_({color:k(o).countDown.otherColor})},{default:y((()=>[w("活动已结束 ")])),_:1},8,["style"])):(b(),g(i,{key:0,class:"text-[24rpx] font-500",style:_({color:k(o).countDown.otherColor})},{default:y((()=>[w("活动未开始")])),_:1},8,["style"]))])),_:1},512),[[Ne,a.value&&Object.keys(a.value).length]])])),_:1}),h(H,{"scroll-x":"true",class:"content"},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"inline-flex bg-[#fff] p-[16rpx] box-border",style:_(j()),onClick:t[1]||(t[1]=e=>P(U.value[0]))},{default:y((()=>[h(u,{class:"w-[150rpx] h-[150rpx] flex items-center justify-center"},{default:y((()=>[h(V,{radius:k(M).val,width:"150rpx",height:"150rpx",src:k(d)(U.value[0].sku_image||""),model:"aspectFill"},{error:y((()=>[h(s,{class:"w-[150rpx] h-[150rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:1},8,["radius","src"])])),_:1}),h(u,{class:"flex flex-col ml-[20rpx] py-[4rpx] flex-1"},{default:y((()=>[U.value[0].goods?(b(),g(u,{key:0,class:"text-[26rpx] w-[200rpx] whitespace-pre-wrap leading-[1.4] multi-hidden"},{default:y((()=>[w(C(U.value[0].goods.goods_name),1)])),_:1})):S("v-if",!0),h(u,{class:"flex items-center justify-between mt-[auto]"},{default:y((()=>[h(u,{class:"flex flex-1 items-center"},{default:y((()=>[h(i,{class:"text-[20rpx] text-[#FF0000]"},{default:y((()=>[w("￥")])),_:1}),h(i,{class:"text-[28rpx] font-500 text-[#FF0000] max-w[120rpx] truncate"},{default:y((()=>[w(C(p(U.value[0])),1)])),_:1})])),_:1}),h(i,{class:"italic flex items-center justify-center rounded-[40rpx] w-[60rpx] h-[40rpx] leading-1 text-[#fff] font-bold first-btn-bg"},{default:y((()=>[w("抢")])),_:1})])),_:1})])),_:1})])),_:1},8,["style"]),(b(!0),R(B,null,z(U.value,((e,t)=>(b(),R(B,{key:t},[t>0?(b(),g(u,{key:0,class:"ml-[10rpx] inline-flex flex-col items-center p-[16rpx] bg-[#fff] box-border",style:_(j()),onClick:t=>P(e)},{default:y((()=>[h(u,{class:"w-[110rpx] h-[110rpx] flex items-center justify-center"},{default:y((()=>[h(V,{radius:k(M).val,width:"110rpx",height:"110rpx",src:k(d)(e.sku_image||""),model:"aspectFill"},{error:y((()=>[h(s,{class:"w-[110rpx] h-[110rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"])])),_:2},1024),h(u,{class:"flex items-center mt-[auto]"},{default:y((()=>[h(i,{class:"text-[24rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(i,{class:"text-[24rpx] font-500 truncate"},{default:y((()=>[w(C(p(e)),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["style","onClick"])):S("v-if",!0)],64)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(o).style.value?(b(),g(u,{key:1,class:"style-2 p-[20rpx]"},{default:y((()=>[h(u,{class:"head flex justify-between items-center mb-[16rpx]",onClick:t[2]||(t[2]=e=>Y())},{default:y((()=>[k(o).textImg?(b(),g(s,{key:0,class:"h-[34rpx] w-[auto]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),je(h(u,{class:"time-wrap flex items-center ml-[auto]"},{default:y((()=>[k(Fe)()||"decorate"==k(l).mode?m()?(b(),R(B,{key:1},[h(i,{style:_({color:k(o).countDown.otherColor}),class:"mr-[10rpx] text-[24rpx]"},{default:y((()=>[w("倒计时")])),_:1},8,["style"]),h(c,{class:"text-[#fff] text-[28rpx]",time:n.value,format:"DD:HH:mm",onChange:r},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.days?(b(),g(i,{key:0,class:"time-num",style:_(k(A))},{default:y((()=>[w(C(a.value.days),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num",style:_(k(A))},{default:y((()=>[w("0")])),_:1},8,["style"])),h(i,{style:_({color:k(o).countDown.otherColor}),class:"text-[22rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w("天")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.hours?(b(),g(i,{key:0,class:"time-num",style:_(k(A))},{default:y((()=>[w(C(a.value.hours>=10?a.value.hours:"0"+a.value.hours),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(i,{style:_({color:k(o).countDown.otherColor}),class:"text-[22rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w("时")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.minutes?(b(),g(i,{key:0,class:"time-num",style:_(k(A))},{default:y((()=>[w(C(a.value.minutes>=10?a.value.minutes:"0"+a.value.minutes),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(i,{style:_({color:k(o).countDown.otherColor}),class:"text-[22rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w("分")])),_:1},8,["style"])])),_:1})])),_:1})])),_:1},8,["time"])],64)):(b(),g(i,{key:2,class:"text-[28rpx]",style:_({color:k(o).countDown.otherColor})},{default:y((()=>[w("活动已结束 ")])),_:1},8,["style"])):(b(),g(i,{key:0,class:"text-[24rpx] font-500",style:_({color:k(o).countDown.otherColor})},{default:y((()=>[w("活动未开始")])),_:1},8,["style"]))])),_:1},512),[[Ne,a.value&&Object.keys(a.value).length]])])),_:1}),h(H,{"scroll-x":"true",class:"content"},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[(b(!0),R(B,null,z(U.value,((e,t)=>(b(),g(u,{key:t,class:"item-bg mr-[10rpx] inline-flex flex-col items-center p-[6rpx] bg-[#fff] box-border",style:_(j()),onClick:t=>P(e)},{default:y((()=>[h(u,{class:"flex items-center justify-center w-[146rpx] h-[146rpx]"},{default:y((()=>[h(V,{radius:k(M).val,width:"146rpx",height:"146rpx",src:k(d)(e.sku_image||""),model:"aspectFill"},{error:y((()=>[h(s,{class:"w-[146rpx] h-[146rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"])])),_:2},1024),h(s,{class:"h-[32rpx] w-[auto] mt-[12rpx] mb-[8rpx]",src:k(d)("addon/shop/diy/newcomer/style_2_img.png"),mode:"heightFix"},null,8,["src"]),h(u,{class:"flex items-center text-[#fff] pb-[4rpx]"},{default:y((()=>[h(i,{class:"text-[20rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(i,{class:"text-[30rpx] max-w-[120rpx] font-500 truncate"},{default:y((()=>[w(C(p(e)),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["style","onClick"])))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-3"==k(o).style.value?(b(),g(u,{key:2,class:"style-3 pt-[20rpx] pb-[10rpx] px-[10rpx]"},{default:y((()=>[h(u,{class:"head flex mx-[10rpx] items-center mb-[12rpx]",onClick:t[4]||(t[4]=e=>Y())},{default:y((()=>[k(o).textImg?(b(),g(s,{key:0,class:"h-[34rpx] w-[auto] mr-[16rpx]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),je(h(u,{class:"time-wrap flex items-center"},{default:y((()=>[k(Fe)()||"decorate"==k(l).mode?m()?(b(),g(c,{key:1,class:"text-[#fff] text-[28rpx]",time:n.value,format:"HH:mm:ss",onChange:r},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[f()?(b(),g(i,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(f()),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(i,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] font-bold ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.minutes?(b(),g(i,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.minutes>=10?a.value.minutes:"0"+a.value.minutes),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(i,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] font-bold ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.seconds?(b(),g(i,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.seconds<10?"0"+a.value.seconds:a.value.seconds),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"]))])),_:1})])),_:1})])),_:1},8,["time"])):(b(),g(i,{key:2,style:_({color:k(o).countDown.otherColor}),class:"text-[26rpx]"},{default:y((()=>[w("活动已结束 ")])),_:1},8,["style"])):(b(),g(i,{key:0,style:_({color:k(o).countDown.otherColor}),class:"text-[24rpx] font-500"},{default:y((()=>[w("活动未开始")])),_:1},8,["style"]))])),_:1},512),[[Ne,a.value&&Object.keys(a.value).length]]),h(u,{class:"ml-[auto] rounded-[20rpx] flex items-baseline pl-[16rpx] pr-[10rpx] pt-[10rpx] pb-[10rpx]",style:_(k(W)),onClick:t[3]||(t[3]=N((e=>k(l).toRedirect(k(o).subTitle.link)),["stop"]))},{default:y((()=>[h(i,{class:"text-[22rpx]"},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1}),h(i,{class:"iconfont iconarrow-right !text-[18rpx] font-bold"})])),_:1},8,["style"])])),_:1}),h(H,{"scroll-x":"true",class:"content bg-[#fff] box-border p-[16rpx] rounded-[var(--rounded-small)]"},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[(b(!0),R(B,null,z(U.value,((e,t)=>(b(),g(u,{key:t,class:F(["item-bg inline-flex flex-col items-center box-border",{"mr-[16rpx]":t!=U.value.length-1}]),style:_(j()),onClick:t=>P(e)},{default:y((()=>[h(u,{class:"bg-[#f8f8f8] flex items-center justify-center w-[152rpx] h-[152rpx] overflow-hidden",style:_(k(M).style)},{default:y((()=>[h(V,{radius:k(M).val,width:"152rpx",height:"152rpx",src:k(d)(e.sku_image||""),model:"aspectFill"},{error:y((()=>[h(s,{class:"w-[152rpx] h-[152rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"])])),_:2},1032,["style"]),h(s,{class:"h-[32rpx] w-[auto] mt-[12rpx] mb-[10rpx]",src:k(d)("addon/shop/diy/newcomer/style_3_img.png"),mode:"heightFix"},null,8,["src"]),h(u,{class:"flex items-center text-[#FF0E00] pb-[2rpx]"},{default:y((()=>[h(i,{class:"text-[20rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(i,{class:"text-[30rpx] max-w-[120rpx] font-500 truncate"},{default:y((()=>[w(C(p(e)),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-4"==k(o).style.value?(b(),g(u,{key:3,class:"style-4 p-[20rpx] pt-[24rpx]",style:_({background:"url("+k(d)("addon/shop/diy/newcomer/style_4_head.png")+") no-repeat"})},{default:y((()=>[h(u,{class:"head flex mx-[10rpx] items-center justify-between mb-[24rpx]"},{default:y((()=>[k(o).textImg?(b(),g(s,{key:0,class:"h-[34rpx] w-[auto]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),je(h(u,{class:"time-wrap ml-[auto] flex items-center -mt-[8rpx]"},{default:y((()=>[k(Fe)()||"decorate"==k(l).mode?m()?(b(),R(B,{key:1},[h(i,{style:_({color:k(o).countDown.otherColor}),class:"mr-[8rpx] text-[24rpx]"},{default:y((()=>[w("本场仅剩")])),_:1},8,["style"]),h(c,{class:"text-[#fff] text-[28rpx]",time:n.value,format:"HH:mm:ss",onChange:r},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"text-[28rpx] flex items-center"},{default:y((()=>[f()?(b(),g(i,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(f()),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(i,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] ml-[4rpx] font-bold mr-[5rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[28rpx] flex items-center"},{default:y((()=>[a.value.minutes?(b(),g(i,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.minutes>=10?a.value.minutes:"0"+a.value.minutes),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(i,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] ml-[4rpx] font-bold mr-[5rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[28rpx] flex items-center"},{default:y((()=>[a.value.seconds?(b(),g(i,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.seconds<10?"0"+a.value.seconds:a.value.seconds),1)])),_:1},8,["style"])):(b(),g(i,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"]))])),_:1})])),_:1})])),_:1},8,["time"])],64)):(b(),g(i,{key:2,style:_({color:k(o).countDown.otherColor}),class:"w-[200rpx] text-center text-[24rpx] pb-[4rpx]"},{default:y((()=>[w("活动已结束")])),_:1},8,["style"])):(b(),g(i,{key:0,style:_({color:k(o).countDown.otherColor}),class:"w-[200rpx] text-center text-[24rpx] font-500 pb-[4rpx]"},{default:y((()=>[w("活动未开始")])),_:1},8,["style"]))])),_:1},512),[[Ne,a.value&&Object.keys(a.value).length]])])),_:1}),h(H,{"scroll-x":"true",class:"content"},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[(b(!0),R(B,null,z(U.value,((e,t)=>(b(),g(u,{key:t,class:F(["item-bg inline-flex flex-col items-center box-border",{"mr-[20rpx]":t!=U.value.length-1}]),style:_(j()),onClick:t=>P(e)},{default:y((()=>[h(u,{class:"relative flex items-center justify-center w-[100%] h-[130rpx] pt-[40rpx] mb-[10rpx]"},{default:y((()=>[h(V,{radius:k(M).val,width:"130rpx",height:"130rpx",src:k(d)(e.sku_image||""),model:"aspectFill"},{error:y((()=>[h(s,{class:"w-[130rpx] h-[130rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"]),h(u,{class:"content-sign text-[20rpx] text-[#fff]"},{default:y((()=>[w("新人价")])),_:1})])),_:2},1024),h(u,{class:"w-[210rpx] relative -right-[2rpx] -bottom-[2rpx] flex items-center text-[#FF0E00] pb-[2rpx]"},{default:y((()=>[h(u,{class:"flex items-center justify-center flex-1"},{default:y((()=>[h(i,{class:"text-[20rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(i,{class:"text-[36rpx] max-w-[140rpx] font-500 truncate"},{default:y((()=>[w(C(p(e)),1)])),_:2},1024)])),_:2},1024),h(i,{class:"btn-bg ml-auto"},{default:y((()=>[w("抢")])),_:1})])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"])):S("v-if",!0)}}}),[["__scopeId","data-v-03f58329"]]),Hl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component));c((()=>{a()})),p((()=>o.value),((e,t)=>{a()}),{deep:!0});const a=()=>{"decorate"==l.mode?r.value={}:f()},r=u({}),n=i((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),f=()=>{kt().then((e=>{r.value=e.data}))},m=e=>{we({url:"/addon/shop/pages/order/list",param:{status:e}})};return(e,t)=>{const a=E,s=D,i=$;return b(),g(a,{style:_(k(n))},{default:y((()=>[h(a,{class:"diy-text relative"},{default:y((()=>[h(a,{class:"px-[var(--pad-sidebar-m)] pt-[var(--pad-top-m)] pb-[40rpx] flex items-center justify-between"},{default:y((()=>[h(a,{onClick:t[0]||(t[0]=e=>k(l).toRedirect(k(o).link))},{default:y((()=>[h(a,{class:"max-w-[200rpx] truncate leading-[1] text-[30rpx]",style:_({fontSize:2*k(o).fontSize+"rpx",color:k(o).textColor,fontWeight:"normal"==k(o).fontWeight?500:k(o).fontWeight})},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"])])),_:1}),h(a,{class:"flex items-center"},{default:y((()=>[h(a,{onClick:t[1]||(t[1]=e=>k(we)({url:"/addon/shop/pages/order/list"})),class:"flex items-center"},{default:y((()=>[h(s,{class:"max-w-[200rpx] truncate text-[24rpx]",style:_({color:k(o).more.color})},{default:y((()=>[w(C(k(o).more.text),1)])),_:1},8,["style"]),h(s,{class:"nc-iconfont nc-icon-youV6xx text-[24rpx]",style:_({color:k(o).more.color})},null,8,["style"])])),_:1})])),_:1})])),_:1})])),_:1}),h(a,{class:"pb-[var(--pad-top-m)] px-[var(--pad-sidebar-m)] flex items-center justify-between text-center"},{default:y((()=>[h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[2]||(t[2]=e=>m(1))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(i,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order1.png")},null,8,["src"]),r.value.wait_pay?(b(),g(a,{key:0,class:F(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.wait_pay>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.wait_pay>99?"99+":r.value.wait_pay),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("待付款")])),_:1},8,["style"])])),_:1}),h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[3]||(t[3]=e=>m(2))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(i,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order2.png")},null,8,["src"]),r.value.wait_shipping?(b(),g(a,{key:0,class:F(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.wait_shipping>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.wait_shipping>99?"99+":r.value.wait_shipping),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("待发货")])),_:1},8,["style"])])),_:1}),h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[4]||(t[4]=e=>m(3))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(i,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order3.png")},null,8,["src"]),r.value.wait_take?(b(),g(a,{key:0,class:F(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.wait_take>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.wait_take>99?"99+":r.value.wait_take),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("待收货")])),_:1},8,["style"])])),_:1}),h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[5]||(t[5]=e=>m(5))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(i,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order4.png")},null,8,["src"]),r.value.evaluate?(b(),g(a,{key:0,class:F(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.evaluate>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.evaluate>99?"99+":r.value.evaluate),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("待评价")])),_:1},8,["style"])])),_:1}),h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[6]||(t[6]=e=>k(we)({url:"/addon/shop/pages/refund/list"}))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(i,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order5.png")},null,8,["src"]),r.value.refund?(b(),g(a,{key:0,class:F(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.refund>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.refund>99?"99+":r.value.refund),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("售后/退款")])),_:1},8,["style"])])),_:1})])),_:1})])),_:1},8,["style"])}}}),Ll=n({__name:"index",props:["component","index"],setup(e){const t=e,l=s(),o=i((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=i((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=i((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${x.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e}));c((()=>{v(),"decorate"==l.mode&&p((()=>o.value),((e,t)=>{e&&"ShopSearch"==e.componentName&&v()}))}));const n=T(),x=u(0),v=()=>{f((()=>{m().in(n).select(".diy-shop-search").boundingClientRect((e=>{x.value=e.height})).exec()}))},S=e=>{if("decorate"==l.mode)return!1;we({url:e})};return(e,t)=>{const l=E,n=$,s=D;return b(),g(l,{style:_(k(a))},{default:y((()=>[h(l,{style:_(k(r))},null,8,["style"]),h(l,{class:"diy-shop-search relative overflow-hidden flex items-center"},{default:y((()=>[h(n,{src:k(d)("addon/shop/diy/search_01.png"),class:"w-[40rpx] h-[40rpx]",mode:"widthFix",onClick:t[0]||(t[0]=e=>S("/addon/shop/pages/goods/category"))},null,8,["src"]),h(l,{class:"flex-1 ml-[24rpx] rounded-[32rpx] flex items-center bg-[var(--temp-bg)] opacity-90 py-[10rpx] pl-[38rpx] pr-[32rpx] justify-between h-[60rpx] box-border",onClick:t[1]||(t[1]=e=>S("/addon/shop/pages/goods/search"))},{default:y((()=>[h(s,{class:"text-[var(--text-color-light9)] text-[26rpx]"},{default:y((()=>[w(C(k(o).text),1)])),_:1}),h(s,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 text-[24rpx]"})])),_:1})])),_:1})])),_:1},8,["style"])}}}),Ol=Pe(n({__name:"index",props:["component","index","value"],setup(e){const t=e,l=vt(),o=s(),a=u([]),r=i((()=>t.value?t.value:"decorate"==o.mode?o.value[t.index]:t.component));let n=u(!0);n.value=!0;const f=i((()=>{var e="";return e+="position:relative;",r.value.componentStartBgColor&&(r.value.componentStartBgColor&&r.value.componentEndBgColor?e+=`background:linear-gradient(${r.value.componentGradientAngle},${r.value.componentStartBgColor},${r.value.componentEndBgColor});`:e+="background-color:"+r.value.componentStartBgColor+";"),r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;"),e})),m=i((()=>{var e="";return r.value.elementBgColor&&(e+="background-color:"+r.value.elementBgColor+";"),r.value.topElementRounded&&(e+="border-top-left-radius:"+2*r.value.topElementRounded+"rpx;"),r.value.topElementRounded&&(e+="border-top-right-radius:"+2*r.value.topElementRounded+"rpx;"),r.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomElementRounded+"rpx;"),r.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomElementRounded+"rpx;"),r.value.margin&&r.value.margin.both?e+="width: calc((100vw - "+4*r.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );",e})),F=i((()=>{var e="";return r.value.topElementRounded&&(e+="border-radius:"+2*r.value.topElementRounded+"rpx;"),e})),T=i((()=>{var e="";return r.value.topCarouselRounded&&(e+="border-top-left-radius:"+2*r.value.topCarouselRounded+"rpx;"),r.value.topCarouselRounded&&(e+="border-top-right-radius:"+2*r.value.topCarouselRounded+"rpx;"),r.value.bottomCarouselRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomCarouselRounded+"rpx;"),r.value.bottomCarouselRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomCarouselRounded+"rpx;"),r.value.margin&&r.value.margin.both?e+="width: calc((100vw - "+4*r.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );",e}));c((()=>{I(),"decorate"!=o.mode&&p((()=>r.value),((e,t)=>{I()}),{deep:!0})}));const I=()=>{if("decorate"==o.mode){let e={goods_cover_thumb_mid:"",goods_name:"商品名称",sale_num:"100",unit:"件",goodsSku:{show_price:100}};a.value.push(e)}else(()=>{let e={num:1,goods_ids:"custom"==r.value.source?r.value.goods_ids:""};ct(e).then((e=>{a.value=e.data}))})()},M=u(0),A=e=>{M.value=e.detail.current};return(e,t)=>{const s=$,i=E,u=D,c=Y,p=P,I=x(v("u--image"),pt);return a.value&&a.value[0]?(b(),g(i,{key:0,style:_(k(f)),class:"overflow-hidden"},{default:y((()=>[k(r).textImg||k(r).subTitle.text?(b(),g(i,{key:0,class:"flex justify-between items-center mb-[20rpx]"},{default:y((()=>[k(r).textImg?(b(),g(i,{key:0,class:"h-[34rpx] flex items-center",onClick:t[0]||(t[0]=e=>k(o).toRedirect(k(r).textLink))},{default:y((()=>[h(s,{class:"h-[100%] w-[auto]",src:k(d)(k(r).textImg),mode:"heightFix"},null,8,["src"])])),_:1})):S("v-if",!0),k(r).subTitle.text?(b(),g(i,{key:1,class:"flex items-center ml-[auto]",onClick:t[1]||(t[1]=e=>k(o).toRedirect(k(r).subTitle.link)),style:_({color:k(r).subTitle.textColor})},{default:y((()=>[h(u,{class:"text-[24rpx]"},{default:y((()=>[w(C(k(r).subTitle.text),1)])),_:1}),h(u,{class:"text-[22rpx] iconfont iconxiangyoujiantou"})])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),h(i,{class:"flex justify-between"},{default:y((()=>[S(" 轮播图 "),h(i,{class:"relative w-[340rpx] overflow-hidden",style:_(k(T))},{default:y((()=>[1==k(r).list.length?(b(),g(i,{key:0,class:"leading-0 overflow-hidden"},{default:y((()=>[h(i,{onClick:t[2]||(t[2]=e=>k(o).toRedirect(k(r).list[0].link))},{default:y((()=>[k(r).list[0].imageUrl?(b(),g(s,{key:0,src:k(d)(k(r).list[0].imageUrl),mode:"heightFix",class:"h-[504rpx] !w-full","show-menu-by-longpress":!0},null,8,["src"])):(b(),g(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"heightFix",class:"h-[504rpx] !w-full","show-menu-by-longpress":!0},null,8,["src"]))])),_:1})])),_:1})):(b(),g(p,{key:1,class:"swiper ns-indicator-dots-three h-[504rpx]",autoplay:"true",circular:"true","indicator-dots":k(n),onChange:A,"indicator-color":k(r).indicatorColor,"indicator-active-color":k(r).indicatorActiveColor},{default:y((()=>[(b(!0),R(B,null,z(k(r).list,(e=>(b(),g(c,{class:"swiper-item",key:e.id},{default:y((()=>[h(i,{onClick:t=>k(o).toRedirect(e.link)},{default:y((()=>[h(i,{class:"item h-[504rpx]"},{default:y((()=>[e.imageUrl?(b(),g(s,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"])):(b(),g(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"]))])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1},8,["indicator-dots","indicator-color","indicator-active-color"]))])),_:1},8,["style"]),h(i,{class:"w-[340rpx] h-[504rpx] flex flex-col bg-[#fff] box-border overflow-hidden",style:_(k(m)),onClick:t[3]||(t[3]=e=>{return t=a.value[0],void we({url:"/addon/shop/pages/goods/detail",param:{goods_id:t.goods_id}});var t})},{default:y((()=>[h(i,{style:_(k(F)),class:"w-[346rpx] h-[350rpx] overflow-hidden"},{default:y((()=>[h(I,{width:"346rpx",height:"350rpx",src:k(d)(a.value[0].goods_cover_thumb_mid||""),model:"aspectFill"},{error:y((()=>[h(s,{class:"w-[346rpx] h-[350rpx]",src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["src"])])),_:1},8,["style"]),h(i,{class:"px-[16rpx] flex-1 pt-[16rpx] pb-[20rpx] flex flex-col justify-between"},{default:y((()=>[h(i,{class:"text-[#303133] leading-[40rpx] text-[28rpx] truncate",style:_({color:k(r).goodsNameStyle.color,fontWeight:k(r).goodsNameStyle.fontWeight})},{default:y((()=>[w(C(a.value[0].goods_name),1)])),_:1},8,["style"]),h(i,{class:"flex justify-between flex-wrap items-baseline mt-[28rpx]"},{default:y((()=>[h(i,{class:"flex items-center"},{default:y((()=>[h(i,{class:"text-[var(--price-text-color)] price-font truncate max-w-[200rpx]",style:_({color:k(r).priceStyle.mainColor})},{default:y((()=>[h(u,{class:"text-[24rpx] font-400"},{default:y((()=>[w("￥")])),_:1}),h(u,{class:"text-[40rpx] font-500"},{default:y((()=>[w(C(parseFloat(k(l).goodsPrice(a.value[0])).toFixed(2).split(".")[0]),1)])),_:1}),h(u,{class:"text-[24rpx] font-500"},{default:y((()=>[w("."+C(parseFloat(k(l).goodsPrice(a.value[0])).toFixed(2).split(".")[1]),1)])),_:1})])),_:1},8,["style"]),"member_price"==k(l).priceType(a.value[0])?(b(),g(s,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:k(d)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==k(l).priceType(a.value[0])?(b(),g(s,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:k(d)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==k(l).priceType(a.value[0])?(b(),g(s,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:k(d)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):S("v-if",!0)])),_:1}),h(i,{class:"w-[44rpx] h-[44rpx] bg-[red] flex items-center justify-center rounded-[50%]",style:_({backgroundColor:k(r).saleStyle.color})},{default:y((()=>[h(u,{class:"iconfont iconjia font-500 text-[32rpx] text-[#fff]"})])),_:1},8,["style"])])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})])),_:1},8,["style"])):S("v-if",!0)}}}),[["__scopeId","data-v-c2411932"]]);
/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */
function ql(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,o)}return l}function Gl(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?ql(Object(l),!0).forEach((function(t){Zl(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):ql(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}function Xl(e){return(Xl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Zl(e,t,l){return t in e?Object.defineProperty(e,t,{value:l,enumerable:!0,configurable:!0,writable:!0}):e[t]=l,e}function Ql(){return Ql=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var o in l)Object.prototype.hasOwnProperty.call(l,o)&&(e[o]=l[o])}return e},Ql.apply(this,arguments)}function Kl(e,t){if(null==e)return{};var l,o,a=function(e,t){if(null==e)return{};var l,o,a={},r=Object.keys(e);for(o=0;o<r.length;o++)l=r[o],t.indexOf(l)>=0||(a[l]=e[l]);return a}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(o=0;o<r.length;o++)l=r[o],t.indexOf(l)>=0||Object.prototype.propertyIsEnumerable.call(e,l)&&(a[l]=e[l])}return a}function Jl(e){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(e)}var eo=Jl(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),to=Jl(/Edge/i),lo=Jl(/firefox/i),oo=Jl(/safari/i)&&!Jl(/chrome/i)&&!Jl(/android/i),ao=Jl(/iP(ad|od|hone)/i),ro=Jl(/chrome/i)&&Jl(/android/i),no={capture:!1,passive:!1};function so(e,t,l){e.addEventListener(t,l,!eo&&no)}function io(e,t,l){e.removeEventListener(t,l,!eo&&no)}function uo(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(l){return!1}return!1}}function co(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function po(e,t,l,o){if(e){l=l||document;do{if(null!=t&&(">"===t[0]?e.parentNode===l&&uo(e,t):uo(e,t))||o&&e===l)return e;if(e===l)break}while(e=co(e))}return null}var fo,mo=/\s+/g;function xo(e,t,l){if(e&&t)if(e.classList)e.classList[l?"add":"remove"](t);else{var o=(" "+e.className+" ").replace(mo," ").replace(" "+t+" "," ");e.className=(o+(l?" "+t:"")).replace(mo," ")}}function vo(e,t,l){var o=e&&e.style;if(o){if(void 0===l)return document.defaultView&&document.defaultView.getComputedStyle?l=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(l=e.currentStyle),void 0===t?l:l[t];t in o||-1!==t.indexOf("webkit")||(t="-webkit-"+t),o[t]=l+("string"==typeof l?"":"px")}}function bo(e,t){var l="";if("string"==typeof e)l=e;else do{var o=vo(e,"transform");o&&"none"!==o&&(l=o+" "+l)}while(!t&&(e=e.parentNode));var a=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return a&&new a(l)}function go(e,t,l){if(e){var o=e.getElementsByTagName(t),a=0,r=o.length;if(l)for(;a<r;a++)l(o[a],a);return o}return[]}function yo(){var e=document.scrollingElement;return e||document.documentElement}function ho(e,t,l,o,a){if(e.getBoundingClientRect||e===window){var r,n,s,i,d,u,c;if(e!==window&&e.parentNode&&e!==yo()?(n=(r=e.getBoundingClientRect()).top,s=r.left,i=r.bottom,d=r.right,u=r.height,c=r.width):(n=0,s=0,i=window.innerHeight,d=window.innerWidth,u=window.innerHeight,c=window.innerWidth),(t||l)&&e!==window&&(a=a||e.parentNode,!eo))do{if(a&&a.getBoundingClientRect&&("none"!==vo(a,"transform")||l&&"static"!==vo(a,"position"))){var p=a.getBoundingClientRect();n-=p.top+parseInt(vo(a,"border-top-width")),s-=p.left+parseInt(vo(a,"border-left-width")),i=n+r.height,d=s+r.width;break}}while(a=a.parentNode);if(o&&e!==window){var f=bo(a||e),m=f&&f.a,x=f&&f.d;f&&(i=(n/=x)+(u/=x),d=(s/=m)+(c/=m))}return{top:n,left:s,bottom:i,right:d,width:c,height:u}}}function _o(e,t,l){for(var o=Ro(e,!0),a=ho(e)[t];o;){var r=ho(o)[l];if(!("top"===l||"left"===l?a>=r:a<=r))return o;if(o===yo())break;o=Ro(o,!1)}return!1}function ko(e,t,l,o){for(var a=0,r=0,n=e.children;r<n.length;){if("none"!==n[r].style.display&&n[r]!==Ta.ghost&&(o||n[r]!==Ta.dragged)&&po(n[r],l.draggable,e,!1)){if(a===t)return n[r];a++}r++}return null}function wo(e,t){for(var l=e.lastElementChild;l&&(l===Ta.ghost||"none"===vo(l,"display")||t&&!uo(l,t));)l=l.previousElementSibling;return l||null}function Co(e,t){var l=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)"TEMPLATE"===e.nodeName.toUpperCase()||e===Ta.clone||t&&!uo(e,t)||l++;return l}function So(e){var t=0,l=0,o=yo();if(e)do{var a=bo(e),r=a.a,n=a.d;t+=e.scrollLeft*r,l+=e.scrollTop*n}while(e!==o&&(e=e.parentNode));return[t,l]}function Ro(e,t){if(!e||!e.getBoundingClientRect)return yo();var l=e,o=!1;do{if(l.clientWidth<l.scrollWidth||l.clientHeight<l.scrollHeight){var a=vo(l);if(l.clientWidth<l.scrollWidth&&("auto"==a.overflowX||"scroll"==a.overflowX)||l.clientHeight<l.scrollHeight&&("auto"==a.overflowY||"scroll"==a.overflowY)){if(!l.getBoundingClientRect||l===document.body)return yo();if(o||t)return l;o=!0}}}while(l=l.parentNode);return yo()}function Bo(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function zo(e,t){return function(){if(!fo){var l=arguments;1===l.length?e.call(this,l[0]):e.apply(this,l),fo=setTimeout((function(){fo=void 0}),t)}}}function Fo(e,t,l){e.scrollLeft+=t,e.scrollTop+=l}function To(e){var t=window.Polymer,l=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):l?l(e).clone(!0)[0]:e.cloneNode(!0)}function Eo(e,t,l){var o={};return Array.from(e.children).forEach((function(a){var r,n,s,i;if(po(a,t.draggable,e,!1)&&!a.animated&&a!==l){var d=ho(a);o.left=Math.min(null!==(r=o.left)&&void 0!==r?r:1/0,d.left),o.top=Math.min(null!==(n=o.top)&&void 0!==n?n:1/0,d.top),o.right=Math.max(null!==(s=o.right)&&void 0!==s?s:-1/0,d.right),o.bottom=Math.max(null!==(i=o.bottom)&&void 0!==i?i:-1/0,d.bottom)}})),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var Do="Sortable"+(new Date).getTime();function $o(){var e,t=[];return{captureAnimationState:function(){(t=[],this.options.animation)&&[].slice.call(this.el.children).forEach((function(e){if("none"!==vo(e,"display")&&e!==Ta.ghost){t.push({target:e,rect:ho(e)});var l=Gl({},t[t.length-1].rect);if(e.thisAnimationDuration){var o=bo(e,!0);o&&(l.top-=o.f,l.left-=o.e)}e.fromRect=l}}))},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(e,t){for(var l in e)if(e.hasOwnProperty(l))for(var o in t)if(t.hasOwnProperty(o)&&t[o]===e[l][o])return Number(l);return-1}(t,{target:e}),1)},animateAll:function(l){var o=this;if(!this.options.animation)return clearTimeout(e),void("function"==typeof l&&l());var a=!1,r=0;t.forEach((function(e){var t=0,l=e.target,n=l.fromRect,s=ho(l),i=l.prevFromRect,d=l.prevToRect,u=e.rect,c=bo(l,!0);c&&(s.top-=c.f,s.left-=c.e),l.toRect=s,l.thisAnimationDuration&&Bo(i,s)&&!Bo(n,s)&&(u.top-s.top)/(u.left-s.left)==(n.top-s.top)/(n.left-s.left)&&(t=function(e,t,l,o){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-l.top,2)+Math.pow(t.left-l.left,2))*o.animation}(u,i,d,o.options)),Bo(s,n)||(l.prevFromRect=n,l.prevToRect=s,t||(t=o.options.animation),o.animate(l,u,s,t)),t&&(a=!0,r=Math.max(r,t),clearTimeout(l.animationResetTimer),l.animationResetTimer=setTimeout((function(){l.animationTime=0,l.prevFromRect=null,l.fromRect=null,l.prevToRect=null,l.thisAnimationDuration=null}),t),l.thisAnimationDuration=t)})),clearTimeout(e),a?e=setTimeout((function(){"function"==typeof l&&l()}),r):"function"==typeof l&&l(),t=[]},animate:function(e,t,l,o){if(o){vo(e,"transition",""),vo(e,"transform","");var a=bo(this.el),r=a&&a.a,n=a&&a.d,s=(t.left-l.left)/(r||1),i=(t.top-l.top)/(n||1);e.animatingX=!!s,e.animatingY=!!i,vo(e,"transform","translate3d("+s+"px,"+i+"px,0)"),this.forRepaintDummy=function(e){return e.offsetWidth}(e),vo(e,"transition","transform "+o+"ms"+(this.options.easing?" "+this.options.easing:"")),vo(e,"transform","translate3d(0,0,0)"),"number"==typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout((function(){vo(e,"transition",""),vo(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1}),o)}}}}var Io=[],Mo={initializeByDefault:!0},Ao={mount:function(e){for(var t in Mo)Mo.hasOwnProperty(t)&&!(t in e)&&(e[t]=Mo[t]);Io.forEach((function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")})),Io.push(e)},pluginEvent:function(e,t,l){var o=this;this.eventCanceled=!1,l.cancel=function(){o.eventCanceled=!0};var a=e+"Global";Io.forEach((function(o){t[o.pluginName]&&(t[o.pluginName][a]&&t[o.pluginName][a](Gl({sortable:t},l)),t.options[o.pluginName]&&t[o.pluginName][e]&&t[o.pluginName][e](Gl({sortable:t},l)))}))},initializePlugins:function(e,t,l,o){for(var a in Io.forEach((function(o){var a=o.pluginName;if(e.options[a]||o.initializeByDefault){var r=new o(e,t,e.options);r.sortable=e,r.options=e.options,e[a]=r,Ql(l,r.defaults)}})),e.options)if(e.options.hasOwnProperty(a)){var r=this.modifyOption(e,a,e.options[a]);void 0!==r&&(e.options[a]=r)}},getEventProperties:function(e,t){var l={};return Io.forEach((function(o){"function"==typeof o.eventProperties&&Ql(l,o.eventProperties.call(t[o.pluginName],e))})),l},modifyOption:function(e,t,l){var o;return Io.forEach((function(a){e[a.pluginName]&&a.optionListeners&&"function"==typeof a.optionListeners[t]&&(o=a.optionListeners[t].call(e[a.pluginName],l))})),o}};var jo=["evt"],No=function(e,t){var l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=l.evt,a=Kl(l,jo);Ao.pluginEvent.bind(Ta)(e,t,Gl({dragEl:Uo,parentEl:Yo,ghostEl:Po,rootEl:Vo,nextEl:Ho,lastDownEl:Lo,cloneEl:Oo,cloneHidden:qo,dragStarted:na,putSortable:Jo,activeSortable:Ta.active,originalEvent:o,oldIndex:Go,oldDraggableIndex:Zo,newIndex:Xo,newDraggableIndex:Qo,hideGhostForTarget:Ra,unhideGhostForTarget:Ba,cloneNowHidden:function(){qo=!0},cloneNowShown:function(){qo=!1},dispatchSortableEvent:function(e){Wo({sortable:t,name:e,originalEvent:o})}},a))};function Wo(e){!function(e){var t=e.sortable,l=e.rootEl,o=e.name,a=e.targetEl,r=e.cloneEl,n=e.toEl,s=e.fromEl,i=e.oldIndex,d=e.newIndex,u=e.oldDraggableIndex,c=e.newDraggableIndex,p=e.originalEvent,f=e.putSortable,m=e.extraEventProperties;if(t=t||l&&l[Do]){var x,v=t.options,b="on"+o.charAt(0).toUpperCase()+o.substr(1);!window.CustomEvent||eo||to?(x=document.createEvent("Event")).initEvent(o,!0,!0):x=new CustomEvent(o,{bubbles:!0,cancelable:!0}),x.to=n||l,x.from=s||l,x.item=a||l,x.clone=r,x.oldIndex=i,x.newIndex=d,x.oldDraggableIndex=u,x.newDraggableIndex=c,x.originalEvent=p,x.pullMode=f?f.lastPutMode:void 0;var g=Gl(Gl({},m),Ao.getEventProperties(o,t));for(var y in g)x[y]=g[y];l&&l.dispatchEvent(x),v[b]&&v[b].call(t,x)}}(Gl({putSortable:Jo,cloneEl:Oo,targetEl:Uo,rootEl:Vo,oldIndex:Go,oldDraggableIndex:Zo,newIndex:Xo,newDraggableIndex:Qo},e))}var Uo,Yo,Po,Vo,Ho,Lo,Oo,qo,Go,Xo,Zo,Qo,Ko,Jo,ea,ta,la,oa,aa,ra,na,sa,ia,da,ua,ca=!1,pa=!1,fa=[],ma=!1,xa=!1,va=[],ba=!1,ga=[],ya="undefined"!=typeof document,ha=ao,_a=to||eo?"cssFloat":"float",ka=ya&&!ro&&!ao&&"draggable"in document.createElement("div"),wa=function(){if(ya){if(eo)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),Ca=function(e,t){var l=vo(e),o=parseInt(l.width)-parseInt(l.paddingLeft)-parseInt(l.paddingRight)-parseInt(l.borderLeftWidth)-parseInt(l.borderRightWidth),a=ko(e,0,t),r=ko(e,1,t),n=a&&vo(a),s=r&&vo(r),i=n&&parseInt(n.marginLeft)+parseInt(n.marginRight)+ho(a).width,d=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+ho(r).width;if("flex"===l.display)return"column"===l.flexDirection||"column-reverse"===l.flexDirection?"vertical":"horizontal";if("grid"===l.display)return l.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(a&&n.float&&"none"!==n.float){var u="left"===n.float?"left":"right";return!r||"both"!==s.clear&&s.clear!==u?"horizontal":"vertical"}return a&&("block"===n.display||"flex"===n.display||"table"===n.display||"grid"===n.display||i>=o&&"none"===l[_a]||r&&"none"===l[_a]&&i+d>o)?"vertical":"horizontal"},Sa=function(e){function t(e,l){return function(o,a,r,n){var s=o.options.group.name&&a.options.group.name&&o.options.group.name===a.options.group.name;if(null==e&&(l||s))return!0;if(null==e||!1===e)return!1;if(l&&"clone"===e)return e;if("function"==typeof e)return t(e(o,a,r,n),l)(o,a,r,n);var i=(l?o:a).options.group.name;return!0===e||"string"==typeof e&&e===i||e.join&&e.indexOf(i)>-1}}var l={},o=e.group;o&&"object"==Xl(o)||(o={name:o}),l.name=o.name,l.checkPull=t(o.pull,!0),l.checkPut=t(o.put),l.revertClone=o.revertClone,e.group=l},Ra=function(){!wa&&Po&&vo(Po,"display","none")},Ba=function(){!wa&&Po&&vo(Po,"display","")};ya&&!ro&&document.addEventListener("click",(function(e){if(pa)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),pa=!1,!1}),!0);var za=function(e){if(Uo){e=e.touches?e.touches[0]:e;var t=(a=e.clientX,r=e.clientY,fa.some((function(e){var t=e[Do].options.emptyInsertThreshold;if(t&&!wo(e)){var l=ho(e),o=a>=l.left-t&&a<=l.right+t,s=r>=l.top-t&&r<=l.bottom+t;return o&&s?n=e:void 0}})),n);if(t){var l={};for(var o in e)e.hasOwnProperty(o)&&(l[o]=e[o]);l.target=l.rootEl=t,l.preventDefault=void 0,l.stopPropagation=void 0,t[Do]._onDragOver(l)}}var a,r,n},Fa=function(e){Uo&&Uo.parentNode[Do]._isOutsideThisEl(e.target)};function Ta(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=Ql({},t),e[Do]=this;var l={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Ca(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Ta.supportPointer&&"PointerEvent"in window&&!oo,emptyInsertThreshold:5};for(var o in Ao.initializePlugins(this,e,l),l)!(o in t)&&(t[o]=l[o]);for(var a in Sa(t),this)"_"===a.charAt(0)&&"function"==typeof this[a]&&(this[a]=this[a].bind(this));this.nativeDraggable=!t.forceFallback&&ka,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?so(e,"pointerdown",this._onTapStart):(so(e,"mousedown",this._onTapStart),so(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(so(e,"dragover",this),so(e,"dragenter",this)),fa.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),Ql(this,$o())}function Ea(e,t,l,o,a,r,n,s){var i,d,u=e[Do],c=u.options.onMove;return!window.CustomEvent||eo||to?(i=document.createEvent("Event")).initEvent("move",!0,!0):i=new CustomEvent("move",{bubbles:!0,cancelable:!0}),i.to=t,i.from=e,i.dragged=l,i.draggedRect=o,i.related=a||t,i.relatedRect=r||ho(t),i.willInsertAfter=s,i.originalEvent=n,e.dispatchEvent(i),c&&(d=c.call(u,i,n)),d}function Da(e){e.draggable=!1}function $a(){ba=!1}function Ia(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,l=t.length,o=0;l--;)o+=t.charCodeAt(l);return o.toString(36)}function Ma(e){return setTimeout(e,0)}function Aa(e){return clearTimeout(e)}Ta.prototype={constructor:Ta,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||(sa=null)},_getDirection:function(e,t){return"function"==typeof this.options.direction?this.options.direction.call(this,e,t,Uo):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,l=this.el,o=this.options,a=o.preventOnFilter,r=e.type,n=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,s=(n||e).target,i=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,d=o.filter;if(function(e){ga.length=0;var t=e.getElementsByTagName("input"),l=t.length;for(;l--;){var o=t[l];o.checked&&ga.push(o)}}(l),!Uo&&!(/mousedown|pointerdown/.test(r)&&0!==e.button||o.disabled)&&!i.isContentEditable&&(this.nativeDraggable||!oo||!s||"SELECT"!==s.tagName.toUpperCase())&&!((s=po(s,o.draggable,l,!1))&&s.animated||Lo===s)){if(Go=Co(s),Zo=Co(s,o.draggable),"function"==typeof d){if(d.call(this,e,s,this))return Wo({sortable:t,rootEl:i,name:"filter",targetEl:s,toEl:l,fromEl:l}),No("filter",t,{evt:e}),void(a&&e.cancelable&&e.preventDefault())}else if(d&&(d=d.split(",").some((function(o){if(o=po(i,o.trim(),l,!1))return Wo({sortable:t,rootEl:o,name:"filter",targetEl:s,fromEl:l,toEl:l}),No("filter",t,{evt:e}),!0}))))return void(a&&e.cancelable&&e.preventDefault());o.handle&&!po(i,o.handle,l,!1)||this._prepareDragStart(e,n,s)}}},_prepareDragStart:function(e,t,l){var o,a=this,r=a.el,n=a.options,s=r.ownerDocument;if(l&&!Uo&&l.parentNode===r){var i=ho(l);if(Vo=r,Yo=(Uo=l).parentNode,Ho=Uo.nextSibling,Lo=l,Ko=n.group,Ta.dragged=Uo,ea={target:Uo,clientX:(t||e).clientX,clientY:(t||e).clientY},aa=ea.clientX-i.left,ra=ea.clientY-i.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,Uo.style["will-change"]="all",o=function(){No("delayEnded",a,{evt:e}),Ta.eventCanceled?a._onDrop():(a._disableDelayedDragEvents(),!lo&&a.nativeDraggable&&(Uo.draggable=!0),a._triggerDragStart(e,t),Wo({sortable:a,name:"choose",originalEvent:e}),xo(Uo,n.chosenClass,!0))},n.ignore.split(",").forEach((function(e){go(Uo,e.trim(),Da)})),so(s,"dragover",za),so(s,"mousemove",za),so(s,"touchmove",za),so(s,"mouseup",a._onDrop),so(s,"touchend",a._onDrop),so(s,"touchcancel",a._onDrop),lo&&this.nativeDraggable&&(this.options.touchStartThreshold=4,Uo.draggable=!0),No("delayStart",this,{evt:e}),!n.delay||n.delayOnTouchOnly&&!t||this.nativeDraggable&&(to||eo))o();else{if(Ta.eventCanceled)return void this._onDrop();so(s,"mouseup",a._disableDelayedDrag),so(s,"touchend",a._disableDelayedDrag),so(s,"touchcancel",a._disableDelayedDrag),so(s,"mousemove",a._delayedDragTouchMoveHandler),so(s,"touchmove",a._delayedDragTouchMoveHandler),n.supportPointer&&so(s,"pointermove",a._delayedDragTouchMoveHandler),a._dragStartTimer=setTimeout(o,n.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){Uo&&Da(Uo),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;io(e,"mouseup",this._disableDelayedDrag),io(e,"touchend",this._disableDelayedDrag),io(e,"touchcancel",this._disableDelayedDrag),io(e,"mousemove",this._delayedDragTouchMoveHandler),io(e,"touchmove",this._delayedDragTouchMoveHandler),io(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?so(document,"pointermove",this._onTouchMove):so(document,t?"touchmove":"mousemove",this._onTouchMove):(so(Uo,"dragend",this),so(Vo,"dragstart",this._onDragStart));try{document.selection?Ma((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(l){}},_dragStarted:function(e,t){if(ca=!1,Vo&&Uo){No("dragStarted",this,{evt:t}),this.nativeDraggable&&so(document,"dragover",Fa);var l=this.options;!e&&xo(Uo,l.dragClass,!1),xo(Uo,l.ghostClass,!0),Ta.active=this,e&&this._appendGhost(),Wo({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(ta){this._lastX=ta.clientX,this._lastY=ta.clientY,Ra();for(var e=document.elementFromPoint(ta.clientX,ta.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(ta.clientX,ta.clientY))!==t;)t=e;if(Uo.parentNode[Do]._isOutsideThisEl(e),t)do{if(t[Do]){if(t[Do]._onDragOver({clientX:ta.clientX,clientY:ta.clientY,target:e,rootEl:t})&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);Ba()}},_onTouchMove:function(e){if(ea){var t=this.options,l=t.fallbackTolerance,o=t.fallbackOffset,a=e.touches?e.touches[0]:e,r=Po&&bo(Po,!0),n=Po&&r&&r.a,s=Po&&r&&r.d,i=ha&&ua&&So(ua),d=(a.clientX-ea.clientX+o.x)/(n||1)+(i?i[0]-va[0]:0)/(n||1),u=(a.clientY-ea.clientY+o.y)/(s||1)+(i?i[1]-va[1]:0)/(s||1);if(!Ta.active&&!ca){if(l&&Math.max(Math.abs(a.clientX-this._lastX),Math.abs(a.clientY-this._lastY))<l)return;this._onDragStart(e,!0)}if(Po){r?(r.e+=d-(la||0),r.f+=u-(oa||0)):r={a:1,b:0,c:0,d:1,e:d,f:u};var c="matrix(".concat(r.a,",").concat(r.b,",").concat(r.c,",").concat(r.d,",").concat(r.e,",").concat(r.f,")");vo(Po,"webkitTransform",c),vo(Po,"mozTransform",c),vo(Po,"msTransform",c),vo(Po,"transform",c),la=d,oa=u,ta=a}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!Po){var e=this.options.fallbackOnBody?document.body:Vo,t=ho(Uo,!0,ha,!0,e),l=this.options;if(ha){for(ua=e;"static"===vo(ua,"position")&&"none"===vo(ua,"transform")&&ua!==document;)ua=ua.parentNode;ua!==document.body&&ua!==document.documentElement?(ua===document&&(ua=yo()),t.top+=ua.scrollTop,t.left+=ua.scrollLeft):ua=yo(),va=So(ua)}xo(Po=Uo.cloneNode(!0),l.ghostClass,!1),xo(Po,l.fallbackClass,!0),xo(Po,l.dragClass,!0),vo(Po,"transition",""),vo(Po,"transform",""),vo(Po,"box-sizing","border-box"),vo(Po,"margin",0),vo(Po,"top",t.top),vo(Po,"left",t.left),vo(Po,"width",t.width),vo(Po,"height",t.height),vo(Po,"opacity","0.8"),vo(Po,"position",ha?"absolute":"fixed"),vo(Po,"zIndex","100000"),vo(Po,"pointerEvents","none"),Ta.ghost=Po,e.appendChild(Po),vo(Po,"transform-origin",aa/parseInt(Po.style.width)*100+"% "+ra/parseInt(Po.style.height)*100+"%")}},_onDragStart:function(e,t){var l=this,o=e.dataTransfer,a=l.options;No("dragStart",this,{evt:e}),Ta.eventCanceled?this._onDrop():(No("setupClone",this),Ta.eventCanceled||((Oo=To(Uo)).removeAttribute("id"),Oo.draggable=!1,Oo.style["will-change"]="",this._hideClone(),xo(Oo,this.options.chosenClass,!1),Ta.clone=Oo),l.cloneId=Ma((function(){No("clone",l),Ta.eventCanceled||(l.options.removeCloneOnHide||Vo.insertBefore(Oo,Uo),l._hideClone(),Wo({sortable:l,name:"clone"}))})),!t&&xo(Uo,a.dragClass,!0),t?(pa=!0,l._loopId=setInterval(l._emulateDragOver,50)):(io(document,"mouseup",l._onDrop),io(document,"touchend",l._onDrop),io(document,"touchcancel",l._onDrop),o&&(o.effectAllowed="move",a.setData&&a.setData.call(l,o,Uo)),so(document,"drop",l),vo(Uo,"transform","translateZ(0)")),ca=!0,l._dragStartId=Ma(l._dragStarted.bind(l,t,e)),so(document,"selectstart",l),na=!0,oo&&vo(document.body,"user-select","none"))},_onDragOver:function(e){var t,l,o,a,r=this.el,n=e.target,s=this.options,i=s.group,d=Ta.active,u=Ko===i,c=s.sort,p=Jo||d,f=this,m=!1;if(!ba){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),n=po(n,s.draggable,r,!0),F("dragOver"),Ta.eventCanceled)return m;if(Uo.contains(e.target)||n.animated&&n.animatingX&&n.animatingY||f._ignoreWhileAnimating===n)return E(!1);if(pa=!1,d&&!s.disabled&&(u?c||(o=Yo!==Vo):Jo===this||(this.lastPutMode=Ko.checkPull(this,d,Uo,e))&&i.checkPut(this,d,Uo,e))){if(a="vertical"===this._getDirection(e,n),t=ho(Uo),F("dragOverValid"),Ta.eventCanceled)return m;if(o)return Yo=Vo,T(),this._hideClone(),F("revert"),Ta.eventCanceled||(Ho?Vo.insertBefore(Uo,Ho):Vo.appendChild(Uo)),E(!0);var x=wo(r,s.draggable);if(!x||function(e,t,l){var o=ho(wo(l.el,l.options.draggable)),a=Eo(l.el,l.options,Po),r=10;return t?e.clientX>a.right+r||e.clientY>o.bottom&&e.clientX>o.left:e.clientY>a.bottom+r||e.clientX>o.right&&e.clientY>o.top}(e,a,this)&&!x.animated){if(x===Uo)return E(!1);if(x&&r===e.target&&(n=x),n&&(l=ho(n)),!1!==Ea(Vo,r,Uo,t,n,l,e,!!n))return T(),x&&x.nextSibling?r.insertBefore(Uo,x.nextSibling):r.appendChild(Uo),Yo=r,D(),E(!0)}else if(x&&function(e,t,l){var o=ho(ko(l.el,0,l.options,!0)),a=Eo(l.el,l.options,Po),r=10;return t?e.clientX<a.left-r||e.clientY<o.top&&e.clientX<o.right:e.clientY<a.top-r||e.clientY<o.bottom&&e.clientX<o.left}(e,a,this)){var v=ko(r,0,s,!0);if(v===Uo)return E(!1);if(l=ho(n=v),!1!==Ea(Vo,r,Uo,t,n,l,e,!1))return T(),r.insertBefore(Uo,v),Yo=r,D(),E(!0)}else if(n.parentNode===r){l=ho(n);var b,g,y,h=Uo.parentNode!==r,_=!function(e,t,l){var o=l?e.left:e.top,a=l?e.right:e.bottom,r=l?e.width:e.height,n=l?t.left:t.top,s=l?t.right:t.bottom,i=l?t.width:t.height;return o===n||a===s||o+r/2===n+i/2}(Uo.animated&&Uo.toRect||t,n.animated&&n.toRect||l,a),k=a?"top":"left",w=_o(n,"top","top")||_o(Uo,"top","top"),C=w?w.scrollTop:void 0;if(sa!==n&&(g=l[k],ma=!1,xa=!_&&s.invertSwap||h),b=function(e,t,l,o,a,r,n,s){var i=o?e.clientY:e.clientX,d=o?l.height:l.width,u=o?l.top:l.left,c=o?l.bottom:l.right,p=!1;if(!n)if(s&&da<d*a){if(!ma&&(1===ia?i>u+d*r/2:i<c-d*r/2)&&(ma=!0),ma)p=!0;else if(1===ia?i<u+da:i>c-da)return-ia}else if(i>u+d*(1-a)/2&&i<c-d*(1-a)/2)return function(e){return Co(Uo)<Co(e)?1:-1}(t);if((p=p||n)&&(i<u+d*r/2||i>c-d*r/2))return i>u+d/2?1:-1;return 0}(e,n,l,a,_?1:s.swapThreshold,null==s.invertedSwapThreshold?s.swapThreshold:s.invertedSwapThreshold,xa,sa===n),0!==b){var S=Co(Uo);do{S-=b,y=Yo.children[S]}while(y&&("none"===vo(y,"display")||y===Po))}if(0===b||y===n)return E(!1);sa=n,ia=b;var R=n.nextElementSibling,B=!1,z=Ea(Vo,r,Uo,t,n,l,e,B=1===b);if(!1!==z)return 1!==z&&-1!==z||(B=1===z),ba=!0,setTimeout($a,30),T(),B&&!R?r.appendChild(Uo):n.parentNode.insertBefore(Uo,B?R:n),w&&Fo(w,0,C-w.scrollTop),Yo=Uo.parentNode,void 0===g||xa||(da=Math.abs(g-ho(n)[k])),D(),E(!0)}if(r.contains(Uo))return E(!1)}return!1}function F(s,i){No(s,f,Gl({evt:e,isOwner:u,axis:a?"vertical":"horizontal",revert:o,dragRect:t,targetRect:l,canSort:c,fromSortable:p,target:n,completed:E,onMove:function(l,o){return Ea(Vo,r,Uo,t,l,ho(l),e,o)},changed:D},i))}function T(){F("dragOverAnimationCapture"),f.captureAnimationState(),f!==p&&p.captureAnimationState()}function E(t){return F("dragOverCompleted",{insertion:t}),t&&(u?d._hideClone():d._showClone(f),f!==p&&(xo(Uo,Jo?Jo.options.ghostClass:d.options.ghostClass,!1),xo(Uo,s.ghostClass,!0)),Jo!==f&&f!==Ta.active?Jo=f:f===Ta.active&&Jo&&(Jo=null),p===f&&(f._ignoreWhileAnimating=n),f.animateAll((function(){F("dragOverAnimationComplete"),f._ignoreWhileAnimating=null})),f!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(n===Uo&&!Uo.animated||n===r&&!n.animated)&&(sa=null),s.dragoverBubble||e.rootEl||n===document||(Uo.parentNode[Do]._isOutsideThisEl(e.target),!t&&za(e)),!s.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),m=!0}function D(){Xo=Co(Uo),Qo=Co(Uo,s.draggable),Wo({sortable:f,name:"change",toEl:r,newIndex:Xo,newDraggableIndex:Qo,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){io(document,"mousemove",this._onTouchMove),io(document,"touchmove",this._onTouchMove),io(document,"pointermove",this._onTouchMove),io(document,"dragover",za),io(document,"mousemove",za),io(document,"touchmove",za)},_offUpEvents:function(){var e=this.el.ownerDocument;io(e,"mouseup",this._onDrop),io(e,"touchend",this._onDrop),io(e,"pointerup",this._onDrop),io(e,"touchcancel",this._onDrop),io(document,"selectstart",this)},_onDrop:function(e){var t=this.el,l=this.options;Xo=Co(Uo),Qo=Co(Uo,l.draggable),No("drop",this,{evt:e}),Yo=Uo&&Uo.parentNode,Xo=Co(Uo),Qo=Co(Uo,l.draggable),Ta.eventCanceled||(ca=!1,xa=!1,ma=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Aa(this.cloneId),Aa(this._dragStartId),this.nativeDraggable&&(io(document,"drop",this),io(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),oo&&vo(document.body,"user-select",""),vo(Uo,"transform",""),e&&(na&&(e.cancelable&&e.preventDefault(),!l.dropBubble&&e.stopPropagation()),Po&&Po.parentNode&&Po.parentNode.removeChild(Po),(Vo===Yo||Jo&&"clone"!==Jo.lastPutMode)&&Oo&&Oo.parentNode&&Oo.parentNode.removeChild(Oo),Uo&&(this.nativeDraggable&&io(Uo,"dragend",this),Da(Uo),Uo.style["will-change"]="",na&&!ca&&xo(Uo,Jo?Jo.options.ghostClass:this.options.ghostClass,!1),xo(Uo,this.options.chosenClass,!1),Wo({sortable:this,name:"unchoose",toEl:Yo,newIndex:null,newDraggableIndex:null,originalEvent:e}),Vo!==Yo?(Xo>=0&&(Wo({rootEl:Yo,name:"add",toEl:Yo,fromEl:Vo,originalEvent:e}),Wo({sortable:this,name:"remove",toEl:Yo,originalEvent:e}),Wo({rootEl:Yo,name:"sort",toEl:Yo,fromEl:Vo,originalEvent:e}),Wo({sortable:this,name:"sort",toEl:Yo,originalEvent:e})),Jo&&Jo.save()):Xo!==Go&&Xo>=0&&(Wo({sortable:this,name:"update",toEl:Yo,originalEvent:e}),Wo({sortable:this,name:"sort",toEl:Yo,originalEvent:e})),Ta.active&&(null!=Xo&&-1!==Xo||(Xo=Go,Qo=Zo),Wo({sortable:this,name:"end",toEl:Yo,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){No("nulling",this),Vo=Uo=Yo=Po=Ho=Oo=Lo=qo=ea=ta=na=Xo=Qo=Go=Zo=sa=ia=Jo=Ko=Ta.dragged=Ta.ghost=Ta.clone=Ta.active=null,ga.forEach((function(e){e.checked=!0})),ga.length=la=oa=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":Uo&&(this._onDragOver(e),function(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move");e.cancelable&&e.preventDefault()}(e));break;case"selectstart":e.preventDefault()}},toArray:function(){for(var e,t=[],l=this.el.children,o=0,a=l.length,r=this.options;o<a;o++)po(e=l[o],r.draggable,this.el,!1)&&t.push(e.getAttribute(r.dataIdAttr)||Ia(e));return t},sort:function(e,t){var l={},o=this.el;this.toArray().forEach((function(e,t){var a=o.children[t];po(a,this.options.draggable,o,!1)&&(l[e]=a)}),this),t&&this.captureAnimationState(),e.forEach((function(e){l[e]&&(o.removeChild(l[e]),o.appendChild(l[e]))})),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return po(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var l=this.options;if(void 0===t)return l[e];var o=Ao.modifyOption(this,e,t);l[e]=void 0!==o?o:t,"group"===e&&Sa(l)},destroy:function(){No("destroy",this);var e=this.el;e[Do]=null,io(e,"mousedown",this._onTapStart),io(e,"touchstart",this._onTapStart),io(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(io(e,"dragover",this),io(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),(function(e){e.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),fa.splice(fa.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!qo){if(No("hideClone",this),Ta.eventCanceled)return;vo(Oo,"display","none"),this.options.removeCloneOnHide&&Oo.parentNode&&Oo.parentNode.removeChild(Oo),qo=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(qo){if(No("showClone",this),Ta.eventCanceled)return;Uo.parentNode!=Vo||this.options.group.revertClone?Ho?Vo.insertBefore(Oo,Ho):Vo.appendChild(Oo):Vo.insertBefore(Oo,Uo),this.options.group.revertClone&&this.animate(Uo,Oo),vo(Oo,"display",""),qo=!1}}else this._hideClone()}},ya&&so(document,"touchmove",(function(e){(Ta.active||ca)&&e.cancelable&&e.preventDefault()})),Ta.utils={on:so,off:io,css:vo,find:go,is:function(e,t){return!!po(e,t,e,!1)},extend:function(e,t){if(e&&t)for(var l in t)t.hasOwnProperty(l)&&(e[l]=t[l]);return e},throttle:zo,closest:po,toggleClass:xo,clone:To,index:Co,nextTick:Ma,cancelNextTick:Aa,detectDirection:Ca,getChild:ko},Ta.get=function(e){return e[Do]},Ta.mount=function(){for(var e=arguments.length,t=new Array(e),l=0;l<e;l++)t[l]=arguments[l];t[0].constructor===Array&&(t=t[0]),t.forEach((function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(Ta.utils=Gl(Gl({},Ta.utils),e.utils)),Ao.mount(e)}))},Ta.create=function(e,t){return new Ta(e,t)},Ta.version="1.15.2";var ja,Na,Wa,Ua,Ya,Pa,Va=[],Ha=!1;function La(){Va.forEach((function(e){clearInterval(e.pid)})),Va=[]}function Oa(){clearInterval(Pa)}var qa=zo((function(e,t,l,o){if(t.scroll){var a,r=(e.touches?e.touches[0]:e).clientX,n=(e.touches?e.touches[0]:e).clientY,s=t.scrollSensitivity,i=t.scrollSpeed,d=yo(),u=!1;Na!==l&&(Na=l,La(),ja=t.scroll,a=t.scrollFn,!0===ja&&(ja=Ro(l,!0)));var c=0,p=ja;do{var f=p,m=ho(f),x=m.top,v=m.bottom,b=m.left,g=m.right,y=m.width,h=m.height,_=void 0,k=void 0,w=f.scrollWidth,C=f.scrollHeight,S=vo(f),R=f.scrollLeft,B=f.scrollTop;f===d?(_=y<w&&("auto"===S.overflowX||"scroll"===S.overflowX||"visible"===S.overflowX),k=h<C&&("auto"===S.overflowY||"scroll"===S.overflowY||"visible"===S.overflowY)):(_=y<w&&("auto"===S.overflowX||"scroll"===S.overflowX),k=h<C&&("auto"===S.overflowY||"scroll"===S.overflowY));var z=_&&(Math.abs(g-r)<=s&&R+y<w)-(Math.abs(b-r)<=s&&!!R),F=k&&(Math.abs(v-n)<=s&&B+h<C)-(Math.abs(x-n)<=s&&!!B);if(!Va[c])for(var T=0;T<=c;T++)Va[T]||(Va[T]={});Va[c].vx==z&&Va[c].vy==F&&Va[c].el===f||(Va[c].el=f,Va[c].vx=z,Va[c].vy=F,clearInterval(Va[c].pid),0==z&&0==F||(u=!0,Va[c].pid=setInterval(function(){o&&0===this.layer&&Ta.active._onTouchMove(Ya);var t=Va[this.layer].vy?Va[this.layer].vy*i:0,l=Va[this.layer].vx?Va[this.layer].vx*i:0;"function"==typeof a&&"continue"!==a.call(Ta.dragged.parentNode[Do],l,t,e,Ya,Va[this.layer].el)||Fo(Va[this.layer].el,l,t)}.bind({layer:c}),24))),c++}while(t.bubbleScroll&&p!==d&&(p=Ro(p,!1)));Ha=u}}),30),Ga=function(e){var t=e.originalEvent,l=e.putSortable,o=e.dragEl,a=e.activeSortable,r=e.dispatchSortableEvent,n=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(t){var i=l||a;n();var d=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,u=document.elementFromPoint(d.clientX,d.clientY);s(),i&&!i.el.contains(u)&&(r("spill"),this.onSpill({dragEl:o,putSortable:l}))}};function Xa(){}function Za(){}function Qa(e={}){let t="";const l=u({}),o=s(),a=u(["fixed","top_fixed","right_fixed","bottom_fixed","left_fixed"]),r=i((()=>"decorate"==o.mode?o:e.data)),n=u(!1);We((()=>{n.value=!1})),Ue((()=>{n.value=!0}));return{scrollV:u().value,data:r.value,componentsScrollBool:l.value,placeholderEvent:()=>{},refresh:()=>{f((()=>{let t=null,l=()=>{let l=!1;try{e.getFormRef(),l=!0}catch(n){l=!1}var a;return l&&(o.componentRefs=e.getFormRef().componentRefs,r.value.componentRefs=e.getFormRef().componentRefs,null==(a=e.getFormRef().componentRefs.topTabbarRef)||a.refresh(),t&&clearInterval(t)),l};l()||(t=setInterval((()=>{l()}),100))}))},isShowPlaceHolder:(e,t)=>{if("decorate"==o.mode){let l=document.getElementById("componentList");if(l&&l.children.length&&l.children[e]){let o=l.children[e].offsetHeight,a=0;if(t.margin.top<0&&(a=2*t.margin.top*-1,a>o))return!1}return!0}return!1},getComponentClass:(e,t)=>{let l={relative:!0,selected:o.currentIndex==e,decorate:"decorate"==o.mode};return l["top-fixed-"+o.topFixedStatus]=!0,t.position&&-1!=a.value.indexOf(t.position)?l["ignore-draggable-element"]=!0:l["draggable-element"]=!0,"ImageAds"==t.componentName&&(l["overflow-hidden"]=!0),l},onPageScroll:()=>{V((e=>{if(t&&!n.value)for(let o in t)e.scrollTop<=0?l.value[o]=-1:e.scrollTop>t[o]?l.value[o]=1:l.value[o]=2}))},onMounted:()=>{c((()=>{if("decorate"==o.mode){var e=document.getElementById("componentList");const t=Ta.create(e,{draggable:".draggable-element",animation:200,onEnd:e=>{let l=o.value[e.oldIndex];o.value.splice(e.oldIndex,1),o.value.splice(e.newIndex,0,l),f((()=>{t.sort(Ut(o.value.length).map((e=>e.toString()))),o.postMessage(e.newIndex,o.value[e.newIndex])}))}})}f((()=>{setTimeout((()=>{if(t=uni.getStorageSync("componentsScrollValGroup"),t)for(let e in t)l.value[e]=-1}),500)}))}))}}}Xa.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,l=e.putSortable;this.sortable.captureAnimationState(),l&&l.captureAnimationState();var o=ko(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(t,o):this.sortable.el.appendChild(t),this.sortable.animateAll(),l&&l.animateAll()},drop:Ga},Ql(Xa,{pluginName:"revertOnSpill"}),Za.prototype={onSpill:function(e){var t=e.dragEl,l=e.putSortable||this.sortable;l.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),l.animateAll()},drop:Ga},Ql(Za,{pluginName:"removeOnSpill"}),Ta.mount(new function(){function e(){for(var e in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this))}return e.prototype={dragStarted:function(e){var t=e.originalEvent;this.sortable.nativeDraggable?so(document,"dragover",this._handleAutoScroll):this.options.supportPointer?so(document,"pointermove",this._handleFallbackAutoScroll):t.touches?so(document,"touchmove",this._handleFallbackAutoScroll):so(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var t=e.originalEvent;this.options.dragOverBubble||t.rootEl||this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?io(document,"dragover",this._handleAutoScroll):(io(document,"pointermove",this._handleFallbackAutoScroll),io(document,"touchmove",this._handleFallbackAutoScroll),io(document,"mousemove",this._handleFallbackAutoScroll)),Oa(),La(),clearTimeout(fo),fo=void 0},nulling:function(){Ya=Na=ja=Ha=Pa=Wa=Ua=null,Va.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,t){var l=this,o=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,r=document.elementFromPoint(o,a);if(Ya=e,t||this.options.forceAutoScrollFallback||to||eo||oo){qa(e,this.options,r,t);var n=Ro(r,!0);!Ha||Pa&&o===Wa&&a===Ua||(Pa&&Oa(),Pa=setInterval((function(){var r=Ro(document.elementFromPoint(o,a),!0);r!==n&&(n=r,La()),qa(e,l.options,r,t)}),10),Wa=o,Ua=a)}else{if(!this.options.bubbleScroll||Ro(r,!0)===yo())return void La();qa(e,this.options,Ro(r,!1),!1)}}},Ql(e,{pluginName:"scroll",initializeByDefault:!0})}),Ta.mount(Za,Xa);const Ka=Pe(n({__name:"index",props:["data"],setup(e,{expose:t}){const l=e,o=T(),a=()=>({componentRefs:o.refs}),r=s(),n=Qa({...l,getFormRef:a}),i=u(n.data);return n.onMounted(),n.onPageScroll(),t({refresh:n.refresh,getFormRef:a}),(e,t)=>{const l=E,o=x(v("diy-active-cube"),Yt),a=x(v("diy-carousel-search"),Pt),s=x(v("diy-float-btn"),Vt),d=x(v("diy-form-address"),Ht),u=x(v("diy-form-checkbox"),Ot),c=x(v("diy-form-date"),Jt),p=x(v("diy-form-date-scope"),el),f=x(v("diy-form-email"),tl),m=x(v("diy-form-file"),ll),w=x(v("diy-form-identity"),al),C=x(v("diy-form-identity-privacy"),ol),T=x(v("diy-form-image"),rl),D=x(v("diy-form-input"),nl),$=x(v("diy-form-location"),sl),I=x(v("diy-form-mobile"),dl),M=x(v("diy-form-number"),ul),A=x(v("diy-form-privacy"),il),j=x(v("diy-form-privacy-pop"),Lt),W=x(v("diy-form-radio"),cl),U=x(v("diy-form-submit"),pl),Y=x(v("diy-form-table"),fl),P=x(v("diy-form-textarea"),ml),V=x(v("diy-form-time"),xl),H=x(v("diy-form-time-scope"),vl),L=x(v("diy-form-video"),bl),O=x(v("diy-form-wechat-name"),gl),q=x(v("diy-graphic-nav"),yl),G=x(v("diy-horz-blank"),hl),X=x(v("diy-horz-line"),_l),Z=x(v("diy-hot-area"),kl),Q=x(v("diy-image-ads"),wl),K=x(v("diy-member-info"),Cl),J=x(v("diy-member-level"),Sl),ee=x(v("diy-notice"),Rl),te=x(v("diy-picture-show"),Bl),le=x(v("diy-rich-text"),zl),oe=x(v("diy-rubik-cube"),Fl),ae=x(v("diy-text"),Tl),re=x(v("tabbar"),at);return b(),g(l,{class:"diy-group",id:"componentList"},{default:y((()=>[i.value.global&&Object.keys(i.value.global).length&&i.value.global.topStatusBar&&i.value.global.topStatusBar.isShow?(b(),g(Le,{key:0,scrollBool:k(n).componentsScrollBool.TopTabbar,ref:"topTabbarRef",data:i.value.global},null,8,["scrollBool","data"])):S("v-if",!0),(b(!0),R(B,null,z(i.value.value,((e,t)=>(b(),g(l,{key:e.id,onClick:l=>k(r).changeCurrentIndex(t,e),class:F(k(n).getComponentClass(t,e)),style:_(e.pageStyle)},{default:y((()=>[h(l,{class:"relative",style:_({marginTop:e.margin.top<0?2*e.margin.top+"rpx":"0"})},{default:y((()=>[S(" 装修模式下，设置负上边距后超出的内容，禁止选中设置 "),k(n).isShowPlaceHolder(t,e)?(b(),g(l,{key:0,class:"absolute w-full z-1",style:_({height:2*e.margin.top*-1+"rpx"}),onClick:N(k(n).placeholderEvent,["stop"])},null,8,["style","onClick"])):S("v-if",!0),"ActiveCube"==e.componentName?(b(),g(o,{key:1,ref_for:!0,ref:"diyActiveCubeRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ActiveCube},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"CarouselSearch"==e.componentName?(b(),g(a,{key:2,ref_for:!0,ref:"diyCarouselSearchRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.CarouselSearch},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FloatBtn"==e.componentName?(b(),g(s,{key:3,ref_for:!0,ref:"diyFloatBtnRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FloatBtn},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormAddress"==e.componentName?(b(),g(d,{key:4,ref_for:!0,ref:"diyFormAddressRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormAddress},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormCheckbox"==e.componentName?(b(),g(u,{key:5,ref_for:!0,ref:"diyFormCheckboxRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormCheckbox},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormDate"==e.componentName?(b(),g(c,{key:6,ref_for:!0,ref:"diyFormDateRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormDate},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormDateScope"==e.componentName?(b(),g(p,{key:7,ref_for:!0,ref:"diyFormDateScopeRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormDateScope},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormEmail"==e.componentName?(b(),g(f,{key:8,ref_for:!0,ref:"diyFormEmailRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormEmail},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormFile"==e.componentName?(b(),g(m,{key:9,ref_for:!0,ref:"diyFormFileRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormFile},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormIdentity"==e.componentName?(b(),g(w,{key:10,ref_for:!0,ref:"diyFormIdentityRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormIdentity},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormIdentityPrivacy"==e.componentName?(b(),g(C,{key:11,ref_for:!0,ref:"diyFormIdentityPrivacyRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormIdentityPrivacy},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormImage"==e.componentName?(b(),g(T,{key:12,ref_for:!0,ref:"diyFormImageRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormImage},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormInput"==e.componentName?(b(),g(D,{key:13,ref_for:!0,ref:"diyFormInputRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormInput},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormLocation"==e.componentName?(b(),g($,{key:14,ref_for:!0,ref:"diyFormLocationRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormLocation},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormMobile"==e.componentName?(b(),g(I,{key:15,ref_for:!0,ref:"diyFormMobileRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormMobile},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormNumber"==e.componentName?(b(),g(M,{key:16,ref_for:!0,ref:"diyFormNumberRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormNumber},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormPrivacy"==e.componentName?(b(),g(A,{key:17,ref_for:!0,ref:"diyFormPrivacyRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormPrivacy},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormPrivacyPop"==e.componentName?(b(),g(j,{key:18,ref_for:!0,ref:"diyFormPrivacyPopRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormPrivacyPop},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormRadio"==e.componentName?(b(),g(W,{key:19,ref_for:!0,ref:"diyFormRadioRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormRadio},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormSubmit"==e.componentName?(b(),g(U,{key:20,ref_for:!0,ref:"diyFormSubmitRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormSubmit},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormTable"==e.componentName?(b(),g(Y,{key:21,ref_for:!0,ref:"diyFormTableRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormTable},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormTextarea"==e.componentName?(b(),g(P,{key:22,ref_for:!0,ref:"diyFormTextareaRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormTextarea},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormTime"==e.componentName?(b(),g(V,{key:23,ref_for:!0,ref:"diyFormTimeRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormTime},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormTimeScope"==e.componentName?(b(),g(H,{key:24,ref_for:!0,ref:"diyFormTimeScopeRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormTimeScope},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormVideo"==e.componentName?(b(),g(L,{key:25,ref_for:!0,ref:"diyFormVideoRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormVideo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormWechatName"==e.componentName?(b(),g(O,{key:26,ref_for:!0,ref:"diyFormWechatNameRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormWechatName},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"GraphicNav"==e.componentName?(b(),g(q,{key:27,ref_for:!0,ref:"diyGraphicNavRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.GraphicNav},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"HorzBlank"==e.componentName?(b(),g(G,{key:28,ref_for:!0,ref:"diyHorzBlankRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.HorzBlank},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"HorzLine"==e.componentName?(b(),g(X,{key:29,ref_for:!0,ref:"diyHorzLineRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.HorzLine},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"HotArea"==e.componentName?(b(),g(Z,{key:30,ref_for:!0,ref:"diyHotAreaRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.HotArea},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ImageAds"==e.componentName?(b(),g(Q,{key:31,ref_for:!0,ref:"diyImageAdsRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ImageAds},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"MemberInfo"==e.componentName?(b(),g(K,{key:32,ref_for:!0,ref:"diyMemberInfoRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.MemberInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"MemberLevel"==e.componentName?(b(),g(J,{key:33,ref_for:!0,ref:"diyMemberLevelRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.MemberLevel},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"Notice"==e.componentName?(b(),g(ee,{key:34,ref_for:!0,ref:"diyNoticeRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.Notice},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"PictureShow"==e.componentName?(b(),g(te,{key:35,ref_for:!0,ref:"diyPictureShowRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.PictureShow},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"RichText"==e.componentName?(b(),g(le,{key:36,ref_for:!0,ref:"diyRichTextRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.RichText},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"RubikCube"==e.componentName?(b(),g(oe,{key:37,ref_for:!0,ref:"diyRubikCubeRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.RubikCube},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"Text"==e.componentName?(b(),g(ae,{key:38,ref_for:!0,ref:"diyTextRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.Text},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"GoodsCoupon"==e.componentName?(b(),g(El,{key:39,ref_for:!0,ref:"diyGoodsCouponRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.GoodsCoupon},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"GoodsList"==e.componentName?(b(),g(nt,{key:40,ref_for:!0,ref:"diyGoodsListRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.GoodsList},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ManyGoodsList"==e.componentName?(b(),g(Dl,{key:41,ref_for:!0,ref:"diyManyGoodsListRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ManyGoodsList},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopExchangeGoods"==e.componentName?(b(),g($l,{key:42,ref_for:!0,ref:"diyShopExchangeGoodsRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopExchangeGoods},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopExchangeInfo"==e.componentName?(b(),g(Il,{key:43,ref_for:!0,ref:"diyShopExchangeInfoRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopExchangeInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopGoodsRanking"==e.componentName?(b(),g(Ml,{key:44,ref_for:!0,ref:"diyShopGoodsRankingRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopGoodsRanking},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopGoodsRecommend"==e.componentName?(b(),g(Al,{key:45,ref_for:!0,ref:"diyShopGoodsRecommendRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopGoodsRecommend},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopMemberInfo"==e.componentName?(b(),g(jl,{key:46,ref_for:!0,ref:"diyShopMemberInfoRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopMemberInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"YzSheMemberInfo"==e.componentName?(b(),g(Nl,{key:47,ref_for:!0,ref:"diyYzSheMemberInfoRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.YzSheMemberInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"YzSheOrderInfo"==e.componentName?(b(),g(Wl,{key:48,ref_for:!0,ref:"diyYzSheOrderInfoRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.YzSheOrderInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"YzSheRecycleProcess"==e.componentName?(b(),g(Ul,{key:49,ref_for:!0,ref:"diyYzSheRecycleProcessRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.YzSheRecycleProcess},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"YzSheReviews"==e.componentName?(b(),g(Yl,{key:50,ref_for:!0,ref:"diyYzSheReviewsRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.YzSheReviews},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"YzSheFaq"==e.componentName?(b(),g(Pl,{key:51,ref_for:!0,ref:"diyYzSheFaqRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.YzSheFaq},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopNewcomer"==e.componentName?(b(),g(Vl,{key:52,ref_for:!0,ref:"diyShopNewcomerRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopNewcomer},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopOrderInfo"==e.componentName?(b(),g(Hl,{key:53,ref_for:!0,ref:"diyShopOrderInfoRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopOrderInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopSearch"==e.componentName?(b(),g(Ll,{key:54,ref_for:!0,ref:"diyShopSearchRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopSearch},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"SingleRecommend"==e.componentName?(b(),g(Ol,{key:55,ref_for:!0,ref:"diySingleRecommendRef",component:e,global:i.value.global,index:t,scrollBool:k(n).componentsScrollBool.SingleRecommend},null,8,["component","global","index","scrollBool"])):S("v-if",!0)])),_:2},1032,["style"])])),_:2},1032,["onClick","class","style"])))),128)),""==k(r).mode&&i.value.global&&i.value.global.bottomTabBarSwitch?(b(),R(B,{key:1},[h(l,{class:"pt-[20rpx]"}),h(re)],64)):S("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-8114c0b3"]]);export{Ka as d};
