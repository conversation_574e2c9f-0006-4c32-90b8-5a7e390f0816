<template>
    <!-- 内容 -->
    <div class="content-wrap" v-show="diyStore.editTab == 'content'">
        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">标题设置</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="标题">
                    <el-input v-model.trim="diyStore.editComponent.title" placeholder="请输入标题" clearable maxlength="10" show-word-limit />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">更多设置</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="显示更多">
                    <el-switch v-model="diyStore.editComponent.showMore" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]" v-if="diyStore.editComponent.showMore">
                <el-form-item label="更多文字">
                    <el-input v-model.trim="diyStore.editComponent.moreText" placeholder="请输入更多文字" clearable maxlength="8" show-word-limit />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]" v-if="diyStore.editComponent.showMore">
                <el-form-item label="更多链接">
                    <diy-link v-model="diyStore.editComponent.moreLink" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">流程步骤</h3>
            <div v-for="(step, index) in diyStore.editComponent.steps" :key="index" class="mb-[20px] p-[15px] border border-gray-200 rounded">
                <h4 class="mb-[10px]">步骤{{ index + 1 }}</h4>
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="步骤名称">
                        <el-input v-model.trim="step.title" placeholder="请输入步骤名称" clearable maxlength="6" show-word-limit />
                    </el-form-item>
                </el-form>
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="自定义图标">
                        <upload-image v-model="step.icon" :limit="1" />
                    </el-form-item>
                </el-form>
                <el-form label-width="80px" class="px-[10px]">
                    <el-form-item label="说明">
                        <el-text type="info" size="small">不上传图标时使用默认图标</el-text>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="diyStore.editTab == 'style'">
        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">标题样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="文字颜色">
                    <el-color-picker v-model="diyStore.editComponent.titleColor" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="字体大小">
                    <el-slider v-model="diyStore.editComponent.titleSize" :min="12" :max="20" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap" v-if="diyStore.editComponent.showMore">
            <h3 class="mb-[10px]">更多样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="文字颜色">
                    <el-color-picker v-model="diyStore.editComponent.moreColor" />
                </el-form-item>
            </el-form>
        </div>

        <div class="edit-attr-item-wrap">
            <h3 class="mb-[10px]">步骤样式</h3>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="文字颜色">
                    <el-color-picker v-model="diyStore.editComponent.stepTextColor" />
                </el-form-item>
            </el-form>
            <el-form label-width="80px" class="px-[10px]">
                <el-form-item label="字体大小">
                    <el-slider v-model="diyStore.editComponent.stepTextSize" :min="10" :max="16" />
                </el-form-item>
            </el-form>
        </div>

        <!-- 组件样式 -->
        <slot name="style"></slot>
    </div>
</template>

<script lang="ts" setup>
import useDiyStore from '@/stores/modules/diy'

const diyStore = useDiyStore()
diyStore.editComponent.ignore = ['componentBgUrl'] // 忽略公共属性

defineExpose({})

</script>

<style lang="scss" scoped></style>
