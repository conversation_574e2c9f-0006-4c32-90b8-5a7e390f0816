import{A as t}from"./index-3caf046d.js";const e={down:{offset:80,native:!1},up:{offset:150,toTop:{src:"https://www.mescroll.com/img/mescroll-totop.png",offset:1e3,right:20,bottom:120,width:72},empty:{use:!0,icon:t("static/resource/images/system/empty.png")}},i18n:{zh:{down:{textInOffset:"下拉刷新",textOutOffset:"释放更新",textLoading:"加载中 ...",textSuccess:"加载成功",textErr:"加载失败"},up:{textLoading:"加载中 ...",textNoMore:"",empty:{tip:"暂无相关数据"}}},en:{down:{textInOffset:"drop down refresh",textOutOffset:"release updates",textLoading:"loading ...",textSuccess:"loaded successfully",textErr:"loading failed"},up:{textLoading:"loading ...",textNoMore:"",empty:{tip:"~ absolutely empty ~"}}}}},o={def:"zh",getType(){return uni.getStorageSync("mescroll-i18n")||this.def},setType(t){uni.setStorageSync("mescroll-i18n",t)}};export{e as G,o as m};
