import{d as e,r as t,J as a,N as r,as as l,o as s,c as o,w as i,b as n,y as d,z as p,F as x,g as u,e as c,n as f,am as _,an as m,k as v,au as g,i as y,j as h,$ as b,t as k,x as F,O as j,P as w,ao as C,A as S,a as U,bG as z,B as E,S as I,Q as M}from"./index-3caf046d.js";import{_ as B}from"./u--image.eb573bce.js";import{_ as A}from"./pay.e8ba1ab9.js";import{d as N,e as O,o as P,b as Q}from"./order.5c5c6bee.js";import{g as R}from"./shop.547718ad.js";import{M as T}from"./mescroll-body.36f14dc3.js";import{M as V}from"./mescroll-empty.d02c7bd6.js";import{u as $}from"./useMescroll.26ccf5de.js";import{_ as G}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-popup.1b30ffa7.js";import"./u-safe-bottom.98e092c5.js";import"./pay.1a29db5c.js";import"./mescroll-i18n.e7c22011.js";const J=G(e({__name:"list",setup(e){const{mescrollInit:G,downCallback:J,getMescroll:Y}=$(m,_),Z=t([]),q=t(!1),D=t(!1),H=t(""),K=t([]),L=t(""),W=t(""),X=t(!1);t(null),a((e=>{H.value=e.status||"",ee(),ae()})),r((()=>{l((()=>{Y()&&Y().resetUpScroll()}))}));const ee=()=>{R().then((e=>{L.value=e.data}))},te=e=>{q.value=!1;let t={page:e.num,limit:e.size,status:H.value};O(t).then((t=>{let a=t.data.data;1==e.num&&(Z.value=[]),a.forEach((e=>{e.is_show_evaluate=!0;let t=0;for(let a=0;a<e.order_goods.length;a++)1==e.order_goods[a].status&&1!=e.order_goods[a].is_enable_refund||t++;t==e.order_goods.length&&(e.is_show_evaluate=!1)})),Z.value=Z.value.concat(a),e.endSuccess(a.length),W.value=t.data.mch_id,X.value=t.data.is_trade_managed,q.value=!0})).catch((()=>{q.value=!0,e.endErr()}))},ae=()=>{D.value=!1,K.value=[];K.value.push({name:"全部",status:""}),N().then((e=>{Object.values(e.data).forEach(((e,t)=>{K.value.push(e)})),D.value=!0})).catch((()=>{D.value=!0}))},re=t(null),le=(e,t="")=>{var a;"pay"==t?null==(a=re.value)||a.open(e.order_type,e.order_id,`/addon/shop/pages/order/detail?order_id=${e.order_id}`):"close"==t?se(e):"finish"==t?oe(e):"evaluate"==t&&(e.is_evaluate?U({url:"/addon/shop/pages/evaluate/order_evaluate_view",param:{order_id:e.order_id}}):U({url:"/addon/shop/pages/evaluate/order_evaluate",param:{order_id:e.order_id}}))},se=e=>{z({title:"提示",content:"您确定要关闭该订单吗？",confirmColor:E().themeColor["--primary-color"],success:t=>{t.confirm&&P(e.order_id).then((e=>{Y().resetUpScroll()}))}})},oe=e=>{z({title:"提示",content:"您确定物品已收到吗？",confirmColor:E().themeColor["--primary-color"],success:t=>{t.confirm&&Q(e.order_id).then((e=>{Y().resetUpScroll()}))}})};return(e,t)=>{const a=v,r=g,l=I,_=M,m=y(h("u--image"),B),z=y(h("pay"),A);return s(),o(a,{class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden order-list",style:f(e.themeColor())},{default:i((()=>[D.value?(s(),o(a,{key:0,class:"fixed left-0 top-0 right-0 z-10"},{default:i((()=>[n(r,{"scroll-x":!0,class:"tab-style-2"},{default:i((()=>[n(a,{class:"tab-content"},{default:i((()=>[(s(!0),d(x,null,p(K.value,((e,t)=>(s(),o(a,{class:b(["tab-items",{"class-select":H.value===e.status.toString()}]),onClick:t=>{return a=e.status,H.value=a.toString(),Z.value=[],void Y().resetUpScroll();var a}},{default:i((()=>[k(F(e.name),1)])),_:2},1032,["class","onClick"])))),256))])),_:1})])),_:1})])),_:1})):u("v-if",!0),n(T,{ref:"mescrollRef",top:"88rpx",onInit:c(G),down:{use:!1},onUp:te},{default:i((()=>[Z.value.length?(s(),o(a,{key:0,class:"sidebar-margin pt-[var(--top-m)]"},{default:i((()=>[(s(!0),d(x,null,p(Z.value,((e,t)=>(s(),o(a,{key:t,class:"mb-[var(--top-m)] card-template"},{default:i((()=>[n(a,{onClick:j((t=>{U({url:"/addon/shop/pages/order/detail",param:{order_id:e.order_id}})}),["stop"])},{default:i((()=>[n(a,{class:"flex justify-between items-center"},{default:i((()=>[n(a,{class:"text-[#303133] text-[26rpx] font-400 leading-[36rpx]"},{default:i((()=>[n(l,null,{default:i((()=>[k(F(c(w)("orderNo"))+":",1)])),_:1}),n(l,{class:"ml-[10rpx]"},{default:i((()=>[k(F(e.order_no),1)])),_:2},1024),n(l,{class:"text-[#303133] text-[26rpx] font-400 nc-iconfont nc-icon-fuzhiV6xx1 ml-[11rpx]",onClick:j((t=>c(C)(e.order_no)),["stop"])},null,8,["onClick"])])),_:2},1024),-1==e.status?(s(),o(a,{key:0,class:b(["text-[#303133] text-[26rpx] max-w-[85px] leading-[34rpx] truncate",{"text-primary":1==e.status,"!text-[var(--text-color-light9)]":5==e.status||-1==e.status}])},{default:i((()=>[k(F(e.close_type_name),1)])),_:2},1032,["class"])):(s(),o(a,{key:1,class:b(["text-[#303133] text-[26rpx] leading-[34rpx]",{"text-primary":1==e.status,"!text-[var(--text-color-light9)]":5==e.status||-1==e.status}])},{default:i((()=>[k(F(e.status_name.name),1)])),_:2},1032,["class"]))])),_:2},1024),(s(!0),d(x,null,p(e.order_goods,((t,r)=>(s(),d(x,{key:r},[n(a,{class:"flex box-border mt-[20rpx]"},{default:i((()=>[n(m,{width:"150rpx",height:"150rpx",radius:"var(--goods-rounded-big)",src:c(S)(t.goods_image_thumb_small?t.goods_image_thumb_small:""),mode:"aspectFill"},{error:i((()=>[n(_,{class:"w-[150rpx] h-[150rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:c(S)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["radius","src"]),n(a,{class:"ml-[20rpx] flex flex-1 flex-col box-border"},{default:i((()=>[n(a,{class:"flex justify-between items-baseline"},{default:i((()=>[n(a,{class:"max-w-[322rpx] text-[28rpx] leading-[40rpx] font-400 truncate text-[#303133]"},{default:i((()=>[k(F(t.goods_name),1)])),_:2},1024),"exchange"==e.activity_type?(s(),d(x,{key:0},[parseFloat(t.price)?(s(),o(a,{key:0,class:"text-right ml-[10rpx] leading-[42rpx]"},{default:i((()=>[n(l,{class:"text-[22rpx] font-400 price-font"},{default:i((()=>[k("￥")])),_:1}),n(l,{class:"text-[36rpx] font-500 price-font"},{default:i((()=>[k(F(parseFloat(t.price).toFixed(2).split(".")[0]),1)])),_:2},1024),n(l,{class:"text-[22rpx] font-500 price-font"},{default:i((()=>[k("."+F(parseFloat(t.price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024)):u("v-if",!0)],64)):t.extend&&t.extend.is_impulse_buy?(s(),d(x,{key:1},[parseFloat(t.goods_money)?(s(),o(a,{key:0,class:"text-right ml-[10rpx] leading-[42rpx]"},{default:i((()=>[n(l,{class:"text-[22rpx] font-400 price-font"},{default:i((()=>[k("￥")])),_:1}),n(l,{class:"text-[36rpx] font-500 price-font"},{default:i((()=>[k(F(parseFloat(t.goods_money).toFixed(2).split(".")[0]),1)])),_:2},1024),n(l,{class:"text-[22rpx] font-500 price-font"},{default:i((()=>[k("."+F(parseFloat(t.goods_money).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024)):u("v-if",!0)],64)):(s(),o(a,{key:2,class:"text-right leading-[42rpx] ml-[10rpx]"},{default:i((()=>[n(l,{class:"text-[22rpx] price-font"},{default:i((()=>[k("￥")])),_:1}),n(l,{class:"text-[36rpx] font-500 price-font"},{default:i((()=>[k(F(parseFloat(t.price).toFixed(2).split(".")[0]),1)])),_:2},1024),n(l,{class:"text-[22rpx] font-500 price-font"},{default:i((()=>[k("."+F(parseFloat(t.price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024))])),_:2},1024),n(a,{class:"flex justify-between items-baseline text-[#303133] mt-[14rpx]"},{default:i((()=>[n(a,null,{default:i((()=>[t.sku_name?(s(),o(a,{key:0,class:"text-[24rpx] text-[var(--text-color-light6)] font-400 truncate leading-[34rpx] max-w-[369rpx] mb-[10rpx]"},{default:i((()=>[k(F(t.sku_name),1)])),_:2},1024)):u("v-if",!0),"virtual"!=e.delivery_type?(s(),o(a,{key:1,class:"text-[24rpx] font-400 leading-[34rpx] text-[var(--text-color-light6)]"},{default:i((()=>[k(F(c(w)("deliveryType"))+" ： "+F(e.delivery_type_name),1)])),_:2},1024)):(s(),o(a,{key:2,class:"text-[24rpx] font-400 leading-[34rpx] text-[var(--text-color-light6)]"},{default:i((()=>[k(F(c(w)("createTime"))+" ："+F(e.create_time),1)])),_:2},1024))])),_:2},1024),n(l,{class:"text-right text-[26rpx] font-400 w-[90rpx] leading-[36rpx]"},{default:i((()=>[k("x"+F(t.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),t.extend&&t.extend.is_newcomer&&t.num>1?(s(),o(a,{key:0,class:"flex items-center box-border mt-[8rpx]"},{default:i((()=>[n(_,{class:"h-[24rpx] w-[56rpx]",src:c(S)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"]),n(a,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:i((()=>[k("第1"+F(t.unit)+"，￥"+F(parseFloat(t.extend.newcomer_price).toFixed(2))+"/"+F(t.unit)+"；第"+F(t.num>2?"2~"+t.num:"2")+F(t.unit)+"，￥"+F(parseFloat(t.price).toFixed(2))+"/"+F(t.unit),1)])),_:2},1024)])),_:2},1024)):u("v-if",!0)],64)))),128))])),_:2},1032,["onClick"]),n(a,{class:"flex justify-end items-center mt-[20rpx]"},{default:i((()=>[n(a,{class:"flex items-baseline"},{default:i((()=>[parseFloat(e.delivery_money)?(s(),o(a,{key:0,class:"text-[22rpx] text-[var(--text-color-light9)] leading-[30rpx] mr-[6rpx]"},{default:i((()=>[k(F(c(w)("service")),1)])),_:1})):u("v-if",!0),n(a,{class:"text-[22rpx] font-400 leading-[30rpx] text-[#303133]"},{default:i((()=>[k(F(c(w)("actualPayment"))+"：",1)])),_:1}),n(a,{class:"leading-[1] text-[var(--price-text-color)]"},{default:i((()=>["exchange"==e.activity_type?(s(),d(x,{key:0},[n(l,{class:"text-[36rpx] mr-[2rpx] leading-[40rpx] price-font font-500"},{default:i((()=>[k(F(e.point),1)])),_:2},1024),n(l,{class:"text-[20rpx] leading-[28rpx] font-500"},{default:i((()=>[k(F(c(w)("point")),1)])),_:1}),parseFloat(e.order_money)?(s(),d(x,{key:0},[n(l,{class:"text-[20rpx] mx-[4rpx] font-500 leading-[28rpx]"},{default:i((()=>[k("+")])),_:1}),n(l,{class:"text-[36rpx] font-500 leading-[40rpx] price-font"},{default:i((()=>[k(F(parseFloat(e.order_money).toFixed(2)),1)])),_:2},1024),n(l,{class:"text-[20rpx] font-500 leading-[28rpx] ml-[2rpx]"},{default:i((()=>[k(F(c(w)("money")),1)])),_:1})],64)):u("v-if",!0)],64)):(s(),d(x,{key:1},[n(l,{class:"text-[22rpx] leading-[26rpx] price-font"},{default:i((()=>[k("￥")])),_:1}),n(l,{class:"text-[36rpx] font-500 leading-[40rpx] price-font"},{default:i((()=>[k(F(parseFloat(e.order_money).toFixed(2).split(".")[0]),1)])),_:2},1024),n(l,{class:"text-[22rpx] font-500 leading-[28rpx] price-font"},{default:i((()=>[k("."+F(parseFloat(e.order_money).toFixed(2).split(".")[1]),1)])),_:2},1024)],64))])),_:2},1024)])),_:2},1024)])),_:2},1024),1==e.status||3==e.status||5==e.status&&L.value.evaluate_is_show&&1==L.value.is_evaluate?(s(),o(a,{key:0,class:"flex justify-end text-[28rpx] mt-[20rpx] items-center"},{default:i((()=>[1==e.status?(s(),o(a,{key:0,class:"text-[24rpx] font-500 leading-[52rpx] h-[56rpx] min-w-[150rpx] text-center border-[2rpx] border-solid border-[var(--text-color-light9)] rounded-full text-[var(--text-color-light6)] box-border",onClick:j((t=>le(e,"close")),["stop"])},{default:i((()=>[k(F(c(w)("orderClose")),1)])),_:2},1032,["onClick"])):u("v-if",!0),1==e.status?(s(),o(a,{key:1,class:"text-[24rpx] font-500 flex-center h-[56rpx] min-w-[150rpx] text-center border-[0] text-[#fff] primary-btn-bg rounded-full ml-[20rpx] box-border",onClick:j((t=>le(e,"pay")),["stop"])},{default:i((()=>[k(F(c(w)("topay")),1)])),_:2},1032,["onClick"])):u("v-if",!0),3==e.status?(s(),o(a,{key:2,class:"text-[24rpx] font-500 flex-center h-[56rpx] min-w-[150rpx] text-center border-[0] text-[#fff] primary-btn-bg rounded-full ml-[20rpx] box-border",onClick:j((t=>le(e,"finish")),["stop"])},{default:i((()=>[k(F(c(w)("orderFinish")),1)])),_:2},1032,["onClick"])):u("v-if",!0),5==e.status&&L.value.evaluate_is_show&&1==L.value.is_evaluate?(s(),o(a,{key:3,class:"text-[24rpx] font-500 leading-[52rpx] h-[56rpx] min-w-[150rpx] text-center border-[2rpx] border-solid border-[var(--text-color-light9)] rounded-full ml-[20rpx] text-[var(--text-color-light6)] box-border",onClick:j((t=>le(e,"evaluate")),["stop"])},{default:i((()=>[k(F(1==e.is_evaluate?c(w)("selectedEvaluate"):c(w)("evaluate")),1)])),_:2},1032,["onClick"])):u("v-if",!0)])),_:2},1024)):u("v-if",!0)])),_:2},1024)))),128))])),_:1})):u("v-if",!0),!Z.value.length&&q.value?(s(),o(V,{key:1,option:{tip:"暂无订单"}})):u("v-if",!0)])),_:1},8,["onInit"]),n(z,{ref_key:"payRef",ref:re,onClose:e.payClose},null,8,["onClose"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-bf1b8658"]]);export{J as default};
