import{d as e,r as t,_ as a,P as l,o as r,c as o,w as s,b as c,O as n,g as i,$ as u,t as x,y as p,z as d,F as m,e as f,n as v,am as _,an as g,S as h,al as y,k as b,i as j,j as k,A as w,x as C,a as V,Q as F}from"./index-3caf046d.js";import{_ as M}from"./tabbar.2c31519d.js";import{b as I}from"./point.0698952c.js";import{M as U}from"./mescroll-body.36f14dc3.js";import{M as E}from"./mescroll-empty.d02c7bd6.js";import{u as S}from"./useMescroll.26ccf5de.js";import{_ as z}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-tabbar-item.31141540.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-tabbar.38f37e13.js";import"./u-safe-bottom.98e092c5.js";import"./mescroll-i18n.e7c22011.js";const N=z(e({__name:"list",setup(e){const{mescrollInit:z,downCallback:N,getMescroll:T}=S(g,_),A=t([]),G=t(""),O=t(""),P=t(null),Q=t(!1),R=t(""),Y=t(""),Z=t(""),$=t("total_order_num"),q=e=>{Q.value=!1;let t={goods_category:O.value,page:e.num,limit:e.size,names:R.value,coupon_id:G.value,order:"total_order_num"===$.value?"":$.value,sort:"price"==$.value?Y.value:Z.value};I(t).then((t=>{let a=t.data.data;1===Number(e.num)&&(A.value=[]),A.value=A.value.concat(a),e.endSuccess(a.length),Q.value=!0})).catch((()=>{Q.value=!0,e.endErr()}))},B=e=>{$.value=e,"total_order_num"==e&&(Z.value="",Y.value=""),"price"==e&&(Z.value="",Y.value?Y.value="asc"==Y.value?"desc":"asc":Y.value="asc"),"total_exchange_num"==e&&(Y.value="",Z.value?Z.value="asc"==Z.value?"desc":"asc":Z.value="asc"),A.value=[],T().resetUpScroll()};return a((()=>{setTimeout((()=>{T().optUp.textNoMore=l("end")}),500)})),(e,t)=>{const a=h,l=y,_=b,g=F,I=j(k("tabbar"),M);return r(),o(_,{class:"bg-[var(--page-bg-color)] min-h-[100vh]",style:v(e.themeColor())},{default:s((()=>[c(_,{class:"fixed left-0 right-0 top-0 product-warp bg-[#fff]"},{default:s((()=>[c(_,{class:"py-[14rpx] flex items-center justify-between px-[20rpx]"},{default:s((()=>[c(_,{class:"flex-1 search-input"},{default:s((()=>[c(a,{onClick:t[0]||(t[0]=n((e=>B("all")),["stop"])),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"}),c(l,{class:"input",maxlength:"50",type:"text",modelValue:R.value,"onUpdate:modelValue":t[1]||(t[1]=e=>R.value=e),placeholder:"请搜索您想要的商品",placeholderClass:"text-[var(--text-color-light9)] text-[24rpx]","confirm-type":"search",onConfirm:t[2]||(t[2]=e=>B("all"))},null,8,["modelValue"]),R.value?(r(),o(a,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[3]||(t[3]=e=>R.value="")})):i("v-if",!0)])),_:1})])),_:1}),c(_,{class:"h-[88rpx] px-[30rpx]"},{default:s((()=>[c(_,{class:"flex items-center justify-between text-[26rpx] text-[var(--text-color-light6)] h-[88rpx]"},{default:s((()=>[c(a,{class:u({"!text-[var(--primary-color)] font-500":"total_order_num"==$.value}),onClick:t[4]||(t[4]=e=>B("total_order_num"))},{default:s((()=>[x("综合排序")])),_:1},8,["class"]),c(_,{class:"flex items-center",onClick:t[5]||(t[5]=e=>B("total_exchange_num"))},{default:s((()=>[c(a,{class:u(["mr-[4rpx]",{"!text-[var(--primary-color)] font-500":"total_exchange_num"==$.value}])},{default:s((()=>[x("销量")])),_:1},8,["class"]),"asc"==Z.value?(r(),o(a,{key:0,class:u(["text-[16rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-a-xiangshangV6xx1",{"!text-[var(--primary-color)]":"total_exchange_num"==$.value}])},null,8,["class"])):(r(),o(a,{key:1,class:u(["text-[16rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-a-xiangxiaV6xx1",{"!text-[var(--primary-color)]":"total_exchange_num"==$.value}])},null,8,["class"]))])),_:1}),c(_,{class:"flex items-center",onClick:t[6]||(t[6]=e=>B("price"))},{default:s((()=>[c(a,{class:u(["mr-[4rpx]",{"!text-[var(--primary-color)] font-500":"price"==$.value}])},{default:s((()=>[x("价格")])),_:1},8,["class"]),"asc"==Y.value?(r(),o(a,{key:0,class:u(["text-[16rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-a-xiangshangV6xx1",{"!text-[var(--primary-color)]":"price"==$.value}])},null,8,["class"])):(r(),o(a,{key:1,class:u(["text-[16rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-a-xiangxiaV6xx1",{"!text-[var(--primary-color)]":"price"==$.value}])},null,8,["class"]))])),_:1})])),_:1})])),_:1})])),_:1}),c(U,{ref_key:"mescrollRef",ref:P,top:"176rpx",bottom:"60px",onInit:f(z),down:{use:!1},onUp:q},{default:s((()=>[A.value.length?(r(),o(_,{key:0,class:"sidebar-margin flex justify-between flex-wrap"},{default:s((()=>[(r(!0),p(m,null,d(A.value,((e,t)=>(r(),o(_,{class:"goods-item-style-two flex flex-col bg-[#fff] box-border rounded-[var(--rounded-mid)] overflow-hidden mt-[var(--top-m)]",onClick:t=>{return a=e.id,void V({url:"/addon/shop/pages/point/detail",param:{id:a},mode:"navigateTo"});var a}},{default:s((()=>[i(' <u--image width="100%" height="350rpx" :src="img(item.goods_cover_thumb_mid ? item.goods_cover_thumb_mid : \'\')" model="aspectFill">\n                          <template #error>\n                            <image class="w-[100%] h-[350rpx]" :src="img(\'static/resource/images/diy/shop_default.jpg\')" mode="aspectFill" />\n                          </template>\n                        </u--image> '),e.goods_cover_thumb_mid?(r(),o(g,{key:0,class:"w-[100%] h-[350rpx] rounded-tl-[var(--rounded-mid)] rounded-tr-[var(--rounded-mid)]",src:f(w)(e.goods_cover_thumb_mid),mode:"aspectFill",onError:t=>e.goods_cover_thumb_mid="static/resource/images/diy/shop_default.jpg"},null,8,["src","onError"])):(r(),o(g,{key:1,class:"w-[100%] h-[350rpx] rounded-tl-[var(--rounded-mid)] rounded-tr-[var(--rounded-mid)]",src:f(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])),c(_,{class:"px-[16rpx] flex-1 pt-[10rpx] pb-[20rpx] flex flex-col justify-between"},{default:s((()=>[c(_,{class:"text-[] leading-[40rpx] text-[28rpx] multi-hidden"},{default:s((()=>[x(C(e.names),1)])),_:2},1024),c(_,{class:"text-[24rpx] font-400 leading-[34rpx] mt-[10rpx] text-[var(--text-color-light9)]"},{default:s((()=>[x("已兑"+C(e.total_exchange_num)+"人",1)])),_:2},1024),c(_,{class:"flex justify-between flex-wrap items-center mt-[16rpx]"},{default:s((()=>[c(_,{class:"flex flex-col"},{default:s((()=>[c(_,{class:"text-[var(--price-text-color)] price-font ml-[2rpx] flex items-center"},{default:s((()=>[c(a,{class:"text-[32rpx]"},{default:s((()=>[x(C(e.point),1)])),_:2},1024),c(a,{class:"text-[26rpx] ml-[4rpx]"},{default:s((()=>[x("积分")])),_:1})])),_:2},1024),e.price&&e.price>0?(r(),o(_,{key:0,class:"flex items-center price-font mt-[6rpx]"},{default:s((()=>[c(a,{class:"text-[var(--price-text-color)] font-400 text-[32rpx]"},{default:s((()=>[x("+"+C(parseFloat(e.price).toFixed(2)),1)])),_:2},1024),c(a,{class:"text-[var(--price-text-color)] font-400 ml-[4rpx] text-[20rpx]"},{default:s((()=>[x("元")])),_:1})])),_:2},1024)):i("v-if",!0)])),_:2},1024),c(_,{class:"w-[120rpx] h-[54rpx] text-[22rpx] flex-center !text-[#fff] m-0 rounded-full primary-btn-bg remove-border text-center",shape:"circle"},{default:s((()=>[x("去兑换")])),_:1})])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),256))])),_:1})):i("v-if",!0),!A.value.length&&Q.value?(r(),o(E,{key:1,option:{tip:"暂无商品"}})):i("v-if",!0)])),_:1},8,["onInit"]),c(I)])),_:1},8,["style"])}}}),[["__scopeId","data-v-c8298083"]]);export{N as default};
