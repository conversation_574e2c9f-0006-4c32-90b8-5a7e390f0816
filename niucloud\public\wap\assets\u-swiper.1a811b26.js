import{ab as e,ac as t,ad as i,ae as r,o as a,c as n,w as o,$ as s,n as l,b as d,g as c,y as u,F as p,z as g,k as y,af as _,aU as h,bd as w,i as m,j as f,t as v,x as I,a7 as S,Q as b,bg as C,S as M,aM as k,aN as x}from"./index-3caf046d.js";import{_ as N}from"./u-loading-icon.255170b9.js";import{_ as j}from"./_plugin-vue_export-helper.1b428a4d.js";const U=j({name:"u-swiper-indicator",mixins:[t,i,{props:{length:{type:[String,Number],default:()=>e.swiperIndicator.length},current:{type:[String,Number],default:()=>e.swiperIndicator.current},indicatorActiveColor:{type:String,default:()=>e.swiperIndicator.indicatorActiveColor},indicatorInactiveColor:{type:String,default:()=>e.swiperIndicator.indicatorInactiveColor},indicatorMode:{type:String,default:()=>e.swiperIndicator.indicatorMode}}}],data:()=>({lineWidth:22}),computed:{lineStyle(){let e={};return e.width=r(this.lineWidth),e.transform=`translateX(${r(this.current*this.lineWidth)})`,e.backgroundColor=this.indicatorActiveColor,e},dotStyle(){return e=>{let t={};return t.backgroundColor=e===this.current?this.indicatorActiveColor:this.indicatorInactiveColor,t}}},methods:{addUnit:r}},[["render",function(e,t,i,r,_,h){const w=y;return a(),n(w,{class:"u-swiper-indicator"},{default:o((()=>["line"===e.indicatorMode?(a(),n(w,{key:0,class:s(["u-swiper-indicator__wrapper",[`u-swiper-indicator__wrapper--${e.indicatorMode}`]]),style:l({width:h.addUnit(_.lineWidth*e.length),backgroundColor:e.indicatorInactiveColor})},{default:o((()=>[d(w,{class:"u-swiper-indicator__wrapper--line__bar",style:l([h.lineStyle])},null,8,["style"])])),_:1},8,["class","style"])):c("v-if",!0),"dot"===e.indicatorMode?(a(),n(w,{key:1,class:"u-swiper-indicator__wrapper"},{default:o((()=>[(a(!0),u(p,null,g(e.length,((t,i)=>(a(),n(w,{class:s(["u-swiper-indicator__wrapper__dot",[i===e.current&&"u-swiper-indicator__wrapper__dot--active"]]),key:i,style:l([h.dotStyle(i)])},null,8,["class","style"])))),128))])),_:1})):c("v-if",!0)])),_:1})}],["__scopeId","data-v-939cf1e0"]]);const A=j({name:"u-swiper",mixins:[t,i,{props:{list:{type:Array,default:()=>e.swiper.list},indicator:{type:Boolean,default:()=>e.swiper.indicator},indicatorActiveColor:{type:String,default:()=>e.swiper.indicatorActiveColor},indicatorInactiveColor:{type:String,default:()=>e.swiper.indicatorInactiveColor},indicatorStyle:{type:[String,Object],default:()=>e.swiper.indicatorStyle},indicatorMode:{type:String,default:()=>e.swiper.indicatorMode},autoplay:{type:Boolean,default:()=>e.swiper.autoplay},current:{type:[String,Number],default:()=>e.swiper.current},currentItemId:{type:String,default:()=>e.swiper.currentItemId},interval:{type:[String,Number],default:()=>e.swiper.interval},duration:{type:[String,Number],default:()=>e.swiper.duration},circular:{type:Boolean,default:()=>e.swiper.circular},previousMargin:{type:[String,Number],default:()=>e.swiper.previousMargin},nextMargin:{type:[String,Number],default:()=>e.swiper.nextMargin},acceleration:{type:Boolean,default:()=>e.swiper.acceleration},displayMultipleItems:{type:Number,default:()=>e.swiper.displayMultipleItems},easingFunction:{type:String,default:()=>e.swiper.easingFunction},keyName:{type:String,default:()=>e.swiper.keyName},imgMode:{type:String,default:()=>e.swiper.imgMode},height:{type:[String,Number],default:()=>e.swiper.height},bgColor:{type:String,default:()=>e.swiper.bgColor},radius:{type:[String,Number],default:()=>e.swiper.radius},loading:{type:Boolean,default:()=>e.swiper.loading},showTitle:{type:Boolean,default:()=>e.swiper.showTitle}}}],data:()=>({currentIndex:0}),watch:{current(e,t){e!==t&&(this.currentIndex=e)}},emits:["click","change"],computed:{itemStyle(){return e=>{const t={};return this.nextMargin&&this.previousMargin&&(t.borderRadius=r(this.radius),e!==this.currentIndex&&(t.transform="scale(0.92)")),t}}},methods:{addStyle:_,addUnit:r,testObject:h.object,testImage:h.image,getItemType(e){return"string"==typeof e?h.video(this.getSource(e))?"video":"image":"object"==typeof e&&this.keyName?e.type?"image"===e.type?"image":"video"===e.type?"video":"image":h.video(this.getSource(e))?"video":"image":void 0},getSource(e){return"string"==typeof e?e:"object"==typeof e&&this.keyName?e[this.keyName]:""},change(e){const{current:t}=e.detail;this.pauseVideo(this.currentIndex),this.currentIndex=t,this.$emit("change",e.detail)},pauseVideo(e){const t=this.getSource(this.list[e]);if(h.video(t)){w(`video-${e}`,this).pause()}},getPoster:e=>"object"==typeof e&&e.poster?e.poster:"",clickHandler(e){this.$emit("click",e)}}},[["render",function(e,t,i,r,s,_){const h=m(f("u-loading-icon"),N),w=y,j=b,A=C,T=M,$=k,B=x,F=m(f("u-swiper-indicator"),U);return a(),n(w,{class:"u-swiper",style:l({backgroundColor:e.bgColor,height:_.addUnit(e.height),borderRadius:_.addUnit(e.radius)})},{default:o((()=>[e.loading?(a(),n(w,{key:0,class:"u-swiper__loading"},{default:o((()=>[d(h,{mode:"circle"})])),_:1})):(a(),n(B,{key:1,class:"u-swiper__wrapper",style:l({flex:"1",height:_.addUnit(e.height)}),onChange:_.change,circular:e.circular,interval:e.interval,duration:e.duration,autoplay:e.autoplay,current:e.current,currentItemId:e.currentItemId,previousMargin:_.addUnit(e.previousMargin),nextMargin:_.addUnit(e.nextMargin),acceleration:e.acceleration,displayMultipleItems:e.displayMultipleItems,easingFunction:e.easingFunction},{default:o((()=>[(a(!0),u(p,null,g(e.list,((t,i)=>(a(),n($,{class:"u-swiper__wrapper__item",key:i},{default:o((()=>[d(w,{class:"u-swiper__wrapper__item__wrapper",style:l([_.itemStyle(i)])},{default:o((()=>[c(" 在nvue中，image图片的宽度默认为屏幕宽度，需要通过flex:1撑开，另外必须设置高度才能显示图片 "),"image"===_.getItemType(t)?(a(),n(j,{key:0,class:"u-swiper__wrapper__item__wrapper__image",src:_.getSource(t),mode:e.imgMode,onClick:e=>_.clickHandler(i),style:l({height:_.addUnit(e.height),borderRadius:_.addUnit(e.radius)})},null,8,["src","mode","onClick","style"])):c("v-if",!0),"video"===_.getItemType(t)?(a(),n(A,{key:1,class:"u-swiper__wrapper__item__wrapper__video",id:`video-${i}`,"enable-progress-gesture":!1,src:_.getSource(t),poster:_.getPoster(t),title:e.showTitle&&_.testObject(t)&&t.title?t.title:"",style:l({height:_.addUnit(e.height)}),controls:"",onClick:e=>_.clickHandler(i)},null,8,["id","src","poster","title","style","onClick"])):c("v-if",!0),e.showTitle&&_.testObject(t)&&t.title&&_.testImage(_.getSource(t))?(a(),n(T,{key:2,class:"u-swiper__wrapper__item__wrapper__title u-line-1"},{default:o((()=>[v(I(t.title),1)])),_:2},1024)):c("v-if",!0)])),_:2},1032,["style"])])),_:2},1024)))),128))])),_:1},8,["style","onChange","circular","interval","duration","autoplay","current","currentItemId","previousMargin","nextMargin","acceleration","displayMultipleItems","easingFunction"])),d(w,{class:"u-swiper__indicator",style:l([_.addStyle(e.indicatorStyle)])},{default:o((()=>[S(e.$slots,"indicator",{},(()=>[e.loading||!e.indicator||e.showTitle?c("v-if",!0):(a(),n(F,{key:0,indicatorActiveColor:e.indicatorActiveColor,indicatorInactiveColor:e.indicatorInactiveColor,length:e.list.length,current:s.currentIndex,indicatorMode:e.indicatorMode},null,8,["indicatorActiveColor","indicatorInactiveColor","length","current","indicatorMode"]))]),!0)])),_:3},8,["style"])])),_:3},8,["style"])}],["__scopeId","data-v-2457dd58"]]);export{A as _};
