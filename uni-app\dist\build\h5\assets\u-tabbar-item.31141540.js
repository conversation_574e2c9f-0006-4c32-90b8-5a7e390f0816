import{_ as e}from"./u-icon.ba193921.js";import{ab as t,ac as a,ad as o,ae as s,af as i,o as l,c as r,w as n,t as u,x as d,$ as b,n as c,g as h,S as p,i as m,j as g,b as f,y,F as v,a7 as S,k as x}from"./index-3caf046d.js";import{_}from"./_plugin-vue_export-helper.1b428a4d.js";const D=_({name:"u-badge",mixins:[a,{props:{isDot:{type:Boolean,default:()=>t.badge.isDot},value:{type:[Number,String],default:()=>t.badge.value},modelValue:{type:[Number,String],default:()=>t.badge.modelValue},show:{type:Boolean,default:()=>t.badge.show},max:{type:[Number,String],default:()=>t.badge.max},type:{type:String,default:()=>t.badge.type},showZero:{type:Boolean,default:()=>t.badge.showZero},bgColor:{type:[String,null],default:()=>t.badge.bgColor},color:{type:[String,null],default:()=>t.badge.color},shape:{type:String,default:()=>t.badge.shape},numberType:{type:String,default:()=>t.badge.numberType},offset:{type:Array,default:()=>t.badge.offset},inverted:{type:Boolean,default:()=>t.badge.inverted},absolute:{type:Boolean,default:()=>t.badge.absolute}}},o],computed:{boxStyle:()=>({}),badgeStyle(){const e={};if(this.color&&(e.color=this.color),this.bgColor&&!this.inverted&&(e.backgroundColor=this.bgColor),this.absolute&&(e.position="absolute",this.offset.length)){const t=this.offset[0],a=this.offset[1]||t;e.top=s(t),e.right=s(a)}return e},showValue(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}}},methods:{addStyle:i}},[["render",function(e,t,a,o,s,i){const m=p;return e.show&&(0!==Number(e.value)||e.showZero||e.isDot)?(l(),r(m,{key:0,class:b([[e.isDot?"u-badge--dot":"u-badge--not-dot",e.inverted&&"u-badge--inverted","horn"===e.shape&&"u-badge--horn",`u-badge--${e.type}${e.inverted?"--inverted":""}`],"u-badge"]),style:c([i.addStyle(e.customStyle),i.badgeStyle])},{default:n((()=>[u(d(e.isDot?"":i.showValue),1)])),_:1},8,["class","style"])):h("v-if",!0)}],["__scopeId","data-v-0e56696e"]]);const k=_({name:"u-tabbar-item",mixins:[a,o,{props:{name:{type:[String,Number,null],default:()=>t.tabbarItem.name},icon:{icon:String,default:()=>t.tabbarItem.icon},badge:{type:[String,Number,null],default:()=>t.tabbarItem.badge},dot:{type:Boolean,default:()=>t.tabbarItem.dot},text:{type:String,default:()=>t.tabbarItem.text},badgeStyle:{type:[Object,String],default:()=>t.tabbarItem.badgeStyle}}}],data:()=>({isActive:!1,parentData:{value:null,activeColor:"",inactiveColor:""}}),options:{virtualHost:!0},created(){this.init()},emits:["click","change"],methods:{addStyle:i,init(){this.updateParentData(),this.parent;const e=this.parent.children.indexOf(this);this.isActive=(this.name||e)===this.parentData.value},updateParentData(){this.getParentData("u-tabbar")},updateFromParent(){this.init()},clickHandler(){this.$nextTick((()=>{const e=this.parent.children.indexOf(this),t=this.name||e;t!==this.parent.value&&this.parent.$emit("change",t),this.$emit("click",t)}))}}},[["render",function(t,a,o,s,i,b){const h=m(g("u-icon"),e),_=m(g("u-badge"),D),k=x,w=p;return l(),r(k,{class:"u-tabbar-item cursor-pointer",style:c([b.addStyle(t.customStyle)]),onClick:b.clickHandler},{default:n((()=>[f(k,{class:"u-tabbar-item__icon"},{default:n((()=>[t.icon?(l(),r(h,{key:0,name:t.icon,color:i.isActive?i.parentData.activeColor:i.parentData.inactiveColor,size:20},null,8,["name","color"])):(l(),y(v,{key:1},[i.isActive?S(t.$slots,"active-icon",{key:0},void 0,!0):S(t.$slots,"inactive-icon",{key:1},void 0,!0)],64)),f(_,{absolute:"",offset:[0,t.dot?"34rpx":t.badge>9?"14rpx":"20rpx"],customStyle:t.badgeStyle,isDot:t.dot,value:t.badge||(t.dot?1:null),show:t.dot||t.badge>0},null,8,["offset","customStyle","isDot","value","show"])])),_:3}),S(t.$slots,"text",{},(()=>[f(w,{class:"u-tabbar-item__text",style:c({color:i.isActive?i.parentData.activeColor:i.parentData.inactiveColor})},{default:n((()=>[u(d(t.text),1)])),_:1},8,["style"])]),!0)])),_:3},8,["style","onClick"])}],["__scopeId","data-v-ed2788ef"]]);export{k as _,D as a};
