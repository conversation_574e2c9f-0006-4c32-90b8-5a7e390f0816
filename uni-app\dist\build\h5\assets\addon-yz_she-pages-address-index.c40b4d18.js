import{d as e,r as a,J as s,N as l,o as t,c as r,w as o,b as d,y as c,z as n,F as i,g as u,t as p,n as f,bM as x,a as m,k as _,R as v,au as h,x as b,e as g,bK as k,O as y,$ as C,bQ as j,bR as w,S}from"./index-3caf046d.js";import{M as z}from"./mescroll-empty.d02c7bd6.js";import{_ as A}from"./_plugin-vue_export-helper.1b428a4d.js";import"./mescroll-i18n.e7c22011.js";const E=A(e({__name:"index",setup(e){const A=a(!0),E=a(0),M=a([]),R=a(""),V=a("");s((e=>{R.value=e.type||"",V.value=e.source||"",e.type&&(E.value="address"==e.type?0:1)}));l((()=>{x({}).then((({data:e})=>{const a=[],s=[];e.forEach((e=>{"address"==e.type?a.push(e):s.push(e)})),V.value?M.value=0==E.value?a:s:M.value=e,A.value=!1})).catch((()=>{A.value=!1}))}));const F=()=>{m({url:"/app/pages/member/address_edit",param:{source:V.value}})};return(e,a)=>{const s=_,l=S,x=v,E=h;return A.value?u("v-if",!0):(t(),r(s,{key:0,class:"address bg-[var(--page-bg-color)] min-h-[100vh]",style:f(e.themeColor())},{default:o((()=>[d(E,{"scroll-y":"true"},{default:o((()=>[M.value.length?(t(),r(s,{key:0,class:"sidebar-margin pt-[var(--top-m)]"},{default:o((()=>[(t(!0),c(i,null,n(M.value,((e,a)=>(t(),r(s,{class:"mb-[var(--top-m)] rounded-[var(--rounded-big)] overflow-hidden"},{default:o((()=>[d(s,{class:"flex flex-col card-template"},{default:o((()=>[d(s,{class:"flex-1 line-feed mr-[20rpx]",onClick:a=>(e=>{const a=uni.getStorageSync("selectAddressCallback");a&&(a.address_id=e.id,a.address_info={id:e.id,name:e.name,mobile:e.mobile,full_address:e.full_address},uni.setStorage({key:"selectAddressCallback",data:a,success(){m({url:a.back,mode:"redirectTo"})}}))})(e)},{default:o((()=>[d(s,{class:"flex items-center"},{default:o((()=>[d(s,{class:"text-[#333] text-[30rpx] leading-[34rpx] font-500"},{default:o((()=>[p(b(e.name),1)])),_:2},1024),d(l,{class:"text-[#333] text-[30rpx] ml-[10rpx]"},{default:o((()=>[p(b(g(k)(e.mobile)),1)])),_:2},1024)])),_:2},1024),d(s,{class:"mt-[16rpx] text-[26rpx] line-feed text-[var(--text-color-light9)] leading-[1.4]"},{default:o((()=>[p(b(e.full_address),1)])),_:2},1024)])),_:2},1032,["onClick"]),d(s,{class:"flex justify-between pt-[26rpx]"},{default:o((()=>[d(s,{class:"flex items-center text-[26rpx] leading-none",onClick:y((e=>(e=>{const a=M.value[e];a.is_default||(a.is_default=1,w(a).then((()=>{M.value.forEach(((a,s)=>{a.is_default&&(a.is_default=0),s==e&&(a.is_default=1)}))})).catch())})(a)),["stop"])},{default:o((()=>[d(l,{class:C(["iconfont !text-[26rpx] mr-[10rpx]",{"iconduigou text-primary":e.is_default,iconcheckbox_nol:!e.is_default}])},null,8,["class"]),p(" 设为默认 ")])),_:2},1032,["onClick"]),d(s,{class:"flex"},{default:o((()=>[d(s,{class:"text-[26rpx]",onClick:y((a=>{return s=e.id,void m({url:"/app/pages/member/address_edit",param:{id:s,source:V.value}});var s}),["stop"])},{default:o((()=>[d(l,{class:"nc-iconfont nc-icon-xiugaiV6xx shrink-0 text-[26rpx] mr-[4rpx]"}),p(" 编辑 ")])),_:2},1032,["onClick"]),d(s,{onClick:y((e=>(e=>{const a=M.value[e];j(a.id).then((()=>{M.value.splice(e,1)})).catch()})(a)),["stop"]),class:"ml-[40rpx] text-[26rpx]"},{default:o((()=>[d(l,{class:"nc-iconfont nc-icon-shanchu-yuangaizhiV6xx shrink-0 text-[26rpx] mr-[4rpx]"}),p(" 删除 ")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1})):u("v-if",!0),M.value.length?u("v-if",!0):(t(),r(z,{key:1,option:{tip:"暂无收货地址"}})),d(s,{class:"w-full footer"},{default:o((()=>[d(s,{class:"py-[var(--top-m)] px-[var(--sidebar-m)] footer w-full fixed bottom-0 left-0 right-0 box-border"},{default:o((()=>[d(x,{"hover-class":"none",class:"primary-btn-bg text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",onClick:F},{default:o((()=>[p("新增收货地址")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["style"]))}}}),[["__scopeId","data-v-2e783369"]]);export{E as default};
