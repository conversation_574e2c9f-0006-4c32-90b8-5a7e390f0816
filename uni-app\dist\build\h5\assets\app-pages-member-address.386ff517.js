import{d as e,r as a,J as s,N as t,o as l,c as r,w as c,b as o,y as d,z as n,F as u,g as i,t as p,x as f,e as x,n as m,bM as v,a as _,k as b,R as g,au as h,bK as k,O as y,$ as C,P as S,bQ as j,bR as w,S as A}from"./index-3caf046d.js";import{M as z}from"./mescroll-empty.d02c7bd6.js";import{_ as E}from"./_plugin-vue_export-helper.1b428a4d.js";import"./mescroll-i18n.e7c22011.js";const M=E(e({__name:"address",setup(e){const E=a(!0),M=a(0),R=a([]),V=a(""),F=a("");s((e=>{V.value=e.type||"",F.value=e.source||"",e.type&&(M.value="address"==e.type?0:1),uni.getStorageSync("selectAddressCallback")&&uni.removeStorage({key:"selectAddressCallback"})}));t((()=>{v({}).then((({data:e})=>{const a=[],s=[];e.forEach((e=>{"address"==e.type?a.push(e):s.push(e)})),F.value?R.value=0==M.value?a:s:R.value=e,E.value=!1})).catch((()=>{E.value=!1}))}));const I=()=>{_({url:"/app/pages/member/address_edit",param:{source:F.value}})};return(e,a)=>{const s=b,t=A,v=g,M=h;return E.value?i("v-if",!0):(l(),r(s,{key:0,class:"address bg-[var(--page-bg-color)] min-h-[100vh]",style:m(e.themeColor())},{default:c((()=>[o(M,{"scroll-y":"true"},{default:c((()=>[R.value.length?(l(),r(s,{key:0,class:"sidebar-margin pt-[var(--top-m)]"},{default:c((()=>[(l(!0),d(u,null,n(R.value,((e,a)=>(l(),r(s,{class:"mb-[var(--top-m)] rounded-[var(--rounded-big)] overflow-hidden"},{default:c((()=>[o(s,{class:"flex flex-col card-template"},{default:c((()=>[o(s,{class:"flex-1 line-feed mr-[20rpx]",onClick:a=>(e=>{const a=uni.getStorageSync("selectAddressCallback");a&&(a.address_id=e.id,uni.setStorage({key:"selectAddressCallback",data:a,success(){_({url:a.back,mode:"redirectTo"})}}))})(e)},{default:c((()=>[o(s,{class:"flex items-center"},{default:c((()=>[o(s,{class:"text-[#333] text-[30rpx] leading-[34rpx] font-500"},{default:c((()=>[p(f(e.name),1)])),_:2},1024),o(t,{class:"text-[#333] text-[30rpx] ml-[10rpx]"},{default:c((()=>[p(f(x(k)(e.mobile)),1)])),_:2},1024)])),_:2},1024),o(s,{class:"mt-[16rpx] text-[26rpx] line-feed text-[var(--text-color-light9)] leading-[1.4]"},{default:c((()=>[p(f(e.full_address),1)])),_:2},1024)])),_:2},1032,["onClick"]),o(s,{class:"flex justify-between pt-[26rpx]"},{default:c((()=>[o(s,{class:"flex items-center text-[26rpx] leading-none",onClick:y((e=>(e=>{const a=R.value[e];a.is_default||(a.is_default=1,w(a).then((()=>{R.value.forEach(((a,s)=>{a.is_default&&(a.is_default=0),s==e&&(a.is_default=1)}))})).catch())})(a)),["stop"])},{default:c((()=>[o(t,{class:C(["iconfont !text-[26rpx] mr-[10rpx]",{"iconduigou text-primary":e.is_default,iconcheckbox_nol:!e.is_default}])},null,8,["class"]),p(" 设为默认 ")])),_:2},1032,["onClick"]),o(s,{class:"flex"},{default:c((()=>[o(s,{class:"text-[26rpx]",onClick:y((a=>{return s=e.id,void _({url:"/app/pages/member/address_edit",param:{id:s,source:F.value}});var s}),["stop"])},{default:c((()=>[o(t,{class:"nc-iconfont nc-icon-xiugaiV6xx shrink-0 text-[26rpx] mr-[4rpx]"}),p(" 编辑 ")])),_:2},1032,["onClick"]),o(s,{onClick:y((e=>(e=>{const a=R.value[e];j(a.id).then((()=>{R.value.splice(e,1)})).catch()})(a)),["stop"]),class:"ml-[40rpx] text-[26rpx]"},{default:c((()=>[o(t,{class:"nc-iconfont nc-icon-shanchu-yuangaizhiV6xx shrink-0 text-[26rpx] mr-[4rpx]"}),p(" 删除 ")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1})):i("v-if",!0),R.value.length?i("v-if",!0):(l(),r(z,{key:1,option:{tip:"暂无收货地址"}})),o(s,{class:"w-full footer"},{default:c((()=>[o(s,{class:"py-[var(--top-m)] px-[var(--sidebar-m)] footer w-full fixed bottom-0 left-0 right-0 box-border"},{default:c((()=>[o(v,{"hover-class":"none",class:"primary-btn-bg text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",onClick:I},{default:c((()=>[p(f(x(S)("createAddress")),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["style"]))}}}),[["__scopeId","data-v-4a5b7c65"]]);export{M as default};
