import{d as e,r as a,J as l,N as t,o as s,c as o,w as c,g as u,b as d,t as n,x as i,$ as r,y as f,a_ as v,O as _,z as m,F as p,be as g,M as y,bP as k,a2 as h,S as x,k as C,al as w,i as b,j as D}from"./index-3caf046d.js";import{_ as I}from"./u-icon.ba193921.js";import{c as q}from"./recycle_order.a252d983.js";import{_ as S}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const z=S(e({__name:"batch",setup(e){const S=a({}),z=a(1),$=a(null),T=a("pickup"),V=a(null),j=a(!1),M=a("尽快上门"),N=a(""),A=a(""),P=a(!1),B=a(!1),L=a("today"),O=a("today-urgent"),U=a(""),E=a(""),F=a(""),J=["顺丰速运","京东物流","德邦快递","中通快递","韵达速递","圆通速递","申通快递","极兔速递"],G=()=>{z.value>1&&(z.value--,console.log("减少数量:",z.value))},H=()=>{z.value<100&&(z.value++,console.log("增加数量:",z.value))},K=e=>{T.value=e,console.log("选择配送方式:",e)},Q=()=>{uni.setStorage({key:"selectAddressCallback",data:{back:"/addon/yz_she/pages/evaluate/batch",pageState:{quantity:z.value,categoryId:$.value,selectedDelivery:T.value,selectedTime:M.value,selectedTimeSlot:O.value}},success(){console.log("保存页面状态:",{quantity:z.value,categoryId:$.value}),g({url:"/addon/yz_she/pages/address/index"})}})},R={"today-urgent":"尽快上门","today-afternoon":"今天 14:00-16:00","today-evening":"今天 16:00-18:00","tomorrow-morning":"明天 09:00-11:00","tomorrow-noon":"明天 11:00-13:00","tomorrow-afternoon":"明天 14:00-16:00","tomorrow-evening":"明天 16:00-18:00","dayafter-morning":"后天 09:00-11:00","dayafter-noon":"后天 11:00-13:00","dayafter-afternoon":"后天 14:00-16:00","dayafter-evening":"后天 16:00-18:00"},W=()=>{V.value?P.value=!0:y({title:"请先选择取件地址",icon:"none",duration:2e3})},X=()=>{P.value=!1},Y=e=>{L.value=e},Z=e=>{O.value=e;const a=R[e]||e;M.value=a,P.value=!1,console.log("选择时间:",a)},ee=()=>{B.value=!0},ae=()=>{B.value=!1};l((e=>{if(console.log("批量回收页面接收到的参数:",e),S.value=e||{},e&&e.category_id){const a=parseInt(e.category_id);!isNaN(a)&&a>0?($.value=a,console.log("获取到分类ID:",$.value)):(console.warn("分类ID无效:",e.category_id),$.value=1)}else console.warn("未传入分类ID，使用默认值"),$.value=1;(()=>{const e=new Date,a=new Date(e);a.setDate(e.getDate()+1);const l=new Date(e);l.setDate(e.getDate()+2),U.value=`${e.getMonth()+1}月${e.getDate()}日`,E.value=`${a.getMonth()+1}月${a.getDate()}日`,F.value=`${l.getMonth()+1}月${l.getDate()}日`})()}));const le=async()=>{var e;if(console.log("开始提交批量回收订单"),!j.value)try{if(!te())return;j.value=!0;const a=se();console.log("批量订单数据:",a),console.log("分类ID详细信息:",{"categoryId.value":$.value,"pageParams.category_id":null==(e=S.value)?void 0:e.category_id,"orderData.category_id":a.category_id,"typeof categoryId.value":typeof $.value,"typeof orderData.category_id":typeof a.category_id});const l=await q(a);if(1!==l.code)throw new Error(l.msg||"订单提交失败");console.log("批量回收订单创建成功:",l.data),y({title:"订单提交成功",icon:"success",duration:1e3}),setTimeout((()=>{k({url:`/addon/yz_she/pages/order/success/batch-success?recycleOrderId=${l.data.id}`})}),1e3)}catch(a){console.error("提交批量订单失败:",a),y({title:a.message||"提交失败，请重试",icon:"none",duration:3e3})}finally{j.value=!1}},te=()=>{if(z.value<1)return y({title:"请选择商品数量",icon:"none"}),!1;if("pickup"===T.value){if(!V.value)return y({title:"请选择取件地址",icon:"none"}),!1}else if("self"===T.value){if(!N.value)return y({title:"请选择快递公司",icon:"none"}),!1;if(!A.value)return y({title:"请输入快递单号",icon:"none"}),!1}return!0},se=()=>{var e;console.log("构建订单数据时的状态:",{categoryId:$.value,quantity:z.value,selectedDelivery:T.value,pageParams:S.value});let a=$.value;if(!a&&(null==(e=S.value)?void 0:e.category_id)&&(a=parseInt(S.value.category_id),console.log("从页面参数获取分类ID:",a)),!a){const e=h(),l=e[e.length-1];l&&l.options&&l.options.category_id&&(a=parseInt(l.options.category_id),console.log("从当前页面选项获取分类ID:",a))}(!a||isNaN(a)||a<=0)&&(a=1,console.warn("使用默认分类ID:",a)),console.log("最终使用的分类ID:",a);const l={category_id:a,brand_id:null,product_id:null,product_name:"批量回收商品",product_code:"",product_image:"",expected_price:0,voucher_amount:0,source_type:3,delivery_type:"pickup"===T.value?1:2,quantity:z.value,voucher_id:null,quote_order_id:null};return"pickup"===T.value&&V.value&&(l.pickup_address_id=V.value.id,l.pickup_contact_name=V.value.name,l.pickup_contact_phone=V.value.mobile,l.pickup_address_detail=V.value.full_address||V.value.address_detail||"",l.pickup_time=M.value||"尽快上门"),"self"===T.value&&(l.pickup_contact_name="放心星仓库",l.pickup_contact_phone="13060000687",l.admin_note=`用户自寄，快递公司：${N.value}，快递单号：${A.value}`,l.express_company=N.value,l.express_number=A.value,l.status=2),l};return t((()=>{(()=>{var e;console.log("页面显示，检查地址回调");const a=uni.getStorageSync("selectAddressCallback");if(a&&a.address_id){if(console.log("发现地址回调数据:",a),a.pageState){const l=a.pageState;console.log("恢复页面状态:",l),l.quantity&&(z.value=l.quantity,console.log("恢复数量:",z.value)),l.categoryId?($.value=l.categoryId,console.log("恢复分类ID:",$.value)):(null==(e=S.value)?void 0:e.category_id)&&!$.value&&($.value=parseInt(S.value.category_id),console.log("从页面参数恢复分类ID:",$.value)),l.selectedDelivery&&(T.value=l.selectedDelivery),l.selectedTime&&(M.value=l.selectedTime),l.selectedTimeSlot&&(O.value=l.selectedTimeSlot)}a.address_info&&(V.value=a.address_info,console.log("设置选中地址:",V.value)),uni.removeStorage({key:"selectAddressCallback"})}})()})),(e,a)=>{const l=x,t=C,g=w,y=b(D("u-icon"),I);return s(),o(t,{class:"batch-order-page"},{default:c((()=>[u(" 三步换钱 "),d(t,{class:"process-section"},{default:c((()=>[d(l,{class:"section-title"},{default:c((()=>[n("三步换钱")])),_:1}),d(t,{class:"process-steps"},{default:c((()=>[d(t,{class:"step-item"},{default:c((()=>[d(t,{class:"step-icon"},{default:c((()=>[n("📦")])),_:1}),d(l,{class:"step-text"},{default:c((()=>[n(i("pickup"===T.value?"包邮寄出":"自行寄出"),1)])),_:1})])),_:1}),d(t,{class:"step-arrow"},{default:c((()=>[n(">")])),_:1}),d(t,{class:"step-item"},{default:c((()=>[d(t,{class:"step-icon"},{default:c((()=>[n("📋")])),_:1}),d(l,{class:"step-text"},{default:c((()=>[n("专业质检估价")])),_:1})])),_:1}),d(t,{class:"step-arrow"},{default:c((()=>[n(">")])),_:1}),d(t,{class:"step-item"},{default:c((()=>[d(t,{class:"step-icon"},{default:c((()=>[n("💰")])),_:1}),d(l,{class:"step-text"},{default:c((()=>[n("确认回收秒到账")])),_:1})])),_:1})])),_:1})])),_:1}),u(" 回收数量设置 "),d(t,{class:"quantity-section"},{default:c((()=>[d(t,{class:"quantity-header"},{default:c((()=>[d(l,{class:"quantity-title"},{default:c((()=>[n("回收数量")])),_:1}),d(l,{class:"quantity-desc"},{default:c((()=>[n("最多可添加100件")])),_:1})])),_:1}),d(t,{class:"quantity-control"},{default:c((()=>[d(t,{class:"quantity-btn",onClick:G},{default:c((()=>[d(l,{class:"btn-text"},{default:c((()=>[n("-")])),_:1})])),_:1}),d(g,{class:"quantity-input",modelValue:z.value,"onUpdate:modelValue":a[0]||(a[0]=e=>z.value=e),type:"number",maxlength:3},null,8,["modelValue"]),d(t,{class:"quantity-btn",onClick:H},{default:c((()=>[d(l,{class:"btn-text"},{default:c((()=>[n("+")])),_:1})])),_:1})])),_:1})])),_:1}),u(" 配送方式 "),d(t,{class:"delivery-section"},{default:c((()=>[d(t,{class:"delivery-options"},{default:c((()=>[d(t,{class:r(["delivery-option",{active:"pickup"===T.value}]),onClick:a[1]||(a[1]=e=>K("pickup"))},{default:c((()=>[d(t,{class:"option-content"},{default:c((()=>[d(l,{class:"option-name"},{default:c((()=>[n("快递上门")])),_:1}),d(t,{class:"option-tag"},{default:c((()=>[d(l,{class:"tag-text"},{default:c((()=>[n("免费")])),_:1})])),_:1})])),_:1})])),_:1},8,["class"]),d(t,{class:r(["delivery-option",{active:"self"===T.value}]),onClick:a[2]||(a[2]=e=>K("self"))},{default:c((()=>[d(l,{class:"option-name"},{default:c((()=>[n("自行寄出")])),_:1})])),_:1},8,["class"])])),_:1}),u(" 快递上门内容 "),"pickup"===T.value?(s(),o(t,{key:0,class:"pickup-content"},{default:c((()=>[u(" 地址选择 "),d(t,{class:"address-item",onClick:Q},{default:c((()=>[d(t,{class:"address-icon"},{default:c((()=>[(s(),f("svg",{viewBox:"0 0 1024 1024",width:"32",height:"32"},[v("path",{d:"M512 85.333333c-164.949333 0-298.666667 133.717333-298.666667 298.666667 0 164.949333 298.666667 554.666667 298.666667 554.666667s298.666667-389.717333 298.666667-554.666667c0-164.949333-133.717333-298.666667-298.666667-298.666667z m0 405.333334c-58.88 0-106.666667-47.786667-106.666667-106.666667s47.786667-106.666667 106.666667-106.666667 106.666667 47.786667 106.666667 106.666667-47.786667 106.666667-106.666667 106.666667z",fill:"#0d7377"})]))])),_:1}),d(t,{class:"address-content"},{default:c((()=>[V.value?(s(),o(t,{key:1,class:"selected-address"},{default:c((()=>[d(l,{class:"address-name"},{default:c((()=>[n(i(V.value.name)+" "+i(V.value.mobile),1)])),_:1}),d(l,{class:"address-detail"},{default:c((()=>[n(i(V.value.full_address),1)])),_:1})])),_:1})):(s(),o(l,{key:0,class:"address-text"},{default:c((()=>[n("请选择取件地址")])),_:1}))])),_:1}),d(t,{class:"divider-line"}),d(l,{class:"address-action"},{default:c((()=>[n("地址簿")])),_:1})])),_:1}),u(" 预约时间 "),d(t,{class:r(["time-item",{disabled:!V.value}]),onClick:W},{default:c((()=>[d(t,{class:"time-icon"},{default:c((()=>[(s(),f("svg",{viewBox:"0 0 1024 1024",width:"32",height:"32"},[v("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m176.5 585.7l-28.6 39c-4.4 6.1-11.9 9.3-19.4 9.3-4.7 0-9.5-1.4-13.6-4.3L512 609.7l-114.9 84c-9.9 7.2-23.8 5.1-31-4.9l-28.6-39c-7.2-9.9-5.1-23.8 4.9-31L457 515.3V264c0-11 9-20 20-20h70c11 0 20 9 20 20v251.3l114.6 103.5c9.9 7.1 12 21 4.9 30.9z",fill:V.value?"#0d7377":"#ccc"},null,8,["fill"])]))])),_:1}),d(l,{class:"time-text"},{default:c((()=>[n("期望上门时间")])),_:1}),d(l,{class:r(["time-action",{disabled:!V.value}])},{default:c((()=>[n(i(V.value?M.value||"尽快上门":"请先选择地址")+" > ",1)])),_:1},8,["class"])])),_:1},8,["class"])])),_:1})):u("v-if",!0),u(" 自行寄出内容 "),"self"===T.value?(s(),o(t,{key:1,class:"self-content"},{default:c((()=>[u(" 收货地址 "),d(t,{class:"address-item"},{default:c((()=>[d(t,{class:"address-icon orange-bg"},{default:c((()=>[d(l,{class:"address-text-icon"},{default:c((()=>[n("收")])),_:1})])),_:1}),d(t,{class:"address-info"},{default:c((()=>[d(l,{class:"address-name"},{default:c((()=>[n("放心星仓库 13060000687")])),_:1}),d(l,{class:"address-detail"},{default:c((()=>[n("四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递")])),_:1})])),_:1}),d(l,{class:"copy-btn"},{default:c((()=>[n("复制")])),_:1})])),_:1}),u(" 快递公司 "),d(t,{class:"express-item",onClick:ee},{default:c((()=>[d(t,{class:"express-icon"},{default:c((()=>[d(y,{name:"car",color:"#0d7377",size:"32"})])),_:1}),d(l,{class:"express-text"},{default:c((()=>[n("快递公司")])),_:1}),d(l,{class:"express-action"},{default:c((()=>[n(i(N.value||"请选择快递公司")+" >",1)])),_:1})])),_:1}),u(" 快递单号 "),d(t,{class:"tracking-item"},{default:c((()=>[d(t,{class:"tracking-icon"},{default:c((()=>[d(y,{name:"order",color:"#0d7377",size:"32"})])),_:1}),d(l,{class:"tracking-text"},{default:c((()=>[n("快递单号")])),_:1}),d(g,{class:"tracking-input",modelValue:A.value,"onUpdate:modelValue":a[3]||(a[3]=e=>A.value=e),placeholder:"请输入快递单号",maxlength:"30"},null,8,["modelValue"])])),_:1})])),_:1})):u("v-if",!0)])),_:1}),u(" 时间选择弹窗 "),P.value?(s(),o(t,{key:0,class:"time-modal",onClick:X},{default:c((()=>[d(t,{class:"time-modal-content",onClick:a[18]||(a[18]=_((()=>{}),["stop"]))},{default:c((()=>[d(t,{class:"time-header"},{default:c((()=>[d(l,{class:"time-title"},{default:c((()=>[n("期望上门时间")])),_:1}),d(t,{class:"close-btn",onClick:X},{default:c((()=>[n("×")])),_:1})])),_:1}),u(" 左右分栏布局 "),d(t,{class:"time-container"},{default:c((()=>[u(" 左侧日期选择 "),d(t,{class:"date-sidebar"},{default:c((()=>[d(t,{class:r(["date-item",{active:"today"===L.value}]),onClick:a[4]||(a[4]=e=>Y("today"))},{default:c((()=>[d(l,{class:"date-name"},{default:c((()=>[n("今天")])),_:1}),d(l,{class:"date-desc"},{default:c((()=>[n(i(U.value),1)])),_:1})])),_:1},8,["class"]),d(t,{class:r(["date-item",{active:"tomorrow"===L.value}]),onClick:a[5]||(a[5]=e=>Y("tomorrow"))},{default:c((()=>[d(l,{class:"date-name"},{default:c((()=>[n("明天")])),_:1}),d(l,{class:"date-desc"},{default:c((()=>[n(i(E.value),1)])),_:1})])),_:1},8,["class"]),d(t,{class:r(["date-item",{active:"dayafter"===L.value}]),onClick:a[6]||(a[6]=e=>Y("dayafter"))},{default:c((()=>[d(l,{class:"date-name"},{default:c((()=>[n("后天")])),_:1}),d(l,{class:"date-desc"},{default:c((()=>[n(i(F.value),1)])),_:1})])),_:1},8,["class"])])),_:1}),u(" 右侧时间选择 "),d(t,{class:"time-content"},{default:c((()=>[u(" 今天的时间选项 "),"today"===L.value?(s(),o(t,{key:0,class:"time-slots"},{default:c((()=>[d(t,{class:r(["time-slot urgent",{active:"today-urgent"===O.value}]),onClick:a[7]||(a[7]=e=>Z("today-urgent"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("尽快上门")])),_:1}),d(l,{class:"slot-desc"},{default:c((()=>[n("工作日2小时内")])),_:1})])),_:1},8,["class"]),d(t,{class:r(["time-slot",{active:"today-afternoon"===O.value}]),onClick:a[8]||(a[8]=e=>Z("today-afternoon"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("14:00-16:00")])),_:1})])),_:1},8,["class"]),d(t,{class:r(["time-slot",{active:"today-evening"===O.value}]),onClick:a[9]||(a[9]=e=>Z("today-evening"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("16:00-18:00")])),_:1})])),_:1},8,["class"])])),_:1})):u("v-if",!0),u(" 明天的时间选项 "),"tomorrow"===L.value?(s(),o(t,{key:1,class:"time-slots"},{default:c((()=>[d(t,{class:r(["time-slot",{active:"tomorrow-morning"===O.value}]),onClick:a[10]||(a[10]=e=>Z("tomorrow-morning"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("09:00-11:00")])),_:1})])),_:1},8,["class"]),d(t,{class:r(["time-slot",{active:"tomorrow-noon"===O.value}]),onClick:a[11]||(a[11]=e=>Z("tomorrow-noon"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("11:00-13:00")])),_:1})])),_:1},8,["class"]),d(t,{class:r(["time-slot",{active:"tomorrow-afternoon"===O.value}]),onClick:a[12]||(a[12]=e=>Z("tomorrow-afternoon"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("14:00-16:00")])),_:1})])),_:1},8,["class"]),d(t,{class:r(["time-slot",{active:"tomorrow-evening"===O.value}]),onClick:a[13]||(a[13]=e=>Z("tomorrow-evening"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("16:00-18:00")])),_:1})])),_:1},8,["class"])])),_:1})):u("v-if",!0),u(" 后天的时间选项 "),"dayafter"===L.value?(s(),o(t,{key:2,class:"time-slots"},{default:c((()=>[d(t,{class:r(["time-slot",{active:"dayafter-morning"===O.value}]),onClick:a[14]||(a[14]=e=>Z("dayafter-morning"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("09:00-11:00")])),_:1})])),_:1},8,["class"]),d(t,{class:r(["time-slot",{active:"dayafter-noon"===O.value}]),onClick:a[15]||(a[15]=e=>Z("dayafter-noon"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("11:00-13:00")])),_:1})])),_:1},8,["class"]),d(t,{class:r(["time-slot",{active:"dayafter-afternoon"===O.value}]),onClick:a[16]||(a[16]=e=>Z("dayafter-afternoon"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("14:00-16:00")])),_:1})])),_:1},8,["class"]),d(t,{class:r(["time-slot",{active:"dayafter-evening"===O.value}]),onClick:a[17]||(a[17]=e=>Z("dayafter-evening"))},{default:c((()=>[d(l,{class:"slot-title"},{default:c((()=>[n("16:00-18:00")])),_:1})])),_:1},8,["class"])])),_:1})):u("v-if",!0)])),_:1})])),_:1})])),_:1})])),_:1})):u("v-if",!0),u(" 快递公司选择弹窗 "),B.value?(s(),o(t,{key:1,class:"express-modal",onClick:ae},{default:c((()=>[d(t,{class:"modal-content",onClick:a[19]||(a[19]=_((()=>{}),["stop"]))},{default:c((()=>[d(t,{class:"modal-header"},{default:c((()=>[d(l,{class:"modal-title"},{default:c((()=>[n("选择快递公司")])),_:1}),d(t,{class:"close-btn",onClick:ae},{default:c((()=>[n("×")])),_:1})])),_:1}),d(t,{class:"express-list"},{default:c((()=>[(s(),f(p,null,m(J,(e=>d(t,{class:"express-option",key:e,onClick:a=>(e=>{N.value=e,B.value=!1,console.log("选择快递公司:",e)})(e)},{default:c((()=>[d(l,{class:"express-name"},{default:c((()=>[n(i(e),1)])),_:2},1024),N.value===e?(s(),o(t,{key:0,class:"express-check"},{default:c((()=>[n("✓")])),_:1})):u("v-if",!0)])),_:2},1032,["onClick"]))),64))])),_:1})])),_:1})])),_:1})):u("v-if",!0),u(" 底部确认按钮 "),d(t,{class:"bottom-action"},{default:c((()=>[d(t,{class:"confirm-button",onClick:le},{default:c((()=>[d(l,{class:"confirm-text"},{default:c((()=>[n("确认回收")])),_:1})])),_:1})])),_:1})])),_:1})}}}),[["__scopeId","data-v-d0370ace"]]);export{z as default};
