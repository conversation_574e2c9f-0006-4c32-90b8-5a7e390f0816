<template>
    <view :style="warpCss">
        <view class="px-[30rpx] py-[30rpx]">
            <!-- 标题栏 -->
            <view class="flex items-center justify-between mb-[30rpx]">
                <view class="flex items-center">
                    <view class="text-[30rpx] font-500" :style="{ fontSize: diyComponent.titleSize * 2 + 'rpx', color: diyComponent.titleColor }">{{ diyComponent.title }}</view>
                </view>
                <view v-if="diyComponent.showMore && diyComponent.moreText" class="flex items-center" @click="handleMoreClick">
                    <text class="text-[24rpx]" :style="{ color: diyComponent.moreColor }">{{ diyComponent.moreText }}</text>
                    <text class="nc-iconfont nc-icon-youV6xx text-[24rpx] ml-[8rpx]" :style="{ color: diyComponent.moreColor }"></text>
                </view>
            </view>

            <!-- 评价滚动区域 -->
            <view class="h-[300rpx] overflow-hidden relative">
                <view class="h-full relative">
                    <view
                        class="absolute w-full transition-transform duration-500 ease-in-out"
                        :style="{ transform: `translateY(${-currentIndex * 310}rpx)` }">
                        <view v-for="(review, index) in displayReviews" :key="'review-' + index" class="h-[300rpx] flex items-center">
                            <view class="w-full bg-white rounded-[16rpx] p-[24rpx] shadow-sm relative overflow-hidden">
                                <!-- 背景渐变 -->
                                <view class="absolute inset-0 opacity-30 rounded-[16rpx]"
                                      :style="{ background: `linear-gradient(135deg, ${diyComponent.bgGradient.startColor}, ${diyComponent.bgGradient.endColor})` }"></view>
                                
                                <!-- 内容区域 -->
                                <view class="relative z-10">
                                    <!-- 用户信息 -->
                                    <view class="flex items-center mb-[16rpx]">
                                        <image v-if="review.avatar" class="w-[60rpx] h-[60rpx] rounded-full mr-[16rpx]" :src="img(review.avatar)" mode="aspectFill" />
                                        <view v-else class="w-[60rpx] h-[60rpx] rounded-full mr-[16rpx] bg-[#F5F5F5] flex items-center justify-center">
                                            <text class="nc-iconfont nc-icon-yonghuV6xx text-[32rpx] text-[#999999]"></text>
                                        </view>
                                        <view class="flex-1">
                                            <view class="flex items-center">
                                                <text class="text-[28rpx] font-500 text-[#333333] mr-[16rpx]">{{ review.nickname }}</text>
                                                <text class="text-[24rpx] text-[#666666]">{{ review.time }}</text>
                                                
                                            </view>
                                        </view>
                                        
                                    </view>

                                    <!-- 评价内容 -->
                                    <view class="text-[26rpx] text-[#333333] leading-[1.5] mb-[16rpx]">{{ review.content }}</view>

                                    <!-- 价格信息 -->
                                    <view class="flex items-center justify-between">
                                        <view class="flex items-center">
                                            <text class="text-[24rpx] text-[#666666] mr-[8rpx]">预估价¥</text>
                                            <text class="text-[24rpx] text-[#666666] line-through mr-[16rpx]">{{ review.originalPrice }}</text>
                                            <text class="text-[24rpx] text-[#666666] mr-[8rpx]">最终鉴定价</text>
                                            <text class="text-[32rpx] font-600" :style="{ color: review.priceColor }">¥{{ review.finalPrice }}</text>
                                        </view>
                                        
                                        <!-- 印章 -->
                                        <view class="relative">
                                            <image v-if="review.stampImage" class="w-[80rpx] h-[80rpx]" :src="img(review.stampImage)" mode="aspectFit" />
                                            <view v-else class="w-[80rpx] h-[80rpx] rounded-full border-[4rpx] border-[#FF4444] flex items-center justify-center transform rotate-12">
                                                <text class="text-[20rpx] text-[#FF4444] font-600">专业<br/>鉴定</text>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import useDiyStore from '@/app/stores/diy';
import { img } from '@/utils/common';

const props = defineProps(['component', 'index']);
const diyStore = useDiyStore();

const diyComponent = computed(() => {
    if (diyStore.mode == 'decorate') {
        return diyStore.value[props.index];
    } else {
        return props.component;
    }
})

const currentIndex = ref(0)
let scrollTimer: any = null

// 复制评价数据以实现无限滚动
const displayReviews = computed(() => {
    const reviews = diyComponent.value.reviews || []
    if (reviews.length === 0) return []

    // 复制数据以实现无限滚动效果
    return [...reviews, ...reviews]
})

const warpCss = computed(() => {
    var style = '';
    if (diyComponent.value.componentStartBgColor) {
        if (diyComponent.value.componentStartBgColor && diyComponent.value.componentEndBgColor) style += `background:linear-gradient(${ diyComponent.value.componentGradientAngle },${ diyComponent.value.componentStartBgColor },${ diyComponent.value.componentEndBgColor });`;
        else style += 'background-color:' + diyComponent.value.componentStartBgColor + ';';
    }
    if (diyComponent.value.componentBgUrl) {
        style += 'background-image:url(' + img(diyComponent.value.componentBgUrl) + ');';
        style += 'background-size: 100% 100%;';
        style += 'background-repeat: no-repeat;';
    }

    if (diyComponent.value.topRounded) style += 'border-top-left-radius:' + diyComponent.value.topRounded * 2 + 'rpx;';
    if (diyComponent.value.topRounded) style += 'border-top-right-radius:' + diyComponent.value.topRounded * 2 + 'rpx;';
    if (diyComponent.value.bottomRounded) style += 'border-bottom-left-radius:' + diyComponent.value.bottomRounded * 2 + 'rpx;';
    if (diyComponent.value.bottomRounded) style += 'border-bottom-right-radius:' + diyComponent.value.bottomRounded * 2 + 'rpx;';
    return style;
})

const handleMoreClick = () => {
    if (diyComponent.value.moreLink && diyComponent.value.moreLink.name) {
        diyStore.toRedirect(diyComponent.value.moreLink)
    }
}

const startAutoScroll = () => {
    if (!diyComponent.value.autoScroll || diyStore.mode === 'decorate') return

    const reviews = diyComponent.value.reviews || []
    if (reviews.length === 0) return

    // 使用配置的滚动速度作为切换间隔
    const speed = diyComponent.value.scrollSpeed || 3000

    scrollTimer = setInterval(() => {
        currentIndex.value++

        // 当到达原始数据的末尾时，重置到开始位置实现无限循环
        if (currentIndex.value >= reviews.length) {
            // 先切换到复制的第一条（无缝衔接）
            setTimeout(() => {
                currentIndex.value = 0
            }, 500) // 等待动画完成后重置
        }
    }, speed)
}

const stopAutoScroll = () => {
    if (scrollTimer) {
        clearInterval(scrollTimer)
        scrollTimer = null
    }
}

onMounted(() => {
    // 延迟启动滚动，确保组件完全渲染
    setTimeout(() => {
        startAutoScroll()
    }, 1000)
})

onUnmounted(() => {
    stopAutoScroll()
})

// 监听组件数据变化，重新启动滚动
watch(() => diyComponent.value.autoScroll, (newVal) => {
    stopAutoScroll()
    currentIndex.value = 0 // 重置索引
    if (newVal) {
        setTimeout(() => {
            startAutoScroll()
        }, 100)
    }
})

// 监听滚动速度变化
watch(() => diyComponent.value.scrollSpeed, () => {
    if (diyComponent.value.autoScroll) {
        stopAutoScroll()
        setTimeout(() => {
            startAutoScroll()
        }, 100)
    }
})

// 监听评价数据变化
watch(() => diyComponent.value.reviews, () => {
    stopAutoScroll()
    currentIndex.value = 0 // 重置索引
    if (diyComponent.value.autoScroll) {
        setTimeout(() => {
            startAutoScroll()
        }, 100)
    }
}, { deep: true })
</script>

<style>
</style>
