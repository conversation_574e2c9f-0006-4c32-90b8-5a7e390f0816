import{d as s,m as a,s as e,o as t,c as l,w as o,b as c,e as r,t as n,x as d,n as i,Q as u,k as m,A as p,P as g,a as f}from"./index-3caf046d.js";const h=s({__name:"close",setup(s){const h=a();return e((()=>h.site),((s,a)=>{s&&1==s.status&&f({url:"/app/pages/index/index",mode:"reLaunch"})})),(s,a)=>{const e=u,f=m;return t(),l(f,{class:"min-h-[100vh] bg-[var(--page-bg-color)] overflow-hidden",style:i(s.themeColor())},{default:o((()=>[c(f,{class:"empty-page"},{default:o((()=>[c(e,{class:"img",src:r(p)("static/resource/images/site/close.png"),model:"aspectFit"},null,8,["src"]),c(f,{class:"desc"},{default:o((()=>[n(d(r(g)("siteClose")),1)])),_:1})])),_:1})])),_:1},8,["style"])}}});export{h as default};
