import{d as e,K as a,ak as t,r,bC as l,a as p,i as o,j as s,o as n,c as u,w as d,O as i,b as c,t as y,x as v,e as m,P as _,ah as f,y as x,F as g,z as b,A as h,g as k,M as S,k as w,au as C,R as T}from"./index-3caf046d.js";import{_ as j}from"./u-image.04cba9a2.js";import{_ as I}from"./u-icon.ba193921.js";import{_ as P}from"./u-popup.1b30ffa7.js";import{p as R,g as O}from"./pay.1a29db5c.js";import{_ as z}from"./_plugin-vue_export-helper.1b428a4d.js";const A=z(e({__name:"pay",emits:["close","confirm"],setup(e,{expose:z,emit:A}){a()&&t.init();const E=r(!1),H=r(!1),U=r(null),$=r(""),B=()=>{var e,r,l,o;if(!uni.$u.test.isEmpty($.value))return"friendspay"==$.value?(p({url:"/app/pages/friendspay/share",param:{id:null==(e=U.value)?void 0:e.trade_id,type:null==(r=U.value)?void 0:r.trade_type},mode:"redirectTo"}),!1):void(H.value||(H.value=!0,R({trade_type:null==(l=U.value)?void 0:l.trade_type,trade_id:null==(o=U.value)?void 0:o.trade_id,type:$.value,openid:uni.getStorageSync("openid")||""}).then((e=>{var r,l,o,s,n,u;switch($.value){case"wechatpay":a()?(e.data.timestamp=e.data.timeStamp,delete e.data.timeStamp,t.pay({...e.data,success:()=>{K()},cancel:()=>{H.value=!1}})):(uni.setStorageSync("paymenting",{trade_type:null==(r=U.value)?void 0:r.trade_type,trade_id:null==(l=U.value)?void 0:l.trade_id}),location.href=e.data.h5_url);break;case"alipay":a()?p({url:"/app/pages/pay/browser",param:{trade_type:null==(o=U.value)?void 0:o.trade_type,trade_id:null==(s=U.value)?void 0:s.trade_id,alipay:encodeURIComponent(e.data.url)},mode:"redirectTo"}):(uni.setStorageSync("paymenting",{trade_type:null==(n=U.value)?void 0:n.trade_type,trade_id:null==(u=U.value)?void 0:u.trade_id}),location.href=e.data.url);break;default:if(e.data.url)return void p({url:e.data.url,param:e.data.param||{},mode:"redirectTo"});K()}})).catch((()=>{H.value=!1}))));S({title:_("pay.notHavePayType"),icon:"none"})};l("checkIsReturnAfterPayment",(()=>{const e=uni.getStorageSync("paymenting");uni.getStorageSync("paymenting")&&p({url:"/app/pages/pay/result",param:{trade_type:e.trade_type,trade_id:e.trade_id},mode:"redirectTo",success(){uni.removeStorageSync("paymenting")}})}));const F=r(!1),K=()=>{var e,a;A("confirm"),p({url:"/app/pages/pay/result",param:{trade_type:null==(e=U.value)?void 0:e.trade_type,trade_id:null==(a=U.value)?void 0:a.trade_id},mode:"redirectTo"})},M=()=>{uni.removeStorageSync("paymenting"),E.value=!1,A("close")};return z({open:(e,a,t="",r="")=>{if(F.value)return;F.value=!0,uni.setStorageSync("payReturn",encodeURIComponent(t));const l={};r&&(l.scene=r),O(e,a,l).then((e=>{let{data:a}=e;U.value=a,uni.$u.test.isEmpty(a)?S({title:_("pay.notObtainedInfo"),icon:"none"}):0!=a.money?($.value=a.pay_type_list[0]?a.pay_type_list[0].key:"",E.value=!0,F.value=!1):K()})).catch((()=>{F.value=!1}))}}),(e,a)=>{const t=w,r=o(s("u-image"),j),l=o(s("u-icon"),I),p=C,S=T,R=o(s("u-popup"),P);return n(),u(R,{show:E.value,round:10,onClose:M,closeable:!0,bgColor:"#fff",zIndex:"10081",closeOnClickOverlay:!1},{default:d((()=>[U.value?(n(),u(t,{key:0,class:"flex flex-col h-[65vh] popup-common",onTouchmove:a[0]||(a[0]=i((()=>{}),["prevent","stop"]))},{default:d((()=>[c(t,{class:"head"},{default:d((()=>[c(t,{class:"title"},{default:d((()=>[y(v(m(_)("pay.payTitle")),1)])),_:1}),c(t,{class:"flex items-end justify-center w-full text-xl font-bold py-[20rpx] price-font"},{default:d((()=>[c(t,{class:"text-base mr-[4rpx]"},{default:d((()=>[y(v(m(_)("currency")),1)])),_:1}),y(" "+v(m(f)(U.value.money)),1)])),_:1})])),_:1}),c(p,{"scroll-y":"true",class:"flex-1 pt-[20rpx]"},{default:d((()=>[c(t,{class:"flex text-[28rpx] px-[36rpx] py-[20rpx] mb-[10rpx]"},{default:d((()=>[c(t,{class:"text-[var(--text-color-light6)]"},{default:d((()=>[y(v(m(_)("pay.orderInfo")),1)])),_:1}),c(t,{class:"text-right flex-1 pl-[30rpx] truncate"},{default:d((()=>[y(v(U.value.body),1)])),_:1})])),_:1}),c(t,{class:"mx-[var(--popup-sidebar-m)] px-[30rpx] bg-white rounded-[20rpx] bg-[var(--temp-bg)]"},{default:d((()=>[U.value.pay_type_list.length?(n(!0),x(g,{key:0},b(U.value.pay_type_list,((e,a)=>(n(),u(t,{class:"pay-item py-[30rpx] flex items-center border-0 border-b border-solid border-[#eee]",key:a,onClick:a=>$.value=e.key},{default:d((()=>[c(r,{src:m(h)(e.icon),width:"50rpx",height:"50rpx"},null,8,["src"]),c(t,{class:"flex-1 px-[20rpx] text-[28rpx] font-500"},{default:d((()=>[y(v(e.name),1)])),_:2},1024),e.key==$.value?(n(),u(l,{key:0,name:"checkbox-mark",color:"var(--primary-color)"})):k("v-if",!0)])),_:2},1032,["onClick"])))),128)):(n(),u(t,{key:1,class:"py-[30rpx] text-center text-[24rpx] text-gray-subtitle"},{default:d((()=>[y(v(m(_)("pay.notHavePayType")),1)])),_:1}))])),_:1})])),_:1}),c(t,{class:"btn-wrap"},{default:d((()=>[c(S,{class:"primary-btn-bg btn","hover-class":"none",loading:H.value,onClick:B},{default:d((()=>[y(v(m(_)("pay.confirmPay")),1)])),_:1},8,["loading"])])),_:1})])),_:1})):k("v-if",!0)])),_:1},8,["show"])}}}),[["__scopeId","data-v-a154be19"]]);export{A as _};
