import{d as e,p as t,I as a,r as o,q as l,J as r,P as s,o as i,c as n,w as d,b as p,t as u,x as m,e as c,O as b,g,n as x,Y as _,Z as f,a as h,U as v,k as y,i as j,j as k,S,R as C,T,B as V}from"./index-3caf046d.js";import{_ as w}from"./u-input.2d8dc7a4.js";import{_ as P,a as F}from"./u-form.49dbb57f.js";import{_ as O}from"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import{_ as M,a as U}from"./u-checkbox-group.0328273c.js";import{t as q}from"./topTabbar.9217e319.js";import{_ as z}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-line.69c0c00f.js";import"./u-modal.8624728a.js";import"./u-loading-icon.255170b9.js";import"./u-popup.1b30ffa7.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-safe-bottom.98e092c5.js";const B=z(e({__name:"bind",setup(e){let z={};q().setTopTabbarParam({title:"",topStatusBar:{bgColor:"#fff",textColor:"#333"}}),t((()=>Object.keys(z).length?T(Number(z.height))+T(z.top)+T(8)+"rpx":"auto"));const B=a(),I=t((()=>B.info)),A=t((()=>V().login)),E=o(!1),J=o(!1),L=l({mobile:"",mobile_code:"",mobile_key:""}),R=o(!0);o(null),r((()=>{setTimeout((()=>{R.value=!1}),800),uni.getStorageSync("openid")&&Object.assign(L,{openid:uni.getStorageSync("openid")}),uni.getStorageSync("pid")&&Object.assign(L,{pid:uni.getStorageSync("pid")}),uni.getStorageSync("unionid")&&Object.assign(L,{unionid:uni.getStorageSync("unionid")})}));const K={mobile:[{type:"string",required:!0,message:s("mobilePlaceholder"),trigger:["blur","change"]},{validator(e,t,a){/^1[3-9]\d{9}$/.test(t)?a():a(new Error("请输入正确的手机号"))},message:s("mobileError"),trigger:["change","blur"]}],mobile_code:{type:"string",required:!0,message:s("codePlaceholder"),trigger:["blur","change"]}},N=()=>{J.value=!J.value},Y=o(null),Z=()=>{Y.value.validate().then((()=>{if(E.value)return;E.value=!0;(I.value?_:f)(L).then((e=>{I.value?(B.getMemberInfo(),h({url:"/app/pages/member/personal",mode:"redirectTo"})):(B.setToken(e.data.token),v().handleLoginBack())})).catch((()=>{E.value=!1}))}))};return(e,t)=>{const a=y,o=j(k("u-input"),w),l=j(k("u-form-item"),P),r=j(k("sms-code"),O),_=j(k("u-form"),F),f=j(k("u-checkbox"),M),v=j(k("u-checkbox-group"),U),T=S,V=C;return i(),n(a,{class:"w-screen h-screen flex flex-col",style:x(e.themeColor())},{default:d((()=>[p(a,{class:"mx-[60rpx]"},{default:d((()=>[p(a,{class:"pt-[140rpx] text-[50rpx] text-[#333]"},{default:d((()=>[u(m(c(s)("bindMobile")),1)])),_:1}),p(a,{class:"text-[26rpx] leading-[39rpx] text-[var(--text-color-light6)] mt-[16rpx] mb-[80rpx]"},{default:d((()=>[u(m(c(s)("bindMobileTip")),1)])),_:1}),p(_,{labelPosition:"left",model:L,errorType:"toast",rules:K,ref_key:"formRef",ref:Y},{default:d((()=>[p(a,{class:"h-[90rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6]"},{default:d((()=>[p(l,{label:"",prop:"mobile","border-bottom":!1},{default:d((()=>[p(o,{modelValue:L.mobile,"onUpdate:modelValue":t[0]||(t[0]=e=>L.mobile=e),type:"number",maxlength:"11",border:"none",placeholder:c(s)("mobilePlaceholder"),class:"!bg-transparent",disabled:R.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),p(a,{class:"h-[90rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:d((()=>[p(l,{label:"",prop:"mobile_code","border-bottom":!1},{default:d((()=>[p(o,{modelValue:L.mobile_code,"onUpdate:modelValue":t[2]||(t[2]=e=>L.mobile_code=e),type:"number",maxlength:"4",border:"none",placeholder:c(s)("codePlaceholder"),class:"!bg-transparent",disabled:R.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)]"},{suffix:d((()=>[p(r,{mobile:L.mobile,type:"bind_mobile",modelValue:L.mobile_key,"onUpdate:modelValue":t[1]||(t[1]=e=>L.mobile_key=e)},null,8,["mobile","modelValue"])])),_:1},8,["modelValue","placeholder","disabled"])])),_:1})])),_:1})])),_:1},8,["model"]),p(a,{class:"mt-[100rpx]"},{default:d((()=>[c(A).agreement_show?(i(),n(a,{key:0,class:"flex items-center mb-[20rpx] py-[10rpx]",onClick:b(N,["stop"])},{default:d((()=>[p(v,{onChange:N},{default:d((()=>[p(f,{activeColor:"var(--primary-color)",checked:J.value,shape:"circle",size:"24rpx",customStyle:{marginTop:"4rpx"}},null,8,["checked"])])),_:1}),p(a,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:d((()=>[p(T,null,{default:d((()=>[u(m(c(s)("agreeTips")),1)])),_:1}),p(T,{onClick:t[3]||(t[3]=b((e=>c(h)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:d((()=>[u("《"+m(c(s)("privacyAgreement"))+"》",1)])),_:1}),p(T,null,{default:d((()=>[u(m(c(s)("and")),1)])),_:1}),p(T,{onClick:t[4]||(t[4]=b((e=>c(h)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:d((()=>[u("《"+m(c(s)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])):g("v-if",!0),p(V,{class:"w-full h-[80rpx] !bg-[var(--primary-color)] text-[26rpx] rounded-[40rpx] leading-[80rpx] font-500 !text-[#fff]",onClick:Z},{default:d((()=>[u(m(c(s)("bind")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-34b5edf0"]]);export{B as default};
