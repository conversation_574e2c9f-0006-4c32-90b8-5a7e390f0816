const s={name:"",appid:"__UNI__ED923AB",description:"",versionName:"1.0.0",versionCode:"100",transformPx:!1,"app-plus":{usingComponents:!0,nvueStyleCompiler:"uni-app",compilerVersion:3,splashscreen:{alwaysShowBeforeRender:!0,waiting:!0,autoclose:!0,delay:0},modules:{},distribute:{android:{permissions:['<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>','<uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>','<uses-permission android:name="android.permission.VIBRATE"/>','<uses-permission android:name="android.permission.READ_LOGS"/>','<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>','<uses-feature android:name="android.hardware.camera.autofocus"/>','<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>','<uses-permission android:name="android.permission.CAMERA"/>','<uses-permission android:name="android.permission.GET_ACCOUNTS"/>','<uses-permission android:name="android.permission.READ_PHONE_STATE"/>','<uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>','<uses-permission android:name="android.permission.WAKE_LOCK"/>','<uses-permission android:name="android.permission.FLASHLIGHT"/>','<uses-feature android:name="android.hardware.camera"/>','<uses-permission android:name="android.permission.WRITE_SETTINGS"/>']},ios:{},sdkConfigs:{}}},quickapp:{},"mp-weixin":{appid:"",setting:{urlCheck:!1},usingComponents:!0,permission:{"scope.userLocation":{desc:"为了更好地为您提供服务"},"scope.writePhotosAlbum":{desc:"为了更好地为您提供服务"}},requiredPrivateInfos:["chooseLocation","getLocation","chooseAddress"],__usePrivacyCheck__:!0},"mp-alipay":{usingComponents:!0},"mp-baidu":{usingComponents:!0},"mp-toutiao":{usingComponents:!0},uniStatistics:{enable:!1},vueVersion:"3",h5:{router:{mode:"history",base:"/wap/"},sdkConfigs:{maps:{qqmap:{key:""}}},async:{loading:"",error:"",delay:0,timeout:3e3}},fallbackLocale:"zh-Hans"};export{s as m};
