import{d as a,I as e,p as l,r as t,J as s,_ as o,a2 as c,s as n,o as u,c as i,w as d,g as r,b as v,y as f,a_ as g,t as h,z as m,F as _,x as y,f as p,v as b,bN as k,bO as C,M as x,bG as I,U as w,be as z,k as S,al as L,S as j,e as B,A as M,Q as H,au as T}from"./index-3caf046d.js";import{d as U,e as $,f as N,c as R,h as V,i as A}from"./brand.3bc41d81.js";import{g as E}from"./quote.9b84c391.js";import{_ as F}from"./_plugin-vue_export-helper.1b428a4d.js";const O=F(a({__name:"index",setup(a){const T=e(),F=l((()=>T.info)),O=t(""),P=t({id:"",name:""}),q=()=>{const a=c(),e=a[a.length-1],l=e.options||{};return!l.brandId&&P.value&&P.value.id&&(l.brandId=P.value.id.toString()),console.log("获取完整页面参数:"),console.log("- 当前页面路由:",e.route),console.log("- URL参数:",l),console.log("- 当前brandInfo:",P.value),l},D=t(0);let G=null;const J=t([]),Q=t([]),K=t([]),W=t([]),X=()=>{G&&clearTimeout(G),O.value&&""!==O.value.trim()?G=setTimeout((()=>{Z()}),500):oa()},Y=()=>{G&&clearTimeout(G),O.value.trim()?Z():oa()},Z=async()=>{try{let a;if(k({title:"搜索中..."}),a=P.value&&P.value.id?await U(P.value.id,O.value):await $(O.value),C(),na(O.value),1===a.code&&a.data&&a.data.data&&a.data.data.length>0){const e=a.data.data;Q.value=e,W.value=e;const l=P.value?`在${P.value.name}品牌中`:"";x({title:`${l}找到 ${e.length} 个商品`,icon:"success"})}else{Q.value=[];const a=P.value?`${P.value.name}品牌中`:"";x({title:`${a}未找到该货号商品`,icon:"none"})}}catch(a){C(),Q.value=[],x({title:"搜索失败，请重试",icon:"none"})}},aa=()=>{I({title:"如何查找货号",content:"货号通常位于鞋盒标签、鞋舌标签或鞋垫下方。您也可以拍照上传，我们的专业评估师会帮您识别。",showCancel:!1,confirmText:"知道了"})},ea=a=>{if(console.log("选择商品:",a),!F.value){console.log("selectItem - 用户未登录，跳转到登录页面");const a=q();return console.log("selectItem - 保存的完整参数:",a),w().setLoginBack({url:"/addon/yz_she/pages/evaluate/index",param:a}),!1}z({url:`/addon/yz_she/pages/evaluate/detail?id=${a.id}`})},la=()=>{if(console.log("从index页面跳转到拍照估价"),!F.value){console.log("goToPhotoEvaluate - 用户未登录，跳转到登录页面");const a=q();return console.log("goToPhotoEvaluate - 保存的完整参数:",a),w().setLoginBack({url:"/addon/yz_she/pages/evaluate/index",param:a}),!1}let a=null;if(K.value.length>0){const e=K.value[0];e.brand&&(a=e.brand)}if(!a&&W.value.length>0){const e=W.value[0];e.brand&&(a=e.brand)}console.log("使用品牌信息:",a);const e={from:"index",brandId:a.id,brandName:a.name||"",brandLogo:a.logo||"",categoryId:a.category_id||""};console.log("存储跳转数据:",e),uni.setStorageSync("photoEvaluateData",e),z({url:"/addon/yz_she/pages/evaluate/photo?from=index"})},ta=()=>{z({url:"/addon/yz_she/pages/order/quote-list?status=3"})},sa=async()=>{try{let a;if(P.value&&P.value.id)a=await N(P.value.id,{limit:6});else{const e={limit:6,is_hot:1,status:1};a=await R(e)}1===a.code&&(K.value=a.data.data||[])}catch(a){console.error("加载热门回收商品失败:",a)}},oa=async()=>{try{let a;if(P.value&&P.value.id)a=await V(P.value.id,{limit:20});else{const e={limit:20,status:1};a=await A(e)}1===a.code&&(W.value=a.data.data||[])}catch(a){console.error("加载全部回收商品失败:",a)}};s((a=>{if(console.log("评估页面接收到的参数:",a),a.brandId){const e=uni.getStorageSync("selectedBrandInfo");console.log("从缓存获取的品牌信息:",e),e&&e.id==a.brandId?(P.value={id:e.id,name:e.name,logo:e.logo,hot_name:e.hot_name,category_id:e.category_id},console.log("使用缓存的品牌信息:",P.value)):(P.value={id:parseInt(a.brandId),name:a.brandName?decodeURIComponent(a.brandName):""},console.log("使用URL参数的品牌信息:",P.value))}})),o((async()=>{console.log("页面挂载时验证参数:"),console.log("- 当前brandInfo:",P.value);const a=c(),e=a[a.length-1].options||{};if(console.log("- 当前URL参数:",e),await(async()=>{try{const a=await E();1===a.code&&(D.value=a.data.count||0)}catch(a){console.error("获取购物车数量失败:",a),D.value=0}})(),e.brandId&&(!P.value||!P.value.id)){const a=uni.getStorageSync("selectedBrandInfo");a&&a.id==e.brandId&&(P.value=a,console.log("- 从缓存恢复brandInfo:",P.value))}da(),await Promise.all([sa(),oa()])})),n(O,(a=>{a&&""!==a.trim()||(Q.value=[],oa())}));const ca=()=>{O.value="",Q.value=[],oa()},na=a=>{if(!a||""===a.trim())return;const e=a.trim(),l=J.value.indexOf(e);l>-1&&J.value.splice(l,1),J.value.unshift(e),J.value.length>10&&(J.value=J.value.slice(0,10)),ia()},ua=()=>{J.value=[],uni.removeStorageSync("evaluate_search_history"),x({title:"已清空搜索历史",icon:"success"})},ia=()=>{try{uni.setStorageSync("evaluate_search_history",J.value)}catch(a){console.error("保存搜索历史失败:",a)}},da=()=>{try{const a=uni.getStorageSync("evaluate_search_history");a&&Array.isArray(a)&&(J.value=a)}catch(a){console.error("加载搜索历史失败:",a)}};return(a,e)=>{const l=S,t=L,s=j,o=H;return u(),i(l,{class:"evaluate-page"},{default:d((()=>[r(" 顶部搜索栏 "),v(l,{class:"search-section"},{default:d((()=>[v(l,{class:"search-container"},{default:d((()=>[v(l,{class:"search-box"},{default:d((()=>[v(l,{class:"search-icon"},{default:d((()=>[(u(),f("svg",{viewBox:"0 0 1024 1024",width:"20",height:"20"},[g("path",{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z",fill:"currentColor"})]))])),_:1}),v(t,{class:"search-input",type:"text",placeholder:"输入货号（一步估价，立即实现）",modelValue:O.value,"onUpdate:modelValue":e[0]||(e[0]=a=>O.value=a),onInput:X,onConfirm:Y},null,8,["modelValue"]),O.value?(u(),i(l,{key:0,class:"clear-icon",onClick:ca},{default:d((()=>[h("✕")])),_:1})):r("v-if",!0)])),_:1}),v(l,{class:"search-button",onClick:Y},{default:d((()=>[h("搜索")])),_:1})])),_:1})])),_:1}),r(" 搜索记录区域 "),O.value.trim()&&(J.value.length>0||Q.value.length>0)?(u(),i(l,{key:0,class:"search-history-section"},{default:d((()=>[r(" 搜索历史 "),J.value.length>0?(u(),i(l,{key:0,class:"history-container"},{default:d((()=>[v(l,{class:"history-header"},{default:d((()=>[v(s,{class:"history-title"},{default:d((()=>[h("搜索历史")])),_:1}),v(l,{class:"clear-history",onClick:ua},{default:d((()=>[v(s,{class:"clear-text"},{default:d((()=>[h("清空")])),_:1})])),_:1})])),_:1}),v(l,{class:"history-tags"},{default:d((()=>[(u(!0),f(_,null,m(J.value,((a,e)=>(u(),i(l,{class:"history-tag",key:e,onClick:e=>{return l=a,O.value=l,void Z();var l}},{default:d((()=>[v(s,{class:"tag-text"},{default:d((()=>[h(y(a),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):r("v-if",!0),r(" 搜索结果记录 "),Q.value.length>0?(u(),i(l,{key:1,class:"search-results-container"},{default:d((()=>[v(l,{class:"results-header"},{default:d((()=>[v(s,{class:"results-title"},{default:d((()=>[h("搜索到的商品 ("+y(Q.value.length)+")",1)])),_:1})])),_:1}),v(l,{class:"results-list"},{default:d((()=>[(u(!0),f(_,null,m(Q.value,(a=>(u(),i(l,{class:"result-item",key:a.id,onClick:e=>ea(a)},{default:d((()=>[v(l,{class:"result-image"},{default:d((()=>[v(o,{class:"product-image",src:a.image?B(M)(a.image):B(M)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1024),v(l,{class:"result-info"},{default:d((()=>[v(s,{class:"result-name"},{default:d((()=>[h(y(a.name),1)])),_:2},1024),v(s,{class:"result-code"},{default:d((()=>[h(y(a.code),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):r("v-if",!0)])),_:1})):r("v-if",!0),r(" 如何查找货号提示 "),p(v(l,{class:"help-section"},{default:d((()=>[v(l,{class:"help-content"},{default:d((()=>[v(s,{class:"help-text"},{default:d((()=>[h("如何查找货号")])),_:1}),v(l,{class:"help-icon",onClick:aa},{default:d((()=>[v(s,{class:"icon-text"},{default:d((()=>[h("?")])),_:1})])),_:1})])),_:1})])),_:1},512),[[b,!O.value.trim()]]),r(" 热门回收横向滚动区域 "),r("v-if",!0),r(" 全部回收商品列表 "),v(l,{class:"all-recycle-section"},{default:d((()=>[v(l,{class:"section-header"},{default:d((()=>[v(s,{class:"section-title"},{default:d((()=>[h("全部回收")])),_:1}),v(s,{class:"section-subtitle"},{default:d((()=>[h(y(W.value.length)+"个商品",1)])),_:1})])),_:1}),W.value.length>0?(u(),i(l,{key:0,class:"recycle-list"},{default:d((()=>[(u(!0),f(_,null,m(W.value,(a=>(u(),i(l,{class:"recycle-item",key:a.id,onClick:e=>ea(a)},{default:d((()=>[v(l,{class:"item-image"},{default:d((()=>[v(o,{class:"product-image",src:a.image?B(M)(a.image):B(M)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1024),v(l,{class:"item-info"},{default:d((()=>[v(s,{class:"item-name"},{default:d((()=>[h(y(a.name),1)])),_:2},1024),v(s,{class:"item-code"},{default:d((()=>[h(y(a.code),1)])),_:2},1024)])),_:2},1024),v(l,{class:"item-arrow"},{default:d((()=>[v(s,{class:"arrow-icon"},{default:d((()=>[h("›")])),_:1})])),_:1})])),_:2},1032,["onClick"])))),128))])),_:1})):(u(),i(l,{key:1,class:"empty-state"},{default:d((()=>[v(s,{class:"empty-text"},{default:d((()=>[h("暂无商品数据")])),_:1}),v(s,{class:"empty-hint"},{default:d((()=>[h("请先选择品牌或添加商品数据")])),_:1})])),_:1}))])),_:1}),r(" 底部操作按钮 "),v(l,{class:"bottom-action"},{default:d((()=>[v(l,{class:"action-buttons"},{default:d((()=>[v(l,{class:"no-code-button",onClick:aa},{default:d((()=>[v(s,{class:"no-code-text"},{default:d((()=>[h("找不到货号点这里")])),_:1})])),_:1}),v(l,{class:"camera-button",onClick:la},{default:d((()=>[v(l,{class:"camera-icon"},{default:d((()=>[(u(),f("svg",{viewBox:"0 0 1024 1024",width:"20",height:"20"},[g("path",{d:"M864 260H728l-32.4-90.8a32.07 32.07 0 0 0-30.2-21.2H358.6c-13.5 0-25.6 8.5-30.1 21.2L296 260H160c-44.2 0-80 35.8-80 80v456c0 44.2 35.8 80 80 80h704c44.2 0 80-35.8 80-80V340c0-44.2-35.8-80-80-80zM512 716c-88.4 0-160-71.6-160-160s71.6-160 160-160 160 71.6 160 160-71.6 160-160 160zm0-256c-53 0-96 43-96 96s43 96 96 96 96-43 96-96-43-96-96-96z",fill:"currentColor"})]))])),_:1}),v(s,{class:"camera-text"},{default:d((()=>[h("拍照估价")])),_:1})])),_:1})])),_:1})])),_:1}),r(" 购物车图标 "),v(l,{class:"cart-icon",onClick:ta},{default:d((()=>[v(l,{class:"cart-badge"},{default:d((()=>[h(y(D.value),1)])),_:1}),(u(),f("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24"},[g("path",{d:"M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12.1 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 0 0-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-17.4-28-34.6-28H96.5a35.3 35.3 0 1 0 0 70.6h125.9L246 312.8l58.1 281.3-74.8 122.1a34.96 34.96 0 0 0-3 36.8c6.1 11.9 18.4 19.4 31.5 19.4h62.8a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 0 0-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6z",fill:"currentColor"})]))])),_:1})])),_:1})}}}),[["__scopeId","data-v-ee042c10"]]);export{O as default};
