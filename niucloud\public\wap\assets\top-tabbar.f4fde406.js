import{m as t,J as e,K as l,ak as a,b9 as o,d as s,a9 as n,aE as i,p as r,a2 as c,r as d,_ as u,e as p,o as y,c as f,w as m,b as g,n as x,$ as _,g as k,t as v,x as h,O as S,a as b,a3 as w,as as C,av as B,k as I,Q as A,S as T,A as V,aw as z}from"./index-3caf046d.js";import{m as $}from"./manifest.ed582bbb.js";import{_ as j}from"./_plugin-vue_export-helper.1b428a4d.js";function F(s){const n=t();let i=s;1!=n.mapConfig.is_open&&(i=!1);const r=()=>{if(!i)return!1;uni.getStorageSync("manually_select_location_from_map")||l()&&n.mapConfig.is_open&&!uni.getStorageSync("location_address")&&a.init((()=>{a.getLocation((t=>{let e=t.latitude+","+t.longitude;c(e)}))}))},c=(t="")=>{if(!i)return!1;let e={latlng:""};e.latlng=t||n.diyAddressInfo.latitude+","+n.diyAddressInfo.longitude,o(e).then((t=>{if(t.data&&Object.keys(t.data).length){let l={},a=e.latlng.split(",");l.latitude=a[0],l.longitude=a[1],l.full_address=null!=t.data.province?t.data.province:"",l.full_address+=null!=t.data.city?t.data.city:"",l.full_address+=null!=t.data.district?t.data.district:"",l.full_address+=null!=t.data.community?t.data.community:"",l.province_id=t.data.province_id,l.province=t.data.province,l.city_id=t.data.city_id,l.city=t.data.city,l.district_id=t.data.district_id,l.district=t.data.district,l.community=t.data.community,l.formatted_addresses=t.data.formatted_addresses,n.setAddressInfo(l)}else n.setAddressInfo();setTimeout((()=>{uni.removeStorageSync("manually_select_location_from_map")}),500)}))},d=()=>{let t=uni.getStorageSync("location_address");if(t){var e=new Date;n.mapConfig.valid_time>0?t.is_expired=e.getTime()/1e3>t.valid_time:t.is_expired=!1}else t={is_expired:!1};return t};return{init:r,onLoad:(t="")=>{e((e=>{e&&e.latng&&c(e.latng),uni.removeStorageSync("manually_select_location_from_map"),"function"==typeof t&&t(e)}))},refresh:()=>{if(!i)return!1;!uni.getStorageSync("manually_select_location_from_map")&&uni.getStorageSync("location_address")&&(d()&&!d().is_expired?n.setAddressInfo(uni.getStorageSync("location_address")):uni.removeStorageSync("location_address")),!uni.getStorageSync("manually_select_location_from_map")&&d()&&d().is_expired&&r()},reposition:()=>{if(!i)return!1;n.diyAddressInfo&&n.diyAddressInfo.latitude,n.diyAddressInfo&&n.diyAddressInfo.longitude,uni.setStorageSync("manually_select_location_from_map",!0);let t=location.origin+location.pathname;window.location.href="https://apis.map.qq.com/tools/locpicker?search=1&type=0&backurl="+encodeURIComponent(t)+"&key="+$.h5.sdkConfigs.maps.qqmap.key+"&referer=myapp"}}}const R=j(s({__name:"top-tabbar",props:{data:{type:Object,default:{}},titleColor:{type:String,default:"#606266"},customBack:{type:Function,default:null},scrollBool:{type:[String,Number],default:-1},isBack:{type:Boolean,default:!0},isFill:{type:Boolean,default:!0}},setup(e,{expose:l}){const a=e;n().platform;const o=t(),s=i(),$=r((()=>{let t=!0;return t=!Q.value&&a.isFill,t})),j=r((()=>a.data)),R=r((()=>{if(a.data&&a.data.topStatusBar)return a.data.topStatusBar})),q=r((()=>{let t="";return a.isBack?(t+="padding-left: 30rpx;","style-1"==R.value.style&&(t+="padding-right:80rpx;")):("style-1"==R.value.style&&(t+="padding-right: 30rpx;"),t+="padding-left: 30rpx;"),t})),G=r((()=>{let t="";return t+="font-size: 28rpx;",t+=`color: ${L.value};`,"style-1"==R.value.style&&(t+=`text-align: ${R.value.textAlign};`),t})),L=r((()=>{let t="";return t=1==a.scrollBool?R.value.rollTextColor:R.value.textColor,t})),O=r((()=>{let t="";return t=1==a.scrollBool?R.value.rollBgColor:R.value.bgColor,t}));let U=uni.getStorageSync("componentsScrollValGroup");if(U)U.TopTabbar=0,uni.setStorageSync("componentsScrollValGroup",U);else{let t={TopTabbar:0};uni.setStorageSync("componentsScrollValGroup",t)}let H=c();const P=()=>{1===H.length?"app/pages/auth/index"===H[0].route?uni.getStorage({key:"loginBack",success:t=>{b(t?{...t.data,mode:"redirectTo"}:{url:"/app/pages/index/index",mode:"switchTab"})},fail:t=>{b({url:"/app/pages/index/index",mode:"switchTab"})}}):"function"==typeof a.customBack?a.customBack():b({url:"/app/pages/index/index",mode:"switchTab"}):"function"==typeof a.customBack?a.customBack():w()},D=r((()=>`calc(100vw - ${o.menuButtonInfo.right}px + ${o.menuButtonInfo.width}px + 10px)`)),E=d(0),J=z();let K=!1;R.value&&"style-4"==R.value.style&&(K=!0);const N=F(K);N.onLoad(),N.init();let Q=d(!1);u((()=>{C((()=>{B().in(J).select(".ns-navbar-wrap .u-navbar .content-wrap").boundingClientRect((t=>{E.value=t?t.height:0,s.$patch((t=>{t.topTabarHeight=E.value}))})).exec()})),N.refresh(),Q.value=uni.getStorageSync("imageAdsSameScreen")||!1}));return l({refresh:()=>{N.refresh()}}),(t,l)=>{const n=I,i=A,r=T;return"decorate"!=p(s).mode&&p(R)?(y(),f(n,{key:0,class:_(["ns-navbar-wrap",p(R).style])},{default:m((()=>[g(n,{class:_(["u-navbar",{fixed:-1!=a.scrollBool,absolute:-1==a.scrollBool}]),style:x({backgroundColor:p(O)})},{default:m((()=>[g(n,{class:"navbar-inner",style:x({width:"100%",height:E.value+"px"})},{default:m((()=>["style-1"==p(R).style?(y(),f(n,{key:0,class:_(["content-wrap",[p(R).textAlign]]),style:x(p(q))},{default:m((()=>[e.isBack?(y(),f(n,{key:0,class:"back-wrap -ml-[16rpx] text-[26px] nc-iconfont nc-icon-zuoV6xx",style:x({color:p(L)}),onClick:P},null,8,["style"])):k("v-if",!0),g(n,{class:"title-wrap",style:x(p(G))},{default:m((()=>[v(h(p(j).title),1)])),_:1},8,["style"])])),_:1},8,["class","style"])):k("v-if",!0),"style-2"==p(R).style?(y(),f(n,{key:1,class:"content-wrap",style:x(p(q)),onClick:l[0]||(l[0]=t=>p(s).toRedirect(p(R).link))},{default:m((()=>[e.isBack?(y(),f(n,{key:0,class:"back-wrap -ml-[16rpx] text-[26px] nc-iconfont nc-icon-zuoV6xx",style:x({color:p(L)}),onClick:P},null,8,["style"])):k("v-if",!0),g(n,{class:"title-wrap",style:x({color:p(R).textColor})},{default:m((()=>[g(n,null,{default:m((()=>[g(i,{src:p(V)(p(R).imgUrl),mode:"heightFix"},null,8,["src"])])),_:1}),g(n,{style:x({color:p(R).textColor})},{default:m((()=>[v(h(p(j).title),1)])),_:1},8,["style"])])),_:1},8,["style"])])),_:1},8,["style"])):k("v-if",!0),"style-3"==p(R).style?(y(),f(n,{key:2,style:x(p(q)),class:"content-wrap"},{default:m((()=>[e.isBack?(y(),f(n,{key:0,class:"back-wrap -ml-[16rpx] text-[26px] nc-iconfont nc-icon-zuoV6xx",style:x({color:p(L)}),onClick:P},null,8,["style"])):k("v-if",!0),g(n,{class:"title-wrap",onClick:l[1]||(l[1]=t=>p(s).toRedirect(p(R).link))},{default:m((()=>[g(i,{src:p(V)(p(R).imgUrl),mode:"heightFix"},null,8,["src"])])),_:1}),g(n,{class:"search",onClick:l[2]||(l[2]=t=>p(s).toRedirect(p(R).link)),style:x({height:p(o).menuButtonInfo.height-2+"px",lineHeight:p(o).menuButtonInfo.height-2+"px"})},{default:m((()=>[g(r,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 text-[24rpx] absolute left-[20rpx]"}),g(r,{class:"text-[24rpx]"},{default:m((()=>[v(h(p(R).inputPlaceholder),1)])),_:1})])),_:1},8,["style"]),g(n,{style:x({width:p(D)})},null,8,["style"])])),_:1},8,["style"])):k("v-if",!0),"style-4"==p(R).style?(y(),f(n,{key:3,style:x(p(q)),class:"content-wrap"},{default:m((()=>[e.isBack?(y(),f(n,{key:0,class:"back-wrap -ml-[16rpx] text-[26px] nc-iconfont nc-icon-zuoV6xx",style:x({color:p(L)}),onClick:P},null,8,["style"])):k("v-if",!0),g(r,{class:"nc-iconfont nc-icon-dizhiguanliV6xx text-[28rpx]",style:x({color:p(R).textColor})},null,8,["style"]),p(o).diyAddressInfo?(y(),f(n,{key:1,class:"title-wrap",onClick:l[3]||(l[3]=S((t=>p(N).reposition()),["stop"])),style:x({color:p(R).textColor})},{default:m((()=>[v(h(p(o).diyAddressInfo.community),1)])),_:1},8,["style"])):(y(),f(n,{key:2,class:"title-wrap",onClick:l[4]||(l[4]=S((t=>p(N).reposition()),["stop"])),style:x({color:p(R).textColor})},{default:m((()=>[v(h(p(o).defaultPositionAddress),1)])),_:1},8,["style"])),g(r,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx]",onClick:l[5]||(l[5]=S((t=>p(N).reposition()),["stop"])),style:x({color:p(R).textColor})},null,8,["style"])])),_:1},8,["style"])):k("v-if",!0)])),_:1},8,["style"])])),_:1},8,["class","style"]),k(" 解决fixed定位后导航栏塌陷的问题 "),p($)?(y(),f(n,{key:0,class:"u-navbar-placeholder",style:x({width:"100%",paddingTop:E.value+"px"})},null,8,["style"])):k("v-if",!0)])),_:1},8,["class"])):k("v-if",!0)}}}),[["__scopeId","data-v-fb5eb5f5"]]);export{R as _,F as u};
