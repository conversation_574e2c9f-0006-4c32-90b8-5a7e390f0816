import{d as a,r as t,J as e,ap as s,a as r,o as n,c as o,w as d,b as u,t as _,x as l,n as c,k as h}from"./index-3caf046d.js";const m=a({__name:"order_shipping",setup(a){const m=t(""),p=t("");e((a=>{a.merchant_trade_no?(m.value=a.merchant_trade_no,f()):p.value="缺少merchant_trade_no参数"}));const f=()=>{s({out_trade_no:m.value}).then((a=>{a.data&&a.data.path&&r({url:"/"+a.data.path,mode:"reLaunch"})}))};return(a,t)=>{const e=h;return n(),o(e,{style:c(a.themeColor())},{default:d((()=>[u(e,{class:"error-msg"},{default:d((()=>[_(l(p.value),1)])),_:1})])),_:1},8,["style"])}}});export{m as default};
