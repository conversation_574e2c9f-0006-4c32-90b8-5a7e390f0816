import{d as e,B as a,q as t,p as o,ay as l,s as r,b8 as n,as as s,av as c,i,j as u,e as d,o as b,y as p,b as m,w as f,F as k,z as y,c as v,A as h,g,D as x,a as C,aw as j,k as _}from"./index-3caf046d.js";import{_ as O}from"./u-tabbar-item.31141540.js";import{_ as S}from"./u-tabbar.38f37e13.js";import{_ as w}from"./_plugin-vue_export-helper.1b428a4d.js";const I=w(e({__name:"tabbar",props:{addon:{type:String,default:""},color:{type:Object,default:()=>({backgroundColor:"",textColor:"",textHoverColor:""})},border:{type:Boolean,default:!0}},setup(e){const w=e;let I=w.addon;const P=a();!I&&P.addon&&(I=P.addon);const B=t({}),L=o((()=>"/app/pages/index/tabbar"!=l())),z=()=>{let e=x(a().tabbarList);if(1==e.length)Object.assign(B,e[0]);else{let a=!1;for(let t=0;t<e.length;t++)if(e[t].key==I){Object.assign(B,e[t]),a=!0;break}if(!a){let a=0,o={};try{e&&e.forEach((e=>{"app"==e.info.type&&(a++,o=e)})),1==a&&Object.assign(B,o)}catch(t){}}}if(w.color)for(let a in w.color)w.color[a]&&B.value[a]&&(B.value[a]=w.color[a])};z(),r((()=>w.addon),((e,a)=>{e&&a&&e!=a&&z()}),{immediate:!0}),r((()=>w.color),((e,a)=>{e&&a&&e!=a&&z()}),{immediate:!0,deep:!0}),w.addon||r((()=>a().tabbarList),((e,a)=>{e&&z()}),{deep:!0,immediate:!0});const A=o((()=>{let e=n().params,a=[];for(let t in e)a.push(t+"="+e[t]);return"/"+l()+(a.length>0?"?"+a.join("&"):"")})),H=e=>{if(-1!=e.indexOf("http")||-1!=e.indexOf("http"))window.location.href=e;else{let a=n().params,t=[];for(let e in a)t.push(e+"="+a[e]);if(e=="/"+l()&&!t.length)return;C({url:e,mode:"reLaunch"})}},q=j();return s((()=>{c().in(q).select(".tab-bar-placeholder").boundingClientRect((e=>{let a={height:e?e.height:0};uni.setStorageSync("tabbarInfo",a)})).exec()})),(e,a)=>{const t=i(u("u-tabbar-item"),O),o=i(u("u-tabbar"),S),l=_;return d(L)&&B&&Object.keys(B).length?(b(),p(k,{key:0},[m(o,{value:d(A),zIndex:"9999",fixed:!0,placeholder:!0,safeAreaInsetBottom:!0,"inactive-color":B.value.textColor,"active-color":B.value.textHoverColor,border:w.border,class:"custom-tabbar"},{default:f((()=>[(b(!0),p(k,null,y(B.value.list,(e=>(b(),p(k,null,[1==B.value.type?(b(),v(t,{key:0,class:"py-[5rpx]","custom-style":{"background-color":B.value.backgroundColor},text:e.text,icon:d(h)(d(A)==e.link.url?e.iconSelectPath:e.iconPath),name:e.link.url,onClick:a=>H(e.link.url)},null,8,["custom-style","text","icon","name","onClick"])):g("v-if",!0),2==B.value.type?(b(),v(t,{key:1,class:"py-[5rpx]","custom-style":{"background-color":B.value.backgroundColor},icon:d(h)(d(A)==e.link.url?e.iconSelectPath:e.iconPath),name:e.link.url,onClick:a=>H(e.link.url)},null,8,["custom-style","icon","name","onClick"])):g("v-if",!0),3==B.value.type?(b(),v(t,{key:2,class:"py-[5rpx]","custom-style":{"background-color":B.value.backgroundColor},text:e.text,name:e.link.url,onClick:a=>H(e.link.url)},null,8,["custom-style","text","name","onClick"])):g("v-if",!0)],64)))),256))])),_:1},8,["value","inactive-color","active-color","border"]),m(l,{class:"tab-bar-placeholder"})],64)):g("v-if",!0)}}}),[["__scopeId","data-v-494f2735"]]);export{I as _};
