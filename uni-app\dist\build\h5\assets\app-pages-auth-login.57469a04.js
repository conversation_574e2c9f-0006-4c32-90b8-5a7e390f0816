import{d as e,p as l,r as a,I as t,B as r,J as o,L as s,M as i,a as u,K as n,q as p,_ as d,o as m,c,w as x,b as f,t as g,x as b,e as _,y as v,g as h,F as y,$ as k,O as w,n as j,a0 as C,a1 as V,U as T,a2 as F,a3 as L,k as P,i as S,j as A,S as U,R as z,P as q,T as R}from"./index-3caf046d.js";import{_ as B}from"./u-input.2d8dc7a4.js";import{_ as O,a as I}from"./u-form.49dbb57f.js";import{_ as J}from"./u-icon.ba193921.js";import{_ as K}from"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import{_ as M,a as $}from"./u-checkbox-group.0328273c.js";import{_ as E}from"./uni-popup.a1c93ccb.js";import{t as N}from"./topTabbar.9217e319.js";import{_ as D}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-line.69c0c00f.js";/* empty css                                                               */import"./u-modal.8624728a.js";import"./u-loading-icon.255170b9.js";import"./u-popup.1b30ffa7.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-safe-bottom.98e092c5.js";const G=D(e({__name:"login",setup(e){let D={};N().setTopTabbarParam({title:"",topStatusBar:{bgColor:"#fff",textColor:"#333"}}),l((()=>Object.keys(D).length?R(Number(D.height))+R(D.top)+R(8)+"rpx":"auto"));const G=a(!0),H=t(),Q=r(),W=a(""),X=a(!1),Y=a(!1),Z=a(),ee=a(!0),le=()=>{ee.value=!ee.value},ae=()=>{Z.value.close()},te=()=>{X.value=!0,Z.value.close(),pe()};o((async e=>{await Q.getLoginConfig(),s()||Q.login.is_username||Q.login.is_mobile||(i({title:"商家未开启普通账号登录",icon:"none"}),setTimeout((()=>{u({url:"/app/pages/index/index",mode:"reLaunch"})}),100)),e.type?"mobile"==e.type?Q.login.is_mobile&&(W.value=e.type,uni.getStorageSync("pid")&&Object.assign(re,{pid:uni.getStorageSync("pid")})):"username"==e.type&&Q.login.is_username&&(W.value=e.type):Q.login.is_username?W.value="username":Q.login.is_mobile&&(W.value="mobile"),n()&&Q.login.is_auth_register?Y.value=!0:Y.value=!1}));const re=p({username:"",password:"",mobile:"",mobile_code:"",mobile_key:""});d((()=>{setTimeout((()=>{G.value=!1}),800)}));const oe=()=>{X.value=!X.value},se=()=>{W.value="username"==W.value?"mobile":"username"},ie=a(!1),ue=l((()=>({username:{type:"string",required:"username"==W.value,message:q("usernamePlaceholder"),trigger:["blur","change"]},password:{type:"string",required:"username"==W.value,message:q("passwordPlaceholder"),trigger:["blur","change"]},mobile:[{type:"string",required:"mobile"==W.value,message:q("mobilePlaceholder"),trigger:["blur","change"]},{validator:(e,l)=>"mobile"!=W.value||uni.$u.test.mobile(l),message:q("mobileError"),trigger:["change","blur"]}],mobile_code:{type:"string",required:"mobile"==W.value,message:q("codePlaceholder"),trigger:["blur","change"]}}))),ne=a(null),pe=()=>{ne.value.validate().then((()=>{if(Q.login.agreement_show&&!X.value)return Z.value.open(),!1;if(ie.value)return;ie.value=!0;("username"==W.value?C:V)(re).then((e=>{H.setToken(e.data.token),T().handleLoginBack()})).catch((()=>{ie.value=!1}))}))},de=()=>{const e=F();if(e.length>1){"app/pages/auth/index"==e[e.length-2].route?L({delta:1}):u({url:"/app/pages/auth/index",mode:"redirectTo"})}else u({url:"/app/pages/auth/index",mode:"redirectTo"})};return(e,l)=>{const a=P,t=S(A("u-input"),B),r=S(A("u-form-item"),O),o=S(A("u-icon"),J),s=S(A("sms-code"),K),i=S(A("u-form"),I),n=S(A("u-checkbox"),M),p=S(A("u-checkbox-group"),$),d=U,C=z,V=S(A("uni-popup"),E);return W.value?(m(),c(a,{key:0,class:"w-screen h-screen flex flex-col",style:j(e.themeColor())},{default:x((()=>[f(a,{class:"mx-[60rpx]"},{default:x((()=>[f(a,{class:"pt-[140rpx] text-[44rpx] font-500 text-[#333]"},{default:x((()=>[g(b("username"==W.value?_(q)("accountLogin"):_(q)("mobileLogin")),1)])),_:1}),f(a,{class:"text-[26rpx] leading-[39rpx] text-[var(--text-color-light6)] mt-[16rpx] mb-[80rpx]"},{default:x((()=>[g(b("username"==W.value?_(q)("accountLoginTip"):_(q)("mobileLoginTip")),1)])),_:1}),f(i,{labelPosition:"left",model:re,errorType:"toast",rules:_(ue),ref_key:"formRef",ref:ne},{default:x((()=>["username"==W.value?(m(),v(y,{key:0},[f(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6]"},{default:x((()=>[f(r,{label:"",prop:"username","border-bottom":!1},{default:x((()=>[f(t,{modelValue:re.username,"onUpdate:modelValue":l[0]||(l[0]=e=>re.username=e),border:"none",maxlength:"40",placeholder:_(q)("usernamePlaceholder"),autocomplete:"off",class:"!bg-transparent",disabled:G.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),f(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:x((()=>[f(r,{label:"",prop:"password","border-bottom":!1},{default:x((()=>[f(t,{modelValue:re.password,"onUpdate:modelValue":l[1]||(l[1]=e=>re.password=e),border:"none",password:ee.value,maxlength:"40",placeholder:_(q)("passwordPlaceholder"),autocomplete:"new-password",class:"!bg-transparent",disabled:G.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:x((()=>[re.password?(m(),c(a,{key:0,onClick:le},{default:x((()=>[f(o,{name:ee.value?"eye-off":"eye-fill",color:"#b9b9b9",size:"20"},null,8,["name"])])),_:1})):h("v-if",!0)])),_:1},8,["modelValue","password","placeholder","disabled"])])),_:1})])),_:1})],64)):h("v-if",!0),"mobile"==W.value?(m(),v(y,{key:1},[f(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6]"},{default:x((()=>[f(r,{label:"",prop:"mobile","border-bottom":!1},{default:x((()=>[f(t,{modelValue:re.mobile,"onUpdate:modelValue":l[2]||(l[2]=e=>re.mobile=e),type:"number",maxlength:"11",border:"none",placeholder:_(q)("mobilePlaceholder"),autocomplete:"off",class:"!bg-transparent",disabled:G.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),f(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx] text-[26rpx]"},{default:x((()=>[f(r,{label:"",prop:"mobile_code","border-bottom":!1},{default:x((()=>[f(t,{modelValue:re.mobile_code,"onUpdate:modelValue":l[5]||(l[5]=e=>re.mobile_code=e),type:"number",maxlength:"4",border:"none",class:"!bg-transparent",fontSize:"26rpx",disabled:G.value,placeholder:_(q)("codePlaceholder"),placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:x((()=>[_(Q).login.agreement_show?(m(),c(s,{key:0,mobile:re.mobile,type:"login",modelValue:re.mobile_key,"onUpdate:modelValue":l[3]||(l[3]=e=>re.mobile_key=e),isAgree:X.value},null,8,["mobile","modelValue","isAgree"])):(m(),c(s,{key:1,mobile:re.mobile,type:"login",modelValue:re.mobile_key,"onUpdate:modelValue":l[4]||(l[4]=e=>re.mobile_key=e)},null,8,["mobile","modelValue"]))])),_:1},8,["modelValue","disabled","placeholder"])])),_:1})])),_:1})],64)):h("v-if",!0)])),_:1},8,["model","rules"]),"username"==W.value?(m(),c(a,{key:0,class:"text-right text-[24rpx] text-[var(--text-color-light9)] leading-[34rpx] mt-[20rpx]",onClick:l[6]||(l[6]=e=>_(u)({url:"/app/pages/auth/resetpwd"}))},{default:x((()=>[g(b(_(q)("resetpwd")),1)])),_:1})):h("v-if",!0),f(a,{class:k({"mt-[160rpx]":"username"!=W.value,"mt-[106rpx]":"username"==W.value})},{default:x((()=>[_(Q).login.agreement_show?(m(),c(a,{key:0,class:"flex items-center mb-[20rpx] py-[14rpx]",onClick:w(oe,["stop"])},{default:x((()=>[f(p,{onChange:oe},{default:x((()=>[f(n,{activeColor:"var(--primary-color)",checked:X.value,shape:"circle",size:"30rpx"},null,8,["checked"])])),_:1}),f(a,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap leading-[30rpx]"},{default:x((()=>[f(d,null,{default:x((()=>[g(b(_(q)("agreeTips")),1)])),_:1}),f(d,{onClick:l[7]||(l[7]=w((e=>_(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:x((()=>[g("《"+b(_(q)("privacyAgreement"))+"》",1)])),_:1}),f(d,null,{default:x((()=>[g(b(_(q)("and")),1)])),_:1}),f(d,{onClick:l[8]||(l[8]=w((e=>_(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:x((()=>[g("《"+b(_(q)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])):h("v-if",!0),f(C,{class:"w-full h-[80rpx] !bg-[var(--primary-color)] text-[26rpx] rounded-[40rpx] leading-[80rpx] font-500 !text-[#fff] !mx-[0]",loadingText:_(q)("logining"),onClick:pe},{default:x((()=>[g(b(_(q)("login")),1)])),_:1},8,["loadingText"]),f(a,{class:"flex items-center justify-between mt-[30rpx]"},{default:x((()=>["username"==W.value&&_(Q).login.is_mobile||"mobile"==W.value&&_(Q).login.is_username?(m(),c(a,{key:0,class:"text-[26rpx] text-[var(--text-color-light6)] leading-[34rpx]",onClick:se},{default:x((()=>[g(b("username"==W.value?_(q)("mobileLogin"):_(q)("accountLogin")),1)])),_:1})):h("v-if",!0),f(a,{class:"text-[26rpx] text-[#333] leading-[34rpx]",onClick:l[9]||(l[9]=e=>_(u)({url:"/app/pages/auth/register",param:{type:W.value}}))},{default:x((()=>[f(d,null,{default:x((()=>[g(b(_(q)("noAccount"))+",",1)])),_:1}),f(d,{class:"text-primary"},{default:x((()=>[g(b(_(q)("toRegister")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["class"])])),_:1}),f(V,{ref_key:"popupRef",ref:Z,type:"dialog"},{default:x((()=>[f(a,{class:"bg-[#fff] flex flex-col justify-between w-[600rpx] min-h-[280rpx] rounded-[var(--rounded-big)] box-border px-[35rpx] pt-[35rpx] pb-[8rpx] relative"},{default:x((()=>[f(a,{class:"flex justify-center"},{default:x((()=>[f(d,{class:"text-[33rpx] font-700"},{default:x((()=>[g(" 用户协议及隐私保护")])),_:1})])),_:1}),f(a,{class:"flex items-center mb-[20rpx] mt-[20rpx] py-[20rpx]",onClick:w(oe,["stop"])},{default:x((()=>[f(a,{class:"text-[26rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:x((()=>[f(d,null,{default:x((()=>[g(b(_(q)("agreeTips")),1)])),_:1}),f(d,{onClick:l[10]||(l[10]=w((e=>_(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:x((()=>[g("《"+b(_(q)("privacyAgreement"))+"》",1)])),_:1}),f(d,null,{default:x((()=>[g(b(_(q)("and")),1)])),_:1}),f(d,{onClick:l[11]||(l[11]=w((e=>_(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:x((()=>[g("《"+b(_(q)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"]),f(a,null,{default:x((()=>[f(a,{class:"w-[100%] flex justify-center bg-[var(--primary-color)] h-[70rpx] leading-[70rpx] text-[#fff] text-[26rpx] border-[0] font-500 rounded-[50rpx]",onClick:te},{default:x((()=>[g("同意并登录")])),_:1}),f(a,{class:"w-[100%] flex justify-center h-[70rpx] leading-[70rpx] text-[#999] text-[24rpx] border-[0] font-500 rounded-[50rpx]",onClick:ae},{default:x((()=>[g("不同意")])),_:1})])),_:1})])),_:1})])),_:1},512),Y.value?(m(),c(a,{key:0,class:"footer w-full"},{default:x((()=>[f(a,{class:"text-[26rpx] leading-[36rpx] text-[#333] text-center mb-[30rpx] font-400"},{default:x((()=>[g(b(_(q)("oneClicklogin")),1)])),_:1}),f(a,{class:"flex justify-center"},{default:x((()=>[f(C,{class:"h-[80rpx] w-[80rpx] text-[46rpx] !text-[#1AAB37] text-center !p-0 !bg-transparent leading-[79rpx] border-[2rpx] rounded-[50%] border-solid border-[#ddd] nc-iconfont nc-icon-weixinV6mm overflow-hidden",onClick:de})])),_:1})])),_:1})):h("v-if",!0)])),_:1},8,["style"])):h("v-if",!0)}}}),[["__scopeId","data-v-92b2da28"]]);export{G as default};
