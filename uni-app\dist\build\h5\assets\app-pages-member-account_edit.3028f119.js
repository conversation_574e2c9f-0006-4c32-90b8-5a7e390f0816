import{d as e,r as a,q as l,p as t,J as o,W as r,P as n,bY as c,o as d,c as p,w as u,b as s,y as i,t as m,x as _,e as f,F as x,g as b,n as h,ci as y,cj as g,a as k,aC as v,ch as A,aB as C,k as V,i as w,j,Q as P,S as T,R,au as W,A as N}from"./index-3caf046d.js";import{_ as M}from"./u-input.2d8dc7a4.js";import{_ as S,a as z}from"./u-form.49dbb57f.js";import{_ as U}from"./u-upload.********.js";import{_ as B}from"./u-modal.8624728a.js";import{_ as q}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-line.69c0c00f.js";import"./u-loading-icon.255170b9.js";import"./u-popup.1b30ffa7.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-safe-bottom.98e092c5.js";const D=q(e({__name:"account_edit",setup(e){const q=a(!1),D=a(null),F=a("get"),I=a(!1),E=l({account_id:0,account_type:"bank",bank_name:"",realname:"",account_no:"",transfer_payment_code:""}),J=t((()=>({realname:{type:"string",required:!0,message:"bank"==E.account_type?n("bankRealnamePlaceholder"):n("alipayRealnamePlaceholder"),trigger:["blur","change"]},bank_name:{type:"string",required:"bank"==E.account_type,message:n("bankNamePlaceholder"),trigger:["blur","change"]},transfer_payment_code:{validator(e,a,l){if(a&&a.length)l();else{let e="alipay"==E.account_type?n("alipayAccountImgPlaceholder"):n("wechatCodeAccountImgPlaceholder");l(new Error(e))}}}})));o((e=>{e.type&&(E.account_type=e.type),e.mode&&(F.value=e.mode),e.id&&(E.account_id=e.id||"",E.account_id?r({title:n("editAccountTitle")}):r({title:n("addAccountTitle")}),c({account_id:e.id}).then((e=>{e.data&&Object.keys(E).forEach((a=>{null!=e.data[a]&&(E[a]=e.data[a])}))})))}));const K=()=>{const e=E.account_id?y:g;D.value.validate().then((()=>{q.value||(q.value=!0,e(E).then((e=>{"get"==F.value?k({url:"/app/pages/member/account",param:{type:E.account_type,mode:F.value}}):k({url:"/app/pages/member/apply_cash_out",param:{account_id:E.account_id?E.account_id:e.data.id,type:E.account_type},mode:"redirectTo"})})).catch((()=>{q.value=!1})))}))},O=e=>{v({filePath:e.file.url,name:"file"}).then((e=>{e.data&&(E.transfer_payment_code="",E.transfer_payment_code=e.data.url)})).catch((()=>{}))},Q=e=>{E.transfer_payment_code=""},Y=()=>{A(E.account_id).then((()=>{k({url:"/app/pages/member/account",mode:"redirectTo"})}))},G=e=>{C({current:0,urls:[e]})};return(e,a)=>{const l=V,t=w(j("u-input"),M),o=w(j("u-form-item"),S),r=w(j("u-form"),z),c=P,y=T,g=w(j("u-upload"),U),k=R,v=W,A=w(j("u-modal"),B);return d(),p(l,{class:"w-screen h-screen bg-[var(--page-bg-color)] overflow-hidden",style:h(e.themeColor())},{default:u((()=>[s(v,{"scroll-y":"true"},{default:u((()=>[s(l,{class:"sidebar-margin card-template top-mar account pb-[20rpx]"},{default:u((()=>["bank"==E.account_type?(d(),i(x,{key:0},[s(l,{class:"text-center text-[32rpx] font-500 mt-[10rpx] text-[#333] leading-[42rpx]"},{default:u((()=>[m(_(E.account_id?f(n)("editBankCard"):f(n)("addBankCard")),1)])),_:1}),s(l,{class:"text-center text-[24rpx] mt-[16rpx] text-[var(--text-color-light9)]"},{default:u((()=>[m(_(E.account_id?f(n)("editBankCardTips"):f(n)("addBankCardTips")),1)])),_:1}),s(l,{class:"mt-[70rpx] px-[10rpx]"},{default:u((()=>[s(r,{labelPosition:"left",model:E,errorType:"toast",rules:f(J),ref_key:"formRef",ref:D},{default:u((()=>[s(l,null,{default:u((()=>[s(o,{label:f(n)("bankRealname"),prop:"realname",labelWidth:"200rpx"},{default:u((()=>[s(t,{modelValue:E.realname,"onUpdate:modelValue":a[0]||(a[0]=e=>E.realname=e),modelModifiers:{trim:!0},fontSize:"28rpx",maxlength:"30",border:"none",clearable:"",placeholder:f(n)("bankRealnamePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:f(n)("bankName"),prop:"bank_name",labelWidth:"200rpx"},{default:u((()=>[s(t,{modelValue:E.bank_name,"onUpdate:modelValue":a[1]||(a[1]=e=>E.bank_name=e),modelModifiers:{trim:!0},fontSize:"28rpx",maxlength:"30",border:"none",clearable:"",placeholder:f(n)("bankNamePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:f(n)("bankAccountNo"),prop:"account_no",labelWidth:"200rpx"},{default:u((()=>[s(t,{modelValue:E.account_no,"onUpdate:modelValue":a[2]||(a[2]=e=>E.account_no=e),modelModifiers:{trim:!0},fontSize:"28rpx",maxlength:"30",border:"none",clearable:"",placeholder:f(n)("bankAccountNoPlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1},8,["model","rules"])])),_:1})],64)):b("v-if",!0),"alipay"==E.account_type?(d(),i(x,{key:1},[s(l,{class:"text-center text-[32rpx] font-500 mt-[20rpx] text-[#333] leading-[42rpx]"},{default:u((()=>[m(_(E.account_id?f(n)("editAlipayAccount"):f(n)("addAlipayAccount")),1)])),_:1}),b(" <view class=\"text-center text-[28rpx] mt-[16rpx] text-[var(--text-color-light9)] leading-[36rpx]\">{{ formData.account_id ? t('editAlipayAccountTips') : t('addAlipayAccountTips') }}</view> "),s(l,{class:"mt-[70rpx] px-[10rpx]"},{default:u((()=>[s(r,{labelPosition:"left",model:E,labelWidth:"200rpx",errorType:"toast",rules:f(J),ref_key:"formRef",ref:D},{default:u((()=>[s(l,null,{default:u((()=>[s(o,{label:f(n)("alipayRealname"),prop:"realname"},{default:u((()=>[s(t,{modelValue:E.realname,"onUpdate:modelValue":a[3]||(a[3]=e=>E.realname=e),modelModifiers:{trim:!0},maxlength:"30",border:"none",fontSize:"28rpx",clearable:"",placeholder:f(n)("alipayRealnamePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:f(n)("alipayAccountNo"),prop:"account_no"},{default:u((()=>[s(t,{modelValue:E.account_no,"onUpdate:modelValue":a[4]||(a[4]=e=>E.account_no=e),modelModifiers:{trim:!0},border:"none",maxlength:"30",fontSize:"28rpx",clearable:"",placeholder:f(n)("alipayAccountNoPlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:"收款码",prop:"transfer_payment_code"},{default:u((()=>[E.transfer_payment_code?(d(),p(l,{key:0,class:"relative w-[160rpx] h-[160rpx]"},{default:u((()=>[s(c,{class:"w-[160rpx] h-[160rpx]",src:f(N)(E.transfer_payment_code),mode:"aspectFill",onClick:a[5]||(a[5]=e=>G(f(N)(E.transfer_payment_code)))},null,8,["src"]),s(l,{class:"absolute top-0 right-0 bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:Q},{default:u((()=>[s(y,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:1})])),_:1})):(d(),p(g,{key:1,onAfterRead:O,onDelete:Q,maxCount:1}))])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})],64)):b("v-if",!0),"wechat_code"==E.account_type?(d(),i(x,{key:2},[s(l,{class:"text-center text-[32rpx] font-500 mt-[20rpx] text-[#333] leading-[42rpx]"},{default:u((()=>[m(_(E.account_id?f(n)("editWechatCodeAccount"):f(n)("addWechatCodeAccount")),1)])),_:1}),b(" <view class=\"text-center text-[28rpx] mt-[16rpx] text-[var(--text-color-light9)] leading-[36rpx]\">{{ formData.account_id ? t('editWechatCodeAccountTips') : t('addWechatCodeAccountTips') }}</view> "),s(l,{class:"mt-[70rpx] px-[10rpx]"},{default:u((()=>[s(r,{labelPosition:"left",model:E,labelWidth:"200rpx",errorType:"toast",rules:f(J),ref_key:"formRef",ref:D},{default:u((()=>[s(l,null,{default:u((()=>[s(o,{label:f(n)("alipayRealname"),prop:"realname"},{default:u((()=>[s(t,{modelValue:E.realname,"onUpdate:modelValue":a[6]||(a[6]=e=>E.realname=e),modelModifiers:{trim:!0},maxlength:"30",border:"none",fontSize:"28rpx",clearable:"",placeholder:f(n)("alipayRealnamePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:f(n)("wechatCodeAccountNo"),prop:"account_no"},{default:u((()=>[s(t,{modelValue:E.account_no,"onUpdate:modelValue":a[7]||(a[7]=e=>E.account_no=e),modelModifiers:{trim:!0},border:"none",maxlength:"30",fontSize:"28rpx",clearable:"",placeholder:f(n)("wechatCodeAccountNoPlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:"收款码",prop:"transfer_payment_code"},{default:u((()=>[E.transfer_payment_code?(d(),p(l,{key:0,class:"relative w-[160rpx] h-[160rpx]"},{default:u((()=>[s(c,{class:"w-[160rpx] h-[160rpx]",src:f(N)(E.transfer_payment_code),mode:"aspectFill",onClick:a[8]||(a[8]=e=>G(f(N)(E.transfer_payment_code)))},null,8,["src"]),s(l,{class:"absolute top-0 right-0 bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:Q},{default:u((()=>[s(y,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:1})])),_:1})):(d(),p(g,{key:1,onAfterRead:O,onDelete:Q,maxCount:1}))])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})],64)):b("v-if",!0)])),_:1}),s(l,{class:"common-tab-bar-placeholder"}),s(l,{class:"common-tab-bar fixed left-[var(--sidebar-m)] right-[var(--sidebar-m)] bottom-[0]"},{default:u((()=>[s(k,{loading:q.value,class:"primary-btn-bg text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",onClick:K},{default:u((()=>[m(_(f(n)("save")),1)])),_:1},8,["loading"])])),_:1})])),_:1}),s(A,{show:I.value,content:f(n)("deleteConfirm"),confirmText:f(n)("confirm"),cancelText:f(n)("cancel"),showCancelButton:!0,onConfirm:Y,onCancel:a[9]||(a[9]=e=>I.value=!1),confirmColor:"var(--primary-color)"},null,8,["show","content","confirmText","cancelText"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-a52c840c"]]);export{D as default};
