import{d as e,r as s,o as t,c as a,w as l,b as o,O as r,t as u,x as i,e as c,y as n,z as p,F as d,g as x,k as f,au as v,S as m,i as _,j as b,P as y,$ as h,ao as g}from"./index-3caf046d.js";import{_ as k,a as w}from"./u-steps.e6c2645d.js";import{_ as j}from"./u-popup.1b30ffa7.js";import{c as C}from"./order.5c5c6bee.js";import{_ as I}from"./_plugin-vue_export-helper.1b428a4d.js";const T=I(e({__name:"logistics-tracking",setup(e,{expose:I}){const T=s(!1),z=s([]),O=s({}),V=async e=>{let s=await C(e);O.value=s.data},A=s(0),B=s(0),F=()=>{T.value=!1};return I({packageList:z,open:e=>{A.value=e.id,V(e),T.value=!0}}),(e,s)=>{const C=f,I=v,L=m,P=_(b("u-steps-item"),k),Q=_(b("u-steps"),w),S=_(b("u-popup"),j);return t(),a(C,{onTouchmove:s[3]||(s[3]=r((()=>{}),["prevent","stop"]))},{default:l((()=>[o(S,{show:T.value,mode:"bottom",round:10,onClose:F,closeable:!0,safeAreaInsetBottom:!0,onTouchmove:s[2]||(s[2]=r((()=>{}),["prevent","stop"]))},{default:l((()=>[Object.keys(O.value).length?(t(),a(C,{key:0,class:"h-[70vh] px-[24rpx] bg-page pb-[20rpx]",onTouchmove:s[1]||(s[1]=r((()=>{}),["prevent","stop"]))},{default:l((()=>[o(C,{class:"font-500 text-center text-[32rpx] leading-[104rpx] box-border h-[104rpx]"},{default:l((()=>[u(i(c(y)("detailedInformation")),1)])),_:1}),o(I,{"scroll-x":!0,"scroll-with-animation":"","scroll-into-view":"id"+(B.value>3?B.value-2:0)},{default:l((()=>[z.value.length>1?(t(),a(C,{key:0,class:"flex py-[22rpx] whitespace-nowrap"},{default:l((()=>[(t(!0),n(d,null,p(z.value,((e,s)=>(t(),a(C,{id:"id"+s,class:h(["text-[26rpx] leading-[36rpx] mr-[30rpx] text-[#626779]",{"!text-primary class-select":e.id==A.value}]),key:s,onClick:t=>((e,s)=>{A.value=e.id,B.value=s;let t={id:e.id,mobile:e.mobile};V(t)})(e,s)},{default:l((()=>[u(i(e.name),1)])),_:2},1032,["id","class","onClick"])))),128))])),_:1})):x("v-if",!0)])),_:1},8,["scroll-into-view"]),o(C,{class:"text-[28rpx] mt-[20rpx]"},{default:l((()=>[o(C,{class:"flex justify-between mb-[20rpx]"},{default:l((()=>["none_express"==O.value.sub_delivery_type?(t(),a(L,{key:0,class:"mr-[20rpx]"},{default:l((()=>[u("无需物流")])),_:1})):(t(),n(d,{key:1},[o(L,{class:"mr-[20rpx]"},{default:l((()=>[u(i(O.value.company.company_name),1)])),_:1}),o(C,null,{default:l((()=>[o(L,{class:"mr-[14rpx]"},{default:l((()=>[u(i(O.value.express_number),1)])),_:1}),o(L,{onClick:s[0]||(s[0]=e=>c(g)(O.value.express_number))},{default:l((()=>[u(i(c(y)("copy")),1)])),_:1})])),_:1})],64))])),_:1})])),_:1}),"express"==O.value.sub_delivery_type?(t(),a(C,{key:0,class:"parcel",style:{height:"53vh"}},{default:l((()=>[0==O.value.traces.success?(t(),a(C,{key:0,class:"h-[56vh] flex flex-col items-center justify-center"},{default:l((()=>[o(L,{class:"nc-iconfont nc-icon-daishouhuoV6xx text-[180rpx] text-[#bfbfbf]"}),o(C,{class:"text-[28rpx] text-[#bfbfbf] leading-8"},{default:l((()=>[u("暂无物流信息～～")])),_:1})])),_:1})):(t(),a(I,{key:1,"scroll-y":!0,style:{height:"53vh",padding:"20rpx","box-sizing":"border-box"},class:"bg-white rounded-md"},{default:l((()=>[o(Q,{current:0,dot:"",direction:"column",activeColor:"var(--primary-color)"},{default:l((()=>[(t(!0),n(d,null,p(O.value.traces.list,((e,s)=>(t(),a(P,{key:s+"id",title:e.remark,desc:e.datetime},null,8,["title","desc"])))),128))])),_:1})])),_:1}))])),_:1})):"none_express"==O.value.sub_delivery_type?(t(),a(C,{key:1,style:{height:"53vh"}},{default:l((()=>[o(C,{class:"h-[56vh] flex-col flex items-center justify-center"},{default:l((()=>[o(L,{class:"nc-iconfont nc-icon-daishouhuoV6xx text-[180rpx] text-[#bfbfbf]"}),o(C,{class:"text-[28rpx] text-[#bfbfbf] leading-8"},{default:l((()=>[u("无需物流～～")])),_:1})])),_:1})])),_:1})):x("v-if",!0)])),_:1})):x("v-if",!0)])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-706a88b4"]]);export{T as l};
