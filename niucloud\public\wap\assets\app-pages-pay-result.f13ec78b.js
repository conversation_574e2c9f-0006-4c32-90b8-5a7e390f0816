import{_ as t}from"./top-tabbar.f4fde406.js";import{d as e,r as a,J as s,o as l,c as o,w as r,b as n,e as u,$ as c,t as i,x as p,g as m,n as d,a as f,ag as x,i as _,j as v,S as y,k as b,R as j,P as g,ah as h}from"./index-3caf046d.js";import{_ as T}from"./u-loading-icon.255170b9.js";import{_ as w}from"./u-modal.8624728a.js";import{g as C}from"./pay.1a29db5c.js";import{t as k}from"./topTabbar.9217e319.js";import"./manifest.ed582bbb.js";import"./_plugin-vue_export-helper.1b428a4d.js";import"./u-line.69c0c00f.js";import"./u-popup.1b30ffa7.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-safe-bottom.98e092c5.js";const S=e({__name:"result",setup(e){const S=a(null),P=a(!1);let R="",V=0,B=0;let L=k().setTopTabbarParam({title:"",isBack:!1});s((t=>{R=t.trade_type,V=t.trade_id,$()}));const $=()=>{C(R,V,{}).then((t=>{if(!uni.$u.test.isEmpty(t.data)){if(1==t.data.status&&B<5)return P.value=!0,B++,void setTimeout((()=>{$()}),1e3);S.value=t.data,P.value=!1}}))},z=()=>{var t;const e=decodeURIComponent(uni.getStorageSync("payReturn"));f(e?{url:e,mode:"reLaunch"}:{url:x(),param:{code:null==(t=S.value)?void 0:t.out_trade_no},mode:"reLaunch"})};return(e,a)=>{const s=_(v("top-tabbar"),t),f=y,x=b,C=j,k=_(v("u-loading-icon"),T),R=_(v("u-modal"),w);return l(),o(x,{style:d(e.themeColor())},{default:r((()=>[S.value?(l(),o(x,{key:0,class:"w-screen h-screen flex flex-col items-center"},{default:r((()=>[n(s,{ref:"topTabbarRef",data:u(L)},null,8,["data"]),n(x,{class:"flex-1 flex flex-col items-center w-full pt-[180rpx]"},{default:r((()=>[n(x,{class:c(["flex items-baseline",{"text-[#06c05d]":2==S.value.status,"text-red":2!=S.value.status}])},{default:r((()=>[n(f,{class:c(["nc-iconfont -mb-[4rpx] !text-[32rpx]",{"nc-icon-duihaoV6mm":2==S.value.status,"nc-icon-tanhaoV6mm":2!=S.value.status}])},null,8,["class"]),n(f,{class:"text-[36rpx] ml-[16rpx] font-500"},{default:r((()=>[i(p(2==S.value.status?"支付成功":"支付失败"),1)])),_:1})])),_:1},8,["class"]),n(x,{class:"text-[56rpx] font-500 mt-[60rpx] price-font"},{default:r((()=>[n(f,{class:"text-[36rpx] mr-[6rpx]"},{default:r((()=>[i(p(u(g)("currency")),1)])),_:1}),n(f,null,{default:r((()=>[i(p(u(h)(S.value.money)),1)])),_:1})])),_:1})])),_:1}),n(x,{class:"pb-[260rpx]"},{default:r((()=>[n(C,{class:"w-[380rpx] !border-0 h-[80rpx] text-[28rpx] text-[#333] !bg-[#f2f2f2] flex-center font-500 rounded-[20rpx]",plain:!0,onClick:z},{default:r((()=>[i(p(2==S.value.status?u(g)("complete"):u(g)("close")),1)])),_:1})])),_:1})])),_:1})):m("v-if",!0),n(R,{show:P.value,showCancelButton:!0,confirmText:u(g)("pay.completePay"),cancelText:u(g)("pay.incompletePay"),onCancel:z,confirmColor:"var(--primary-color)"},{default:r((()=>[n(x,{class:"py-[20rpx]"},{default:r((()=>[n(k,{text:u(g)("pay.getting"),textSize:"16",mode:"circle",vertical:!0},null,8,["text"])])),_:1})])),_:1},8,["show","confirmText","cancelText"])])),_:1},8,["style"])}}});export{S as default};
