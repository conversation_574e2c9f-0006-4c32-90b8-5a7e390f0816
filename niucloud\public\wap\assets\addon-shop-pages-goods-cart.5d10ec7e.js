import{d as e,I as t,p as o,r as s,N as a,P as l,o as r,c as n,w as i,e as u,b as d,t as c,x as p,y as x,z as m,F as f,g as _,O as g,$ as v,n as h,U as b,M as k,a as y,Q as j,k as w,R as F,S as C,i as S,j as E,au as V,A as z,as as $,al as I}from"./index-3caf046d.js";import{_ as B}from"./u--image.eb573bce.js";import{_ as N}from"./u-number-box.8a6aafb5.js";import{_ as O}from"./u-swipe-action-item.94f3b080.js";import{_ as T}from"./u-swipe-action.7923d016.js";import{_ as U}from"./u-popup.1b30ffa7.js";import{_ as M}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{_ as R}from"./tabbar.2c31519d.js";import{u as A,g as L,a as D}from"./cart.ef6e9a72.js";import{b as G}from"./bind-mobile.25318c0e.js";import{u as J}from"./useGoods.9c8f1c51.js";import{n as K}from"./ns-goods-manjian.50c99c6a.js";import{_ as P}from"./ns-goods-recommend.vue_vue_type_script_setup_true_lang.94c7a4d0.js";import{_ as Q}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-safe-bottom.98e092c5.js";import"./u-loading-icon.255170b9.js";import"./u-tabbar-item.31141540.js";import"./u-tabbar.38f37e13.js";import"./u-form.49dbb57f.js";import"./u-line.69c0c00f.js";import"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import"./u-input.2d8dc7a4.js";import"./u-modal.8624728a.js";import"./u-checkbox-group.0328273c.js";import"./index.32583a71.js";import"./goods.6a81cb49.js";const q=Q(e({__name:"cart",setup(e){const Q=J(),q=t(),H=o((()=>q.info)),W=s(!0),X=s(!1),Y=s({goods_money:0,order_money:0,promotion_money:0}),Z=s([]),ee=s([]),te=s(!1),oe=s(!0),se=A(),ae=s(null),le=s(!1),re=()=>{L({}).then((({data:e})=>{Z.value=[],ee.value=[],e.forEach((e=>{e.checked=!1,e.goodsSku&&(e.goods.status&&0==e.goods.delete_time&&e.goodsSku.stock?(e.num>e.goodsSku.stock&&(e.num=e.goodsSku.stock),Z.value.push(e)):ee.value.push(e))})),fe(),ue(),W.value=!1,oe.value&&(oe.value=!1)})).catch((e=>{401==e.code&&(Z.value=[],ee.value=[],W.value=!1)}))};a((()=>{re(),se.getList()}));const ne=o((()=>{let e=0;return Z.value.forEach((t=>{t.checked&&(e+=1)})),e}));let ie=!1;const ue=()=>{let e=[];return Z.value.forEach((t=>{if(t.checked&&t.goodsSku){let o={};o.num=t.num,o.sku_id=t.sku_id,e.push(o)}})),e.length?!ie&&(ie=!0,void D({sku_ids:e}).then((({data:e})=>{Y.value.goods_money=e.goods_money,Y.value.order_money=e.order_money,Y.value.promotion_money=e.promotion_money,Z.value.forEach(((t,o)=>{for(let s=0;s<e.match_list.length;s++)if(t.goods_id==e.match_list[s].goods_id&&t.sku_id==e.match_list[s].sku_id&&t.manjian_info&&Object.keys(t.manjian_info).length){t.manjian_info.is_show=!0;let o=0;return t.manjian_info.rule_json.forEach(((t,a)=>{a==e.match_list[s].level?(t.is_show=!0,o++):t.is_show=!1})),void(t.manjian_info.is_show=0!=o)}t.manjian_info&&Object.keys(t.manjian_info).length&&(t.manjian_info.is_show=!1)})),ie=!1}))):(Y.value.order_money=0,Y.value.promotion_money=0,!1)},de=()=>{b().setLoginBack({url:"/addon/shop/pages/goods/cart"})},ce=e=>{let t={min:1,max:e.goodsSku.stock||1};if(e.goods.is_limit&&e.goods.max_buy){let o=0;o=e.goods.max_buy,o>e.goods.stock?t.max=e.goods.stock:o<=e.goods.stock&&(t.max=o)}return e.goods.min_buy>0&&(t.min=e.goods.min_buy),t},pe=s([{text:l("delete"),style:{backgroundColor:"var(--primary-color)",width:"100rpx",height:"100%",borderRadius:"10rpx"}}]),xe=s(),me=(e={})=>{e.checked=!e.checked,ue()},fe=()=>{const e=Z.value.length!=ne.value;Z.value.forEach((t=>{t.checked=e})),ue()},_e=s(null);s(uni.getStorageSync("isBindMobile"));const ge=()=>{if(!ne.value)return void k({title:"还没有选择商品",icon:"none"});const e=[];Z.value.forEach((t=>{t.checked&&e.push(t.id)})),uni.setStorage({key:"orderCreateData",data:{cart_ids:e},success(){y({url:"/addon/shop/pages/order/payment"})}})},ve=()=>{if(!ne.value)return void k({title:"还没有选择商品",icon:"none"});if(X.value)return;X.value=!0;const e=[];Z.value.forEach((t=>{t.checked&&e.push(t.id)})),se.delete(e,(()=>{re(),X.value=!1}))},he=()=>{if(X.value)return;X.value=!0;const e=ee.value.map((e=>e.id));se.delete(e,(()=>{re(),X.value=!1})),ee.value=[]},be=e=>{let t="0.00";return t=e.goodsSku.show_price,t};return(e,t)=>{const o=j,s=w,a=F,l=C,b=S(E("u--image"),B),A=I,L=S(E("u-number-box"),N),D=S(E("u-swipe-action-item"),O),J=S(E("u-swipe-action"),T),q=V,oe=S(E("u-popup"),U),re=S(E("loading-page"),M),ie=S(E("tabbar"),R);return r(),n(s,{style:h(e.themeColor())},{default:i((()=>[W.value?_("v-if",!0):(r(),n(s,{key:0,class:"bg-page min-h-[100vh] overflow-hidden flex flex-col"},{default:i((()=>[u(H)?Z.value.length||ee.value.length?(r(),n(s,{key:2,class:"flex-1 h-0"},{default:i((()=>[d(q,{class:"scroll-height","scroll-y":!0},{default:i((()=>[d(s,{class:"py-[var(--top-m)] sidebar-margin"},{default:i((()=>[Z.value.length?(r(),n(s,{key:0,class:"bg-[#fff] pb-[10rpx] box-border rounded-[var(--rounded-big)]"},{default:i((()=>[d(s,{class:"flex mx-[var(--rounded-big)] pt-[var(--pad-top-m)] justify-between items-center box-border font-400 text-[24rpx] mb-[24rpx] leading-[30rpx]"},{default:i((()=>[d(s,{class:"flex items-baseline text-[24rpx] text-[#333]"},{default:i((()=>[d(l,null,{default:i((()=>[c("共")])),_:1}),d(l,{class:"text-[32rpx] mx-[2rpx] text-[var(--price-text-color)]"},{default:i((()=>[c(p(Z.value.length),1)])),_:1}),d(l,null,{default:i((()=>[c("种商品")])),_:1})])),_:1}),d(l,{onClick:t[1]||(t[1]=e=>te.value=!te.value),class:"text-[var(--text-color-light6)] text-[24rpx]"},{default:i((()=>[c(p(te.value?"完成":"管理"),1)])),_:1})])),_:1}),d(J,{ref_key:"swipeActive",ref:xe},{default:i((()=>[(r(!0),x(f,null,m(Z.value,((e,a)=>(r(),x(f,null,[e.goodsSku?(r(),n(s,{key:0,class:"py-[20rpx] overflow-hidden w-full"},{default:i((()=>[d(D,{options:pe.value,onClick:t=>((e,t)=>{X.value||(X.value=!0,se.delete(t.id,(()=>{Z.value.splice(e,1),$((()=>{xe.value&&xe.value.closeOther()})),ue(),X.value=!1})))})(a,e)},{default:i((()=>[d(s,{class:"flex px-[var(--pad-sidebar-m)]",onClick:g((t=>me(e)),["stop"])},{default:i((()=>[d(s,{class:"self-center w-[34rpx] mr-[24rpx] h-[60rpx] flex items-center",onClick:g((t=>me(e)),["stop"])},{default:i((()=>[d(l,{class:v(["iconfont text-color text-[34rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden shrink-0",{iconxuanze1:e.checked,"bg-[#F5F5F5]":!e.checked}])},null,8,["class"])])),_:2},1032,["onClick"]),d(s,{class:"w-[200rpx] h-[200rpx] flex items-center justify-center rounded-[var(--goods-rounded-big)] overflow-hidden",onClick:t=>{y({url:"/addon/shop/pages/goods/detail",param:{goods_id:e.goods_id}})}},{default:i((()=>[d(b,{radius:"var(--goods-rounded-big)",width:"200rpx",height:"200rpx",src:u(z)(e.goodsSku.sku_image_thumb_mid||""),model:"aspectFill"},{error:i((()=>[d(o,{class:"w-[200rpx] h-[200rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:u(z)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1032,["onClick"]),d(s,{class:"flex flex-1 flex-col justify-between ml-[20rpx]"},{default:i((()=>[d(s,{class:"w-[100%] flex flex-col items-baseline"},{default:i((()=>[d(s,{class:"text-[#333] text-[28rpx] max-h-[80rpx] leading-[40rpx] multi-hidden font-400"},{default:i((()=>[c(p(e.goods.goods_name),1)])),_:2},1024),e.goodsSku&&e.goodsSku.sku_spec_format?(r(),n(s,{key:0,class:"box-border max-w-[376rpx] mt-[10rpx] px-[14rpx] h-[36rpx] leading-[36rpx] truncate text-[var(--text-color-light6)] bg-[#F5F5F5] text-[22rpx] rounded-[20rpx]"},{default:i((()=>[c(p(e.goodsSku.sku_spec_format),1)])),_:2},1024)):_("v-if",!0)])),_:2},1024),e.goods&&e.goods.goods_label_name&&e.goods.goods_label_name.length?(r(),n(s,{key:0,class:"flex flex-wrap mb-[auto]"},{default:i((()=>[(r(!0),x(f,null,m(e.goods.goods_label_name,((e,t)=>(r(),x(f,null,["icon"==e.style_type&&e.icon?(r(),n(o,{key:0,class:"img-tag",src:u(z)(e.icon),mode:"heightFix",onError:t=>u(Q).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?_("v-if",!0):(r(),n(s,{key:1,class:"base-tag",style:h(u(Q).baseTagStyle(e))},{default:i((()=>[c(p(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):_("v-if",!0),e.manjian_info&&Object.keys(e.manjian_info).length&&e.manjian_info.is_join?(r(),n(s,{key:1,class:"flex items-center mt-[8rpx] mb-[auto]",onClick:g((t=>(e=>{let t={};t.condition_type=e.condition_type,t.rule_json=e.rule_json,t.name=e.manjian_name,ae.value.open(t)})(e.manjian_info)),["stop"])},{default:i((()=>[d(s,{class:"bg-[var(--primary-color-light)] text-[var(--primary-color)] rounded-[6rpx] text-[20rpx] flex items-center justify-center w-[88rpx] h-[36rpx] mr-[6rpx]"},{default:i((()=>[c("满减送")])),_:1}),d(l,{class:"text-[22rpx] text-[#999]"},{default:i((()=>[c(p(e.manjian_info.manjian_name),1)])),_:2},1024)])),_:2},1032,["onClick"])):_("v-if",!0),d(s,{class:"flex justify-between items-end self-end mt-[10rpx] w-[100%]"},{default:i((()=>[d(s,{class:"text-[var(--price-text-color)] price-font truncate max-w-[200rpx]"},{default:i((()=>[d(l,{class:"text-[24rpx] font-500"},{default:i((()=>[c("￥")])),_:1}),d(l,{class:"text-[40rpx] font-500"},{default:i((()=>[c(p(parseFloat(be(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),d(l,{class:"text-[24rpx] font-500"},{default:i((()=>[c("."+p(parseFloat(be(e)).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),d(L,{modelValue:e.num,"onUpdate:modelValue":t=>e.num=t,min:ce(e).min,max:ce(e).max,integer:"",step:1,"input-width":"68rpx","input-height":"52rpx","button-size":"52rpx",disabledInput:"",onChange:e=>((e,t)=>{uni.$u.debounce((e=>{const o=Z.value[t];se.increase({id:o.id,goods_id:o.goods_id,sku_id:o.sku_id,stock:o.goodsSku.stock,sale_price:o.goodsSku.sale_price,num:o.num},0,ue())}),500)})(0,a)},{minus:i((()=>[d(s,{class:"relative w-[26rpx] h-[26rpx]",onClick:t=>(e=>{if(e.goods.is_limit&&e.goods.min_buy){let t=`该商品起购${e.goods.min_buy}件`;e.num<=e.goods.min_buy&&k({title:t,icon:"none"})}})(e)},{default:i((()=>[d(l,{class:v([{"text-[var(--text-color-light9)]":e.num===ce(e).min,"text-[#303133]":e.num!==ce(e).min},"text-[24rpx] absolute flex items-center justify-center -left-[20rpx] -bottom-[20rpx] -right-[20rpx] -top-[20rpx] font-500 nc-iconfont nc-icon-jianV6xx"])},null,8,["class"])])),_:2},1032,["onClick"])])),input:i((()=>[d(A,{class:"text-[#303133] text-[28rpx] mx-[14rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:t=>{return o=e,void setTimeout((()=>{(!o.num||o.num<=ce(o).min)&&(o.num=ce(o).min),o.num>=ce(o).max&&(o.num=ce(o).max),uni.$u.debounce((e=>{se.increase({id:o.id,goods_id:o.goods_id,sku_id:o.sku_id,stock:o.goodsSku.stock,sale_price:o.goodsSku.sale_price,num:Number(o.num)},0)}),500)}),0);var o},onBlur:e=>((e,t)=>{setTimeout((()=>{const e=Z.value[t];(!e.num||e.num<=ce(e).min)&&(e.num=ce(e).min),e.num>=ce(e).max&&(e.num=ce(e).max),uni.$u.debounce((t=>{se.increase({id:e.id,goods_id:e.goods_id,sku_id:e.sku_id,stock:e.goodsSku.stock,sale_price:e.goodsSku.sale_price,num:Number(e.num)},0,ue())}),500)}),0)})(0,a),onClick:t[2]||(t[2]=g((()=>{}),["stop"])),modelValue:e.num,"onUpdate:modelValue":t=>e.num=t},null,8,["onInput","onBlur","modelValue","onUpdate:modelValue"])])),plus:i((()=>[d(s,{class:"relative w-[26rpx] h-[26rpx]",onClick:t=>(e=>{if(e.num>=e.goods.stock)k({title:"商品库存不足",icon:"none"});else if(e.goods.is_limit){let t=`该商品单次限购${e.goods.max_buy}件`;1!=e.goods.limit_type&&(t=`该商品每人限购${e.goods.max_buy}件`),e.num>=e.goods.max_buy&&k({title:t,icon:"none"})}})(e)},{default:i((()=>[d(l,{class:v([{"text-[var(--text-color-light9)]":e.num===ce(e).max," text-[#303133]":e.num!==ce(e).max},"text-[24rpx] absolute flex items-center justify-center -left-[20rpx] -bottom-[20rpx] -right-[20rpx] -top-[20rpx] font-500 nc-iconfont nc-icon-jiahaoV6xx"])},null,8,["class"])])),_:2},1032,["onClick"])])),_:2},1032,["modelValue","onUpdate:modelValue","min","max","onChange"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1032,["options","onClick"])])),_:2},1024)):_("v-if",!0)],64)))),256))])),_:1},512)])),_:1})):_("v-if",!0),ee.value.length?(r(),n(s,{key:1,class:"bg-[#fff] pb-[10rpx] box-border rounded-[var(--rounded-big)] mt-[var(--top-m)]"},{default:i((()=>[d(s,{class:"flex mx-[var(--pad-sidebar-m)] pt-[var(--pad-top-m)] justify-between items-center box-border font-400 text-[#303133] text-[24rpx] mb-[24rpx] leading-[30rpx]"},{default:i((()=>[d(s,{class:"flex items-center text-[24rpx] text-[#333]"},{default:i((()=>[d(l,null,{default:i((()=>[c("共")])),_:1}),d(l,{class:"text-[28rpx] text-[var(--price-text-color)]"},{default:i((()=>[c(p(ee.value.length),1)])),_:1}),d(l,null,{default:i((()=>[c("件失效商品")])),_:1})])),_:1}),d(l,{class:"text-[var(--text-color-light6)] text-[24rpx]",onClick:he},{default:i((()=>[c("清空")])),_:1})])),_:1}),(r(!0),x(f,null,m(ee.value,((e,t)=>(r(),n(s,{class:"py-[20rpx] overflow-hidden"},{default:i((()=>[d(s,{class:"flex px-[var(--pad-sidebar-m)]"},{default:i((()=>[d(l,{class:"self-center iconfont iconxuanze1 text-[34rpx] mr-[32rpx] text-[#F5F5F5] rounded-[50%] overflow-hidden shrink-0"}),d(s,{class:"relative w-[200rpx] h-[200rpx] rounded-[var(--goods-rounded-big)] overflow-hidden"},{default:i((()=>[d(b,{radius:"var(--goods-rounded-big)",width:"200rpx",height:"200rpx",src:u(z)(e.goodsSku.sku_image_thumb_mid),model:"aspectFill"},{error:i((()=>[d(o,{class:"w-[200rpx] h-[200rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:u(z)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),0==e.goodsSku.stock?(r(),n(s,{key:0,class:"absolute left-0 top-0 w-[200rpx] h-[200rpx] leading-[200rpx] text-center",style:{"background-color":"rgba(0,0,0,0.3)"}},{default:i((()=>[d(l,{class:"text-[#fff] text-[28rpx]"},{default:i((()=>[c("已售罄")])),_:1})])),_:1})):_("v-if",!0),0!=e.goodsSku.stock?(r(),n(s,{key:1,class:"absolute left-0 top-0 w-[200rpx] h-[200rpx] leading-[200rpx] text-center",style:{"background-color":"rgba(0,0,0,0.3)"}},{default:i((()=>[d(l,{class:"text-[#fff] text-[28rpx]"},{default:i((()=>[c("已失效")])),_:1})])),_:1})):_("v-if",!0)])),_:2},1024),d(s,{class:"flex flex-1 flex-wrap ml-[20rpx]"},{default:i((()=>[d(s,{class:"w-[100%] flex flex-col items-baseline"},{default:i((()=>[d(s,{class:"text-[#333] text-[28rpx] max-h-[80rpx] leading-[40rpx] font-400 multi-hidden"},{default:i((()=>[c(p(e.goods.goods_name),1)])),_:2},1024),e.goodsSku&&e.goodsSku.sku_spec_format?(r(),n(s,{key:0,class:"box-border max-w-[376rpx] mt-[10rpx] px-[14rpx] h-[36rpx] leading-[36rpx] truncate text-[var(--text-color-light6)] bg-[#F5F5F5] text-[22rpx] rounded-[20rpx]"},{default:i((()=>[c(p(e.goodsSku.sku_spec_format),1)])),_:2},1024)):_("v-if",!0)])),_:2},1024),d(s,{class:"flex justify-between items-end self-end w-[100%]"},{default:i((()=>[d(s,{class:"text-[var(--price-text-color)] price-font"},{default:i((()=>[d(l,{class:"text-[24rpx] font-500"},{default:i((()=>[c("￥")])),_:1}),d(l,{class:"text-[36rpx] font-500"},{default:i((()=>[c(p(parseFloat(be(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),d(l,{class:"text-[24rpx] font-500"},{default:i((()=>[c("."+p(parseFloat(be(e)).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1})):_("v-if",!0)])),_:1}),d(P)])),_:1})])),_:1})):(r(),n(s,{key:1,class:"pb-[100rpx]"},{default:i((()=>[d(s,{class:"empty-page"},{default:i((()=>[d(o,{class:"img",src:u(z)("addon/shop/cart-empty.png"),model:"aspectFit"},null,8,["src"]),d(s,{class:"desc"},{default:i((()=>[c("赶紧去逛逛, 购买心仪的商品吧")])),_:1}),d(a,{shape:"circle",plain:"true",class:"btn",onClick:t[0]||(t[0]=e=>u(y)({url:"/addon/shop/pages/goods/list"}))},{default:i((()=>[c("去逛逛")])),_:1})])),_:1}),d(P)])),_:1})):(r(),n(s,{key:0,class:"pb-[100rpx]"},{default:i((()=>[d(s,{class:"empty-page"},{default:i((()=>[d(o,{class:"img",src:u(z)("static/resource/images/system/login.png"),model:"aspectFit"},null,8,["src"]),d(s,{class:"desc"},{default:i((()=>[c("暂未登录")])),_:1}),d(a,{shape:"circle",plain:"true",class:"btn",onClick:de},{default:i((()=>[c("去登录")])),_:1})])),_:1}),d(P)])),_:1}))])),_:1})),_(" 优惠明细 "),d(s,{onTouchmove:t[6]||(t[6]=g((()=>{}),["prevent","stop"]))},{default:i((()=>[d(oe,{class:"popup-type",show:le.value,onClose:t[5]||(t[5]=e=>le.value=!1)},{default:i((()=>[d(s,{class:"min-h-[200rpx] popup-common",onTouchmove:t[4]||(t[4]=g((()=>{}),["prevent","stop"]))},{default:i((()=>[d(s,{class:"flex justify-center items-center pt-[36rpx] pb-[56rpx] px-[26rpx] bg-[#fff] relative"},{default:i((()=>[d(l,{class:"text-[32rpx]"},{default:i((()=>[c("优惠明细")])),_:1}),d(l,{class:"nc-iconfont nc-icon-guanbiV6xx text-[var(--text-color-light6)] absolute text-[32rpx] right-[26rpx]",onClick:t[3]||(t[3]=e=>le.value=!1)})])),_:1}),d(q,{class:"h-[360rpx]","scroll-y":"true"},{default:i((()=>[d(s,{class:"flex justify-between h-[60rpx] px-[var(--pad-sidebar-m)]"},{default:i((()=>[d(l,{class:"text-[28rpx]"},{default:i((()=>[c("商品总额")])),_:1}),d(l,{class:"text-[28rpx]"},{default:i((()=>[c("￥"+p(Y.value.goods_money),1)])),_:1})])),_:1}),Number(Y.value.promotion_money)?(r(),n(s,{key:0,class:"flex justify-between h-[60rpx] px-[var(--pad-sidebar-m)]"},{default:i((()=>[d(l,{class:"text-[28rpx]"},{default:i((()=>[c("满减")])),_:1}),d(l,{class:"text-[28rpx] text-[red]"},{default:i((()=>[c("-￥"+p(Y.value.promotion_money),1)])),_:1})])),_:1})):_("v-if",!0)])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),Z.value.length?(r(),n(s,{key:1,class:"flex h-[96rpx] items-center bg-[#fff] fixed z-99999 left-0 right-0 bottom-[50px] pl-[30rpx] pr-[20rpx] box-solid mb-ios justify-between border-0 border-t-[2rpx] border-solid border-[#f6f6f6]"},{default:i((()=>[d(s,{class:"flex items-center",onClick:fe},{default:i((()=>[d(l,{class:v(["self-center iconfont text-color text-[34rpx] mr-[10rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden shrink-0",Z.value.length==u(ne)?"iconxuanze1":"bg-[#F5F5F5]"])},null,8,["class"]),d(l,{class:"font-400 text-[#303133] text-[26rpx]"},{default:i((()=>[c("全选")])),_:1})])),_:1}),d(s,{class:"flex items-center"},{default:i((()=>[te.value?(r(),n(s,{key:1,class:"flex-1 flex items-center justify-end"},{default:i((()=>[d(a,{class:"w-[180rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] m-0 rounded-full primary-btn-bg remove-border",onClick:ve},{default:i((()=>[c("删除")])),_:1})])),_:1})):(r(),n(s,{key:0,class:"flex-1 flex items-center justify-between"},{default:i((()=>[d(s,{class:"mr-[20rpx]"},{default:i((()=>[d(s,{class:"flex items-center text-[var(--price-text-color)] leading-[45rpx]"},{default:i((()=>[d(s,{class:"font-400 text-[#303133] text-[28rpx]"},{default:i((()=>[c("合计：")])),_:1}),d(l,{class:"text-[var(--price-text-color)] price-font text-[32rpx] font-bold"},{default:i((()=>[c("￥"+p(parseFloat(Y.value.order_money).toFixed(2)),1)])),_:1})])),_:1}),Number(Y.value.promotion_money)?(r(),n(s,{key:0,class:"flex items-center justify-end mt-[6rpx]",onClick:t[7]||(t[7]=e=>le.value=!0)},{default:i((()=>[d(l,{class:"text-[22rpx] text-[#666]"},{default:i((()=>[c("优惠明细")])),_:1}),d(l,{class:"iconfont iconjiantoushang text-[#666] !text-[22rpx] ml-[4rpx] font-bold"})])),_:1})):_("v-if",!0)])),_:1}),d(a,{class:"w-[180rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] m-0 rounded-full primary-btn-bg remove-border",onClick:ge},{default:i((()=>[c("结算")])),_:1})])),_:1}))])),_:1})])),_:1})):_("v-if",!0),d(re,{loading:W.value},null,8,["loading"]),d(K,{ref_key:"manjianShowRef",ref:ae},null,512),d(ie),_(" 强制绑定手机号 "),d(G,{ref_key:"bindMobileRef",ref:_e},null,512)])),_:1},8,["style"])}}}),[["__scopeId","data-v-a07df880"]]);export{q as default};
