<template>
    <view :style="warpCss">
        <view class="px-[30rpx] py-[30rpx]">
            <!-- 标题栏 -->
            <view class="flex items-center justify-between mb-[30rpx]">
                <view class="flex items-center">
                    <view class="font-600" :style="{ fontSize: diyComponent.titleSize * 2 + 'rpx', color: diyComponent.titleColor }">{{ diyComponent.title }}</view>
                </view>
                <view v-if="diyComponent.showMore && diyComponent.moreText" class="flex items-center" @click="handleMoreClick">
                    <text class="text-[26rpx]" :style="{ color: diyComponent.moreColor }">{{ diyComponent.moreText }}</text>
                    <text class="nc-iconfont nc-icon-youV6xx text-[26rpx] ml-[8rpx]" :style="{ color: diyComponent.moreColor }"></text>
                </view>
            </view>

            <!-- 问题列表 -->
            <view class="space-y-[20rpx]">
                <view v-for="(faq, index) in displayFaqList" :key="index" 
                      class="rounded-[16rpx] overflow-hidden border-[2rpx]"
                      :style="{ backgroundColor: diyComponent.itemBgColor, borderColor: diyComponent.borderColor }">
                    <view class="p-[24rpx] flex items-center justify-between" @click="toggleFaq(index)">
                        <view class="flex-1 pr-[20rpx]">
                            <text class="font-500" :style="{ fontSize: diyComponent.questionSize * 2 + 'rpx', color: diyComponent.questionColor }">{{ faq.question }}</text>
                        </view>
                        <view class="w-[40rpx] h-[40rpx] flex items-center justify-center">
                            <text class="nc-iconfont text-[24rpx] text-[#999999] transition-transform duration-300"
                                  :class="[expandedItems.includes(index) ? 'nc-icon-shangV6xx' : 'nc-icon-xiaV6xx']"></text>
                        </view>
                    </view>
                    
                    <!-- 回答内容 -->
                    <view v-if="expandedItems.includes(index)" class="px-[24rpx] pb-[24rpx] border-t-[1rpx]" 
                          :style="{ borderColor: diyComponent.borderColor }">
                        <text class="leading-[1.6]" :style="{ fontSize: diyComponent.answerSize * 2 + 'rpx', color: diyComponent.answerColor }">{{ faq.answer }}</text>
                    </view>
                </view>
            </view>

            <!-- 查看全部/收起按钮 -->
            <view v-if="diyComponent.faqList.length > diyComponent.defaultShowCount" 
                  class="mt-[40rpx] flex justify-center">
                <view class="px-[40rpx] py-[20rpx] rounded-[40rpx] border-[2rpx] border-[#E5E5E5] bg-[#FAFAFA]" 
                      @click="toggleShowAll">
                    <text class="text-[26rpx] text-[#666666] flex items-center">
                        {{ showAll ? diyComponent.collapseText : diyComponent.expandText }}
                        <text class="nc-iconfont ml-[8rpx] text-[20rpx] transition-transform duration-300"
                              :class="[showAll ? 'nc-icon-shangV6xx' : 'nc-icon-xiaV6xx']"></text>
                    </text>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import useDiyStore from '@/app/stores/diy';
import { img } from '@/utils/common';

const props = defineProps(['component', 'index']);
const diyStore = useDiyStore();

const diyComponent = computed(() => {
    if (diyStore.mode == 'decorate') {
        return diyStore.value[props.index];
    } else {
        return props.component;
    }
})

const showAll = ref(false)
const expandedItems = ref<number[]>([])

// 显示的问题列表
const displayFaqList = computed(() => {
    const faqList = diyComponent.value.faqList || []
    if (showAll.value || faqList.length <= diyComponent.value.defaultShowCount) {
        return faqList
    }
    return faqList.slice(0, diyComponent.value.defaultShowCount)
})

const warpCss = computed(() => {
    var style = '';
    if (diyComponent.value.componentStartBgColor) {
        if (diyComponent.value.componentStartBgColor && diyComponent.value.componentEndBgColor) style += `background:linear-gradient(${ diyComponent.value.componentGradientAngle },${ diyComponent.value.componentStartBgColor },${ diyComponent.value.componentEndBgColor });`;
        else style += 'background-color:' + diyComponent.value.componentStartBgColor + ';';
    }
    if (diyComponent.value.componentBgUrl) {
        style += 'background-image:url(' + img(diyComponent.value.componentBgUrl) + ');';
        style += 'background-size: 100% 100%;';
        style += 'background-repeat: no-repeat;';
    }

    if (diyComponent.value.topRounded) style += 'border-top-left-radius:' + diyComponent.value.topRounded * 2 + 'rpx;';
    if (diyComponent.value.topRounded) style += 'border-top-right-radius:' + diyComponent.value.topRounded * 2 + 'rpx;';
    if (diyComponent.value.bottomRounded) style += 'border-bottom-left-radius:' + diyComponent.value.bottomRounded * 2 + 'rpx;';
    if (diyComponent.value.bottomRounded) style += 'border-bottom-right-radius:' + diyComponent.value.bottomRounded * 2 + 'rpx;';
    return style;
})

const handleMoreClick = () => {
    if (diyComponent.value.moreLink && diyComponent.value.moreLink.name) {
        diyStore.toRedirect(diyComponent.value.moreLink)
    }
}

const toggleFaq = (index: number) => {
    const expandedIndex = expandedItems.value.indexOf(index)
    if (expandedIndex > -1) {
        expandedItems.value.splice(expandedIndex, 1)
    } else {
        expandedItems.value.push(index)
    }
}

const toggleShowAll = () => {
    showAll.value = !showAll.value
    // 切换显示状态时，清空已展开的项目
    expandedItems.value = []
}
</script>

<style>
</style>
