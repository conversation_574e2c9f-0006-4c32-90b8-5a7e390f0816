const a="立即提现",o="余额明细",e="提现到",t="请选择提现方式",c="微信默认钱包",s="提现金额",h="可提现余额",p="全部提现",l="最小提现金额为",i="手续费为",n="提现记录",T="提现至微信零钱",u="提现至微信零钱",y="提现至支付宝",O="请先添加支付宝账号",d="提现至银行卡",r="请先添加银行卡",m="提现至微信",A="请先添加微信号",w="支付宝账号",C="微信号",W="储蓄卡",b="异常操作",M="没有可用的提现方式",x="请输入提现金额",B="提现金额格式错误",N="提现金额超出可提现金额",f="提现金额小于最低提现金额",k="更换",E="提现设置未开启",P="添加",v={cashOutNow:a,balanceDetail:o,cashOutTo:"提现到",cashOutTypePlaceholder:t,wechatpay:c,cashOutMoneyTip:s,money:h,allTx:p,minWithdrawal:l,commissionTo:i,cashOutList:n,cashOutToWechat:T,cashOutToWechatTips:u,cashOutToAlipay:y,cashOutToAlipayTips:O,cashOutToBank:d,cashOutToBankTips:r,cashOutToWechatCode:m,cashOutToWechatCodeTips:A,alipayAccountNo:w,wechatCodeAccountNo:"微信号",debitCard:"储蓄卡",abnormalOperation:b,noAvailableCashOutType:M,applyMoneyPlaceholder:x,moneyformatError:B,applyMoneyExceed:N,applyMoneyBelow:f,replace:"更换",isOpenApply:E,toAdd:"添加"};export{b as abnormalOperation,w as alipayAccountNo,p as allTx,f as applyMoneyBelow,N as applyMoneyExceed,x as applyMoneyPlaceholder,o as balanceDetail,n as cashOutList,s as cashOutMoneyTip,a as cashOutNow,e as cashOutTo,y as cashOutToAlipay,O as cashOutToAlipayTips,d as cashOutToBank,r as cashOutToBankTips,T as cashOutToWechat,m as cashOutToWechatCode,A as cashOutToWechatCodeTips,u as cashOutToWechatTips,t as cashOutTypePlaceholder,i as commissionTo,W as debitCard,v as default,E as isOpenApply,l as minWithdrawal,h as money,B as moneyformatError,M as noAvailableCashOutType,k as replace,P as toAdd,C as wechatCodeAccountNo,c as wechatpay};
