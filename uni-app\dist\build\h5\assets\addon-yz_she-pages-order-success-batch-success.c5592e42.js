import{d as e,r as a,J as l,M as s,o as t,c as d,w as i,g as c,b as u,t as r,x as v,y as _,F as n,be as p,i as o,j as f,k as y,S as k}from"./index-3caf046d.js";import{_ as m}from"./u-icon.ba193921.js";import{d as b}from"./recycle_order.a252d983.js";import{_ as x}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */const h=x(e({__name:"batch-success",setup(e){const x=a({}),h=a({quantity:1,delivery:"pickup",deliveryText:"快递上门",address:"",time:"",estimateTotal:"0.00"}),w=a({orderNo:"",status:"",createTime:""}),g=a({});l((e=>{console.log("批量成功页面接收到的参数:",e),x.value=e||{},e.recycleOrderId?I(parseInt(e.recycleOrderId)):s({title:"订单信息缺失",icon:"none"})}));const I=async e=>{var a,l;try{console.log("加载批量回收订单详情:",e);const s=await b(e);if(1!==s.code)throw new Error(s.msg||"获取回收订单详情失败");g.value=s.data,console.log("批量回收订单详情:",s.data),w.value={orderNo:s.data.order_no||"",status:s.data.status_text||"",createTime:s.data.create_time_text||""},h.value={quantity:s.data.quantity||1,delivery:1===s.data.delivery_type?"pickup":"self",deliveryText:1===s.data.delivery_type?"快递上门":"自行寄出",address:(null==(a=s.data.pickupAddress)?void 0:a.full_address)||s.data.pickup_address_detail||"",time:s.data.pickup_time||"",estimateTotal:(null==(l=s.data.expected_price)?void 0:l.toFixed(2))||"0.00"}}catch(t){console.error("加载订单详情失败:",t),s({title:t.message||"加载订单信息失败",icon:"none"})}},A=()=>{var e;if(null==(e=g.value.pickupAddress)?void 0:e.full_address)return g.value.pickupAddress.full_address;if(g.value.pickup_address_detail)return g.value.pickup_address_detail;if(h.value.address)return h.value.address;if(g.value.pickupAddress){const e=g.value.pickupAddress;if(e.address)return e.address}const a=[];return g.value.pickup_province&&a.push(g.value.pickup_province),g.value.pickup_city&&a.push(g.value.pickup_city),g.value.pickup_district&&a.push(g.value.pickup_district),g.value.pickup_address&&a.push(g.value.pickup_address),a.length>0?a.join(""):""},q=()=>{g.value.id?p({url:`/addon/yz_she/pages/recycle_order/detail?id=${g.value.id}`}):s({title:"订单信息缺失",icon:"none"})};return(e,a)=>{const l=o(f("u-icon"),m),s=y,p=k;return t(),d(s,{class:"batch-success-page"},{default:i((()=>[c(" 批量回收信息卡片 "),u(s,{class:"product-card"},{default:i((()=>[u(s,{class:"batch-info"},{default:i((()=>[u(s,{class:"batch-header"},{default:i((()=>[u(s,{class:"batch-icon"},{default:i((()=>[u(l,{name:"shopping-cart-fill",color:"#16a085",size:"48"})])),_:1}),u(s,{class:"batch-details"},{default:i((()=>[u(p,{class:"batch-title"},{default:i((()=>[r("批量回收")])),_:1}),u(p,{class:"batch-status"},{default:i((()=>[r("提交成功")])),_:1})])),_:1}),u(s,{class:"quantity-display"},{default:i((()=>[u(p,{class:"quantity-number"},{default:i((()=>[r(v(h.value.quantity),1)])),_:1}),u(p,{class:"quantity-unit"},{default:i((()=>[r("件")])),_:1})])),_:1})])),_:1}),u(s,{class:"delivery-info"},{default:i((()=>{var e,a;return[u(s,{class:"delivery-item"},{default:i((()=>[u(p,{class:"delivery-label"},{default:i((()=>[r("配送方式")])),_:1}),u(p,{class:"delivery-value"},{default:i((()=>[r(v(h.value.deliveryText),1)])),_:1})])),_:1}),c(" 上门回收显示详细地址信息 "),"pickup"===h.value.delivery?(t(),_(n,{key:0},[g.value.pickup_contact_name||(null==(e=g.value.pickupAddress)?void 0:e.name)?(t(),d(s,{key:0,class:"delivery-item"},{default:i((()=>[u(p,{class:"delivery-label"},{default:i((()=>[r("联系人")])),_:1}),u(p,{class:"delivery-value"},{default:i((()=>{var e;return[r(v(g.value.pickup_contact_name||(null==(e=g.value.pickupAddress)?void 0:e.name)),1)]})),_:1})])),_:1})):c("v-if",!0),g.value.pickup_contact_phone||(null==(a=g.value.pickupAddress)?void 0:a.mobile)?(t(),d(s,{key:1,class:"delivery-item"},{default:i((()=>[u(p,{class:"delivery-label"},{default:i((()=>[r("联系电话")])),_:1}),u(p,{class:"delivery-value"},{default:i((()=>{var e;return[r(v(g.value.pickup_contact_phone||(null==(e=g.value.pickupAddress)?void 0:e.mobile)),1)]})),_:1})])),_:1})):c("v-if",!0),A()?(t(),d(s,{key:2,class:"delivery-item"},{default:i((()=>[u(p,{class:"delivery-label"},{default:i((()=>[r("详细地址")])),_:1}),u(p,{class:"delivery-value address-detail"},{default:i((()=>[r(v(A()),1)])),_:1})])),_:1})):c("v-if",!0),c(" 显示地址组成部分 - 暂时隐藏，因为只有ID没有名称 "),c(' <view class="address-breakdown" v-if="hasAddressBreakdown()">\r\n              <view class="delivery-item" v-if="recycleOrderInfo.pickup_province">\r\n                <text class="delivery-label">省份</text>\r\n                <text class="delivery-value">{{ recycleOrderInfo.pickup_province }}</text>\r\n              </view>\r\n              <view class="delivery-item" v-if="recycleOrderInfo.pickup_city">\r\n                <text class="delivery-label">城市</text>\r\n                <text class="delivery-value">{{ recycleOrderInfo.pickup_city }}</text>\r\n              </view>\r\n              <view class="delivery-item" v-if="recycleOrderInfo.pickup_district">\r\n                <text class="delivery-label">区县</text>\r\n                <text class="delivery-value">{{ recycleOrderInfo.pickup_district }}</text>\r\n              </view>\r\n            </view> '),h.value.time||g.value.pickup_time?(t(),d(s,{key:3,class:"delivery-item"},{default:i((()=>[u(p,{class:"delivery-label"},{default:i((()=>[r("预约时间")])),_:1}),u(p,{class:"delivery-value"},{default:i((()=>[r(v(h.value.time||g.value.pickup_time),1)])),_:1})])),_:1})):c("v-if",!0),c(" 显示订单状态 "),g.value.status_text?(t(),d(s,{key:4,class:"delivery-item"},{default:i((()=>[u(p,{class:"delivery-label"},{default:i((()=>[r("订单状态")])),_:1}),u(p,{class:"delivery-value status-text"},{default:i((()=>[r(v(g.value.status_text),1)])),_:1})])),_:1})):c("v-if",!0)],64)):c("v-if",!0),c(" 用户自寄显示物流信息 "),"self"===h.value.delivery?(t(),_(n,{key:1},[g.value.express_company?(t(),d(s,{key:0,class:"delivery-item"},{default:i((()=>[u(p,{class:"delivery-label"},{default:i((()=>[r("物流公司")])),_:1}),u(p,{class:"delivery-value"},{default:i((()=>[r(v(g.value.express_company),1)])),_:1})])),_:1})):c("v-if",!0),g.value.express_number?(t(),d(s,{key:1,class:"delivery-item"},{default:i((()=>[u(p,{class:"delivery-label"},{default:i((()=>[r("快递单号")])),_:1}),u(p,{class:"delivery-value"},{default:i((()=>[r(v(g.value.express_number),1)])),_:1})])),_:1})):c("v-if",!0),c(" 显示订单状态 "),g.value.status_text?(t(),d(s,{key:2,class:"delivery-item"},{default:i((()=>[u(p,{class:"delivery-label"},{default:i((()=>[r("订单状态")])),_:1}),u(p,{class:"delivery-value status-text"},{default:i((()=>[r(v(g.value.status_text),1)])),_:1})])),_:1})):c("v-if",!0)],64)):c("v-if",!0)]})),_:1})])),_:1})])),_:1}),c(" 成功状态区域 "),u(s,{class:"success-section"},{default:i((()=>[u(s,{class:"success-icon"},{default:i((()=>[u(s,{class:"batch-bg"},{default:i((()=>[u(s,{class:"batch-body"},{default:i((()=>[u(s,{class:"batch-items"},{default:i((()=>[u(s,{class:"item-stack"},{default:i((()=>[u(s,{class:"stack-item"}),u(s,{class:"stack-item"}),u(s,{class:"stack-item"})])),_:1})])),_:1})])),_:1})])),_:1}),u(s,{class:"check-mark"},{default:i((()=>[u(l,{name:"checkmark",color:"#fff",size:"16"})])),_:1})])),_:1}),u(p,{class:"success-title"},{default:i((()=>[r("批量回收提交成功")])),_:1}),u(p,{class:"order-number"},{default:i((()=>[r("回收单号: "+v(w.value.orderNo),1)])),_:1}),u(p,{class:"success-desc"},{default:i((()=>[r("您的"+v(h.value.quantity)+"件商品回收申请已提交，我们将尽快安排处理",1)])),_:1}),c(" 查看订单按钮 "),u(s,{class:"view-order-button",onClick:q},{default:i((()=>[u(p,{class:"button-text"},{default:i((()=>[r("查看回收单")])),_:1})])),_:1})])),_:1})])),_:1})}}}),[["__scopeId","data-v-568c665d"]]);export{h as default};
