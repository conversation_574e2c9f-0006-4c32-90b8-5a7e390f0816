import{d as e,p as l,q as a,r,_ as o,I as t,B as s,J as i,L as d,M as n,a as u,o as p,c as m,w as c,b as x,t as g,x as f,e as b,y as _,g as h,F as v,O as y,n as k,a4 as w,a5 as C,U as j,a2 as V,a3 as F,k as P,i as S,j as T,Q as R,S as z,R as U,P as q,T as A}from"./index-3caf046d.js";import{_ as L}from"./u-input.2d8dc7a4.js";import{_ as O,a as B}from"./u-form.49dbb57f.js";import{_ as E}from"./u-icon.ba193921.js";import{u as I,_ as J}from"./sms-code.vue_vue_type_script_setup_true_lang.641df60a.js";import{_ as M,a as $}from"./u-checkbox-group.0328273c.js";import{_ as K}from"./uni-popup.a1c93ccb.js";import{t as N}from"./topTabbar.9217e319.js";import{_ as Q}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-line.69c0c00f.js";/* empty css                                                               */import"./u-modal.8624728a.js";import"./u-loading-icon.255170b9.js";import"./u-popup.1b30ffa7.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     */import"./u-safe-bottom.98e092c5.js";const D=Q(e({__name:"register",setup(e){let Q={};N().setTopTabbarParam({title:"",topStatusBar:{bgColor:"#fff",textColor:"#333"}}),l((()=>Object.keys(Q).length?A(Number(Q.height))+A(Q.top)+A(8)+"rpx":"auto"));const D=a({username:"",password:"",confirm_password:"",mobile:"",mobile_code:"",mobile_key:"",captcha_key:"",captcha_code:""}),G=r(),H=()=>{G.value.close()},W=()=>{pe.value=!0,G.value.close(),ce()},X=r(!0);o((()=>{setTimeout((()=>{X.value=!1}),800)}));const Y=r(!0),Z=r(!0),ee=()=>{Y.value=!Y.value},le=()=>{Z.value=!Z.value},ae=t(),re=s(),oe=r("");i((async e=>{await re.getLoginConfig(),d()||re.login.is_username||re.login.is_mobile||re.login.is_bind_mobile||(n({title:"商家未开启普通账号注册",icon:"none"}),setTimeout((()=>{u({url:"/app/pages/index/index",mode:"reLaunch"})}),100)),uni.getStorageSync("openid")&&Object.assign(D,{wx_openid:uni.getStorageSync("openid")}),uni.getStorageSync("pid")&&Object.assign(D,{pid:uni.getStorageSync("pid")}),re.login.is_username?ie.value="username":(re.login.is_mobile||re.login.is_bind_mobile)&&(ie.value="mobile"),oe.value=e.type}));const te=I(D);te.refresh();const se=r(!1),ie=r(""),de=()=>{pe.value=!pe.value},ne=l((()=>{const e=[];return re.login.is_username&&e.push({type:"username",title:q("usernameRegister")}),re.login.is_mobile&&!re.login.is_bind_mobile&&e.push({type:"mobile",title:q("mobileRegister")}),e})),ue=l((()=>({username:[{type:"string",required:"username"==ie.value,message:q("usernamePlaceholder"),trigger:["blur","change"]},{validator:(e,l)=>!uni.$u.test.number(l),message:q("usernameTips"),trigger:["change","blur"]}],password:{type:"string",required:"username"==ie.value,message:q("passwordPlaceholder"),trigger:["blur","change"]},confirm_password:[{type:"string",required:"username"==ie.value,message:q("confirmPasswordPlaceholder"),trigger:["blur","change"]},{validator:(e,l)=>l==D.password,message:q("confirmPasswordError"),trigger:["change","blur"]}],mobile:[{type:"string",required:"mobile"==ie.value||re.login.is_bind_mobile,message:q("mobilePlaceholder"),trigger:["blur","change"]},{validator:(e,l)=>"mobile"!=ie.value&&!re.login.is_bind_mobile||uni.$u.test.mobile(l),message:q("mobileError"),trigger:["change","blur"]}],mobile_code:{type:"string",required:"mobile"==ie.value||re.login.is_bind_mobile,message:q("codePlaceholder"),trigger:["blur","change"]},captcha_code:{type:"string",required:"username"==ie.value,message:q("captchaPlaceholder"),trigger:["blur","change"]}}))),pe=r(!1),me=r(null),ce=()=>{me.value.validate().then((()=>{if(re.login.agreement_show&&!pe.value)return G.value.open(),!1;if(se.value)return;se.value=!0;("username"==ie.value?w:C)(D).then((e=>{ae.setToken(e.data.token),j().handleLoginBack()})).catch((()=>{se.value=!1,te.refresh()}))}))},xe=()=>{const e=V();if(e.length>1){"app/pages/auth/login"==e[e.length-2].route?F({delta:1}):u({url:"/app/pages/auth/login",mode:"redirectTo"})}else u({url:"/app/pages/auth/login",mode:"redirectTo"})};return(e,l)=>{const a=P,r=S(T("u-input"),L),o=S(T("u-form-item"),O),t=S(T("u-icon"),E),s=S(T("sms-code"),J),i=R,d=S(T("u-form"),B),n=S(T("u-checkbox"),M),w=S(T("u-checkbox-group"),$),C=z,j=U,V=S(T("uni-popup"),K);return ie.value?(p(),m(a,{key:0,class:"w-screen h-screen flex flex-col",style:k(e.themeColor())},{default:c((()=>[x(a,{class:"mx-[60rpx]"},{default:c((()=>[x(a,{class:"pt-[140rpx] text-[44rpx] font-500 text-[#333]"},{default:c((()=>[g(f("username"==ie.value?b(q)("usernameRegister"):b(q)("mobileRegister")),1)])),_:1}),x(a,{class:"text-[26rpx] leading-[39rpx] text-[var(--text-color-light6)] mt-[16rpx] mb-[80rpx]"},{default:c((()=>[g(f("username"==ie.value?b(q)("usernameRegisterTip"):b(q)("mobileRegisterTip")),1)])),_:1}),x(d,{labelPosition:"left",model:D,errorType:"toast",rules:b(ue),ref_key:"formRef",ref:me},{default:c((()=>["username"==ie.value?(p(),_(v,{key:0},[x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6]"},{default:c((()=>[x(o,{label:"",prop:"username","border-bottom":!1},{default:c((()=>[x(r,{modelValue:D.username,"onUpdate:modelValue":l[0]||(l[0]=e=>D.username=e),border:"none",maxlength:"40",placeholder:b(q)("usernamePlaceholder"),class:"!bg-transparent",disabled:X.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"password","border-bottom":!1},{default:c((()=>[x(r,{modelValue:D.password,"onUpdate:modelValue":l[1]||(l[1]=e=>D.password=e),border:"none",password:Y.value,maxlength:"40",placeholder:b(q)("passwordPlaceholder"),class:"!bg-transparent",disabled:X.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:c((()=>[D.password?(p(),m(a,{key:0,onClick:ee},{default:c((()=>[x(t,{name:Y.value?"eye-off":"eye-fill",color:"#b9b9b9",size:"20"},null,8,["name"])])),_:1})):h("v-if",!0)])),_:1},8,["modelValue","password","placeholder","disabled"])])),_:1})])),_:1}),x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"confirm_password","border-bottom":!1},{default:c((()=>[x(r,{modelValue:D.confirm_password,"onUpdate:modelValue":l[2]||(l[2]=e=>D.confirm_password=e),border:"none",password:Z.value,maxlength:"40",placeholder:b(q)("confirmPasswordPlaceholder"),class:"!bg-transparent",disabled:X.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:c((()=>[D.confirm_password?(p(),m(a,{key:0,onClick:le},{default:c((()=>[x(t,{name:Z.value?"eye-off":"eye-fill",color:"#b9b9b9",size:"20"},null,8,["name"])])),_:1})):h("v-if",!0)])),_:1},8,["modelValue","password","placeholder","disabled"])])),_:1})])),_:1})],64)):h("v-if",!0),"mobile"==ie.value||b(re).login.is_bind_mobile?(p(),_(v,{key:1},[x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"mobile","border-bottom":!1},{default:c((()=>[x(r,{modelValue:D.mobile,"onUpdate:modelValue":l[3]||(l[3]=e=>D.mobile=e),border:"none",maxlength:"11",placeholder:b(q)("mobilePlaceholder"),class:"!bg-transparent",disabled:X.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"mobile_code","border-bottom":!1},{default:c((()=>[x(r,{modelValue:D.mobile_code,"onUpdate:modelValue":l[6]||(l[6]=e=>D.mobile_code=e),border:"none",maxlength:"4",placeholder:b(q)("codePlaceholder"),class:"!bg-transparent",disabled:X.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:c((()=>[b(re).login.agreement_show?(p(),m(s,{key:0,mobile:D.mobile,type:"login",modelValue:D.mobile_key,"onUpdate:modelValue":l[4]||(l[4]=e=>D.mobile_key=e),isAgree:pe.value},null,8,["mobile","modelValue","isAgree"])):(p(),m(s,{key:1,mobile:D.mobile,type:"login",modelValue:D.mobile_key,"onUpdate:modelValue":l[5]||(l[5]=e=>D.mobile_key=e)},null,8,["mobile","modelValue"]))])),_:1},8,["modelValue","placeholder","disabled"])])),_:1})])),_:1})],64)):h("v-if",!0),"username"==ie.value?(p(),m(a,{key:2,class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"captcha_code","border-bottom":!1},{default:c((()=>[x(r,{modelValue:D.captcha_code,"onUpdate:modelValue":l[8]||(l[8]=e=>D.captcha_code=e),border:"none",placeholder:b(q)("captchaPlaceholder"),class:"!bg-transparent",disabled:X.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:c((()=>[x(i,{src:b(te).image.value,class:"h-[48rpx] w-[60rpx] ml-[20rpx]",mode:"heightFix",onClick:l[7]||(l[7]=e=>b(te).refresh())},null,8,["src"])])),_:1},8,["modelValue","placeholder","disabled"])])),_:1})])),_:1})):h("v-if",!0)])),_:1},8,["model","rules"]),x(a,{class:"mt-[160rpx]"},{default:c((()=>[b(re).login.agreement_show?(p(),m(a,{key:0,class:"flex items-center mb-[20rpx] py-[14rpx]",onClick:y(de,["stop"])},{default:c((()=>[x(w,{onChange:de},{default:c((()=>[x(n,{activeColor:"var(--primary-color)",checked:pe.value,shape:"circle",size:"30rpx"},null,8,["checked"])])),_:1}),x(a,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap leading-[30rpx]"},{default:c((()=>[x(C,null,{default:c((()=>[g(f(b(q)("agreeTips")),1)])),_:1}),x(C,{onClick:l[9]||(l[9]=y((e=>b(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:c((()=>[g("《"+f(b(q)("privacyAgreement"))+"》",1)])),_:1}),x(C,null,{default:c((()=>[g(f(b(q)("and")),1)])),_:1}),x(C,{onClick:l[10]||(l[10]=y((e=>b(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:c((()=>[g("《"+f(b(q)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])):h("v-if",!0),x(j,{class:"w-full h-[80rpx] !bg-[var(--primary-color)] text-[26rpx] rounded-[40rpx] leading-[80rpx] font-500 !text-[#fff]",onClick:ce},{default:c((()=>[g(f(b(q)("register")),1)])),_:1}),x(a,{class:"flex items-center justify-between mt-[30rpx]"},{default:c((()=>[b(ne).length>1?(p(),m(a,{key:0,class:"text-[26rpx] text-[var(--text-color-light6)] leading-[34rpx]",onClick:l[11]||(l[11]=e=>ie.value="username"==ie.value?"mobile":"username")},{default:c((()=>[g(f("username"==ie.value?b(q)("mobileRegister"):b(q)("usernameRegister")),1)])),_:1})):h("v-if",!0),x(a,{class:"text-[26rpx] text-[#333] leading-[34rpx]",onClick:xe},{default:c((()=>[x(C,null,{default:c((()=>[g(f(b(q)("haveAccount"))+",",1)])),_:1}),x(C,{class:"text-primary"},{default:c((()=>[g(f(b(q)("toLogin")),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),x(V,{ref_key:"popupRef",ref:G,type:"dialog"},{default:c((()=>[x(a,{class:"bg-[#fff] flex flex-col justify-between w-[600rpx] min-h-[280rpx] rounded-[var(--rounded-big)] box-border px-[35rpx] pt-[35rpx] pb-[8rpx] relative"},{default:c((()=>[x(a,{class:"flex justify-center"},{default:c((()=>[x(C,{class:"text-[33rpx] font-700"},{default:c((()=>[g(" 用户协议及隐私保护")])),_:1})])),_:1}),x(a,{class:"flex items-center mb-[20rpx] mt-[20rpx] py-[20rpx]",onClick:y(de,["stop"])},{default:c((()=>[x(a,{class:"text-[26rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:c((()=>[x(C,null,{default:c((()=>[g(f(b(q)("agreeTips")),1)])),_:1}),x(C,{onClick:l[12]||(l[12]=y((e=>b(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:c((()=>[g("《"+f(b(q)("privacyAgreement"))+"》",1)])),_:1}),x(C,null,{default:c((()=>[g(f(b(q)("and")),1)])),_:1}),x(C,{onClick:l[13]||(l[13]=y((e=>b(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:c((()=>[g("《"+f(b(q)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"]),x(a,null,{default:c((()=>[x(a,{class:"w-[100%] flex justify-center bg-[var(--primary-color)] h-[70rpx] leading-[70rpx] text-[#fff] text-[26rpx] border-[0] font-500 rounded-[50rpx]",onClick:W},{default:c((()=>[g("同意并注册")])),_:1}),x(a,{class:"w-[100%] flex justify-center h-[70rpx] leading-[70rpx] text-[#999] text-[24rpx] border-[0] font-500 rounded-[50rpx]",onClick:H},{default:c((()=>[g("不同意")])),_:1})])),_:1})])),_:1})])),_:1},512)])),_:1},8,["style"])):h("v-if",!0)}}}),[["__scopeId","data-v-6ffb37e8"]]);export{D as default};
