import{d as e,r as a,o as t,y as s,g as l,b as o,w as p,O as n,n as r,c as u,e as i,t as c,F as d,ao as f,bE as v,A as m,Q as _,k as y,S as h,R as g,i as b,j as w}from"./index-3caf046d.js";import{_ as x}from"./u-popup.1b30ffa7.js";import{_ as S}from"./_plugin-vue_export-helper.1b428a4d.js";const j=S(e({__name:"share-poster",props:{posterId:{type:String||Number,default:0},posterType:{type:String,default:""},posterParam:{type:Object,default:{}},copyUrl:{type:String,default:""},copyUrlParam:{type:String,default:""}},emits:["close"],setup(e,{expose:S,emit:j}){const k=e,O=a(!1),P=()=>{let e="";if(k.copyUrl){let a=location.pathname,t=["/app/","/addon/"];for(let e=0;e<t.length;e++)-1!=a.indexOf(t[e])&&(a=a.substr(0,a.indexOf(t[e])));e=location.origin+a+k.copyUrl+k.copyUrlParam}else e=location.origin+location.pathname+k.copyUrlParam;f(e,(()=>{O.value=!1}))},U=a(!1),C=a(!1),T=a(""),D=()=>{if(T.value)U.value=!1,C.value=!0;else{U.value=!0,C.value=!1;let e={id:k.posterId,type:k.posterType,param:k.posterParam},a=Date.parse(new Date);v(e).then((e=>{T.value=e.data&&m(e.data)||"";let t=Date.parse(new Date)-a;t<2200?setTimeout((()=>{U.value=!1,C.value=!0}),2200-t):(U.value=!1,C.value=!0)})).catch((()=>{A()}))}},F=a(!1),I=()=>{F.value=!1},z=a(0),A=()=>{O.value=!1,U.value=!1,C.value=!1,j("close")};return S({openShare:()=>{O.value=!0,D()},loadPoster:D}),(e,a)=>{const f=_,v=y,S=h,j=g,k=b(w("u-popup"),x);return t(),s(d,null,[l(" 分享弹窗 "),o(v,{onTouchmove:a[2]||(a[2]=n((()=>{}),["prevent","stop"])),class:"share-popup"},{default:p((()=>[o(k,{show:O.value,type:"bottom",onClose:A,overlayOpacity:"0.8"},{default:p((()=>[o(v,{onTouchmove:a[0]||(a[0]=n((()=>{}),["prevent","stop"]))},{default:p((()=>[o(v,{class:"poster-img-wrap",style:r({top:z.value})},{default:p((()=>[U.value?(t(),u(f,{key:0,class:"poster-animation",src:i(m)("addon/shop/poster_animation.gif"),mode:"aspectFit"},null,8,["src"])):l("v-if",!0),C.value?(t(),u(f,{key:1,class:"poster-img",src:i(m)(T.value),mode:"aspectFit","show-menu-by-longpress":!0},null,8,["src"])):l("v-if",!0)])),_:1},8,["style"]),o(v,{class:"share-content"},{default:p((()=>[o(v,{class:"share-box",onClick:P},{default:p((()=>[o(j,{class:"share-btn",plain:!0},{default:p((()=>[o(v,{class:"text-[#07c160] iconfont iconfuzhilianjie"}),o(S,null,{default:p((()=>[c("复制链接")])),_:1})])),_:1})])),_:1})])),_:1}),o(v,{class:"share-footer",onClick:A},{default:p((()=>[o(S,null,{default:p((()=>[c("取消分享")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"]),o(k,{show:F.value,mode:"center",round:10,closeable:!0,onClose:a[1]||(a[1]=e=>F.value=!1),"safe-area-inset-bottom":!1},{default:p((()=>[o(v,{class:"dialog-popup"},{default:p((()=>[o(v,{class:"title"},{default:p((()=>[c("提示")])),_:1}),o(v,{class:"message"},{default:p((()=>[c("您拒绝了保存图片到相册的授权请求，无法保存图片到相册，如需正常使用，请授权之后再进行操作。")])),_:1}),o(v,{class:"action-wrap"},{default:p((()=>[o(v,{onClick:I},{default:p((()=>[c("取消")])),_:1}),o(v,null,{default:p((()=>[o(j,{type:"default",class:"authorization-btn","open-type":"openSetting",onOpensetting:I,"hover-class":"none"},{default:p((()=>[c("立即授权")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})],2112)}}}),[["__scopeId","data-v-f91472b0"]]);export{j as s};
