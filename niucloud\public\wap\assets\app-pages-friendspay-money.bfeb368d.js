import{d as e,r as a,J as t,N as l,aq as s,ar as r,W as i,X as o,o as n,c as u,w as p,b as x,e as d,t as f,x as c,g as _,n as m,y as v,F as g,z as y,L as b,U as h,i as j,j as k,S as F,k as w,R as T,A as B,P as C,a as I,$ as P,Q as L}from"./index-3caf046d.js";import{_ as R}from"./u-avatar.30e31e9c.js";import{_ as S}from"./u--image.eb573bce.js";import{_ as V}from"./pay.e8ba1ab9.js";import{_ as z}from"./loading-page.vue_vue_type_script_setup_true_lang.54c95329.js";import{t as A}from"./topTabbar.9217e319.js";import{g as J,_ as M}from"./message.vue_vue_type_script_setup_true_lang.dd3641a4.js";import{_ as N}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-text.f02e6497.js";import"./u-image.04cba9a2.js";import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-popup.1b30ffa7.js";import"./u-safe-bottom.98e092c5.js";import"./pay.1a29db5c.js";import"./u-loading-icon.255170b9.js";const O=N(e({__name:"money",setup(e){const N=A();N.setTopTabbarParam({title:""});const O=a(!0),q=a(0),G=a(""),Q=a({}),U=a(!1),W=a(!0),X=a(null);t((e=>{q.value=e.id||0,G.value=e.type||""})),l((()=>{q.value&&G.value&&$(G.value,q.value)})),s((()=>{X.value&&(clearTimeout(X.value),X.value=null)})),r((()=>{X.value&&(clearTimeout(X.value),X.value=null)}));const $=(e,a)=>{W.value&&(O.value=!0,W.value=!1),J(e,a).then((t=>{Q.value=t.data,i({title:Q.value.config.pay_page_name}),N.setTopTabbarParam({title:Q.value.config.pay_page_name}),O.value=!1,2!=Q.value.status&&1!=Q.value.status&&-1!=Q.value.status?X.value=setTimeout((()=>{$(e,a)}),3e3):(clearTimeout(X.value),X.value=null)})).catch((()=>{X.value&&(clearTimeout(X.value),X.value=null),O.value=!1;o({title:"未找到帮付订单信息",url:"/app/pages/index/index",mode:"reLaunch"})}))},D=a(null),E=()=>{D.value.open(Q.value.config)},H=a(),K=()=>{var e;b()?null==(e=H.value)||e.open(Q.value.trade_type,Q.value.trade_id,"/app/pages/index/index","friendspay"):h().setLoginBack({url:"/app/pages/friendspay/money",param:{id:Q.value.trade_id,type:Q.value.trade_type}})};return(e,a)=>{const t=j(k("u-avatar"),R),l=F,s=w,r=T,i=L,o=j(k("u--image"),S),b=j(k("pay"),V),h=j(k("loading-page"),z);return n(),u(s,{style:m(e.themeColor())},{default:p((()=>[Object.keys(Q.value).length&&!O.value?(n(),u(s,{key:0,class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden"},{default:p((()=>[x(s,{style:m({background:"url("+d(B)("static/resource/images/app/friendpay_money.png")+") left bottom / cover no-repeat"}),class:"pb-[194rpx] overflow-hidden"},{default:p((()=>[x(s,{class:"mt-[20rpx] flex flex-col items-center"},{default:p((()=>[x(t,{src:d(B)(Q.value.member.headimg),size:"50",leftIcon:"none","default-url":d(B)("static/resource/images/default_headimg.png")},null,8,["src","default-url"]),x(s,{class:"flex items-center mt-[20rpx] text-[#fff] text-[26rpx] leading-[36rpx]"},{default:p((()=>[x(l,{class:"font-bold mr-[10rpx] max-w-[250rpx] truncate"},{default:p((()=>[f(c(Q.value.member.nickname),1)])),_:1}),x(l,null,{default:p((()=>[f("请您帮忙付款~")])),_:1})])),_:1}),Q.value.config.pay_leave_message?(n(),u(s,{key:0,class:"message bg-[#fe0708] relative max-w-[520rpx] px-[20rpx] py-[12rpx] rounded-[12rpx] border-solid border-[1rpx] border-color text-[24rpx] text-[#fff] leading-[30rpx] box-border text-center mt-[20rpx] mx-[114rpx]"},{default:p((()=>[f(c(Q.value.config.pay_leave_message),1)])),_:1})):_("v-if",!0)])),_:1})])),_:1},8,["style"]),x(s,{class:"-mt-[154rpx] card-template sidebar-margin mb-[var(--top-m)]"},{default:p((()=>[x(s,{class:"text-[24rpx] text-center mb-[10rpx]"},{default:p((()=>[f(c(d(C)("payMoney")),1)])),_:1}),x(s,{class:"text-center mb-[50rpx]"},{default:p((()=>[x(l,{class:"text-[32rpx] font-500 price-font text-[#FF4142]"},{default:p((()=>[f("￥")])),_:1}),x(l,{class:"text-[56rpx] font-bold price-font text-[#FF4142]"},{default:p((()=>[f(c(parseFloat(Q.value.money).toFixed(2).split(".")[0]),1)])),_:1}),x(l,{class:"text-[32rpx] font-500 price-font text-[#FF4142]"},{default:p((()=>[f("."+c(parseFloat(Q.value.money).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),x(s,{class:"px-[20rpx] box-border"},{default:p((()=>[2==Q.value.status?(n(),u(r,{key:0,class:"bg-[#FFB4B1] !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none"},{default:p((()=>[f(c(d(C)("finish")),1)])),_:1})):-1==Q.value.status?(n(),u(r,{key:1,class:"bg-[#FFB4B1] !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none"},{default:p((()=>[f(c(d(C)("close")),1)])),_:1})):(n(),u(r,{key:2,class:"botton-color !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none",loading:U.value,onClick:K},{default:p((()=>[f(c(Q.value.config.pay_button_name?Q.value.config.pay_button_name:d(C)("payGenerously")),1)])),_:1},8,["loading"]))])),_:1}),x(s,{class:"px-[10px] flex justify-between items-center text-[var(--text-color-light9)] mt-[20rpx]"},{default:p((()=>[x(s,{class:"flex items-baseline justify-between text-[var(--text-color-light9)]",onClick:a[0]||(a[0]=e=>d(I)({url:"/app/pages/index/index"}))},{default:p((()=>[x(l,{class:"text-[24rpx] mr-[6rpx]"},{default:p((()=>[f("返回首页")])),_:1})])),_:1}),Q.value.config.pay_explain_switch?(n(),u(s,{key:0,class:"flex-shrink-0",onClick:E},{default:p((()=>[x(l,{class:"mr-[8rpx] text-[24rpx]"},{default:p((()=>[f(c(Q.value.config.pay_explain_title),1)])),_:1}),x(l,{class:"nc-iconfont nc-icon-jichuxinxiV6xx text-[26rpx]"})])),_:1})):_("v-if",!0)])),_:1})])),_:1}),Q.value.config.pay_info_switch?(n(),u(s,{key:0,class:"card-template sidebar-margin mb-[var(--top-m)]"},{default:p((()=>["[]"!==JSON.stringify(Q.value.trade_info)&&Q.value.trade_info.item_list.length?(n(),v(g,{key:0},[x(s,{class:"flex justify-between items-center mb-[30rpx]"},{default:p((()=>[x(s,{class:"text-[30rpx] text-[#333] font-500"},{default:p((()=>[f(c(d(C)("helpPayInfo")),1)])),_:1}),_(' <view class="flex-shrink-0" @click="handleMessage" v-if="friendsInfo.config.pay_explain_switch">\n                            <text class="mr-[8rpx] text-[24rpx]">{{ friendsInfo.config.pay_explain_title }}</text>\n                            <text class="nc-iconfont nc-icon-jichuxinxiV6xx text-[26rpx]"></text>\n                        </view> ')])),_:1}),x(s,{class:"border-0 border-solid border-b-[1rpx] border-[#f6f6f6] mb-[20rpx]"},{default:p((()=>[(n(!0),v(g,null,y(Q.value.trade_info.item_list,((e,a)=>(n(),u(s,{class:P(["flex justify-between",{" mb-[34rpx]":a+1!=Q.value.trade_info.length}])},{default:p((()=>[x(s,{class:"w-[170rpx] h-[170rpx] rounded-[var(--goods-rounded-big)] overflow-hidden flex-shrink-0"},{default:p((()=>[x(o,{class:"overflow-hidden",radius:"var(--goods-rounded-big)",width:"170rpx",height:"170rpx",src:d(B)(e.item_image?e.item_image:""),model:"aspectFill"},{error:p((()=>[x(i,{class:"w-[170rpx] h-[170rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:d(B)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1024),x(s,{class:"ml-[20rpx] flex flex-1 flex-col justify-between"},{default:p((()=>[x(s,null,{default:p((()=>[x(s,{class:"text-[28rpx] using-hidden leading-[40rpx] text-[#333]"},{default:p((()=>[f(c(e.item_name),1)])),_:2},1024),e.item_sub_name?(n(),u(s,{key:0,class:"text-[24rpx] mt-[14rpx] text-[var(--text-color-light9)] using-hidden leading-[28rpx]"},{default:p((()=>[f(c(e.item_sub_name),1)])),_:2},1024)):_("v-if",!0)])),_:2},1024),x(s,{class:"flex justify-between items-baseline"},{default:p((()=>[x(s,{class:"price-font text-[#FF4142]"},{default:p((()=>[x(l,{class:"text-[24rpx]"},{default:p((()=>[f("￥")])),_:1}),x(l,{class:"text-[40rpx] font-500"},{default:p((()=>[f(c(parseFloat(e.item_price).toFixed(2).split(".")[0]),1)])),_:2},1024),x(l,{class:"text-[24rpx] font-500"},{default:p((()=>[f("."+c(parseFloat(e.item_price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),x(l,{class:"text-right text-[26rpx]"},{default:p((()=>[f("x"+c(e.item_num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),256))])),_:1}),x(s,{class:"text-[26rpx] text-right"},{default:p((()=>[f(c(Q.value.trade_info.item_total),1)])),_:1})],64)):(n(),u(s,{key:1,class:"text-[28rpx] leading-[40rpx] text-[#333]"},{default:p((()=>[f(c(Q.value.body),1)])),_:1}))])),_:1})):_("v-if",!0)])),_:1})):_("v-if",!0),_(" 帮付说明 "),x(M,{ref_key:"messageRef",ref:D},null,512),_(" 支付 "),x(b,{ref_key:"payRef",ref:H},null,512),x(h,{loading:O.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-50fa1e43"]]);export{O as default};
