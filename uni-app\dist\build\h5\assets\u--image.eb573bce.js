import{p as o,_ as r}from"./u-image.04cba9a2.js";import{ac as e,ad as s,a8 as a,o as n,c as i,w as d,a7 as t}from"./index-3caf046d.js";import{_ as c}from"./_plugin-vue_export-helper.1b428a4d.js";const l=c({name:"u--image",mixins:[e,o,s],components:{uvImage:r},emits:["click","error","load"]},[["render",function(o,r,e,s,c,l){const m=a("uvImage");return n(),i(m,{src:o.src,mode:o.mode,width:o.width,height:o.height,shape:o.shape,radius:o.radius,lazyLoad:o.lazyLoad,showMenuByLongpress:o.showMenuByLongpress,loadingIcon:o.loadingIcon,errorIcon:o.errorIcon,showLoading:o.showLoading,showError:o.showError,fade:o.fade,webp:o.webp,duration:o.duration,bgColor:o.bgColor,customStyle:o.customStyle,onClick:r[0]||(r[0]=r=>o.$emit("click")),onError:r[1]||(r[1]=r=>o.$emit("error")),onLoad:r[2]||(r[2]=r=>o.$emit("load"))},{loading:d((()=>[t(o.$slots,"loading")])),error:d((()=>[t(o.$slots,"error")])),_:3},8,["src","mode","width","height","shape","radius","lazyLoad","showMenuByLongpress","loadingIcon","errorIcon","showLoading","showError","fade","webp","duration","bgColor","customStyle"])}]]);export{l as _};
