import{d as e,r as a,J as t,ah as l,X as r,p as o,o as s,c as u,w as d,b as n,e as i,t as p,x as c,g as _,O as x,y as m,z as f,F as v,n as g,aC as y,M as h,a as b,Q as w,i as j,j as k,k as C,S as V,au as N,aM as D,aD as F,R as S,aN as A,A as M}from"./index-3caf046d.js";import{_ as O}from"./u--image.eb573bce.js";import{_ as z}from"./u-upload.83871903.js";import{_ as B,a as I}from"./u-radio-group.63482a1c.js";import{_ as L}from"./u-popup.1b30ffa7.js";import{g as P,c as R,d as T,e as U}from"./refund.d2b0cb91.js";import{_ as q}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-loading-icon.255170b9.js";import"./u-safe-bottom.98e092c5.js";const E=q(e({__name:"edit_apply",setup(e){var q;const E=a(null),J=a({}),Q=a(0),W=a(0),X=a(!1),G=a({order_id:null==(q=E.value)?void 0:q.order_id,order_goods_id:Q.value,order_refund_no:"",refund_type:"",apply_money:"",reason:"",remark:"",voucher:[]}),H=a({}),K=a([]),Y=a("");P().then((({data:e})=>{K.value=e,K.value&&K.value.length&&(Y.value=K.value[0])})),t((e=>{if(Q.value=e.order_goods_id||0,e.order_refund_no)R(e.order_refund_no).then((({data:e})=>{E.value=e,J.value=e.order_goods,G.value.order_goods_id=e.order_goods_id,G.value.order_id=e.order_id,G.value.order_refund_no=e.order_refund_no,G.value.remark=e.remark,G.value.reason=e.reason,Y.value=e.reason,G.value.voucher=e.voucher})),T({order_refund_no:e.order_refund_no}).then((e=>{H.value=e.data,G.value.apply_money=l(H.value.refund_money)}));else{r({url:"/addon/shop/pages/refund/list",title:"缺少订单号"})}})),o((e=>function(e){return""==e||0==e?"70rpx":17*String(e).length+"rpx"}));const Z=e=>{G.value.refund_type=e,W.value=1},$=o((()=>G.value.voucher.map((e=>({url:M(e)}))))),ee=e=>{e.file.forEach((e=>{y({filePath:e.url,name:"file"}).then((e=>{G.value.voucher.length<9&&G.value.voucher.push(e.data.url)})).catch((()=>{}))}))},ae=e=>{G.value.voucher.splice(e.index,1)},te=a(!1),le=()=>G.value.reason?Number(G.value.apply_money).toFixed(2)<0?(h({title:"退款金额不能为0,保留两位小数",icon:"none"}),!1):Number(G.value.apply_money)>Number(H.value.refund_money)?(h({title:"退款金额不能大于可退款总额",icon:"none"}),!1):void(te.value||(te.value=!0,U(G.value).then((e=>{te.value=!1,setTimeout((()=>{b({url:"/addon/shop/pages/order/detail",param:{order_id:G.value.order_id}})}),1e3)})).catch((()=>{te.value=!1})))):(h({title:"请选择退款原因",icon:"none"}),!1),re=()=>{G.value.reason=Y.value,X.value=!1};return(e,a)=>{const t=w,l=j(k("u--image"),O),r=C,o=V,y=N,h=D,b=j(k("u-upload"),z),P=F,R=S,T=j(k("u-radio"),B),U=j(k("u-radio-group"),I),q=j(k("u-popup"),L),Q=A;return s(),u(r,{style:g(e.themeColor())},{default:d((()=>[E.value?(s(),u(Q,{key:0,"indicator-dots":!1,autoplay:!1,"disable-touch":!0,current:W.value,class:"h-screen",duration:300},{default:d((()=>[n(h,null,{default:d((()=>[n(y,{"scroll-y":"true",class:"bg-page min-h-screen overflow-hidden"},{default:d((()=>[n(r,{class:"m-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] py-[var(--pad-top-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"flex"},{default:d((()=>[n(l,{radius:"var(--goods-rounded-small)",width:"120rpx",height:"120rpx",src:i(M)(J.value.sku_image.split(",")[0]),model:"aspectFill"},{error:d((()=>[n(t,{class:"w-[120rpx] h-[120rpx]",radius:"var(--goods-rounded-small)",src:i(M)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["radius","src"])])),_:1},8,["radius","src"]),n(r,{class:"flex-1 w-0 ml-[20rpx]"},{default:d((()=>[n(r,{class:"text-ellipsis text-[28rpx] leading-normal truncate"},{default:d((()=>[p(c(J.value.goods_name),1)])),_:1}),J.value.sku_name?(s(),u(r,{key:0,class:"mt-[6rpx] text-[24rpx] leading-[1.3] text-[var(--text-color-light9)] truncate"},{default:d((()=>[p(c(J.value.sku_name),1)])),_:1})):_("v-if",!0)])),_:1})])),_:1})])),_:1}),n(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"py-[var(--pad-top-m)] flex items-center",onClick:a[0]||(a[0]=e=>Z(1))},{default:d((()=>[n(r,{class:"flex-1"},{default:d((()=>[n(r,{class:"text-[30rpx]"},{default:d((()=>[p("仅退款")])),_:1}),"real"==J.value.goods_type?(s(),u(r,{key:0,class:"text-[24rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:d((()=>[p("未收到货，或与商家协商一致不用退货只退款")])),_:1})):"virtual"==J.value.goods_type?(s(),u(r,{key:1,class:"text-[24rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:d((()=>[p("与商家协商一致不用退货只退款")])),_:1})):_("v-if",!0)])),_:1}),n(o,{class:"nc-iconfont nc-icon-youV6xx text-[28rpx] text-[var(--text-color-light9)]"})])),_:1}),"real"!=J.value.goods_type||J.value.delivery_status&&"wait_delivery"==J.value.delivery_status?_("v-if",!0):(s(),u(r,{key:0,class:"py-[var(--pad-top-m)] flex items-center border-0 !border-t !border-[#f5f5f5] border-solid",onClick:a[1]||(a[1]=e=>Z(2))},{default:d((()=>[n(r,{class:"flex-1"},{default:d((()=>[n(r,{class:"text-[30rpx]"},{default:d((()=>[p("退货退款")])),_:1}),n(r,{class:"text-[24rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:d((()=>[p("已收到货，需退还收到的货物")])),_:1})])),_:1}),n(o,{class:"nc-iconfont nc-icon-youV6xx text-[28rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})])),_:1})])),_:1}),n(h,null,{default:d((()=>[n(y,{"scroll-y":"true",class:"bg-page min-h-screen overflow-hidden"},{default:d((()=>[n(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"py-[var(--pad-top-m)] flex justify-between items-center"},{default:d((()=>[n(r,{class:"text-[28rpx]"},{default:d((()=>[p("退款原因")])),_:1}),n(r,{class:"flex ml-[auto] items-center h-[30rpx]",onClick:a[2]||(a[2]=e=>X.value=!0)},{default:d((()=>[n(o,{class:"text-[26rpx] text-[var(--text-color-light9)] truncate max-w-[460rpx]"},{default:d((()=>[p(c(G.value.reason||"请选择"),1)])),_:1}),n(o,{class:"nc-iconfont nc-icon-youV6xx pt-[4rpx] text-[24rpx] text-[var(--text-color-light9)]"})])),_:1})])),_:1})])),_:1}),n(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"py-[var(--pad-top-m)]"},{default:d((()=>[n(r,{class:"flex items-center justify-between"},{default:d((()=>[n(r,{class:"text-[28rpx] font-500"},{default:d((()=>[p("退款金额")])),_:1}),n(r,{class:"flex justify-end items-center text-[var(--price-text-color)] price-font"},{default:d((()=>[n(o,{class:"font-500 text-[36rpx] leading-none"},{default:d((()=>[p("￥")])),_:1}),n(o,{class:"font-500 text-[36rpx] leading-none"},{default:d((()=>[p(c(G.value.apply_money),1)])),_:1}),_(' <input type="digit" v-model.number="formData.apply_money" class="font-500 text-[36rpx] leading-none" :style="{ width: inputWidth(formData.apply_money) }" @blur="handleInput"> ')])),_:1})])),_:1}),n(r,{class:"text-right text-[24rpx] text-[var(--text-color-light9)] mt-[10rpx]"},{default:d((()=>[_(" <text>最多可退￥{{ refundMoney.refund_money }}</text> "),1===H.value.is_refund_delivery&&Number(H.value.refund_delivery_money)>0?(s(),u(o,{key:0,class:"ml-[10rpx]"},{default:d((()=>[p("(包含运费￥"+c(H.value.refund_delivery_money)+") ",1)])),_:1})):_("v-if",!0)])),_:1})])),_:1})])),_:1}),n(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"pt-[var(--pad-top-m)] pb-[14rpx]"},{default:d((()=>[n(r,{class:"text-[28rpx] flex items-center"},{default:d((()=>[n(o,{class:"font-500"},{default:d((()=>[p("上传凭证")])),_:1}),n(o,{class:"text-[24rpx] text-[var(--text-color-light9)] ml-[10rpx]"},{default:d((()=>[p("选填")])),_:1})])),_:1}),n(r,{class:"mt-[30rpx]"},{default:d((()=>[n(b,{fileList:i($),onAfterRead:ee,onDelete:ae,multiple:"",maxCount:9},null,8,["fileList"])])),_:1})])),_:1})])),_:1}),n(r,{class:"my-[24rpx] sidebar-margin px-[24rpx] rounded-md bg-white"},{default:d((()=>[n(r,{class:"py-[var(--pad-top-m)]"},{default:d((()=>[n(r,{class:"text-[28rpx] flex items-center"},{default:d((()=>[n(o,{class:"font-500"},{default:d((()=>[p("补充描述")])),_:1}),n(o,{class:"text-[24rpx] text-[var(--text-color-light9)] ml-[10rpx]"},{default:d((()=>[p("选填")])),_:1})])),_:1}),n(r,{class:"mt-[30rpx] h-[200rpx]"},{default:d((()=>[n(P,{class:"leading-[1.5] h-[100%] w-[100%] text-[28rpx]",modelValue:G.value.remark,"onUpdate:modelValue":a[3]||(a[3]=e=>G.value.remark=e),cols:"30",rows:"5",placeholder:"补充描述,有助于更好的处理售后问题","placeholder-class":"text-[26rpx] text-[var(--text-color-light9)]"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(r,{class:"w-full"},{default:d((()=>[n(r,{class:"py-[var(--top-m)] px-[var(--sidebar-m)] box-border"},{default:d((()=>[n(R,{class:"primary-btn-bg !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",loading:te.value,onClick:le},{default:d((()=>[p("提交")])),_:1},8,["loading"])])),_:1})])),_:1}),_(" 退款原因 "),n(q,{show:X.value,onClose:a[6]||(a[6]=e=>X.value=!1),onOpen:e.open,closeable:!0},{default:d((()=>[n(r,{class:"popup-common",onTouchmove:a[5]||(a[5]=x((()=>{}),["prevent","stop"]))},{default:d((()=>[n(r,{class:"title"},{default:d((()=>[p("退款原因")])),_:1}),n(y,{"scroll-y":"true",class:"h-[450rpx] px-[30rpx] box-border"},{default:d((()=>[n(U,{modelValue:Y.value,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.value=e),placement:"column",iconPlacement:"right"},{default:d((()=>[(s(!0),m(v,null,f(K.value,((e,a)=>(s(),u(T,{activeColor:"var(--primary-color)",labelSize:"30rpx",labelColor:"#333",customStyle:{marginBottom:"34rpx"},key:a,label:e,name:e},null,8,["label","name"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(r,{class:"btn-wrap"},{default:d((()=>[n(R,{class:"primary-btn-bg btn",onClick:re},{default:d((()=>[p("确定")])),_:1})])),_:1})])),_:1})])),_:1},8,["show","onOpen"])])),_:1})])),_:1})])),_:1},8,["current"])):_("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-f2043f35"]]);export{E as default};
