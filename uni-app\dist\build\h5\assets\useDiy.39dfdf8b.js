import{r as a,aE as e,q as l,p as o,A as t,J as g,N as r,a2 as n,aF as i,W as u,aq as d,ar as p,an as v}from"./index-3caf046d.js";function S(S={}){const b=a(!0),c=e(),s=a(0),m=a(S.name||""),h=a(""),y=a(""),B=l({}),x=l({pageMode:"diy",title:"",global:{},value:[]}),C=o((()=>"decorate"==c.mode?c:x)),k=a(!1);return{getLoading:()=>b.value,data:C.value,isShowTopTabbar:k,pageStyle:()=>{var a="";return C.value.global.pageStartBgColor&&(C.value.global.pageStartBgColor&&C.value.global.pageEndBgColor?a+=`background:linear-gradient(${C.value.global.pageGradientAngle},${C.value.global.pageStartBgColor},${C.value.global.pageEndBgColor});`:a+="background-color:"+C.value.global.pageStartBgColor+";"),C.value.global.bottomTabBarSwitch?a+="min-height:calc(100vh - 50px);":a+="min-height:calc(100vh);",C.value.global.bgUrl&&(a+=`background-image:url('${t(C.value.global.bgUrl)}');`),C.value.global.bgHeightScale&&(a+=`background-size: 100% ${C.value.global.bgHeightScale}%;`),a},onLoad:()=>{g((a=>{c.mode=a.mode||"","decorate"==c.mode&&(b.value=!1),s.value=a.id||"",""==m.value&&(m.value=a.name||""),h.value=a.template||""}))},onShow:(a=null)=>{r((()=>{let e=n();y.value=e[e.length-1]?e[e.length-1].route:"";let l=[];uni.getStorageSync("diyPageBlank")&&(l=uni.getStorageSync("diyPageBlank")),!l.length||l.length&&-1==l.indexOf(y.value)?c.topFixedStatus="home":l.length&&-1!=l.indexOf(y.value)&&(c.topFixedStatus="diy"),"decorate"==c.mode?c.init():i({id:s.value,name:m.value,template:h.value}).then((e=>{if(Object.assign(B,e.data),B.value){x.pageMode=B.mode,x.title=B.title;let a=JSON.parse(B.value);x.global=a.global,x.value=a.value,x.value.forEach(((a,e)=>{a.pageStyle="",a.pageStartBgColor&&(a.pageStartBgColor&&a.pageEndBgColor?a.pageStyle+=`background:linear-gradient(${a.pageGradientAngle},${a.pageStartBgColor},${a.pageEndBgColor});`:a.pageStyle+="background-color:"+a.pageStartBgColor+";"),a.margin&&(a.margin.top>0&&(a.pageStyle+="padding-top:"+2*a.margin.top+"rpx;"),a.pageStyle+="padding-bottom:"+2*a.margin.bottom+"rpx;",a.pageStyle+="padding-right:"+2*a.margin.both+"rpx;",a.pageStyle+="padding-left:"+2*a.margin.both+"rpx;")})),k.value=x.value.some((a=>a&&a.position&&"top_fixed"==a.position)),u({title:x.title})}b.value=!1,a&&a(B)}))}))},onHide:(a=null)=>{d((()=>{let e=[];uni.getStorageSync("diyPageBlank")&&(e=uni.getStorageSync("diyPageBlank")),e.length&&(e=Array.from(new Set(e)),e.forEach(((a,e,l)=>{a==y.value&&l.splice(e,1)}))),"diy"==c.topFixedStatus&&e.push(y.value),uni.setStorageSync("diyPageBlank",e),a&&a()}))},onUnload:()=>{p((()=>{}))},onPageScroll:()=>{v((a=>{a.scrollTop>0&&(c.scrollTop=a.scrollTop)}))}}}export{S as u};
