import{ac as s,ad as a,bi as t,af as e,o,c as r,$ as n,n as u,k as i}from"./index-3caf046d.js";import{_ as m}from"./_plugin-vue_export-helper.1b428a4d.js";const c=m({name:"u-safe-bottom",mixins:[s,a,{props:{}}],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){return t({},e(this.customStyle))}},mounted(){}},[["render",function(s,a,t,e,m,c){const l=i;return o(),r(l,{class:n(["u-safe-bottom",[!m.isNvue&&"u-safe-area-inset-bottom"]]),style:u([c.style])},null,8,["style","class"])}],["__scopeId","data-v-66aaaf9c"]]);export{c as _};
