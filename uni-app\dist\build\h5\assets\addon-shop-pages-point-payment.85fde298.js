import{d as e,r as t,I as l,p as a,N as r,s,o as i,c as d,w as o,b as u,g as n,n as c,e as p,y as x,z as v,F as _,t as y,x as f,$ as m,a_ as g,M as k,as as b,a as h,k as w,Q as j,S as C,al as F,i as V,j as S,R as T,A as B,bK as $}from"./index-3caf046d.js";import{_ as D}from"./u--image.eb573bce.js";import{_ as O,a as R,b as A,n as I,c as z}from"./index.75b1e6c8.js";import{_ as E}from"./u-tabbar.38f37e13.js";import{_ as Q}from"./pay.e8ba1ab9.js";import{o as U,d as G}from"./point.0698952c.js";import{t as K}from"./topTabbar.9217e319.js";import{_ as P}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.04cba9a2.js";import"./u-icon.ba193921.js";/* empty css                                                               */import"./u-transition.4c8b523a.js";/* empty css                                                                     *//* empty css                                                                */import"./u-loading-icon.255170b9.js";import"./u-empty.ce10a891.js";import"./u-popup.1b30ffa7.js";import"./u-safe-bottom.98e092c5.js";import"./order.5c5c6bee.js";import"./u-form.49dbb57f.js";import"./u-line.69c0c00f.js";import"./u-input.2d8dc7a4.js";import"./pay.1a29db5c.js";const J=P(e({__name:"payment",setup(e){const P=t({order_key:"",member_remark:"",discount:{},invoice:{},delivery:{delivery_type:""}}),J=l(),M=a((()=>J.info));K().setTopTabbarParam({title:"待付款订单"});const N=t(null),q=t(),H=t(),L=t(),W=t(!1),X=t(0),Y=t([]),Z=t();uni.getStorageSync("orderCreateData")&&Object.assign(P.value,uni.getStorageSync("orderCreateData"));const ee=t(0),te=t({}),le=t(null),ae=()=>{le.value?le.value.show=!0:k({title:"请选择自提点",icon:"none"})},re=e=>{P.value.delivery.buyer_ask_delivery_time=e},se=t(null),ie=e=>{se.value=e};r((()=>{}));const de=()=>{q.value&&(P.value.delivery.take_store_id||q.value.getData((e=>{e.length&&(P.value.delivery.take_store_id=e[0]&&e[0].store_id?e[0].store_id:0)}))),q.value.open()},oe=uni.getStorageSync("selectAddressCallback");oe&&(P.value.order_key="",P.value.delivery.delivery_type=oe.delivery,P.value.delivery.take_address_id=oe.address_id,uni.removeStorage({key:"selectAddressCallback"}));const ue=(e,t)=>{"store"==e&&0==ee.value&&(ee.value++,q.value.getData((e=>{var t;e.length&&(P.value.delivery.take_store_id=(null==(t=e[0])?void 0:t.store_id)??0,ne())}))),P.value.delivery.delivery_type!=e&&(X.value=t,P.value.order_key="",P.value.delivery.delivery_type=e,P.value.delivery.take_address_id=0,ne())},ne=()=>{U(P.value).then((({data:e})=>{N.value=e,P.value.order_key=e.order_key,N.value.delivery.delivery_type_list&&(Y.value=Object.values(N.value.delivery.delivery_type_list)),"store"==P.value.delivery.delivery_type?(P.value.delivery.taker_name=M.value.nickname,P.value.delivery.taker_mobile=M.value.mobile):N.value.delivery&&N.value.delivery.take_address&&(P.value.delivery.taker_name=N.value.delivery.take_address.name,P.value.delivery.taker_mobile=N.value.delivery.take_address.mobile),N.value.delivery.take_store&&(te.value={time_interval:N.value.delivery.take_store.time_interval,time_week:N.value.delivery.take_store.time_week,trade_time_json:N.value.delivery.take_store.trade_time_json}),oe&&(X.value=Y.value.findIndex((e=>e.key===N.value.delivery.delivery_type))),!P.value.delivery.delivery_type&&e.delivery.delivery_type&&(P.value.delivery.delivery_type=e.delivery.delivery_type),b((()=>{setTimeout((()=>{Y.value&&Object.keys(Y.value).length&&"store"==Y.value[0].key&&q.value&&q.value.getData((e=>{e.length&&(P.value.delivery.take_store_id=e[0]&&e[0].store_id?e[0].store_id:0)}))}),500)}))})).catch()};ne(),s((()=>Y.value.length),((e,t)=>{Y.value.length&&uni.getStorageSync("distributionType")&&(Y.value.forEach(((e,t)=>{e.name==uni.getStorageSync("distributionType")&&(X.value=t,ue(e.key,t))})),uni.removeStorage({key:"distributionType"}))}));let ce=0;const pe=()=>{xe()&&!W.value&&(W.value=!0,G(P.value).then((({data:e})=>{var t;ce=e.order_id,0==N.value.basic.order_money?h({url:"/addon/shop/pages/order/detail",param:{order_id:ce},mode:"redirectTo"}):null==(t=H.value)||t.open(e.trade_type,e.order_id,`/addon/shop/pages/order/detail?order_id=${e.order_id}`)})).catch((()=>{W.value=!1})))},xe=()=>{const e=P.value;if(N.value.basic.has_goods_types.includes("real")){if(["express","local_delivery"].includes(e.delivery.delivery_type)&&!N.value.delivery.take_address)return k({title:"请选择收货地址",icon:"none"}),!1;if("store"==e.delivery.delivery_type&&!e.delivery.take_store_id)return k({title:"请选择自提点",icon:"none"}),!1;if("store"==e.delivery.delivery_type){if(!e.delivery.taker_name)return k({title:"请输入姓名",icon:"none"}),!1;if(!e.delivery.taker_mobile)return k({title:"请输入手机号",icon:"none"}),!1;if(!/^1[3-9]\d{9}$/.test(e.delivery.taker_mobile))return k({title:"请输入正确的手机号",icon:"none"}),!1;if(!e.delivery.buyer_ask_delivery_time)return k({title:"请选择自提时间",icon:"none"}),!1}}return!0},ve=()=>{h({url:"/addon/shop/pages/order/detail",param:{order_id:ce},mode:"redirectTo"})},_e=()=>{let e={};e.delivery=P.value.delivery.delivery_type,e.type="local_delivery"==P.value.delivery.delivery_type?"location_address":"address",e.id=N.value.delivery.take_address.id,Z.value.open(e)},ye=e=>{P.value.delivery.take_store_id=e&&e.store_id?e.store_id:0,e&&(te.value={time_interval:e.time_interval,time_week:e.time_week,trade_time_json:e.trade_time_json}),ne()},fe=e=>{P.value.invoice=e},me=e=>{P.value.order_key="",P.value.delivery.delivery_type=e.delivery,P.value.delivery.take_address_id=e.address_id,ne()};return(e,t)=>{const l=w,a=j,r=C,s=F,k=V(S("u--image"),D),b=V(S("u-alert"),z),h=T,U=V(S("u-tabbar"),E),G=V(S("pay"),Q);return i(),d(l,{style:c(e.themeColor()),class:"payment-wrap"},{default:o((()=>[N.value?(i(),d(l,{key:0,class:"payment-body min-h-[100vh]"},{default:o((()=>[u(l,{class:"pt-[30rpx] sidebar-margin payment-bottom"},{default:o((()=>[n(" 配送方式 "),N.value.basic.has_goods_types.includes("real")&&Y.value.length?(i(),d(l,{key:0,class:"mb-[var(--top-m)] rounded-[var(--rounded-big)] bg-white",style:c({backgroundImage:`url(${p(B)("addon/shop/payment/head_bg.png")})`,backgroundSize:"100%",backgroundRepeat:"no-repeat",backgroundPosition:"bottom"})},{default:o((()=>[Y.value.length>1?(i(),d(l,{key:0,class:"rounded-tl-[var(--rounded-big)] rounded-tr-[var(--rounded-big)] head-tab flex items-center w-full bg-[var(--shop-payment-header-tab-color)]"},{default:o((()=>[(i(!0),x(_,null,v(Y.value,((e,t)=>(i(),d(l,{key:t,class:m(["head-tab-item flex-1 relative",{active:t===X.value}])},{default:o((()=>[u(l,{class:"h-[74rpx] relative z-10 text-center leading-[74rpx] text-[28rpx]",onClick:l=>ue(e.key,t)},{default:o((()=>[y(f(e.name),1)])),_:2},1032,["onClick"]),t===X.value&&3==Y.value.length?(i(),d(a,{key:0,class:"tab-image absolute bottom-[-2rpx] h-[94rpx] w-[240rpx]",src:p(B)(`addon/shop/payment/tab_${t}.png`),mode:"aspectFit"},null,8,["src"])):t===X.value&&2==Y.value.length?(i(),d(a,{key:1,class:"tab-img absolute bottom-[-2rpx] h-[95rpx] w-[354rpx]",src:p(B)(`addon/shop/payment/tabstyle_${t}.png`),mode:"aspectFit"},null,8,["src"])):n("v-if",!0)])),_:2},1032,["class"])))),128))])),_:1})):n("v-if",!0),u(l,{class:"min-h-[140rpx] flex items-center px-[30rpx]"},{default:o((()=>[n(" 收货地址 "),["express","local_delivery"].includes(P.value.delivery.delivery_type)?(i(),d(l,{key:0,class:"w-full",onClick:_e},{default:o((()=>[e.$u.test.isEmpty(N.value.delivery.take_address)?(i(),d(l,{key:1,class:"flex items-center"},{default:o((()=>[u(a,{class:"w-[26rpx] h-[30rpx] mr-[10rpx]",src:p(B)("addon/shop/payment/position_02.png"),mode:"aspectFit"},null,8,["src"]),u(r,{class:"text-[28rpx]"},{default:o((()=>[y("添加收货地址")])),_:1}),u(r,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)] ml-auto"})])),_:1})):(i(),d(l,{key:0,class:"pt-[20rpx] pb-[30rpx] flex items-center"},{default:o((()=>[u(a,{class:"w-[60rpx] h-[60rpx] mr-[20rpx] flex-shrink-0",src:p(B)("addon/shop/payment/position_01.png"),mode:"aspectFit"},null,8,["src"]),u(l,{class:"flex flex-col overflow-hidden"},{default:o((()=>[u(r,{class:"text-[26rpx] text-[var(--text-color-light9)] mt-[16rpx] truncate max-w-[536rpx]"},{default:o((()=>[y(f(N.value.delivery.take_address.full_address.split(N.value.delivery.take_address.address)[0]),1)])),_:1}),u(r,{class:"font-500 text-[30rpx] mt-[14rpx] text-[#333] truncate max-w-[536rpx]"},{default:o((()=>[y(f(N.value.delivery.take_address.address),1)])),_:1}),u(l,{class:"flex items-center text-[26rpx] text-[var(--text-color-light6)] mt-[16rpx]"},{default:o((()=>[u(r,{class:"mr-[16rpx]"},{default:o((()=>[y(f(N.value.delivery.take_address.name),1)])),_:1}),u(r,null,{default:o((()=>[y(f(p($)(N.value.delivery.take_address.mobile)),1)])),_:1})])),_:1})])),_:1}),u(r,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})):n("v-if",!0),n(" 自提点 "),"store"==P.value.delivery.delivery_type?(i(),d(l,{key:1,class:"flex items-center w-full flex items-center",onClick:de},{default:o((()=>[e.$u.test.isEmpty(N.value.delivery.take_store)?(i(),d(l,{key:1,class:"flex items-center w-full"},{default:o((()=>[u(a,{class:"w-[26rpx] h-[30rpx] mr-[10rpx]",src:p(B)("addon/shop/payment/position_02.png"),mode:"aspectFit"},null,8,["src"]),u(r,{class:"text-[28rpx]"},{default:o((()=>[y("请选择自提点")])),_:1}),u(r,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):(i(),d(l,{key:0,class:"pt-[26rpx] pb-[30rpx] w-full flex items-center"},{default:o((()=>[u(l,{class:"flex flex-col"},{default:o((()=>[u(l,{class:"text-[30rpx] font-500 text-[#303133] mb-[20rpx]"},{default:o((()=>[y(f(N.value.delivery.take_store.store_name),1)])),_:1}),u(l,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[14rpx]"},{default:o((()=>[y("门店地址："+f(N.value.delivery.take_store.full_address),1)])),_:1}),u(l,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[14rpx]"},{default:o((()=>[y("联系电话："+f(N.value.delivery.take_store.store_mobile),1)])),_:1}),u(l,{class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:o((()=>[y("营业时间："+f(N.value.delivery.take_store.trade_time),1)])),_:1})])),_:1}),u(r,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})):n("v-if",!0)])),_:1}),"store"==P.value.delivery.delivery_type?(i(),d(l,{key:1},{default:o((()=>[n(" 姓名 "),u(l,{class:"px-[20rpx] py-[14rpx]"},{default:o((()=>[u(l,{class:"flex justify-between items-center"},{default:o((()=>[u(l,{class:"text-color text-[28rpx]",onClick:ae},{default:o((()=>[y("姓名")])),_:1}),u(s,{class:"text-right",maxlength:"20","placeholder-style":"color:#B1B3B5",placeholder:"请输入",modelValue:P.value.delivery.taker_name,"onUpdate:modelValue":t[0]||(t[0]=e=>P.value.delivery.taker_name=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(" 预留手机 "),u(l,{class:"px-[20rpx] py-[14rpx]"},{default:o((()=>[u(l,{class:"flex justify-between items-center"},{default:o((()=>[u(l,{class:"text-color text-[28rpx]"},{default:o((()=>[y("预留手机")])),_:1}),u(s,{class:"text-right",maxlength:"11","placeholder-style":"color:#B1B3B5",placeholder:"请输入",modelValue:P.value.delivery.taker_mobile,"onUpdate:modelValue":t[1]||(t[1]=e=>P.value.delivery.taker_mobile=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(" 提货时间 "),u(l,{class:"flex justify-between items-center box-border pt-[14rpx] pb-[24rpx] px-[20rpx]"},{default:o((()=>[u(l,{class:"text-color text-[28rpx]"},{default:o((()=>[y("提货时间")])),_:1}),u(l,{class:"flex",onClick:ae},{default:o((()=>[u(l,{class:m(["text-[28rpx] ml-2 text-right",{"text-[#63676D]":!P.value.delivery.buyer_ask_delivery_time}])},{default:o((()=>[y(f(P.value.delivery.buyer_ask_delivery_time?se.value:"选择提货时间"),1)])),_:1},8,["class"]),u(r,{class:"text-[26rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1})])),_:1})):n("v-if",!0)])),_:1},8,["style"])):n("v-if",!0),N.value.basic.has_goods_types.includes("real")&&!Y.value.length?(i(),d(l,{key:1,class:"mb-[var(--top-m)] card-template h-[100rpx] flex items-center"},{default:o((()=>[g("p",{class:"text-[28rpx] text-[var(--primary-color)]"},"商家尚未配置配送方式")])),_:1})):n("v-if",!0),u(l,{class:"mb-[var(--top-m)] card-template"},{default:o((()=>[u(l,{class:"mb-[30rpx]"},{default:o((()=>[(i(!0),x(_,null,v(N.value.goods_data,((e,t,s)=>(i(),d(l,{class:m(["flex",{"pb-[40rpx]":s+1!=Object.keys(N.value.goods_data).length}]),key:s},{default:o((()=>[u(k,{radius:"var(--goods-rounded-big)",width:"180rpx",height:"180rpx",src:p(B)(e.sku_image.split(",")[0]),model:"aspectFill"},{error:o((()=>[u(a,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:p(B)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),u(l,{class:"flex flex-1 w-0 flex-col justify-between ml-[20rpx] py-[6rpx]"},{default:o((()=>[u(l,{class:"line-normal"},{default:o((()=>[u(l,{class:"truncate text-[#303133] text-[28rpx] leading-[32rpx]"},{default:o((()=>[y(f(e.goods.goods_name),1)])),_:2},1024),e.sku_name?(i(),d(l,{key:0,class:"mt-[14rpx] flex"},{default:o((()=>[u(r,{class:"truncate text-[24rpx] text-[var(--text-color-light9)] leading-[28rpx]"},{default:o((()=>[y(f(e.sku_name),1)])),_:2},1024)])),_:2},1024)):n("v-if",!0)])),_:2},1024),e.not_support_delivery?(i(),d(l,{key:0,class:m(["mb-auto",{"mt-[6rpx]":!e.sku_name}])},{default:o((()=>[u(b,{type:"error",description:"该商品不支持当前所选配送方式",class:"leading-[30rpx] !inline-block",fontSize:"11"})])),_:2},1032,["class"])):n("v-if",!0),u(l,{class:"flex justify-between items-baseline"},{default:o((()=>[u(l,{class:"text-[var(--price-text-color)] flex items-baseline price-font"},{default:o((()=>[u(l,{class:"flex items-baseline price-font"},{default:o((()=>[u(r,{class:"text-[40rpx] font-200"},{default:o((()=>[y(f(e.exchange_info.point),1)])),_:2},1024),u(r,{class:"text-[32rpx]"},{default:o((()=>[y("积分")])),_:1})])),_:2},1024),parseFloat(e.price)?(i(),x(_,{key:0},[u(r,{class:"mx-[4rpx] text-[32rpx]"},{default:o((()=>[y("+")])),_:1}),u(l,{class:"flex items-baseline price-font"},{default:o((()=>[u(r,{class:"text-[40rpx] font-200"},{default:o((()=>[y(f(parseFloat(e.price).toFixed(2)),1)])),_:2},1024),u(r,{class:"text-[32rpx]"},{default:o((()=>[y("元")])),_:1})])),_:2},1024)],64)):n("v-if",!0)])),_:2},1024),u(l,{class:"font-400 text-[28rpx] text-[#303133]"},{default:o((()=>[u(r,null,{default:o((()=>[y("x")])),_:1}),u(r,null,{default:o((()=>[y(f(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1}),n(" 买家留言 "),u(l,{class:"bg-white flex items-center leading-[30rpx]"},{default:o((()=>[u(l,{class:"text-[28rpx] w-[150rpx] text-[#303133]"},{default:o((()=>[y("买家留言")])),_:1}),u(l,{class:"flex-1 text-[#303133]"},{default:o((()=>[u(s,{type:"text",modelValue:P.value.member_remark,"onUpdate:modelValue":t[2]||(t[2]=e=>P.value.member_remark=e),class:"text-right text-[#333] text-[28rpx]",maxlength:"50",placeholder:"请输入留言信息给卖家","placeholder-class":"text-[var(--text-color-light9)] text-[28rpx]"},null,8,["modelValue"])])),_:1}),n(' <text class="nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"></text> ')])),_:1}),n(" 发票 "),L.value&&L.value.invoiceOpen?(i(),d(l,{key:0,class:"flex items-center text-[#303133] leading-[30rpx] mt-[30rpx]",onClick:t[3]||(t[3]=e=>L.value.open())},{default:o((()=>[u(l,{class:"text-[28rpx] w-[150rpx] text-[#303133]"},{default:o((()=>[y("发票信息")])),_:1}),u(l,{class:"flex-1 w-0 text-right truncate"},{default:o((()=>[u(r,{class:"text-[28rpx] text-[#333]"},{default:o((()=>[y(f(P.value.invoice.header_name||"不需要发票"),1)])),_:1})])),_:1}),u(r,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):n("v-if",!0)])),_:1}),u(l,{class:"card-template"},{default:o((()=>[u(l,{class:"title"},{default:o((()=>[y("价格明细")])),_:1}),u(l,{class:"card-template-item"},{default:o((()=>[u(l,{class:"text-[28rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:o((()=>[y("商品金额")])),_:1}),u(l,{class:"flex-1 w-0 text-right price-font text-[#333] text-[32rpx]"},{default:o((()=>[u(l,{class:"inline-block"},{default:o((()=>[u(r,{class:"text-[32rpx] mr-[2rpx]"},{default:o((()=>[y(f(N.value.basic.point_sum),1)])),_:1}),u(r,{class:"text-[30rpx]"},{default:o((()=>[y("积分")])),_:1})])),_:1}),N.value.basic&&parseFloat(N.value.basic.goods_money)?(i(),x(_,{key:0},[u(r,{class:"text-[28rpx] mx-[4rpx]"},{default:o((()=>[y("+")])),_:1}),u(l,{class:"inline-block"},{default:o((()=>[u(r,{class:"text-[32rpx] mr-[2rpx]"},{default:o((()=>[y(f(parseFloat(N.value.basic.goods_money).toFixed(2)),1)])),_:1}),u(r,{class:"text-[30rpx]"},{default:o((()=>[y("元")])),_:1})])),_:1})],64)):n("v-if",!0)])),_:1})])),_:1}),N.value.basic.delivery_money?(i(),d(l,{key:0,class:"card-template-item"},{default:o((()=>[u(l,{class:"text-[26rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:o((()=>[y("配送费用")])),_:1}),u(l,{class:"flex-1 w-0 text-right price-font text-[#333] text-[32rpx]"},{default:o((()=>[y("￥"+f(parseFloat(N.value.basic.delivery_money)),1)])),_:1})])),_:1})):n("v-if",!0),N.value.basic.discount_money?(i(),d(l,{key:1,class:"card-template-item"},{default:o((()=>[u(l,{class:"text-[26rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:o((()=>[y("优惠金额")])),_:1}),u(l,{class:"flex-1 w-0 text-right text-[var(--price-text-color)] text-[32rpx] price-font leading-[1]"},{default:o((()=>[y("-￥"+f(parseFloat(N.value.basic.discount_money)),1)])),_:1})])),_:1})):n("v-if",!0)])),_:1})])),_:1}),u(U,{fixed:!0,placeholder:!0,safeAreaInsetBottom:!0},{default:o((()=>[u(l,{class:"flex-1 flex items-center justify-between pl-[30rpx] pr-[20rpx]"},{default:o((()=>[u(l,{class:"flex items-baseline"},{default:o((()=>[u(r,{class:"text-[26rpx] text-[#333] leading-[32rpx]"},{default:o((()=>[y("合计：")])),_:1}),u(r,{class:"text-[var(--price-text-color)] price-font inline-block"},{default:o((()=>[u(r,{class:"text-[44rpx]"},{default:o((()=>[y(f(N.value.basic.point_sum),1)])),_:1}),u(r,{class:"text-[38rpx]"},{default:o((()=>[y("积分")])),_:1})])),_:1}),N.value.basic&&parseFloat(N.value.basic.goods_money)?(i(),x(_,{key:0},[u(r,{class:"text-[38rpx] text-[var(--price-text-color)] price-font mx-[4rpx]"},{default:o((()=>[y("+")])),_:1}),u(l,{class:"inline-block"},{default:o((()=>[u(r,{class:"text-[44rpx] text-[var(--price-text-color)] price-font"},{default:o((()=>[y(f(parseFloat(N.value.basic.order_money).toFixed(2)),1)])),_:1}),u(r,{class:"text-[38rpx] text-[var(--price-text-color)] price-font"},{default:o((()=>[y("元")])),_:1})])),_:1})],64)):n("v-if",!0)])),_:1}),u(h,{class:"primary-btn-bg w-[196rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] !border-[0] rounded-[100rpx] m-0","hover-class":"none",onClick:pe},{default:o((()=>[y("提交订单 ")])),_:1})])),_:1})])),_:1}),n(" 选择自提点 "),u(p(O),{ref_key:"storeRef",ref:q,onConfirm:ye},null,512),n(" 发票 "),u(p(R),{ref_key:"invoiceRef",ref:L,onConfirm:fe},null,512),n(" 地址 "),u(p(A),{ref_key:"addressRef",ref:Z,onConfirm:me,back:"/addon/shop/pages/point/payment"},null,512),u(G,{ref_key:"payRef",ref:H,onClose:ve},null,512),Object.keys(te.value).length?(i(),d(p(I),{key:0,ref_key:"selectTime",ref:le,rules:te.value,isQuantum:!0,onChange:re,onGetDate:ie},null,8,["rules"])):n("v-if",!0)])),_:1})):n("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-231b7711"]]);export{J as default};
